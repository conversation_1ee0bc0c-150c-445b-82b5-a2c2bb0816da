<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical" >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background_titlebar"
        android:gravity="center_vertical"
        android:orientation="horizontal" >

<!--        <ImageView-->
<!--            android:id="@+id/title_mainicon"-->
<!--            android:layout_width="44dp"-->
<!--            android:layout_height="44dp"-->
<!--            android:contentDescription="@string/app_name"-->
<!--            android:padding="4dp"-->
<!--            android:src="@drawable/ic_launcher" />-->

        <TextView
            android:id="@+id/title_mainname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:text="@string/app_name"
            android:textColor="@color/foreground_on_dark"
            android:textSize="20sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="fill_parent"
        android:background="@drawable/theme_background"
        android:orientation="vertical" >

        <Button
            android:id="@+id/btn_printer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            android:layout_marginTop="5dp"
            android:background="@color/border_color_on_light"
            android:gravity="center"
            android:hint="@string/noconnectprinter"
            android:onClick="selectPrinterOnClick"
            android:textColorHint="@android:color/white" />

<!--        <LinearLayout-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginTop="5dp"-->
<!--            android:orientation="horizontal" >-->

<!--            <Button-->
<!--                android:id="@+id/btn_printquality"-->
<!--                android:layout_width="0dp"-->
<!--                android:layout_height="match_parent"-->
<!--                android:layout_marginLeft="5dp"-->
<!--                android:layout_weight="50"-->
<!--                android:background="@color/border_color_on_light"-->
<!--                android:gravity="center"-->
<!--                android:onClick="printQualityOnClick"-->
<!--                android:paddingLeft="5dp"-->
<!--                android:paddingRight="5dp"-->
<!--                android:text="" />-->

<!--            <Button-->
<!--                android:id="@+id/btn_gaptype"-->
<!--                android:layout_width="0dp"-->
<!--                android:layout_height="match_parent"-->
<!--                android:layout_marginLeft="5dp"-->
<!--                android:layout_marginRight="5dp"-->
<!--                android:layout_weight="50"-->
<!--                android:background="@color/border_color_on_light"-->
<!--                android:gravity="center"-->
<!--                android:onClick="gapTypeOnClick"-->
<!--                android:paddingLeft="5dp"-->
<!--                android:paddingRight="5dp"-->
<!--                android:text="" />-->
<!--        </LinearLayout>-->

<!--        <LinearLayout-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginTop="5dp"-->
<!--            android:orientation="horizontal" >-->

<!--            <Button-->
<!--                android:id="@+id/btn_printdensity"-->
<!--                android:layout_width="0dp"-->
<!--                android:layout_height="match_parent"-->
<!--                android:layout_marginLeft="5dp"-->
<!--                android:layout_weight="50"-->
<!--                android:background="@color/border_color_on_light"-->
<!--                android:gravity="center"-->
<!--                android:onClick="printDensityOnClick"-->
<!--                android:paddingLeft="5dp"-->
<!--                android:paddingRight="5dp"-->
<!--                android:text="" />-->

<!--            <Button-->
<!--                android:id="@+id/btn_printspeed"-->
<!--                android:layout_width="0dp"-->
<!--                android:layout_height="match_parent"-->
<!--                android:layout_marginLeft="5dp"-->
<!--                android:layout_marginRight="5dp"-->
<!--                android:layout_weight="50"-->
<!--                android:background="@color/border_color_on_light"-->
<!--                android:gravity="center"-->
<!--                android:onClick="printSpeedOnClick"-->
<!--                android:paddingLeft="5dp"-->
<!--                android:paddingRight="5dp"-->
<!--                android:text="" />-->
<!--        </LinearLayout>-->

<!--        <Button-->
<!--            android:id="@+id/btn_printtext"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginLeft="5dp"-->
<!--            android:layout_marginRight="5dp"-->
<!--            android:layout_marginTop="10dp"-->
<!--            android:background="@color/border_color_on_light"-->
<!--            android:gravity="center"-->
<!--            android:onClick="printTextOnClick"-->
<!--            android:text="@string/printtext" />-->

<!--        <Button-->
<!--            android:id="@+id/btn_printtext1dbarcode"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginLeft="5dp"-->
<!--            android:layout_marginRight="5dp"-->
<!--            android:layout_marginTop="5dp"-->
<!--            android:background="@color/border_color_on_light"-->
<!--            android:gravity="center"-->
<!--            android:onClick="printText1DBarcodeOnClick"-->
<!--            android:text="@string/printtext1dbarcode" />-->

        <Button
            android:id="@+id/btn_print2dbarcode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            android:layout_marginTop="5dp"
            android:background="@color/border_color_on_light"
            android:gravity="center"
            android:onClick="print2DBarcodeOnClick"
            android:text="@string/print2dbarcode" />

<!--        <Button-->
<!--            android:id="@+id/btn_printbitmap"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginLeft="5dp"-->
<!--            android:layout_marginRight="5dp"-->
<!--            android:layout_marginTop="5dp"-->
<!--            android:background="@color/border_color_on_light"-->
<!--            android:gravity="center"-->
<!--            android:onClick="printBitmapOnClick"-->
<!--            android:text="@string/printbitmap" />-->
    </LinearLayout>

</LinearLayout>
package io.dcloud.uniplugin.form;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;

import org.json.JSONObject;

import java.io.File;
import java.util.List;
import java.util.Map;

import io.dcloud.uniplugin.enums.SamplingPointStatus;
import io.dcloud.uniplugin.enums.SharedPreferencesEnum;
import io.dcloud.uniplugin.fileUpload.ImageAdapter;
import io.dcloud.uniplugin.form.field.FieldFile;
import io.dcloud.uniplugin.form.media.MediaHandler;
import io.dcloud.uniplugin.form.processor.FormProcessor;
import io.dcloud.uniplugin.form.utils.FormLifecycleManager;
import io.dcloud.uniplugin.form.utils.FormLocalStorageManager;
import io.dcloud.uniplugin.form.utils.FormLocationManager;
import io.dcloud.uniplugin.form.utils.FormMediaPreviewManager;
import io.dcloud.uniplugin.form.utils.FormUIController;
import io.dcloud.uniplugin.form.utils.FormValidationUtils;
import io.dcloud.uniplugin.form.utils.UIUtils;
import io.dcloud.uniplugin.model.FormFieldConfig;
import io.dcloud.uniplugin.model.PhotoInfo;
import io.dcloud.uniplugin.service.FormService;
import io.dcloud.uniplugin.utils.SamplingPointSyncManager;
import uni.dcloud.io.uniplugin_module.R;


public class DynamicFormActivity extends AppCompatActivity implements MediaHandler.MediaCallback, FormProcessor.OnFormProcessListener {

    private static final String TAG = "DynamicFormActivity";
    private static final int REQUEST_PERMISSION_CAMERA = 2002;
    private static final int REQUEST_PERMISSION_LOCATION = 2003;

    // 使用枚举替代常量
    // public static final int STATUS_PENDING_SURVEY = 1; // 待调查
    // public static final int STATUS_PENDING_SUBMIT = 2; // 待提交
    // public static final int STATUS_SUBMITTED = 3;     // 已提交
    // public static final int STATUS_PENDING_RECTIFY = 4; // 待整改

    // 视图组件
    private LinearLayout formContainer;
    private ProgressBar progressBar;
    private Button buttonSubmit;
    private Button buttonSaveOffline;

    // 数据字段
    private String xmmc;
    private String pjdybh;
    private Double latitude;
    private Double longitude;
    private SamplingPointStatus samplingPointStatus = SamplingPointStatus.PENDING_SURVEY; // 默认为待调查状态

    //是否离线登录
    private boolean isOffline;
    private Long zgId;
    // 标志位
    private boolean loadLocalFormFlag = false;
    private boolean loadLocalFormByBsmFlag = false;

    // 各种管理器和控制器
    private MediaHandler mediaHandler;
    private FormUIController uiController;
    private Long pjdyId;
    private Long id;

    // 表单生命周期管理器
    private FormLifecycleManager formLifecycleManager;

    // 持有定时器引用，以便在活动销毁时取消
    private android.os.Handler disableButtonsHandler;
    private Runnable disableButtonsRunnable;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_dynamic_form);

        SharedPreferences sharedPreferences = getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, MODE_PRIVATE);
        isOffline = sharedPreferences.getBoolean(SharedPreferencesEnum.IS_OFFLINE_LOGIN.value, false);

        // 初始化视图
        initViews();

        // 获取传入的采样点ID和其他数据
        parseIntentData();

        // 创建UI控制器
        uiController = new FormUIController(this, formContainer, progressBar, buttonSubmit, buttonSaveOffline);

        // 初始化表单生命周期管理器
        initFormLifecycleManager();

        // 设置ActionBar - 确保在formLifecycleManager初始化之后再调用
        setupActionBar();

        // 设置按钮事件
        setupButtonEvents();
        
        // 添加长按保存按钮显示调试信息
        if (buttonSaveOffline != null) {
            buttonSaveOffline.setOnLongClickListener(v -> {
                debugFormFields();
                return true;
            });
        }
    }

    /**
     * 初始化视图组件
     */
    private void initViews() {
        formContainer = findViewById(R.id.formContainer);
        progressBar = findViewById(R.id.progressBar);
        buttonSubmit = findViewById(R.id.buttonSubmit);
        buttonSaveOffline = findViewById(R.id.buttonSaveOffline);

        // 更新按钮文本
        buttonSaveOffline.setText("保存到本地");

        // 确保容器不拦截触摸事件
        formContainer.setOnTouchListener((v, event) -> false);
    }

    /**
     * 从Intent解析传入的数据
     */
    private void parseIntentData() {
        Intent intent = getIntent();
        if (intent != null) {
            // 获取评价单元标识码
            pjdybh = intent.getStringExtra("pjdybh");
            if (TextUtils.isEmpty(pjdybh)) {
                pjdybh = "default_form"; // 默认表单ID
            }
            Log.d(TAG, "接收到样点标识码: " + pjdybh);

            // 获取评价单元ID
            pjdyId = intent.getLongExtra("pjdyId", 0L);
            Log.d(TAG, "接收到评价单元ID: " + pjdyId);

            // 获取下发记录ID
            id = intent.getLongExtra("id", 0L);
            Log.d(TAG, "接收到下发记录ID: " + id);

            xmmc = intent.getStringExtra("xmmc");
            Log.d(TAG, "接收到项目名称: " + xmmc);

            // 获取经纬度信息 - 修改为接收 String 并解析
            // 日志显示ListAdapter传来的是String, 但这里用getDoubleExtra导致ClassCastException，返回0.0
            // 因此需要用getStringExtra接收，然后手动解析
            String latStr = intent.getStringExtra("latitude");
            String lonStr = intent.getStringExtra("longitude");
            Log.d(TAG, "接收到经纬度字符串: lat=" + latStr + ", lon=" + lonStr);

            try {
                latitude = !TextUtils.isEmpty(latStr) ? Double.parseDouble(latStr) : 0.0;
            } catch (NumberFormatException e) {
                Log.e(TAG, "解析纬度失败: " + latStr, e);
                latitude = 0.0; // 解析失败时设为默认值
            }
            try {
                longitude = !TextUtils.isEmpty(lonStr) ? Double.parseDouble(lonStr) : 0.0;
            } catch (NumberFormatException e) {
                Log.e(TAG, "解析经度失败: " + lonStr, e);
                longitude = 0.0; // 解析失败时设为默认值
            }

            Log.d(TAG, "解析后的经纬度: " + latitude + ", " + longitude);

            // 处理样点状态参数
            int statusCode = intent.getIntExtra("samplingPointStatus", SamplingPointStatus.PENDING_SURVEY.getCode());
            samplingPointStatus = SamplingPointStatus.fromCode(statusCode);
            if (samplingPointStatus == null) {
                samplingPointStatus = SamplingPointStatus.PENDING_SURVEY;
            }

            //如果是待整改，则需要ID
            if (samplingPointStatus == SamplingPointStatus.PENDING_RECTIFY) {
                //获取样点Id, 用于更新
                zgId = intent.getLongExtra("zgId", 0L);
            }



            Log.d(TAG, "样点状态: " + getStatusName(samplingPointStatus));

            // 处理离线表单标志
            isOffline = intent.getBooleanExtra("isOffline", false);
            Log.d(TAG, "是否为离线表单: " + isOffline);

            // 处理加载本地表单标志
            loadLocalFormFlag = intent.getBooleanExtra("loadLocalForm", false);
            Log.d(TAG, "是否加载本地表单: " + loadLocalFormFlag);

            // 处理通过BSM加载本地表单标志
            loadLocalFormByBsmFlag = intent.getBooleanExtra("loadLocalFormByBsm", false);
            Log.d(TAG, "是否通过BSM加载本地表单: " + loadLocalFormByBsmFlag);
            
            // 处理是否使用formDataJson标志
            boolean useFormDataJson = intent.getBooleanExtra("useFormDataJson", false);
            Log.d(TAG, "是否使用formDataJson: " + useFormDataJson);
        }
    }

    /**
     * 获取状态名称
     * @param status 状态码
     * @return 状态名称
     */
    private String getStatusName(SamplingPointStatus status) {
        return status.getDescription();
    }

    /**
     * 设置ActionBar
     */
    private void setupActionBar() {
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            // 确保返回按钮可见且可用
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setHomeButtonEnabled(true);

            // 在标题中显示状态
            String statusName = getStatusName(samplingPointStatus);
            getSupportActionBar().setTitle(pjdybh + " [" + statusName + "]");

            // 根据状态设置按钮可见性和可用性
            configureButtonsByStatus();
        }
    }

    /**
     * 根据样点状态配置按钮
     */
    private void configureButtonsByStatus() {
        switch (samplingPointStatus) {
            case PENDING_SURVEY:
                // 待调查状态: 需要定位并在电子围栏内才能填报
                buttonSubmit.setEnabled(true);
                buttonSubmit.setVisibility(View.VISIBLE);
                buttonSaveOffline.setVisibility(View.VISIBLE);
                // 在这个阶段formLifecycleManager可能尚未初始化
                if (formLifecycleManager != null && formLifecycleManager.getFormViews() != null) {
                    setFormEditable(true);
                } else {
                    Log.d(TAG, "待调查状态：表单未加载完成，稍后设置可编辑状态");
                }
                break;

            case PENDING_SUBMIT:
                // 待提交状态: 需要定位，在电子围栏内允许填报，在电子围栏外只允许提交
                buttonSubmit.setEnabled(true);
                buttonSubmit.setVisibility(View.VISIBLE);
                buttonSaveOffline.setVisibility(View.VISIBLE);
                // 在这个阶段formLifecycleManager可能尚未初始化
                if (formLifecycleManager != null && formLifecycleManager.getFormViews() != null) {
                    setFormEditable(true);
                } else {
                    Log.d(TAG, "待提交状态：表单未加载完成，稍后设置可编辑状态");
                }
                break;

            case SUBMITTED:
                // 已提交状态：表单只读
                setFormEditable(false);
                // 使用FormUIController的方法彻底禁用所有表单控件和按钮
                if (uiController != null) {
                    uiController.setFormEnabled(false);
                    Log.d(TAG, "已提交状态：使用FormUIController完全禁用表单");
                    
                    // 额外确保提交按钮被禁用，防止其他地方重新启用它
                    uiController.setSubmitEnabled(false, false); // 不显示Toast
                    // 确保离线保存按钮也被隐藏
                    if (buttonSaveOffline != null) {
                        buttonSaveOffline.setVisibility(View.GONE);
                    }
                    Log.d(TAG, "已提交状态：额外确保按钮禁用");
                }
                
                // 禁用所有ImageAdapter的删除按钮
                if (formLifecycleManager != null) {
                    Map<String, ImageAdapter> imageAdapters = formLifecycleManager.getImageAdapters();
                    if (imageAdapters != null && !imageAdapters.isEmpty()) {
                        for (ImageAdapter adapter : imageAdapters.values()) {
                            if (adapter != null) {
                                adapter.setShowDeleteButton(false);
                                Log.d(TAG, "已提交状态：禁用ImageAdapter删除按钮");
                            }
                        }
                    }
                }
                break;

            case PENDING_RECTIFY:
                // 待整改状态: 可编辑且可提交
                buttonSubmit.setEnabled(true);
                buttonSubmit.setVisibility(View.VISIBLE);
                buttonSaveOffline.setVisibility(View.VISIBLE);
                // 在这个阶段formLifecycleManager可能尚未初始化
                if (formLifecycleManager != null && formLifecycleManager.getFormViews() != null) {
                    setFormEditable(true);
                } else {
                    Log.d(TAG, "待整改状态：表单未加载完成，稍后设置可编辑状态");
                }
                break;

            default:
                // 未知状态，默认行为
                buttonSubmit.setEnabled(true);
                buttonSubmit.setVisibility(View.VISIBLE);
                buttonSaveOffline.setVisibility(View.VISIBLE);
                // 在这个阶段formLifecycleManager可能尚未初始化
                if (formLifecycleManager != null && formLifecycleManager.getFormViews() != null) {
                    setFormEditable(true);
                } else {
                    Log.d(TAG, "未知状态：表单未加载完成，稍后设置可编辑状态");
                }
                break;
        }
    }

    /**
     * 设置表单是否可编辑
     * @param editable 是否可编辑
     */
    private void setFormEditable(boolean editable) {
        // 获取FormViews映射
        if (formLifecycleManager == null) {
            Log.e(TAG, "formLifecycleManager为空，无法设置表单编辑状态");
            return;
        }
        
        Map<String, View> formViews = formLifecycleManager.getFormViews();
        if (formViews == null) {
            Log.d(TAG, "formViews为空，无法设置表单编辑状态");
            return;
        }

        for (View view : formViews.values()) {
            if (view instanceof android.widget.EditText ||
                view instanceof android.widget.Spinner ||
                view instanceof android.widget.CheckBox ||
                view instanceof android.widget.RadioButton ||
                view instanceof android.widget.Button) {
                view.setEnabled(editable);
            }
        }

        Log.d(TAG, "设置表单编辑状态: " + (editable ? "可编辑" : "只读"));
    }

    /**
     * 初始化表单生命周期管理器
     */
    private void initFormLifecycleManager() {
        mediaHandler = new MediaHandler(this);
        mediaHandler.setMediaCallback(this);

        formLifecycleManager = new FormLifecycleManager(this, formContainer, pjdybh);
        formLifecycleManager.setCoordinates(latitude, longitude);
        formLifecycleManager.setUIController(uiController);
        formLifecycleManager.initServices(mediaHandler);
        
        // 设置pjdyId到表单渲染器
        formLifecycleManager.setPjdyId(pjdyId);

        // 设置表单加载完成的回调
        // 表单加载完成后，根据状态设置可编辑性
        formLifecycleManager.setOnFormLoadedListener(this::onFormLoaded);

        // 加载表单配置
        formLifecycleManager.loadFormConfig(samplingPointStatus,pjdybh,xmmc);
    }

    /**
     * 表单加载完成后调用
     */
    private void onFormLoaded() {
        Log.d(TAG, "表单加载完成，应用状态设置");
        
        // 从数据库加载表单数据
        if (samplingPointStatus == SamplingPointStatus.SUBMITTED || samplingPointStatus == SamplingPointStatus.PENDING_RECTIFY) {
//            loadFormDataFromDatabase();
        }
        
        // 配置位置管理器，在表单加载完成后配置
        configureLocationManager();
        
        // 根据样点状态设置表单的可编辑性
        switch (samplingPointStatus) {
            case PENDING_SURVEY:
                // 待调查状态：表单可编辑
                setFormEditable(true);
                break;
            case PENDING_SUBMIT:
                // 待提交状态：表单可编辑
                setFormEditable(true);
                break;
            case SUBMITTED:
                // 已提交状态：表单只读
                setFormEditable(false);
                // 使用FormUIController的方法彻底禁用所有表单控件和按钮
                if (uiController != null) {
                    uiController.setFormEnabled(false);
                    Log.d(TAG, "已提交状态：使用FormUIController完全禁用表单");
                    
                    // 额外确保提交按钮被禁用，防止其他地方重新启用它
                    uiController.setSubmitEnabled(false, false); // 不显示Toast
                    // 确保离线保存按钮也被隐藏
                    if (buttonSaveOffline != null) {
                        buttonSaveOffline.setVisibility(View.GONE);
                    }
                    Log.d(TAG, "已提交状态：额外确保按钮禁用");
                }
                
                // 禁用所有ImageAdapter的删除按钮
                if (formLifecycleManager != null) {
                    Map<String, ImageAdapter> imageAdapters = formLifecycleManager.getImageAdapters();
                    if (imageAdapters != null && !imageAdapters.isEmpty()) {
                        for (ImageAdapter adapter : imageAdapters.values()) {
                            if (adapter != null) {
                                adapter.setShowDeleteButton(false);
                                Log.d(TAG, "已提交状态：禁用ImageAdapter删除按钮");
                            }
                        }
                    }
                }
                break;
            case PENDING_RECTIFY:
                // 待整改状态：表单可编辑
                setFormEditable(true);
                break;
            default:
                // 默认可编辑
                setFormEditable(true);
                break;
        }
    }

    /**
     * 根据样点状态配置位置管理器
     */
    private void configureLocationManager() {
        // 只有在表单生命周期管理器初始化后才能配置位置管理器
        if (formLifecycleManager == null) {
            Log.e(TAG, "formLifecycleManager为空，无法配置位置管理器");
            return;
        }

        // 已提交状态不进行地理围栏检查
        if (samplingPointStatus == SamplingPointStatus.SUBMITTED) {
            Log.d(TAG, "已提交状态：跳过位置管理器初始化");
            
            // 如果位置管理器已经存在，设置忽略地理围栏检查
            FormLocationManager existingLocationManager = formLifecycleManager.getLocationManager();
            if (existingLocationManager != null) {
                existingLocationManager.setIgnoreGeofenceCheck(true, false); // 不启用提交按钮
                Log.d(TAG, "已提交状态：设置忽略地理围栏检查，但不启用提交按钮");
            }
            
            return;
        }
        
        // 如果位置管理器不存在，则初始化它
        if (formLifecycleManager.getLocationManager() == null) {
            Log.d(TAG, "位置管理器不存在，初始化位置管理器");
            formLifecycleManager.initLocationManager();
        }

        // 获取位置管理器
        FormLocationManager locationManager = formLifecycleManager.getLocationManager();
        if (locationManager == null) {
            Log.e(TAG, "locationManager初始化失败，无法配置位置管理器");
            return;
        }

        switch (samplingPointStatus) {
            case PENDING_SURVEY:
                // 待调查样点需要在电子围栏内才能填报
                locationManager.setAllowSubmitOutsideGeofence(false);
                Log.d(TAG, "待调查状态：不允许在围栏外提交表单");
                break;
                
            case PENDING_SUBMIT:
                // 待提交样点在电子围栏外也可以提交
                locationManager.setAllowSubmitOutsideGeofence(true);
                Log.d(TAG, "待提交状态：允许在围栏外提交表单");
                
                // 在这里额外确保提交按钮可用
                if (!locationManager.getIgnoreGeofenceCheck() && !locationManager.isInGeofence() && uiController != null) {
                    uiController.setFormReadOnlyButSubmittable(false); // 不启用保存到本地按钮
                    Log.d(TAG, "待提交状态：额外确保在围栏外时表单只读但只有提交按钮可用");
                }
                break;
                
            case PENDING_RECTIFY:
                // 待整改样点在电子围栏外也可以提交，与待提交相同
                locationManager.setAllowSubmitOutsideGeofence(true);
                Log.d(TAG, "待整改状态：允许在围栏外提交表单");
                
                // 在这里额外确保提交按钮可用
                if (!locationManager.getIgnoreGeofenceCheck() && !locationManager.isInGeofence() && uiController != null) {
                    uiController.setFormReadOnlyButSubmittable(false); // 不启用保存到本地按钮
                    Log.d(TAG, "待整改状态：额外确保在围栏外时表单只读但只有提交按钮可用");
                }
                break;
                
            default:
                // 默认设置，安全起见不允许在电子围栏外提交
                locationManager.setAllowSubmitOutsideGeofence(false);
                Log.d(TAG, "默认状态：不允许在围栏外提交表单");
                break;
        }
    }

    /**
     * 设置按钮事件
     */
    private void setupButtonEvents() {
        // 提交按钮事件
        buttonSubmit.setOnClickListener(v -> {
            Log.d(TAG, "提交按钮被点击");
//            UIUtils.showToast(this, "提交按钮被点击");

            // 闪烁按钮以显示响应
            v.setAlpha(0.5f);
            v.postDelayed(() -> v.setAlpha(1.0f), 200);

            // 获取FormProcessor
            FormProcessor formProcessor = formLifecycleManager.getFormProcessor();
            if (formProcessor == null) {
                Log.e(TAG, "FormProcessor为空，无法提交表单");
                UIUtils.showToast(this, "表单处理器未初始化，无法提交表单");
                return;
            }

            // 强制更新一次FormProcessor的文件列表，确保所有新添加的文件都已同步
            Log.d(TAG, "提交前强制更新一次FormProcessor的文件列表");
            formLifecycleManager.updateFormProcessorFileList();

            // --- 执行完整的表单校验 ---
            List<FormFieldConfig> fields = formLifecycleManager.getAllFields();
            Map<String, List<FieldFile>> fieldFiles = formLifecycleManager.getFieldFiles();
            JSONObject formDataJson = formProcessor.collectFormData(); // 收集表单控件数据
            Map<String, String> fieldValues = convertJsonObjectToStringMap(formDataJson);
            String validationError = FormValidationUtils.validateFormForSubmission(fields, fieldFiles, fieldValues);

            if (validationError != null) {
                Log.e(TAG, "表单校验失败: " + validationError);
                // 显示详细的错误信息
                new AlertDialog.Builder(this)
                        .setTitle("表单校验失败")
                        .setMessage(validationError) // 直接显示错误字符串
                        .setPositiveButton("确定", (dialog, which) -> {
                             // 尝试高亮第一个出错的字段
                             String firstFieldId = extractFirstFieldIdFromError(validationError);
                             if (firstFieldId != null) {
                                 highlightFieldWithError(firstFieldId);
                             }
                        })
                        .show();
                return; // 校验失败，阻止提交
            }
            // --- 校验结束 ---

            // 处理表单提交 - 使用原始的 formDataJson
            Log.d(TAG, "表单验证通过，准备提交 (不含手动添加的pjdyId): " + formDataJson.toString());

            // 显示提交中对话框
            AlertDialog dialog = new AlertDialog.Builder(this)
                    .setTitle("提交中")
                    .setMessage("正在提交表单数据，请稍候...")
                    .setCancelable(false)
                    .create();
            dialog.show();

            // --- 修改：调用修改后的 FormService 方法，传递 pjdyId ---
            FormService formService = new FormService(this);

            // 判断是否是整改表单，如果是整改，则使用整改专用接口
            if (samplingPointStatus == SamplingPointStatus.PENDING_RECTIFY) {
                // 待整改状态使用整改专用接口
                Log.d(TAG, "当前为整改表单，使用整改专用接口");
                formService.submitZgFormDataWithFiles(
                        this.pjdyId,
                        zgId,
                        formDataJson,
                        fieldFiles, new FormService.FormSubmitCallback() {
                            @Override
                            public void onSuccess(String formId) {
                                dialog.dismiss();
                                // Show a "Syncing..." dialog
                                AlertDialog syncDialog = new AlertDialog.Builder(DynamicFormActivity.this)
                                        .setTitle("同步数据")
                                        .setMessage("正在同步最新的样点数据，请稍候...")
                                        .setCancelable(false)
                                        .create();
                                syncDialog.show();

                                runOnUiThread(() -> {
                                    try {
                                        // 表单提交成功后，使用同步方式更新样点数据
                                        io.dcloud.uniplugin.utils.SamplingPointSyncManager.getInstance()
                                                .downloadDataSynchronously(DynamicFormActivity.this);

                                        // Sync finished, dismiss sync dialog and proceed
                                        syncDialog.dismiss();
                                        UIUtils.showToast(DynamicFormActivity.this, "整改表单提交成功，数据已同步");

                                        // 设置返回结果，附带重载标记
                                        Intent resultIntent = new Intent();
                                        resultIntent.putExtra("RELOAD_SAMPLING_POINTS", true);
                                        resultIntent.putExtra("SWITCH_TO_SUBMITTED_TAB", true); // 添加切换到已提交标签页的标记
                                        setResult(RESULT_OK, resultIntent);

                                        // 发送广播确保页面也收到通知
                                        Intent refreshIntent = new Intent("io.dcloud.uniplugin.REFRESH_SAMPLING_POINTS");
                                        refreshIntent.putExtra("pjdybh", pjdybh);
                                        refreshIntent.putExtra("RELOAD_SAMPLING_POINTS", true); // 确保包含此标记
                                        refreshIntent.putExtra("SWITCH_TO_SUBMITTED_TAB", true); // 添加切换到已提交标签页的标记
                                        sendBroadcast(refreshIntent);

                                        Log.d(TAG, "发送广播通知列表页面刷新数据，pjdybh=" + pjdybh);

                                        // 延迟一秒关闭页面，确保广播被接收
                                        new android.os.Handler().postDelayed(() -> finish(), 500);
                                    } catch (Exception e) {
                                        // Handle sync error
                                        syncDialog.dismiss();
                                        Log.e(TAG, "同步数据时出错: " + e.getMessage(), e);
                                        UIUtils.showToast(DynamicFormActivity.this, "提交成功，但同步数据失败: " + e.getMessage());
                                        // Still finish the activity, but maybe with a different result or message
                                        setResult(RESULT_OK); // Or a custom result code for sync failure
                                        finish();
                                    }
                                });
                            }
                            @Override
                            public void onFailure(String errorMsg) {
                                dialog.dismiss();
                                runOnUiThread(() -> {
                                    UIUtils.showConfirmDialog(
                                            DynamicFormActivity.this,
                                            "提交失败",
                                            "整改表单提交失败：" + errorMsg + "\n是否保存到本地？",
                                            "保存到本地",
                                            "取消",
                                            (dialogInterface, i) -> {
                                                buttonSaveOffline.performClick();
                                            }
                                    );
                                });
                            }
                        });
            } else {
                // 其他状态使用常规接口
                formService.submitFormDataWithFiles(
                        this.id,
                        this.pjdyId,       // 传递 Long 型 pjdyId
                        formDataJson,      // 传递不含 pjdyId 的 JSONObject
                        fieldFiles,
                        new FormService.FormSubmitCallback() {
                            @Override
                            public void onSuccess(String formId) {
                                dialog.dismiss();
                                // Show a "Syncing..." dialog
                                AlertDialog syncDialog = new AlertDialog.Builder(DynamicFormActivity.this)
                                        .setTitle("同步数据")
                                        .setMessage("正在同步最新的样点数据，请稍候...")
                                        .setCancelable(false)
                                        .create();
                                syncDialog.show();

                                runOnUiThread(() -> {
                                    try {
                                        // 表单提交成功后，使用同步方式更新样点数据
                                        SamplingPointSyncManager.getInstance()
                                                .downloadDataSynchronously(DynamicFormActivity.this);

                                        // Sync finished, dismiss sync dialog and proceed
                                        syncDialog.dismiss();
                                        UIUtils.showToast(DynamicFormActivity.this, "表单提交成功，数据已同步");
                                        
                                        // 设置返回结果，附带重载标记
                                        Intent resultIntent = new Intent();
                                        resultIntent.putExtra("RELOAD_SAMPLING_POINTS", true);
                                        resultIntent.putExtra("SWITCH_TO_SUBMITTED_TAB", true); // 添加切换到已提交标签页的标记
                                        setResult(RESULT_OK, resultIntent);
                                        
                                        // 发送广播确保页面也收到通知
                                        Intent refreshIntent = new Intent("io.dcloud.uniplugin.REFRESH_SAMPLING_POINTS");
                                        refreshIntent.putExtra("pjdybh", pjdybh);
                                        refreshIntent.putExtra("RELOAD_SAMPLING_POINTS", true); // 确保包含此标记
                                        refreshIntent.putExtra("SWITCH_TO_SUBMITTED_TAB", true); // 添加切换到已提交标签页的标记
                                        sendBroadcast(refreshIntent);
                                        
                                        Log.d(TAG, "发送广播通知列表页面刷新数据，pjdybh=" + pjdybh);
                                        
                                        // 延迟一秒关闭页面，确保广播被接收
                                        new android.os.Handler().postDelayed(() -> finish(), 500);
                                    } catch (Exception e) {
                                        // Handle sync error
                                        syncDialog.dismiss();
                                        Log.e(TAG, "同步数据时出错: " + e.getMessage(), e);
                                        UIUtils.showToast(DynamicFormActivity.this, "提交成功，但同步数据失败: " + e.getMessage());
                                        // Still finish the activity, but maybe with a different result or message
                                        setResult(RESULT_OK); // Or a custom result code for sync failure
                                        finish();
                                    }
                                });
                            }
                            @Override
                            public void onFailure(String errorMsg) {
                                dialog.dismiss();
                                runOnUiThread(() -> {
                                    UIUtils.showConfirmDialog(
                                            DynamicFormActivity.this,
                                            "提交失败",
                                            "表单提交失败：" + errorMsg + "\n是否保存到本地？",
                                            "保存到本地",
                                            "取消",
                                            (dialogInterface, i) -> {
                                                buttonSaveOffline.performClick();
                                            }
                                    );
                                });
                            }
                        });
            }
        });

        // 保存到本地按钮事件
        buttonSaveOffline.setOnClickListener(v -> {
            // 闪烁按钮以显示响应
            v.setAlpha(0.5f);
            v.postDelayed(() -> v.setAlpha(1.0f), 200);

            // 获取FormProcessor
            FormProcessor formProcessor = formLifecycleManager.getFormProcessor();
            if (formProcessor == null) {
                Log.e(TAG, "FormProcessor为空，无法保存表单");
                UIUtils.showToast(this, "表单处理器未初始化，无法保存表单");
                return;
            }

            // 获取字段文件和媒体预览管理器
            Map<String, List<FieldFile>> fieldFiles = formLifecycleManager.getFieldFiles();
            FormMediaPreviewManager mediaPreviewManager = formLifecycleManager.getMediaPreviewManager();

            // 添加日志调试输出，检查保存前的位置信息
            Log.d(TAG, "==================== 保存前检查位置信息 ====================");

            // 从MediaPreviewManager同步照片信息到FieldFile
            if (mediaPreviewManager != null && fieldFiles != null) {
                Log.d(TAG, "开始从PhotoInfo同步位置信息到FieldFile");

                for (Map.Entry<String, List<FieldFile>> entry : fieldFiles.entrySet()) {
                    String fieldId = entry.getKey();
                    List<FieldFile> files = entry.getValue();

                    // 获取该字段的照片信息
                    List<PhotoInfo> photoInfos = mediaPreviewManager.getPhotoInfos(fieldId);
                    if (photoInfos != null && !photoInfos.isEmpty()) {
                        Log.d(TAG, "字段[" + fieldId + "]有" + photoInfos.size() + "个照片信息");

                        // 为每个文件查找匹配的照片信息
                        for (FieldFile file : files) {
                            if (file != null && file.getPath() != null) {
                                String filePath = file.getPath();
                                Log.d(TAG, "检查文件: " + filePath + ", 当前位置信息: 纬度=" +
                                        file.getLatitude() + ", 经度=" + file.getLongitude() +
                                        ", 方位角=" + file.getDirection());

                                for (PhotoInfo info : photoInfos) {
                                    if (info.getFilePath() != null && info.getFilePath().equals(filePath)) {
                                        // 同步位置信息
                                        double photoLatitude = info.getLatitude();
                                        double photoLongitude = info.getLongitude();
                                        float photoDirection = info.getDirection();
                                        String photoFileTime = info.getFileTime();

                                        // 检查PhotoInfo中是否有有效的位置信息
                                        boolean hasValidLocation = photoLatitude != 0.0 || photoLongitude != 0.0;

                                        if (hasValidLocation) {
                                            Log.d(TAG, "从PhotoInfo同步位置信息: 文件=" + filePath +
                                                    ", 纬度=" + photoLatitude +
                                                    ", 经度=" + photoLongitude +
                                                    ", 方位角=" + photoDirection);

                                            // 更新FieldFile中的位置信息
                                            file.setLatitude(photoLatitude);
                                            file.setLongitude(photoLongitude);
                                            file.setDirection(photoDirection);
                                        } else {
                                            Log.d(TAG, "PhotoInfo中没有有效的位置信息: " + filePath);
                                        }

                                        // 同步fileTime
                                        if (photoFileTime != null && !photoFileTime.isEmpty()) {
                                            Log.d(TAG, "从PhotoInfo同步fileTime: " + photoFileTime);
                                            file.setFileTime(photoFileTime);
                                        }

                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            Log.d(TAG, "====================");

            // 强制更新一次FormProcessor的文件列表，确保所有新添加的文件都已同步
            Log.d(TAG, "保存前强制更新一次FormProcessor的文件列表");
            formLifecycleManager.updateFormProcessorFileList();

            // 执行非必填项的校验（已填写的字段仍需校验其值的有效性）
            boolean isValid = validateNonRequiredFields(formProcessor);
            if (!isValid) {
                // 验证失败，不保存表单
                return;
            }

            // 验证通过，收集表单数据
            JSONObject formData = formProcessor.collectFormData();

            // 再次强制同步文件列表，确保包括签名文件在内的所有文件都被包含
            fieldFiles = formLifecycleManager.getFieldFiles();

            // 使用本地存储管理器保存表单数据到本地
            FormLocalStorageManager localStorageManager = new FormLocalStorageManager(this, pjdybh, pjdybh);
            boolean saveSuccess = localStorageManager.saveFormData(formData, fieldFiles);
            if (saveSuccess) {
                UIUtils.showToast(this, "表单数据已成功保存到本地");

                // 设置返回结果，通知上一个活动刷新数据
                setResult(RESULT_OK);
                
                // 发送广播通知列表页刷新数据
                Intent refreshIntent = new Intent("io.dcloud.uniplugin.REFRESH_SAMPLING_POINTS");
                refreshIntent.putExtra("pjdybh", pjdybh);
                sendBroadcast(refreshIntent);
                
                // 延迟一秒后关闭当前活动，确保UI效果
                new android.os.Handler().postDelayed(() -> finish(), 1000);
            } else {
                Log.e(TAG, "保存表单数据到本地失败");
                UIUtils.showToast(this, "保存表单数据到本地失败，请重试");
            }
        });
    }

    /**
     * 显示退出确认对话框
     */
    private void showExitConfirmDialog() {
        AlertDialog dialog = new AlertDialog.Builder(this)
                .setTitle("提示")
                .setMessage("是否确定退出表单编辑?")
                .setPositiveButton("确定", (dialogInterface, i) -> {
                    dialogInterface.dismiss();
                    finish();
                })
                .setNegativeButton("取消", (dialogInterface, i) -> {
                    dialogInterface.dismiss();
                })
                .create();
        dialog.show();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            // 返回按钮处理
            showExitConfirmDialog();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        // 处理位置权限请求结果
        if (requestCode == REQUEST_PERMISSION_LOCATION) {
            if (formLifecycleManager != null) {
                formLifecycleManager.handleLocationPermissionResult(requestCode, permissions, grantResults);
            }
        } else if (requestCode == REQUEST_PERMISSION_CAMERA) {
            // 处理相机权限请求结果
            if (mediaHandler != null) {
                mediaHandler.handlePermissionResult(requestCode, permissions, grantResults);
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        // 让媒体处理器处理活动结果
        if (mediaHandler != null) {
            mediaHandler.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    public void onMediaSelected(String fieldId, String filePath) {
        if (TextUtils.isEmpty(fieldId) || TextUtils.isEmpty(filePath)) {
            Log.e(TAG, "媒体文件回调参数无效: fieldId=" + fieldId + ", filePath=" + filePath);
            return;
        }

        Log.d(TAG, "收到媒体文件选择回调: fieldId=" + fieldId + ", filePath=" + filePath);

        // 确保文件存在
        File file = new File(filePath);
        if (!file.exists()) {
            Log.e(TAG, "文件不存在: " + filePath);
            UIUtils.showToast(this, "无法添加媒体文件，文件不存在");
            return;
        }

        // 获取媒体预览管理器
        FormMediaPreviewManager mediaPreviewManager = formLifecycleManager.getMediaPreviewManager();
        if (mediaPreviewManager != null) {
            // 用户主动选择的媒体文件，设置autoPlay为true，允许自动播放视频
            boolean added = mediaPreviewManager.addMediaFile(fieldId, filePath, true);
            if (added) {
                Log.d(TAG, "成功添加媒体文件到字段: " + fieldId);

                // 确保按钮监听器正确更新
                formLifecycleManager.updateFormButtonListeners();
            } else {
                Log.e(TAG, "添加媒体文件失败: " + filePath);
                UIUtils.showToast(this, "添加媒体文件失败");
            }
        } else {
            Log.e(TAG, "mediaPreviewManager为空，无法添加媒体文件");
            UIUtils.showToast(this, "系统错误：媒体预览管理器未初始化");
        }
    }

    @Override
    public void onPhotoSelected(String fieldId, PhotoInfo photoInfo) {
        Log.d(TAG, "========== 开始处理照片选择回调 ==========");

        if (TextUtils.isEmpty(fieldId) || photoInfo == null || TextUtils.isEmpty(photoInfo.getFilePath())) {
            return;
        }
        String filePath = photoInfo.getFilePath();
        // 确保文件存在
        File file = new File(filePath);
        if (!file.exists()) {
            UIUtils.showToast(this, "无法添加照片文件，文件不存在");
            return;
        }
        FormMediaPreviewManager mediaPreviewManager = null;

        try {
            mediaPreviewManager = formLifecycleManager.getMediaPreviewManager();
            Log.d(TAG, "媒体预览管理器: " + (mediaPreviewManager != null ? "获取成功" : "获取失败"));
        } catch (Exception e) {
            Log.e(TAG, "获取媒体预览管理器出错: " + e.getMessage(), e);
        }

        if (mediaPreviewManager != null) {
            try {
                // 使用addPhotoInfo方法添加包含位置信息的照片
                Log.d(TAG, "即将调用mediaPreviewManager.addPhotoInfo方法...");
                boolean added = mediaPreviewManager.addPhotoInfo(fieldId, photoInfo, true);
                Log.d(TAG, "mediaPreviewManager.addPhotoInfo返回结果: " + (added ? "成功" : "失败"));

                if (added) {
                    Log.d(TAG, "成功添加照片信息到字段: " + fieldId);

                    // 确保按钮监听器正确更新
                    try {
                        formLifecycleManager.updateFormButtonListeners();
                        Log.d(TAG, "已更新表单按钮监听器");
                    } catch (Exception e) {
                        Log.e(TAG, "更新表单按钮监听器失败: " + e.getMessage(), e);
                    }

                    // 尝试强制刷新UI
                    try {
                        runOnUiThread(() -> {
                            // 获取对应字段的视图
                            Map<String, View> formViews = formLifecycleManager.getFormViews();
                            if (formViews != null) {
                                View previewView = formViews.get(fieldId + "_preview");
                                if (previewView != null) {
                                    Log.d(TAG, "找到预览视图，正在刷新...");
                                    previewView.invalidate();
                                } else {
                                    Log.w(TAG, "未找到预览视图: " + fieldId + "_preview");
                                }
                            } else {
                                Log.w(TAG, "formViews为null，无法查找和刷新预览视图");
                            }
                        });
                    } catch (Exception e) {
                        Log.e(TAG, "刷新UI视图失败: " + e.getMessage(), e);
                    }
                } else {
                    Log.e(TAG, "添加照片信息失败: " + filePath);
                    UIUtils.showToast(this, "添加照片信息失败");
                }
            } catch (Exception e) {
                Log.e(TAG, "调用addPhotoInfo方法出错: " + e.getMessage(), e);
                UIUtils.showToast(this, "添加照片出错: " + e.getMessage());
            }
        } else {
            Log.e(TAG, "mediaPreviewManager为空，无法添加照片信息");
            UIUtils.showToast(this, "系统错误：媒体预览管理器未初始化");
        }

        Log.d(TAG, "========== 结束处理照片选择回调 ==========");
    }

    @Override
    public void onMediaError(String fieldId, String errorMessage) {
        Log.e(TAG, "媒体处理错误: fieldId=" + fieldId + ", error=" + errorMessage);

        // 显示错误提示
        runOnUiThread(() -> {
            if (errorMessage.contains("权限被拒绝")) {
                // 显示权限设置对话框
                UIUtils.showConfirmDialog(this,
                        "权限错误",
                        "无法使用相机功能，因为相机权限被拒绝。请前往系统设置，为应用授予相机权限。",
                        "前往设置",
                        "取消",
                        (dialog, which) -> {
                            // 打开应用设置
                            Intent intent = new Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                            android.net.Uri uri = android.net.Uri.fromParts("package", getPackageName(), null);
                            intent.setData(uri);
                            startActivity(intent);
                        });
            } else {
                // 普通错误提示
                UIUtils.showToast(this, "媒体处理错误: " + errorMessage);
            }
        });
    }

    @Override
    public void onValidationFailed(String fieldId, String errorMessage) {
        // 表单验证失败时的回调
        try {
            Map<String, View> formViews = formLifecycleManager.getFormViews();

            if (formViews == null) {
                Log.e(TAG, "formViews为空，无法找到表单字段");
                // 仍然显示错误提示
                UIUtils.showToast(this, errorMessage);
                return;
            }

            View view = formViews.get(fieldId);
            if (view != null) {
                // 滚动父容器以显示错误字段
                formContainer.post(() -> {
                    view.requestFocus();

                    // 如果是文本输入框，设置错误提示
                    if (view instanceof android.widget.EditText) {
                        //Log.d(TAG, "设置EditText错误提示: " + errorMessage);
                        UIUtils.setEditTextError((android.widget.EditText) view, errorMessage);
                    } else {
                        //Log.d(TAG, "对控件应用高亮动画");
                        UIUtils.applyHighlightAnimation(this, view);
                    }
                });
            } else {
                Log.e(TAG, "找不到表单字段视图: " + fieldId);
            }
            android.widget.Toast.makeText(this, errorMessage, android.widget.Toast.LENGTH_LONG).show();
        } catch (Exception e) {
            Log.e(TAG, "处理验证失败回调时出错: " + e.getMessage(), e);
            // 确保仍然显示错误消息
            android.widget.Toast.makeText(this, errorMessage, android.widget.Toast.LENGTH_LONG).show();
        }
    }

    @Override
    public void onFormSubmitted(JSONObject formData) {}

    @Override
    public void onError(String errorMessage) {
        // 表单处理过程中的错误回调
        UIUtils.showToast(this, "表单处理错误: " + errorMessage);
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    protected void onDestroy() {
        // 先关闭所有对话框
        runOnUiThread(UIUtils::dismissAllDialogs);

        // 释放资源
        if (mediaHandler != null) {
            mediaHandler.release();
        }
        if (formLifecycleManager != null) {
            formLifecycleManager.release();
        }

        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        // 显示确认对话框
        showExitConfirmDialog();
    }

    /**
     * 验证非必填字段
     * 只验证已填写的字段值是否符合规范，如果用户未填写则不验证
     *
     * @param formProcessor 表单处理器
     * @return 验证是否通过
     */
    private boolean validateNonRequiredFields(FormProcessor formProcessor) {
        try {
            Log.d(TAG, "开始验证非必填字段...");

            // 获取所有字段配置
            List<FormFieldConfig> fields = formLifecycleManager.getAllFields();
            if (fields == null || fields.isEmpty()) {
                Log.e(TAG, "没有找到字段配置，无法验证表单");
                return true; // 没有字段配置时默认通过
            }

            boolean allValid = true;
            StringBuilder errorMessages = new StringBuilder();

            // 验证每个字段
            for (FormFieldConfig field : fields) {
                if (field == null || field.getFieldId() == null) {
                    continue;
                }

                String fieldId = field.getFieldId();
                String fieldValue = formProcessor.getFieldValue(fieldId);

                // 如果值为空或未填写，则跳过验证（不检查必填性）
                if (TextUtils.isEmpty(fieldValue) || "null".equals(fieldValue)) {
                    continue;
                }

                // 根据字段类型和值进行验证
                String fieldType = field.getFieldType();
                String errorMessage = null;

                // 数字类型验证
                if (FormFieldConfig.TYPE_NUMBER.equals(fieldType)) {
                    errorMessage = FormValidationUtils.validateNumber(field, fieldValue);
                }
                // 日期类型验证
                else if (FormFieldConfig.TYPE_DATE.equals(fieldType)) {
                    errorMessage = FormValidationUtils.validateDate(field, fieldValue);
                }
                // 文本类型验证
                else if (FormFieldConfig.TYPE_TEXT.equals(fieldType) || FormFieldConfig.TYPE_TEXTAREA.equals(fieldType)) {
                    errorMessage = FormValidationUtils.validateText(field, fieldValue);
                }

                // 如果有错误消息，说明验证失败
                if (errorMessage != null) {
                    allValid = false;
                    Log.e(TAG, "字段验证失败: " + fieldId + " - " + errorMessage);
                    errorMessages.append("\n• ").append(errorMessage);

                    // 高亮显示有错误的字段
                    highlightFieldWithError(fieldId);
                }
            }

            // 如果有错误，显示错误信息
            if (!allValid) {
                String message = "表单验证失败:" + errorMessages.toString();
                UIUtils.showToast(this, "已填写的字段中存在格式错误");

                new AlertDialog.Builder(this)
                        .setTitle("数据格式验证")
                        .setMessage(message)
                        .setPositiveButton("确定", null)
                        .show();
            }

            return allValid;
        } catch (Exception e) {
            Log.e(TAG, "验证非必填字段过程中发生错误: " + e.getMessage(), e);
            UIUtils.showToast(this, "表单验证失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 高亮显示有错误的字段
     */
    private void highlightFieldWithError(String fieldId) {
        try {
            // 获取字段视图
            View fieldView = formLifecycleManager.getFormViews().get(fieldId);
            if (fieldView == null) {
                return;
            }

            // 应用错误样式
            UIUtils.applyHighlightAnimation(this, fieldView);

            // 滚动到此字段
            formContainer.post(() -> {
                formContainer.requestChildFocus(fieldView, fieldView);
            });
        } catch (Exception e) {
            Log.e(TAG, "高亮错误字段失败: " + e.getMessage(), e);
        }
    }

    /**
     * --- 新增辅助方法：将 JSONObject 转换为 Map<String, String> ---
     * @param jsonObject 输入的 JSONObject
     * @return 转换后的 Map，如果输入为 null 则返回空的 Map
     */
    private Map<String, String> convertJsonObjectToStringMap(JSONObject jsonObject) {
        Map<String, String> map = new java.util.HashMap<>();
        if (jsonObject != null) {
            java.util.Iterator<String> keys = jsonObject.keys();
            while (keys.hasNext()) {
                String key = keys.next();
                try {
                    // 只转换基本类型值为 String
                    Object value = jsonObject.opt(key); // 使用 opt 安全获取
                    if (value instanceof String || value instanceof Number || value instanceof Boolean) {
                        map.put(key, value.toString());
                    } else if (value == JSONObject.NULL) {
                        // map.put(key, null); // 或者空字符串，根据需要处理
                    } else {
                        // 对于复杂类型（如 JSONArray 或嵌套 JSONObject），可以选择忽略或记录日志
                        Log.w(TAG, "Skipping non-primitive value for key '" + key + "' during JSONObject to Map conversion.");
                    }
                } catch (Exception e) { // 捕捉通用异常以防万一
                    Log.e(TAG, "Error converting JSONObject to Map for key: " + key, e);
                }
            }
        }
        return map;
    }

    /**
     * --- 新增：从聚合错误消息中提取第一个字段ID ---
     * 简单实现，查找第一个 "(ID: xxx)" 模式
     * @param errorMessage 聚合的错误消息字符串
     * @return 第一个找到的字段ID，或null
     */
    private String extractFirstFieldIdFromError(String errorMessage) {
        if (errorMessage == null) {
            return null;
        }
        try {
            int idStartIndex = errorMessage.indexOf("(ID: ");
            if (idStartIndex != -1) {
                int idEndIndex = errorMessage.indexOf(")", idStartIndex);
                if (idEndIndex != -1) {
                    return errorMessage.substring(idStartIndex + "(ID: ".length(), idEndIndex);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "提取字段ID时出错: " + e.getMessage());
        }
        return null;
    }

    /**
     * 调试表单字段配置
     */
    private void debugFormFields() {
        if (formLifecycleManager == null) {
            Log.d(TAG, "formLifecycleManager为空，无法调试表单字段");
            return;
        }

        List<FormFieldConfig> fields = formLifecycleManager.getAllFields();
        if (fields == null || fields.isEmpty()) {
            Log.d(TAG, "表单字段为空，无法调试");
            return;
        }

        Log.d(TAG, "======== 开始调试表单字段配置 ========");
        Log.d(TAG, "总字段数: " + fields.size());
        
        // 收集字段信息
        StringBuilder debugInfo = new StringBuilder();
        debugInfo.append("表单字段配置信息：\n\n");
        
        for (FormFieldConfig field : fields) {
            if (field == null) continue;
            
            String fieldId = field.getFieldId();
            String fieldName = field.getFieldName();
            String fieldType = field.getFieldType();
//            Integer fieldLength = field.getMaxLength();
            Integer maxLength = field.getMaxLength();
            Boolean required = field.isRequired();
            
            String fieldElement = null;
            try {
                java.lang.reflect.Method method = field.getClass().getMethod("getFieldElement");
                Object result = method.invoke(field);
                if (result instanceof String) {
                    fieldElement = (String) result;
                }
            } catch (Exception e) {
                // 忽略异常
            }
            
            Integer fieldRequired = null;
            try {
                java.lang.reflect.Method method = field.getClass().getMethod("getFieldRequired");
                Object result = method.invoke(field);
                if (result instanceof Integer) {
                    fieldRequired = (Integer) result;
                }
            } catch (Exception e) {
                // 忽略异常
            }
            
            Log.d(TAG, "字段: " + fieldId + 
                     "\n  名称=" + fieldName + 
                     "\n  类型=" + fieldType + 
                     "\n  fieldElement=" + fieldElement +
//                     "\n  长度=" + fieldLength +
                     "\n  最大长度=" + maxLength + 
                     "\n  必填=" + required + 
                     "\n  fieldRequired=" + fieldRequired);
            
            debugInfo.append("ID: ").append(fieldId).append("\n");
            debugInfo.append("名称: ").append(fieldName).append("\n");
            debugInfo.append("类型: ").append(fieldType).append("\n");
            debugInfo.append("fieldElement: ").append(fieldElement).append("\n");
//            debugInfo.append("长度: ").append(fieldLength).append("\n");
            debugInfo.append("最大长度: ").append(maxLength).append("\n");
            debugInfo.append("必填: ").append(required).append("\n");
            debugInfo.append("fieldRequired: ").append(fieldRequired).append("\n");
            debugInfo.append("----------\n\n");
        }
        
        Log.d(TAG, "======== 结束调试表单字段配置 ========");
        
        // 显示调试信息对话框
        new AlertDialog.Builder(this)
            .setTitle("表单字段调试")
            .setMessage(debugInfo.toString())
            .setPositiveButton("确定", null)
            .show();
    }
}
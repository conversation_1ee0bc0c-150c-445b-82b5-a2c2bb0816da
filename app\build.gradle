apply plugin: 'com.android.application'

def getVersionCode = { ->
    try {
        def stdout = new ByteArrayOutputStream()
        exec {
            //此处可以根据实际情况使用git rev-list --all --count
            commandLine 'git', 'rev-list', '--first-parent', '--count', 'HEAD'
            standardOutput = stdout
        }
        //+300是因为与之前的版本命名区分开，不会与之前的重复
        return Integer.parseInt(stdout.toString().trim())+300
    }
    catch (ignored) {
        println "===================error code!"
        return -1;
    }
}

def getVersionName = { ->
    try {
        def stdout = new ByteArrayOutputStream()
        exec {
            commandLine 'git', 'describe', '--tags'
            standardOutput = stdout
        }
        return stdout.toString().trim()
    }
    catch (ignored) {
        println "===================error name!"
        return null;
    }
}
//def gitVersionCode() {
//    def cmd = 'git rev-list HEAD --first-parent --count'
//    int dd=cmd.execute().text.trim().toInteger()
//    println "文件名：-----------------${dd}"
//    return dd
//}

//def gitVersionTag() {
//    def cmd = 'git describe --tags --dirty'
//    def version = cmd.execute().text.trim()
//
//    def pattern = "-(\\d+)-g"
//    def matcher = version =~ pattern
//
//    if (matcher) {
//        version = version.substring(0, matcher.start()) + "." + matcher[0][1]
//    } else {
//        version = version + ".0"
//    }
//
//    return version
//}
android {
    compileSdkVersion 29
    buildToolsVersion '29.0.2'
    /*配置gdal*/
    sourceSets.main.jniLibs.srcDirs = ['libs']
    //lintOptions {
       // checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
       // abortOnError false
   // }
    defaultConfig {
        applicationId "com.android.BcgdUniPlugin"
        minSdkVersion 24 //原23，因为扫码模块修改为24
        //noinspection ExpiredTargetSdkVersion
        targetSdkVersion 28 //建议此属性值设为21 io.dcloud.PandoraEntry 作为apk入口时   必须设置 targetSDKVersion>=21 沉浸式才生效

        versionCode  getVersionCode()
        versionName  getVersionName()
//        versionCode  22
//        versionName  "1.0.0"
        multiDexEnabled true
        ndk {
            abiFilters 'x86_64','armeabi-v7a'
        }
        lintOptions {
            abortOnError false
        }
        compileOptions {
            sourceCompatibility JavaVersion.VERSION_1_8
            targetCompatibility JavaVersion.VERSION_1_8
        }
    }

    signingConfigs {
        config {
            keyAlias 'bcgd'
            keyPassword '123456'
            storeFile file('bcgd.jks')
            storePassword '123456'
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.config
            zipAlignEnabled true
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            signingConfig signingConfigs.config
            zipAlignEnabled true
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    //使用uniapp时，需复制下面代码
    /*代码开始*/
    aaptOptions {
        additionalParameters '--auto-add-overlay'
        //noCompress 'foo', 'bar'
        ignoreAssetsPattern "!.svn:!.git:.*:!CVS:!thumbs.db:!picasa.ini:!*.scc:*~"
    }
    /*代码结束*/
    buildFeatures {
        viewBinding true
    }
    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
    }
    //配置打包名称
    applicationVariants.all {
        //判断是release还是debug版本
        def buildType = it.buildType.name
        def fileName
        def date = new Date().format("yyyyMMdd", TimeZone.getTimeZone("Asia/Chongqing"))
        def formattedDate = date.format('yyyyMMdd')
        it.outputs.all { output ->
            if (buildType == "release") {
                    //我此处的命名规则是：渠道名_项目名_版本名_创建时间_构建类型.apk
                    fileName ="BCGD_${defaultConfig.versionName}_${defaultConfig.versionCode}_${date}-${buildType}.apk"
                    //将名字打印出来，以便及时查看是否满意。
                    println "文件名：-----------------${fileName}"
                    //重新对apk命名。(适用于Gradle3.0以下版本)
//                outputFile = new File(outputFile.parent, fileName)
                    //重新对apk命名。(适用于Gradle3.0（含）以上版本)如果你Gradle版本是4.0以下版本则将上面的一行代码放开并注释下面的这一行。
                    outputFileName = fileName
                }else if(buildType == "debug"){
                    fileName ="BCGD_${defaultConfig.versionName}_${defaultConfig.versionCode}_${date}-${buildType}.apk"
                    //将名字打印出来，以便及时查看是否满意。
                    println "文件名：-----------------${fileName}"
                    //重新对apk命名。(适用于Gradle3.0以下版本)
//                outputFile = new File(outputFile.parent, fileName)
                    //重新对apk命名。(适用于Gradle3.0（含）以上版本)如果你Gradle版本是4.0以下版本则将上面的一行代码放开并注释下面的这一行。
                    outputFileName = fileName
                }
        }
        it.outputs.each {
            { output ->
                println "系统版本：-----------------${gitVersionCode() + gitVersionTag()}"
                output.versionCodeOverride = gitVersionCode() + gitVersionTag()
            }
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation fileTree(dir: 'libs', include: ['*.aar'])
    //noinspection GradleCompatible
    implementation 'com.android.support:support-v4:27.1.1'
    implementation 'com.inthecheesefactory.thecheeselibrary:stated-fragment:0.9.2'
    //打包
    androidTestImplementation('com.android.support.test.espresso:espresso-core:2.2.2', {
        exclude group: 'com.android.support', module: 'support-annotations'
    } )

    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.appcompat:appcompat:1.0.0'

    /*uniapp所需库-----------------------开始*/
    implementation 'androidx.recyclerview:recyclerview:1.0.0'
    implementation 'com.facebook.fresco:fresco:1.13.0'
    implementation "com.facebook.fresco:animated-gif:1.13.0"
    /*uniapp所需库-----------------------结束*/
    // 基座需要，必须添加
    implementation 'com.github.bumptech.glide:glide:4.9.0'
    implementation 'com.alibaba:fastjson:1.1.46.android'

    //arcgis
    implementation 'com.esri.arcgisruntime:arcgis-android:100.15.0'
    implementation 'com.vividsolutions:jts-core:1.14.0'

    // 添加uni-app插件
    implementation project(':uniplugin_component')
    implementation project(':uniplugin_module')
    implementation project(':uniplugin_richalert')
    implementation project(':uniplugin_camera')
    implementation project(':uniplugin_printQr')
    implementation project(':uniplugin_swdc')
    implementation project(':uniplugin_qrcode')
    //implementation 'com.inthecheesefactory.thecheeselibrary:stated-fragment-support-v4:0.9.1'

    // PictureSelector 基础 (必须)
    implementation 'io.github.lucksiege:pictureselector:v3.11.1'

    //视频压缩
    implementation 'com.github.yellowcath:VideoProcessor:2.4.2'
}

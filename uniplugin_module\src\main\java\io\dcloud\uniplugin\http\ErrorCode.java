package io.dcloud.uniplugin.http;

import java.util.HashMap;
import java.util.Map;

/**
 * 错误码映射类
 */
public class ErrorCode {
    private static final Map<Integer, String> ERROR_MAP = new HashMap<>();
    
    static {
        // 初始化错误码映射
        ERROR_MAP.put(200, "操作成功");
        ERROR_MAP.put(401, "认证失败，无法访问系统资源");
        ERROR_MAP.put(403, "当前操作没有权限");
        ERROR_MAP.put(404, "访问资源不存在");
        ERROR_MAP.put(500, "系统内部错误，请联系管理员");
        ERROR_MAP.put(501, "接口未实现");
        ERROR_MAP.put(1002000010, "密码已过期，请修改密码");
        
        // 可以根据需要添加更多错误码
    }
    
    /**
     * 获取错误消息
     * @param code 错误码
     * @return 对应的错误消息，如果不存在则返回默认消息
     */
    public static String getMessage(int code) {
        return ERROR_MAP.getOrDefault(code, "未知错误");
    }
    
    /**
     * 获取错误消息
     * @param code 错误码
     * @param defaultMessage 默认消息
     * @return 对应的错误消息，如果不存在则返回默认消息
     */
    public static String getMessage(int code, String defaultMessage) {
        return ERROR_MAP.getOrDefault(code, defaultMessage);
    }
}
package io.dcloud.uniplugin.sampleflow;

import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import io.dcloud.uniplugin.model.YPLZ;
import uni.dcloud.io.uniplugin_module.R;

/**
 * 新增样品适配器
 */
public class CreateSampleAdapter extends RecyclerView.Adapter<CreateSampleAdapter.ViewHolder> {
    
    private Context context;
    private List<YPLZ> sampleList;
    private OnSampleDeleteListener deleteListener;
    
    // 样品类型选项
    private static final String[] SAMPLE_TYPES = {
        "环境样品", "水样", "土壤", "生物样品", "空气样品", "沉积物", "其他"
    };
    
    // 重量单位选项
    private static final String[] WEIGHT_UNITS = {
        "g", "kg", "mg"
    };
    
    public interface OnSampleDeleteListener {
        void onSampleDelete(int position);
    }
    
    public CreateSampleAdapter(Context context, List<YPLZ> sampleList) {
        this.context = context;
        this.sampleList = sampleList;
    }
    
    public void setOnSampleDeleteListener(OnSampleDeleteListener listener) {
        this.deleteListener = listener;
    }
    
    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_create_sample, parent, false);
        return new ViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        YPLZ sample = sampleList.get(position);
        
        // 设置样品编码
        holder.textViewSampleCode.setText("样品编码：" + sample.getSampleCode());
        
        // 设置各个输入框的值
        holder.editTextSampleName.setText(sample.getSampleName());
        
        // 设置样品类型下拉框
        setupSampleTypeSpinner(holder.spinnerSampleType, sample, position);
        
        // 设置重量单位下拉框
        setupWeightUnitSpinner(holder.spinnerWeightUnit, sample, position);
        
        // 设置样品重量
        if (sample.getSampleWeight() != null) {
            holder.editTextSampleWeight.setText(sample.getSampleWeight().toString());
        } else {
            holder.editTextSampleWeight.setText("");
        }
        
        holder.editTextSampleColour.setText(sample.getSampleColour());
        holder.editTextExteriorState.setText(sample.getSampleExteriorState());
        holder.editTextTexture.setText(sample.getSampleTexture());
        holder.editTextPreservation.setText(sample.getSamplePreservationCondition());
        holder.editTextPackageMaterial.setText(sample.getSamplePackageMaterial());
        holder.editTextRemark.setText(sample.getRemark());
        
        // 设置删除按钮监听器
        holder.buttonDelete.setOnClickListener(v -> {
            if (deleteListener != null) {
                deleteListener.onSampleDelete(position);
            }
        });
    }
    
    private void setupSampleTypeSpinner(Spinner spinner, YPLZ sample, int adapterPosition) {
        // 设置样品类型下拉框适配器
        ArrayAdapter<String> adapter = new ArrayAdapter<>(
                context, android.R.layout.simple_spinner_item, SAMPLE_TYPES);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinner.setAdapter(adapter);
        
        // 设置当前选中项
        if (!TextUtils.isEmpty(sample.getSampleType())) {
            for (int i = 0; i < SAMPLE_TYPES.length; i++) {
                if (SAMPLE_TYPES[i].equals(sample.getSampleType())) {
                    spinner.setSelection(i);
                    break;
                }
            }
        }
        
        // 设置选择监听器
        spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                int pos = spinner.getTag() != null ? 
                        (int) spinner.getTag() : RecyclerView.NO_POSITION;
                
                if (pos != RecyclerView.NO_POSITION && 
                        pos < sampleList.size()) {
                    sampleList.get(pos).setSampleType(SAMPLE_TYPES[position]);
                }
            }
            
            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                // 不做任何处理
            }
        });
        
        // 在每次绑定时设置当前位置标记，以便在选择事件中使用
        spinner.setTag(adapterPosition);
    }
    
    private void setupWeightUnitSpinner(Spinner spinner, YPLZ sample, int adapterPosition) {
        // 设置重量单位下拉框适配器
        ArrayAdapter<String> adapter = new ArrayAdapter<>(
                context, android.R.layout.simple_spinner_item, WEIGHT_UNITS);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinner.setAdapter(adapter);
        
        // 设置当前选中项
        if (!TextUtils.isEmpty(sample.getWeightUnit())) {
            for (int i = 0; i < WEIGHT_UNITS.length; i++) {
                if (WEIGHT_UNITS[i].equals(sample.getWeightUnit())) {
                    spinner.setSelection(i);
                    break;
                }
            }
        }
        
        // 设置选择监听器
        spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                int pos = spinner.getTag() != null ? 
                        (int) spinner.getTag() : RecyclerView.NO_POSITION;
                
                if (pos != RecyclerView.NO_POSITION && pos < sampleList.size()) {
                    sampleList.get(pos).setWeightUnit(WEIGHT_UNITS[position]);
                }
            }
            
            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                // 不做任何处理
            }
        });
        
        // 在每次绑定时设置当前位置标记，以便在选择事件中使用
        spinner.setTag(adapterPosition);
    }
    
    @Override
    public int getItemCount() {
        return sampleList.size();
    }
    
    public class ViewHolder extends RecyclerView.ViewHolder {
        TextView textViewSampleCode;
        EditText editTextSampleName;
        Spinner spinnerSampleType; // 改为Spinner
        Spinner spinnerWeightUnit;
        EditText editTextSampleWeight;
        EditText editTextSampleColour;
        EditText editTextExteriorState;
        EditText editTextTexture;
        EditText editTextPreservation;
        EditText editTextPackageMaterial;
        EditText editTextRemark;
        ImageButton buttonDelete;
        
        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            
            textViewSampleCode = itemView.findViewById(R.id.textViewSampleCode);
            editTextSampleName = itemView.findViewById(R.id.editTextSampleName);
            spinnerSampleType = itemView.findViewById(R.id.spinnerSampleType);
            spinnerWeightUnit = itemView.findViewById(R.id.spinnerWeightUnit);
            editTextSampleWeight = itemView.findViewById(R.id.editTextWeight);
            editTextSampleColour = itemView.findViewById(R.id.editTextColor);
            editTextExteriorState = itemView.findViewById(R.id.editTextExteriorState);
            editTextTexture = itemView.findViewById(R.id.editTextTexture);
            editTextPreservation = itemView.findViewById(R.id.editTextPreservationConditions);
            editTextPackageMaterial = itemView.findViewById(R.id.editTextPackagingMaterial);
            editTextRemark = itemView.findViewById(R.id.editTextRemarks);
            buttonDelete = itemView.findViewById(R.id.buttonDelete);
            
            setupTextWatchers();
        }
        
        private void setupTextWatchers() {
            // 样品名称监听器
            editTextSampleName.addTextChangedListener(new SimpleTextWatcher() {
                @Override
                public void afterTextChanged(Editable s) {
                    int pos = getAdapterPosition();
                    if (pos != RecyclerView.NO_POSITION && pos < sampleList.size()) {
                        sampleList.get(pos).setSampleName(s.toString());
                    }
                }
            });
            
            // 样品重量监听器 - 修正逻辑，确保数据正确保存
            editTextSampleWeight.addTextChangedListener(new SimpleTextWatcher() {
                @Override
                public void afterTextChanged(Editable s) {
                    int pos = getAdapterPosition();
                    if (pos != RecyclerView.NO_POSITION && pos < sampleList.size()) {
                        try {
                            if (!TextUtils.isEmpty(s.toString())) {
                                Double weight = Double.parseDouble(s.toString());
                                sampleList.get(pos).setSampleWeight(weight);
                            } else {
                                sampleList.get(pos).setSampleWeight(null);
                            }
                        } catch (NumberFormatException e) {
                            // 忽略无效输入，保持当前值不变
                        }
                    }
                }
            });
            
            // 样品颜色监听器
            editTextSampleColour.addTextChangedListener(new SimpleTextWatcher() {
                @Override
                public void afterTextChanged(Editable s) {
                    int pos = getAdapterPosition();
                    if (pos != RecyclerView.NO_POSITION && pos < sampleList.size()) {
                        sampleList.get(pos).setSampleColour(s.toString());
                    }
                }
            });
            
            // 外观状态监听器
            editTextExteriorState.addTextChangedListener(new SimpleTextWatcher() {
                @Override
                public void afterTextChanged(Editable s) {
                    int pos = getAdapterPosition();
                    if (pos != RecyclerView.NO_POSITION && pos < sampleList.size()) {
                        sampleList.get(pos).setSampleExteriorState(s.toString());
                    }
                }
            });
            
            // 质地监听器
            editTextTexture.addTextChangedListener(new SimpleTextWatcher() {
                @Override
                public void afterTextChanged(Editable s) {
                    int pos = getAdapterPosition();
                    if (pos != RecyclerView.NO_POSITION && pos < sampleList.size()) {
                        sampleList.get(pos).setSampleTexture(s.toString());
                    }
                }
            });
            
            // 保存条件监听器
            editTextPreservation.addTextChangedListener(new SimpleTextWatcher() {
                @Override
                public void afterTextChanged(Editable s) {
                    int pos = getAdapterPosition();
                    if (pos != RecyclerView.NO_POSITION && pos < sampleList.size()) {
                        sampleList.get(pos).setSamplePreservationCondition(s.toString());
                    }
                }
            });
            
            // 包装材料监听器
            editTextPackageMaterial.addTextChangedListener(new SimpleTextWatcher() {
                @Override
                public void afterTextChanged(Editable s) {
                    int pos = getAdapterPosition();
                    if (pos != RecyclerView.NO_POSITION && pos < sampleList.size()) {
                        sampleList.get(pos).setSamplePackageMaterial(s.toString());
                    }
                }
            });
            
            // 备注监听器
            editTextRemark.addTextChangedListener(new SimpleTextWatcher() {
                @Override
                public void afterTextChanged(Editable s) {
                    int pos = getAdapterPosition();
                    if (pos != RecyclerView.NO_POSITION && pos < sampleList.size()) {
                        sampleList.get(pos).setRemark(s.toString());
                    }
                }
            });
        }
    }
    
    private abstract class SimpleTextWatcher implements TextWatcher {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
        
        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {}
    }
} 
//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.deltaphone.cameramodule;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import androidx.core.app.ActivityCompat;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dcloud.android.annotation.NonNull;
import com.deltaphone.cameramodule.camera.FileUtils;
import com.deltaphone.cameramodule.camera.PermissionReq;
import com.deltaphone.cameramodule.camera.PermissionReq.Result;
import com.luck.picture.lib.app.PictureAppMaster;
import com.luck.picture.lib.config.PictureMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.entity.MediaExtraInfo;
import com.luck.picture.lib.utils.MediaUtils;

import java.io.File;
import java.util.ArrayList;

import io.dcloud.feature.uniapp.annotation.UniJSMethod;
import io.dcloud.feature.uniapp.bridge.UniJSCallback;
import io.dcloud.feature.uniapp.common.UniModule;

public class CameraModule extends UniModule {
    private static int REQUEST_CODE = 1000,FILE_CHANGE_CODE = 1086;
    private UniJSCallback cameraCallback,saveQmPicture,folderSize,deleteFolder,fileChangeCallback;
    private final static String TAG = "PictureSelectorTag";

    public CameraModule() {
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == REQUEST_CODE && resultCode!=FILE_CHANGE_CODE) {
            if (data != null && !TextUtils.isEmpty(data.getStringExtra("imagePath"))) {
                String imagePath = data.getStringExtra("imagePath");
                if (!TextUtils.isEmpty(imagePath)) {
                    imagePath = "file://" + imagePath;
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("url", imagePath);
                    Log.d("imagePath", "onActivityResult: "+imagePath);
                    this.cameraCallback.invoke(jsonObject.toString());
                } else {
                    Toast.makeText(this.mUniSDKInstance.getContext(), "选择照片失败，请重试", Toast.LENGTH_SHORT).show();
                }
            }
        }else if(resultCode == FILE_CHANGE_CODE){
            if (data != null && !TextUtils.isEmpty(data.getStringExtra("imagePath"))) {
                String imagePath = data.getStringExtra("imagePath");
//                Log.d("choosefile", "onActivityResult: "+imagePath);
                if (!TextUtils.isEmpty(imagePath)) {
                    imagePath = "file://" + imagePath;
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("url", imagePath);
//                    Log.d("imagePath", "onActivityResult: "+imagePath);
                    this.fileChangeCallback.invoke(jsonObject.toString());
                } else {
                    Toast.makeText(this.mUniSDKInstance.getContext(), "选择照片失败，请重试", Toast.LENGTH_SHORT).show();
                }
            }
        } else {
            super.onActivityResult(requestCode, resultCode, data);
        }

    }

    @UniJSMethod(
            uiThread = true
    )
    public void btnOpenCamera(JSONObject options, final UniJSCallback callback) {
        Log.d("model", "openCamera: "+options);
        String model = options.getString("model");//规定0是拍照，1是录视频
        if (this.mUniSDKInstance != null && this.mUniSDKInstance.getContext() instanceof Activity) {
            if (ActivityCompat.checkSelfPermission(this.mUniSDKInstance.getContext(), "android.permission.CAMERA") == 0 && ActivityCompat.checkSelfPermission(this.mUniSDKInstance.getContext(), "android.permission.WRITE_EXTERNAL_STORAGE") == 0 && ActivityCompat.checkSelfPermission(this.mUniSDKInstance.getContext(), "android.permission.READ_EXTERNAL_STORAGE") == 0&&ActivityCompat.checkSelfPermission(this.mUniSDKInstance.getContext(), "android.permission.RECORD_AUDIO") == 0) {
                //有权限
//                Log.d("module","有权限，进入到这里");
                this.cameraCallback = callback;
                Intent intent = new Intent(this.mUniSDKInstance.getContext(), CameraActivity.class);
                intent.putExtra("model",model);
                
                // 添加水印相关参数
                if (options.containsKey("projectName")) {
                    intent.putExtra("projectName", options.getString("projectName"));
                }
                if (options.containsKey("unitNumber")) {
                    intent.putExtra("unitNumber", options.getString("unitNumber"));
                }
                if (options.containsKey("enableWatermark")) {
                    intent.putExtra("enableWatermark", options.getBooleanValue("enableWatermark"));
                }
                if (options.containsKey("captureLocationOnShot")) {
                    intent.putExtra("captureLocationOnShot", options.getBooleanValue("captureLocationOnShot"));
                }
                
                ((Activity)this.mUniSDKInstance.getContext()).startActivityForResult(intent, REQUEST_CODE);
            } else {
//                Log.d("module","没有权限，进入到这里");
                PermissionReq.with(this.mUniSDKInstance.getContext()).permissions(new String[]{"android.permission.CAMERA", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.READ_EXTERNAL_STORAGE","android.permission.RECORD_AUDIO"}).result(new Result() {
                    @Override
                    public void onGranted() {
//                        Log.d("module","onGranted授权成功");
                        CameraModule.this.cameraCallback = callback;
                        Intent intent = new Intent(CameraModule.this.mUniSDKInstance.getContext(), CameraActivity.class);
                        intent.putExtra("model",model);
                        
                        // 添加水印相关参数
                        if (options.containsKey("projectName")) {
                            intent.putExtra("projectName", options.getString("projectName"));
                        }
                        if (options.containsKey("unitNumber")) {
                            intent.putExtra("unitNumber", options.getString("unitNumber"));
                        }
                        if (options.containsKey("enableWatermark")) {
                            intent.putExtra("enableWatermark", options.getBooleanValue("enableWatermark"));
                        }
                        if (options.containsKey("captureLocationOnShot")) {
                            intent.putExtra("captureLocationOnShot", options.getBooleanValue("captureLocationOnShot"));
                        }
                        
                        ((Activity)CameraModule.this.mUniSDKInstance.getContext()).startActivityForResult(intent, CameraModule.REQUEST_CODE);
                    }
                    @Override
                    public void onDenied(int status) {
//                        Log.d("module","onDenied授权成功"+status);
                        if (status == 1) {
                            Toast.makeText(CameraModule.this.mUniSDKInstance.getContext(),
                                            "必须要授权相关权限，才能使用该功能哦！请前往设置中心开启权限。",
                                            Toast.LENGTH_LONG)
                                    .show();
                        }
                    }
                }).request();
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        PermissionReq.onRequestPermissionsResult(requestCode, permissions, grantResults);
    }


    @UniJSMethod(
            uiThread = true
    )
    public void saveQmPicture(JSONObject options, final UniJSCallback callback) {
//        Log.d("model", "saveQmPicture: "+options);
        if (ActivityCompat.checkSelfPermission(this.mUniSDKInstance.getContext(), "android.permission.WRITE_EXTERNAL_STORAGE") ==0 && ActivityCompat.checkSelfPermission(this.mUniSDKInstance.getContext(), "android.permission.READ_EXTERNAL_STORAGE") ==0) {
            String sourcePath  = options.getString("termUrl");
            String picturePath = FileUtils.getSDPath(this.mUniSDKInstance.getContext()) + "/TRSPGISData/TRSPCameraData";
            FileUtils.copyImageFile(sourcePath,picturePath);
            this.saveQmPicture = callback;
            File file = new File(sourcePath);
            String fileName = file.getName();
            String destPath = "file://" + picturePath +"/" +fileName;
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("url", destPath);
//            Log.d("destPath", "onActivityResult: "+destPath);
            this.saveQmPicture.invoke(jsonObject.toString());
        }
    }

    @UniJSMethod(
            uiThread = true
    )
    public void calculateFolderSize(final UniJSCallback callback) {
        if (ActivityCompat.checkSelfPermission(this.mUniSDKInstance.getContext(), "android.permission.WRITE_EXTERNAL_STORAGE") ==0 && ActivityCompat.checkSelfPermission(this.mUniSDKInstance.getContext(), "android.permission.READ_EXTERNAL_STORAGE") ==0) {
            String allPath = FileUtils.getSDPath(this.mUniSDKInstance.getContext())+"/TRSPGISData";
            File allFolder = new File(allPath);
            long allSize = FileUtils.getFolderSize(allFolder);

            String picturePath = FileUtils.getSDPath(this.mUniSDKInstance.getContext()) + "/TRSPGISData/TRSPCameraData";
            File cameraFolder = new File(picturePath);
            long cameraSize = FileUtils.getFolderSize(cameraFolder);
//            Log.d("size", "calculateFolderSize: "+cameraSize);

            String sqlPath = FileUtils.getSDPath(this.mUniSDKInstance.getContext())+"/TRSPGISData/TRSPSqliteData";
            File sqlFolder = new File(sqlPath);
            long sqlSize = FileUtils.getFolderSize(sqlFolder);

            long mapSize = allSize - cameraSize - sqlSize;

            this.folderSize = callback;
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("cameraSize", cameraSize);
            jsonObject.put("sqlSize", sqlSize);
            jsonObject.put("mapSize", mapSize);
            this.folderSize.invoke(jsonObject.toString());
        }
    }

    @UniJSMethod(
            uiThread = true
    )
    public void clearFolder(JSONObject options, final UniJSCallback callback) {
        if (ActivityCompat.checkSelfPermission(this.mUniSDKInstance.getContext(), "android.permission.WRITE_EXTERNAL_STORAGE") ==0 && ActivityCompat.checkSelfPermission(this.mUniSDKInstance.getContext(), "android.permission.READ_EXTERNAL_STORAGE") ==0) {
            JSONArray deleteValue = options.getJSONArray("deleteValue");
//            Log.d("size", "clearFolder: "+deleteValue.size());
            if (deleteValue.size() == 3){
                String allDataPath = FileUtils.getSDPath(this.mUniSDKInstance.getContext()) + "/TRSPGISData";
                FileUtils.deleteFiles(allDataPath);
            }else {
                for (int i = 0; i < deleteValue.size(); i++)
                {
                    String jsonObject = String.valueOf(deleteValue.get(i));
                    if ("map".equals(jsonObject)) {
                        String mapPath = FileUtils.getSDPath(this.mUniSDKInstance.getContext()) + "/TRSPGISData";
                        FileUtils.deleteMapData(mapPath);
                    } else if ("sql".equals(jsonObject)) {
                        String sqlPath = FileUtils.getSDPath(this.mUniSDKInstance.getContext()) + "/TRSPGISData/TRSPSqliteData";
                        FileUtils.deleteFiles(sqlPath);
                    } else if ("picture".equals(jsonObject)) {
                        String picturePath = FileUtils.getSDPath(this.mUniSDKInstance.getContext()) + "/TRSPGISData/TRSPCameraData";
                        FileUtils.deleteFiles(picturePath);
                    }
                }
            }
            this.deleteFolder = callback;
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("mag", "success");
            this.deleteFolder.invoke(jsonObject.toString());
        }
    }

    @UniJSMethod(uiThread = true)
    public void btnOpenFileChangeActivity(JSONObject options, final UniJSCallback callback) {
        String model = options.getString("model");//规定0是拍照，1是录视频
        if (this.mUniSDKInstance != null && this.mUniSDKInstance.getContext() instanceof Activity) {
            Intent intent = new Intent(this.mUniSDKInstance.getContext(), fileChangeActivity.class);
            // 设置需要传递的参数，如果有的话
             intent.putExtra("model", model);
            ((Activity)this.mUniSDKInstance.getContext()).startActivityForResult(intent, REQUEST_CODE);
            this.fileChangeCallback = callback;
        }

//        int chooseMode = SelectMimeType.ofImage();
//        if (model.equals("0")) {
//            chooseMode = SelectMimeType.ofImage();
//        }else if(model.equals("1")){
//            chooseMode = SelectMimeType.ofVideo();
//        }
//        PictureSelectorStyle selectorStyle = new PictureSelectorStyle();
//        TitleBarStyle whiteTitleBarStyle = new TitleBarStyle();
//        whiteTitleBarStyle.setHideTitleBar(false);
//        whiteTitleBarStyle.setTitleBarHeight(300);
//        selectorStyle.setTitleBarStyle(whiteTitleBarStyle);
//        PictureSelector.create(this.mUniSDKInstance.getContext())
//                .openGallery(chooseMode)
//                .setImageEngine(GlideEngine.createGlideEngine())
//                .isDisplayCamera(false)
//                .setSelectionMode(SelectModeConfig.SINGLE)
//                .setSelectorUIStyle(selectorStyle)
//                .setFilterVideoMaxSecond(120000)//过滤最大时长
//                .setSelectMaxDurationSecond(120000)//选择最大时长
//                .forResult(new OnResultCallbackListener<LocalMedia>() {
//                    @Override
//                    public void onResult(ArrayList<LocalMedia> result) {
//                        analyticalSelectResults(result);
//                    }
//
//                    @Override
//                    public void onCancel() {
//                        Log.i(TAG, "PictureSelector Cancel");
//                    }
//                });
//        this.fileChangeCallback = callback;
    }


    /**
     * 处理选择结果
     *
     * @param result
     */
    private void analyticalSelectResults(ArrayList<LocalMedia> result) {
        StringBuilder builder = new StringBuilder();
        builder.append("Result").append("\n");
        for (LocalMedia media : result) {
            if (media.getWidth() == 0 || media.getHeight() == 0) {
                if (PictureMimeType.isHasImage(media.getMimeType())) {
                    MediaExtraInfo imageExtraInfo = MediaUtils.getImageSize(this.mUniSDKInstance.getContext(),media.getPath());
                    media.setWidth(imageExtraInfo.getWidth());
                    media.setHeight(imageExtraInfo.getHeight());
                } else if (PictureMimeType.isHasVideo(media.getMimeType())) {
                    MediaExtraInfo videoExtraInfo = MediaUtils.getVideoSize(PictureAppMaster.getInstance().getAppContext(), media.getPath());
                    media.setWidth(videoExtraInfo.getWidth());
                    media.setHeight(videoExtraInfo.getHeight());
                }
            }
            builder.append(media.getAvailablePath()).append("\n");
            Log.i(TAG, "文件名: " + media.getFileName());
            Log.i(TAG, "是否压缩:" + media.isCompressed());
            Log.i(TAG, "压缩:" + media.getCompressPath());
            Log.i(TAG, "原图:" + media.getPath());
            Log.i(TAG, "绝对路径:" + media.getRealPath());
            Log.i(TAG, "是否裁剪:" + media.isCut());
            Log.i(TAG, "裁剪:" + media.getCutPath());
            Log.i(TAG, "是否开启原图:" + media.isOriginal());
            Log.i(TAG, "原图路径:" + media.getOriginalPath());
            Log.i(TAG, "沙盒路径:" + media.getSandboxPath());
            Log.i(TAG, "原始宽高: " + media.getWidth() + "x" + media.getHeight());
            Log.i(TAG, "裁剪宽高: " + media.getCropImageWidth() + "x" + media.getCropImageHeight());
            Log.i(TAG, "文件大小: " + media.getSize());
            String imagePath = media.getRealPath();
//                Log.d("choosefile", "onActivityResult: "+imagePath);
            if (!TextUtils.isEmpty(imagePath)) {
                imagePath = "file://" + imagePath;
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("url", imagePath);
                    Log.d("imagePath", "onActivityResult: "+imagePath);
                this.fileChangeCallback.invoke(jsonObject.toString());
            } else {
                Toast.makeText(this.mUniSDKInstance.getContext(), "选择照片失败，请重试", Toast.LENGTH_SHORT).show();
            }
        }
    }

}
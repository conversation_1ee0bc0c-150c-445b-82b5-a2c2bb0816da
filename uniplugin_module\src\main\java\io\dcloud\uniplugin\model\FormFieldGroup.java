package io.dcloud.uniplugin.model;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

/**
 * 表单字段组模型
 */
public class FormFieldGroup implements Serializable {
    
    @SerializedName("groupId")
    private String groupId;
    
    @SerializedName("title")
    private String title;
    
    @SerializedName("description")
    private String description;
    
    @SerializedName("fields")
    private List<FormFieldConfig> fields;
    
    public FormFieldGroup() {
    }
    
    public String getGroupId() {
        return groupId;
    }
    
    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public List<FormFieldConfig> getFields() {
        return fields;
    }
    
    public void setFields(List<FormFieldConfig> fields) {
        this.fields = fields;
    }
} 
package io.dcloud.uniplugin.form.utils;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;

public class LocationDialogUtils {
    private static final String TAG = "LocationDialogUtils";


    /**
     * 显示位置超出范围对话框
     */
    public static void showLocationOutOfRangeDialog(Activity activity,
            float distance,
            float radius,
            DialogInterface.OnClickListener retryListener,
            DialogInterface.OnClickListener cancelListener) {
        
        String message = String.format(
            "您当前位置距离目标点%.1f米，超出了有效范围%.1f米，无法填写表单。\n\n请移动到目标位置附近再次尝试。", 
            distance, radius);
            
        new AlertDialog.Builder(activity)
                .setTitle("位置不在工作范围内")
                .setMessage(message)
                .setPositiveButton("重新定位", retryListener)
                .setNegativeButton("取消", cancelListener)
                .setCancelable(false)
                .show();
    }

    /**
     * 显示位置服务设置对话框
     */
    public static void showLocationServiceDialog(Activity activity,
            boolean isGpsEnabled,
            boolean isNetworkEnabled,
            DialogInterface.OnClickListener settingsListener,
            DialogInterface.OnClickListener retryListener,
            DialogInterface.OnClickListener cancelListener) {
        
        StringBuilder message = new StringBuilder("无法获取您的当前位置，可能原因：\n\n");
        
        if (!isGpsEnabled) message.append("• GPS未开启\n");
        if (!isNetworkEnabled) message.append("• 网络定位未开启\n");
        
        message.append("\n建议操作：\n");
        if (!isGpsEnabled || !isNetworkEnabled) message.append("• 打开设置，确保位置服务已开启\n");
        message.append("• 移动到空旷区域，提高GPS信号质量\n• 尝试重新定位\n");
        
        AlertDialog.Builder builder = new AlertDialog.Builder(activity)
                .setTitle("位置获取失败")
                .setMessage(message.toString())
                .setPositiveButton("重新定位", retryListener)
                .setNegativeButton("仅查看", cancelListener)
                .setCancelable(false);
        
        if (!isGpsEnabled) {
            builder.setNeutralButton("打开设置", settingsListener);
        }
        
        builder.show();
    }
} 
package io.dcloud.uniplugin;

import com.esri.arcgisruntime.mapping.LayerList;
import com.esri.arcgisruntime.mapping.Viewpoint;

import java.io.Serializable;

public class MapViewState implements Serializable {
    private Viewpoint currentViewpoint;
    public Viewpoint getViewpoint() {
        return currentViewpoint;
    }



    public MapViewState(Viewpoint currentViewpoint){
        this.currentViewpoint=currentViewpoint;
    }
}

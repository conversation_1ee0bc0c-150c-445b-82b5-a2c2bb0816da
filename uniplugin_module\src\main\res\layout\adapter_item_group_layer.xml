<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingBottom="@dimen/adapter_item_layer_padding_vert"
    android:paddingTop="@dimen/adapter_item_layer_padding_vert">

    <CheckBox
        android:id="@+id/layerCheckbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:checked="true"

        />

    <TextView
        android:id="@+id/layerNameTextView"
        android:layout_width="150dp"
        android:layout_height="wrap_content"
        android:textColor="@android:color/black"
        android:textSize="@dimen/adapter_item_layer_name_text_size" />

    <LinearLayout
        android:id="@+id/ll_element_count"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:orientation="horizontal"
        android:visibility="invisible">

        <ImageView
            android:id="@+id/iv_element_sub"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:padding="5dp"
            android:scaleType="fitCenter"
            android:layout_gravity="center_vertical"
            android:src="@drawable/minus" />

        <EditText
            android:id="@+id/et_element_count"
            android:layout_width="30dp"
            android:layout_height="25dp"
            android:gravity="center"
            android:layout_gravity="center_vertical"
            android:enabled="false"
            android:textSize="12sp"
            android:maxLength="3"
            android:background="@drawable/shape_single_element_count_bg"
            android:text="1.0" />

        <ImageView
            android:id="@+id/iv_element_add"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:padding="5dp"
            android:scaleType="fitCenter"
            android:layout_gravity="center_vertical"
            android:src="@drawable/addbold" />

        <ImageButton
            android:id="@+id/move_up_btn"
            style="@style/layers_move"
            android:layout_width="25dp"
            android:layout_height="match_parent"
            android:contentDescription="@string/app_name"
            app:srcCompat="@drawable/move_up" />
        <ImageButton
            style="@style/layers_move"
            android:contentDescription="@string/app_name"
            app:srcCompat="@drawable/down"
            android:id="@+id/down_btn"
            android:layout_width="25dp"
            android:layout_height="match_parent"
            />

        <ImageButton
            android:id="@+id/exitButton"
            android:layout_gravity="end"
            android:layout_marginEnd="5dp"
            style="@style/layers_close"
            android:layout_width="match_parent"
            android:contentDescription="@string/app_name"
            app:srcCompat="@drawable/close"
            android:visibility="invisible"/>
    </LinearLayout>
</LinearLayout>
package io.dcloud.uniplugin.sampleflow;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.dcloud.uniplugin.http.RetrofitManager;
import io.dcloud.uniplugin.model.ApiResponse;
import io.dcloud.uniplugin.model.YplzBatch;
import io.dcloud.uniplugin.model.YplzPageResponse;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import uni.dcloud.io.uniplugin_module.R;

/**
 * 样品流转列表页面
 */
public class SampleFlowListActivity extends AppCompatActivity {
    
    private static final String TAG = "SampleFlowListActivity";
    private static final int REQUEST_CREATE = 1001;
    private static final int REQUEST_MANAGE = 1002;
    private static final int REQUEST_EDIT = 1003;
    
    private EditText editTextSearch;
    private Button buttonSearch;
    private SwipeRefreshLayout swipeRefreshLayout;
    private RecyclerView recyclerView;
    private LinearLayout layoutEmpty;
    private FloatingActionButton fabAdd;
    private Button buttonManageBatch;
    
    private SampleFlowAdapter adapter;
    private List<YplzBatch> batchList = new ArrayList<>();
    
    private int currentPage = 1;
    private int pageSize = 10;
    private boolean isLoading = false;
    private boolean hasMoreData = true;
    
    // 新增：标识是否是接收模式
    private boolean isReceiveMode = false;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sample_flow_list);
        
        // 检查是否是接收模式
        if (getIntent() != null) {
            isReceiveMode = "RECEIVE".equals(getIntent().getStringExtra("VIEW_MODE"));
        }
        
        // 设置ActionBar标题和返回按钮
        setTitle(isReceiveMode ? "样品接收" : "样品流转");
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        
        initViews();
        setupRecyclerView();
        setupListeners();
        
        // 确保初始状态下显示加载中
        layoutEmpty.setVisibility(View.GONE); // 先隐藏空视图
        recyclerView.setVisibility(View.VISIBLE); // 显示RecyclerView
        
        // 添加调试日志
        Log.d(TAG, "开始加载数据");
        
        loadData(true);
    }
    
    private void initViews() {
        editTextSearch = findViewById(R.id.editTextSearch);
        buttonSearch = findViewById(R.id.buttonSearch);
        swipeRefreshLayout = findViewById(R.id.swipeRefreshLayout);
        recyclerView = findViewById(R.id.recyclerView);
        layoutEmpty = findViewById(R.id.layoutEmpty);
        fabAdd = findViewById(R.id.fabAdd);
        buttonManageBatch = findViewById(R.id.buttonManageBatch);
        
        // 根据模式控制按钮显示
        if (isReceiveMode) {
            // 接收模式下隐藏添加和管理批次按钮
            fabAdd.setVisibility(View.GONE);
            buttonManageBatch.setVisibility(View.GONE);
        } else {
            // 送样模式下显示添加按钮，隐藏管理批次按钮（原来的逻辑）
            fabAdd.setVisibility(View.VISIBLE);
            buttonManageBatch.setVisibility(View.GONE);
        }
    }
    
    private void setupRecyclerView() {
        adapter = new SampleFlowAdapter(this, batchList);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);
        
        // 设置编辑、删除和确定寄送操作监听器
        adapter.setOnBatchOperationListener(new SampleFlowAdapter.OnBatchOperationListener() {
            @Override
            public void onEdit(YplzBatch batch, int position) {
                // 仅送样模式下允许编辑
                if (!isReceiveMode) {
                    editSampleBatch(batch);
                }
            }
            
            @Override
            public void onDelete(YplzBatch batch, int position) {
                // 仅送样模式下允许删除
                if (!isReceiveMode) {
                    confirmDeleteSampleBatch(batch, position);
                }
            }
            
            @Override
            public void onConfirmShipment(YplzBatch batch, int position) {
                // 仅送样模式下允许确认寄送
                if (!isReceiveMode) {
                    confirmShipment(batch, position);
                }
            }
        });
        
        // 添加滚动监听，实现分页加载
        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                
                LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
                if (layoutManager != null && !isLoading && hasMoreData) {
                    int visibleItemCount = layoutManager.getChildCount();
                    int totalItemCount = layoutManager.getItemCount();
                    int pastVisibleItems = layoutManager.findFirstVisibleItemPosition();
                    
                    if (pastVisibleItems + visibleItemCount >= totalItemCount - 5) {
                        // 距离底部还有5个item时开始加载下一页
                        loadData(false);
                    }
                }
            }
        });
    }
    
    private void setupListeners() {
        // 下拉刷新
        swipeRefreshLayout.setOnRefreshListener(() -> {
            currentPage = 1;
            hasMoreData = true;
            loadData(true);
        });
        
        // 搜索按钮
        buttonSearch.setOnClickListener(v -> {
            currentPage = 1;
            hasMoreData = true;
            loadData(true);
        });
        
        // 新增按钮
        fabAdd.setOnClickListener(v -> {
            Intent intent = new Intent(this, CreateSampleBatchActivity.class);
            startActivityForResult(intent, REQUEST_CREATE);
        });

        // 批次管理按钮
        buttonManageBatch.setOnClickListener(v -> {
            Intent intent = new Intent(this, SampleBatchListActivity.class);
            startActivityForResult(intent, REQUEST_MANAGE);
        });
    }
    
    /**
     * 确认寄送
     * @param batch 样品批次
     * @param position 在列表中的位置
     */
    private void confirmShipment(final YplzBatch batch, final int position) {
        // 检查批次状态是否为待寄送(状态值为0)
        if (batch.getBatchState() == null || batch.getBatchState() != 0) {
            Toast.makeText(this, "只有待寄送状态的批次才能确认寄送", Toast.LENGTH_SHORT).show();
            return;
        }
        
        new AlertDialog.Builder(this)
                .setTitle("确认寄送")
                .setMessage("确定要将批次编号为 " + (batch.getBatchCode() != null ? batch.getBatchCode() : "未知") + " 的样品批次标记为寄送中状态吗？")
                .setPositiveButton("确定", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        updateBatchStatus(batch, position);
                    }
                })
                .setNegativeButton("取消", null)
                .show();
    }
    
    /**
     * 更新批次状态
     * @param batch 样品批次
     * @param position 在列表中的位置
     */
    private void updateBatchStatus(final YplzBatch batch, final int position) {
        // 显示加载提示
        Toast.makeText(this, "正在更新状态...", Toast.LENGTH_SHORT).show();
        
        // 调用接口更新批次状态为1（寄送中）
        RetrofitManager.getInstance(this)
                .getSampleBatchServiceApi()
                .updateSampleBatchStatus(batch.getId(), 1L)
                .enqueue(new Callback<ApiResponse<Boolean>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<Boolean>> call, Response<ApiResponse<Boolean>> response) {
                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<Boolean> apiResponse = response.body();
                            if (apiResponse.isSuccess()) {
                                // 更新成功
                                Toast.makeText(SampleFlowListActivity.this, "批次状态已更新为寄送中", Toast.LENGTH_SHORT).show();
                                
                                // 更新本地数据
                                batch.setBatchState(1L);
                                adapter.notifyItemChanged(position);
                                
                                // 刷新数据
                                loadData(true);
                            } else {
                                // 更新失败
                                String errorMsg = "更新状态失败";
                                if (apiResponse.getMsg() != null) {
                                    errorMsg = apiResponse.getMsg();
                                }
                                showError(errorMsg);
                            }
                        } else {
                            showError("网络请求失败");
                        }
                    }
                    
                    @Override
                    public void onFailure(Call<ApiResponse<Boolean>> call, Throwable t) {
                        showError("网络请求失败：" + t.getMessage());
                        Log.e(TAG, "更新批次状态失败", t);
                    }
                });
    }
    
    /**
     * 编辑样品批次
     */
    private void editSampleBatch(YplzBatch batch) {
        // 检查批次状态是否为待寄送(状态值为0)
        if (batch.getBatchState() == null || batch.getBatchState() != 0) {
            Toast.makeText(this, "只有待寄送状态的批次可以编辑", Toast.LENGTH_SHORT).show();
            return;
        }
        
        Intent intent = new Intent(this, CreateSampleBatchActivity.class);
        intent.putExtra("receiveOrganizationId", batch.getReceiveOrganizationId());
        intent.putExtra("isEdit", true);
        intent.putExtra("batchId", batch.getId());
        // 传递批次数据用于预填充
        intent.putExtra("batchCode", batch.getBatchCode()); // 添加批次编号（项目编号）
        intent.putExtra("batchType", batch.getBatchType() != null ? batch.getBatchType() : -1); // 添加批次类型
        intent.putExtra("senderName", batch.getSenderName());
        intent.putExtra("senderPhone", batch.getSenderPhone());
        intent.putExtra("sendOrganization", batch.getSendOrganization());
        intent.putExtra("deliveryType", batch.getDeliveryType());
        intent.putExtra("deliveryMessage", batch.getDeliveryMessage());
        intent.putExtra("senderSignature", batch.getSenderSignature());  // 添加签名URL的传递
        startActivityForResult(intent, REQUEST_EDIT);
    }
    
    /**
     * 确认删除样品批次
     */
    private void confirmDeleteSampleBatch(final YplzBatch batch, final int position) {
        // 检查批次状态是否为待寄送(状态值为0)
        if (batch.getBatchState() == null || batch.getBatchState() != 0) {
            Toast.makeText(this, "只有待寄送状态的批次可以删除", Toast.LENGTH_SHORT).show();
            return;
        }
        
        new AlertDialog.Builder(this)
                .setTitle("删除确认")
                .setMessage("确定要删除批次编号为 " + (batch.getBatchCode() != null ? batch.getBatchCode() : "未知") + " 的样品批次吗？")
                .setPositiveButton("删除", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        deleteSampleBatch(batch, position);
                    }
                })
                .setNegativeButton("取消", null)
                .show();
    }
    
    /**
     * 删除样品批次
     */
    private void deleteSampleBatch(final YplzBatch batch, final int position) {
        //显示加载提示
        Toast.makeText(this, "正在删除...", Toast.LENGTH_SHORT).show();
        
        RetrofitManager.getInstance(this)
                .getSampleBatchServiceApi()
                .deleteSampleBatch(batch.getId())
                .enqueue(new Callback<ApiResponse<Boolean>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<Boolean>> call, Response<ApiResponse<Boolean>> response) {
                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<Boolean> apiResponse = response.body();
                            if (apiResponse.getCode() == 0 && apiResponse.getData() != null && apiResponse.getData()) {
                                // 删除成功
                                Toast.makeText(SampleFlowListActivity.this, "删除成功", Toast.LENGTH_SHORT).show();
                                
                                // 从列表中移除
                                if (position < batchList.size()) {
                                    batchList.remove(position);
                                    adapter.notifyItemRemoved(position);
                                    adapter.notifyItemRangeChanged(position, batchList.size());
                                }
                                
                                // 更新UI
                                updateUI();
                            } else {
                                // 删除失败
                                String errorMsg = "删除失败";
                                if (apiResponse.getMsg() != null) {
                                    errorMsg = apiResponse.getMsg();
                                }
                                showError(errorMsg);
                            }
                        } else {
                            showError("网络请求失败");
                        }
                    }
                    
                    @Override
                    public void onFailure(Call<ApiResponse<Boolean>> call, Throwable t) {
                        showError("网络请求失败：" + t.getMessage());
                        Log.e(TAG, "删除样品批次失败", t);
                    }
                });
    }
    
    /**
     * 加载数据
     * @param isRefresh 是否为刷新操作（清空现有数据）
     */
    private void loadData(boolean isRefresh) {
        if (isLoading) return;
        
        isLoading = true;
        
        if (isRefresh) {
            batchList.clear();
            currentPage = 1;
            swipeRefreshLayout.setRefreshing(true);
            
            // 确保UI状态一致
            if (adapter != null) {
                adapter.notifyDataSetChanged();
            }
        }
        // 获取搜索文本
        String searchText = editTextSearch.getText().toString().trim();
        
        // 创建查询参数Map
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("pageNo", currentPage);
        queryParams.put("pageSize", pageSize);
        
        // 如果有搜索文本，添加到查询参数
        if (!TextUtils.isEmpty(searchText)) {
            queryParams.put("batchCode", searchText);
        }
        
        // 发起GET请求，使用正确的接口 /bcgd/sample/yplz/page
        RetrofitManager.getInstance(this)
                .getSampleBatchServiceApi()
                .getSampleBatchPage(queryParams) // 使用Map作为参数
                .enqueue(new Callback<ApiResponse<YplzPageResponse<YplzBatch>>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<YplzPageResponse<YplzBatch>>> call, 
                                         Response<ApiResponse<YplzPageResponse<YplzBatch>>> response) {
                        isLoading = false;
                        swipeRefreshLayout.setRefreshing(false);
                        
                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<YplzPageResponse<YplzBatch>> apiResponse = response.body();
                            if (apiResponse.getCode() == 0 && apiResponse.getData() != null) {
                                YplzPageResponse<YplzBatch> data = apiResponse.getData();
                                
                                // 尝试输出data对象的所有字段
                                try {

                                    // 如果list为null但records不为null，则可能是字段名映射问题
                                    if (data.getList() == null ) {
                                        batchList.clear(); // 确保清空之前的数据
                                        
                                        // 使用records代替list
                                        List<YplzBatch> batchData = data.getList();
                                        Log.d(TAG, "使用records获取的数据条数: " + batchData.size());
                                        
                                        // 处理数据...
                                        batchList.addAll(batchData);
                                        
                                        Log.d(TAG, "添加后批次列表大小：" + batchList.size());
                                        
                                        // 更新UI...
                                        runOnUiThread(() -> {
                                            updateUI();
                                        });
                                        
                                        return; // 提前返回，避免后续重复处理
                                    }
                                } catch (Exception e) {
                                    Log.e(TAG, "解析API返回数据字段时出错", e);
                                }
                                
                                // 处理返回的数据 - 使用getList()替代getRecords()
                                List<YplzBatch> batchData = data.getList();
                                
                                // 添加日志以便调试
                                Log.d(TAG, "接收到批次数据：" + (batchData != null ? batchData.size() : 0) + "条");
                                
                                // 如果通过正常方式获取不到数据，尝试直接解析JSON
                                if (batchData == null || batchData.isEmpty()) {
                                    
                                    // 更新UI
                                    runOnUiThread(() -> {
                                        updateUI();
                                    });
                                    return;
                                }
                                
                                if (batchData != null && !batchData.isEmpty()) {
                                    // 将数据添加到列表
                                    batchList.addAll(batchData);
                                    
                                    // 添加日志输出当前列表大小
                                    Log.d(TAG, "添加后批次列表大小：" + batchList.size());
                                    
                                    // 修复判断是否有更多数据的逻辑
                                    if (isRefresh) {
                                        // 如果是刷新操作，直接根据数据量判断是否有更多数据
                                        hasMoreData = batchData.size() >= pageSize;
                                    } else {
                                        // 如果是加载更多操作，判断返回的数据是否为空
                                        hasMoreData = batchData.size() > 0;
                                    }
                                } else {
                                    hasMoreData = false;
                                }
                                
                                // 确保在UI线程中更新界面
                                runOnUiThread(() -> {
                                    // 无论如何都执行UI更新
                                    updateUI();
                                    
                                    if (batchList.isEmpty()) {
                                        Toast.makeText(SampleFlowListActivity.this, "没有找到相关批次数据", Toast.LENGTH_SHORT).show();
                                    } else {
                                        // 显示找到的数据数量
                                        Toast.makeText(SampleFlowListActivity.this, "找到 " + batchList.size() + " 条批次数据", Toast.LENGTH_SHORT).show();
                                    }
                                });
                            } else {
                                showError("加载失败：" + apiResponse.getMsg());
                            }
                        } else {
                            showError("网络请求失败");
                        }
                    }
                    
                    @Override
                    public void onFailure(Call<ApiResponse<YplzPageResponse<YplzBatch>>> call, Throwable t) {
                        isLoading = false;
                        swipeRefreshLayout.setRefreshing(false);
                        Log.e(TAG, "加载样品流转数据失败", t);
                        showError("加载失败：" + t.getMessage());
                    }
                });
    }
    
    private void updateUI() {
        // 添加日志
        Log.d(TAG, "更新UI，批次列表大小：" + batchList.size());
        
        // 确保adapter不为空
        if (adapter == null) {
            adapter = new SampleFlowAdapter(this, batchList);
            recyclerView.setAdapter(adapter);
        } else {
            adapter.notifyDataSetChanged();
        }
        
        // 根据模式设置适配器的操作按钮显示状态
        if (adapter != null) {
            adapter.setReceiveMode(isReceiveMode);
        }
        
        // 显示或隐藏空数据提示
        if (batchList.isEmpty()) {
            // 确保完全设置可见性
            layoutEmpty.setVisibility(View.VISIBLE);
            layoutEmpty.bringToFront(); // 确保空视图在最上层
            recyclerView.setVisibility(View.GONE);
            Log.d(TAG, "显示空数据视图");
        } else {
            // 确保完全设置可见性
            layoutEmpty.setVisibility(View.GONE);
            recyclerView.setVisibility(View.VISIBLE);
            recyclerView.bringToFront(); // 确保列表在最上层
            Log.d(TAG, "显示列表视图，列表项数：" + batchList.size());
        }
    }
    
    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        // 添加日志跟踪返回结果
        Log.d(TAG, "onActivityResult: requestCode=" + requestCode + ", resultCode=" + resultCode);
        
        if ((requestCode == REQUEST_CREATE || requestCode == REQUEST_MANAGE) && resultCode == RESULT_OK) {
            // 新增成功或从批次页面返回，刷新列表
            currentPage = 1;
            hasMoreData = true;
            loadData(true);
        } else if (requestCode == REQUEST_EDIT && resultCode == RESULT_OK) {
            // 编辑成功返回，刷新列表
            currentPage = 1;
            hasMoreData = true;
            loadData(true);
        } else if (requestCode == 1001 && resultCode == RESULT_OK) {
            // 处理从样品详情页返回的结果（1001是SampleFlowAdapter中定义的REQUEST_DETAIL）
            // 这包括接收确认成功后的返回
            Log.d(TAG, "从样品详情页返回，刷新列表数据");
            currentPage = 1;
            hasMoreData = true;
            loadData(true);
        }
    }
    
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // 注释掉或不添加管理批次的菜单项
        // getMenuInflater().inflate(R.menu.sample_flow_menu, menu);
        return true;
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == R.id.action_manage_batch) {
            Intent intent = new Intent(this, SampleBatchListActivity.class);
            startActivityForResult(intent, REQUEST_MANAGE);
            return true;
        } else if (id == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

} 
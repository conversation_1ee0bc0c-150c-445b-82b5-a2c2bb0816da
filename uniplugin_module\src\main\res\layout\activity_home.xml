<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5"
    tools:context="io.dcloud.uniplugin.HomeActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 用户信息卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="欢迎使用广东省补充耕地系统"
                    android:textColor="#333333"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="用户名："
                        android:textColor="#666666" />

                    <TextView
                        android:id="@+id/textViewUsername"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="未知用户"
                        android:textColor="#333333" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="角色："
                        android:textColor="#666666" />

                    <TextView
                        android:id="@+id/textViewRole"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="未知角色"
                        android:textColor="#333333" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 功能卡片网格 -->
        <GridLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:columnCount="2"
            android:rowCount="3">

            <!-- 样点管理卡片 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardViewSamplingPoints"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_rowWeight="1"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@android:drawable/ic_menu_agenda"
                        android:tint="#E91E63" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="评价单元管理"
                        android:textColor="#333333"
                        android:textSize="16sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 个人信息卡片 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardViewProfile"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_rowWeight="1"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@android:drawable/ic_menu_myplaces"
                        android:tint="#9C27B0" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="个人信息"
                        android:textColor="#333333"
                        android:textSize="16sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 样品流转卡片 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardViewSampleFlow"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_rowWeight="1"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground"
                android:visibility="visible"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@android:drawable/ic_menu_send"
                        android:tint="#FF5722" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="样品流转"
                        android:textColor="#333333"
                        android:textSize="16sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
            
            <!-- 下载离线数据卡片 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardViewDownloadData"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_rowWeight="1"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground"
                android:visibility="gone"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/down"
                        android:tint="#3F51B5" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="同步离线数据"
                        android:textColor="#333333"
                        android:textSize="16sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </GridLayout>
    </LinearLayout>
</ScrollView>
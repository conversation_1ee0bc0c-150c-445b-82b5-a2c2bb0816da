//package io.dcloud.uniplugin;
//
//import android.app.Activity;
//import android.content.Context;
//import android.content.Intent;
//import android.util.Log;
//
//import org.json.JSONArray;
//import org.json.JSONException;
//import org.json.JSONObject;
//
//import java.util.List;
//
//import io.dcloud.feature.uniapp.annotation.UniJSMethod;
//import io.dcloud.feature.uniapp.bridge.UniJSCallback;
//import io.dcloud.feature.uniapp.common.UniModule;
//import io.dcloud.uniplugin.db.SamplingPoint;
//import io.dcloud.uniplugin.db.UploadDao;
//import io.dcloud.uniplugin.fileUpload.FileUploadActivity;
//import io.dcloud.uniplugin.offlineRecords.OfflineRecordsActivity;
//import io.dcloud.uniplugin.samplingPoint.SamplingPointListActivity;
//import io.dcloud.uniplugin.service.SamplingPointSyncService;
//
//public class NativePlugin extends UniModule {
//    private static final String TAG = "NativePlugin";
//
//    /**
//     * 获取Activity实例
//     * @return Activity实例
//     */
//    private Activity getActivity() {
//        Context context = mUniSDKInstance.getContext();
//        if (context instanceof Activity) {
//            return (Activity) context;
//        }
//        Log.e(TAG, "Context不是Activity实例");
//        return null;
//    }
//
//    /**
//     * 打开文件上传页面
//     */
//    @UniJSMethod(uiThread = true)
//    public void openFileUpload() {
//        Activity activity = getActivity();
//        if (activity != null) {
//            Intent intent = new Intent(activity, FileUploadActivity.class);
//            activity.startActivity(intent);
//        } else {
//            Log.e(TAG, "无法打开文件上传页面：Context不是Activity实例");
//        }
//    }
//
//    /**
//     * 打开离线记录页面
//     */
//    @UniJSMethod(uiThread = true)
//    public void openOfflineRecords() {
//        Activity activity = getActivity();
//        if (activity != null) {
//            Intent intent = new Intent(activity, OfflineRecordsActivity.class);
//            activity.startActivity(intent);
//        } else {
//            Log.e(TAG, "无法打开离线记录页面：Context不是Activity实例");
//        }
//    }
//
//    /**
//     * 打开样点列表页面
//     * @param options 参数，包含baseUrl和token
//     */
//    @UniJSMethod(uiThread = true)
//    public void openSamplingPointList(JSONObject options) {
//        try {
//            Activity activity = getActivity();
//            if (activity == null) {
//                Log.e(TAG, "无法打开样点列表页面：Context不是Activity实例");
//                return;
//            }
//
//            Intent intent = new Intent(activity, SamplingPointListActivity.class);
//
//            // 传递参数
//            if (options != null) {
//                String baseUrl = options.optString("baseUrl", "");
//                String token = options.optString("token", "");
//
//                if (!baseUrl.isEmpty()) {
//                    intent.putExtra("baseUrl", baseUrl);
//                }
//
//                if (!token.isEmpty()) {
//                    intent.putExtra("token", token);
//                }
//            }
//
//            activity.startActivity(intent);
//        } catch (Exception e) {
//            Log.e(TAG, "打开样点列表页面失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 同步采样点数据
//     * @param options 参数，包含baseUrl和token
//     * @param callback 回调
//     */
//    @UniJSMethod(uiThread = false)
//    public void syncSamplingPoints(JSONObject options, UniJSCallback callback) {
//        try {
//            String baseUrl = options.optString("baseUrl", "");
//            String token = options.optString("token", "");
//
//            if (baseUrl.isEmpty() || token.isEmpty()) {
//                JSONObject result = new JSONObject();
//                result.put("code", -1);
//                result.put("msg", "baseUrl和token不能为空");
//                callback.invoke(result);
//                return;
//            }
//
//            SamplingPointSyncService syncService = new SamplingPointSyncService(mUniSDKInstance.getContext(), baseUrl, token);
//            syncService.syncSamplingPoints(new SamplingPointSyncService.SyncCallback() {
//                @Override
//                public void onSuccess(int count) {
//                    try {
//                        JSONObject result = new JSONObject();
//                        result.put("code", 0);
//                        result.put("msg", "同步成功");
//                        result.put("count", count);
//                        callback.invoke(result);
//                    } catch (JSONException e) {
//                        e.printStackTrace();
//                    }
//                }
//
//                @Override
//                public void onFailure(String errorMsg) {
//                    try {
//                        JSONObject result = new JSONObject();
//                        result.put("code", -1);
//                        result.put("msg", errorMsg);
//                        callback.invoke(result);
//                    } catch (JSONException e) {
//                        e.printStackTrace();
//                    }
//                }
//            });
//        } catch (Exception e) {
//            try {
//                JSONObject result = new JSONObject();
//                result.put("code", -1);
//                result.put("msg", "同步失败: " + e.getMessage());
//                callback.invoke(result);
//            } catch (JSONException je) {
//                je.printStackTrace();
//            }
//        }
//    }
//
//    /**
//     * 获取所有采样点数据
//     * @param callback 回调
//     */
//    @UniJSMethod(uiThread = false)
//    public void getAllSamplingPoints(UniJSCallback callback) {
//        try {
//            UploadDao uploadDao = new UploadDao(mUniSDKInstance.getContext());
//            List<SamplingPoint> points = uploadDao.getAllSamplingPoints();
//
//            JSONObject result = new JSONObject();
//            JSONArray dataArray = new JSONArray();
//
//            for (SamplingPoint point : points) {
//                JSONObject item = new JSONObject();
//                item.put("id", point.getId());
//                item.put("ydbh", point.getYdbh());
//                item.put("ydlb", point.getYdlb());
//                item.put("zldwdm", point.getZldwdm());
//                item.put("zldwmc", point.getZldwmc());
//                item.put("cyjd", point.getCyjd());
//                item.put("cywd", point.getCywd());
//                item.put("cylx", point.getCylx());
//                item.put("tdlylx", point.getTdlylx());
//                item.put("tdlylxMc", point.getTdlylxMc());
//                item.put("sjdm", point.getSjdm());
//                item.put("shjdm", point.getShjdm());
//                item.put("xjdm", point.getXjdm());
//                item.put("zjdm", point.getZjdm());
//                item.put("cydId", point.getCydId());
//                item.put("cydMc", point.getCydMc());
//                item.put("ts", point.getTs());
//                item.put("tz", point.getTz());
//                item.put("tl", point.getTl());
//                item.put("yl", point.getYl());
//                item.put("sfsty", point.getSfsty());
//                item.put("hbgd", point.getHbgd());
//                item.put("yhId", point.getYhId());
//                item.put("yhMc", point.getYhMc());
//                item.put("dcsj", point.getDcsj());
//                item.put("dcdw", point.getDcdw());
//                item.put("dcz", point.getDcz());
//                item.put("tjsj", point.getTjsj());
//                item.put("sfyjd", point.getSfyjd());
//                item.put("bz", point.getBz());
//                item.put("zt", point.getZt());
//                item.put("cygc", point.getCygc());
//                item.put("sfsjptzg", point.getSfsjptzg());
//                item.put("cygczt", point.getCygczt());
//
//                if (point.getCygcTjsj() != null) {
//                    item.put("cygcTjsj", point.getCygcTjsj());
//                }
//
//                item.put("sfgjptzg", point.getSfgjptzg());
//                item.put("sftsncp", point.getSftsncp());
//                item.put("ncpmc", point.getNcpmc());
//                item.put("gsdmc", point.getGsdmc());
//
//                dataArray.put(item);
//            }
//
//            result.put("code", 0);
//            result.put("msg", "获取成功");
//            result.put("data", dataArray);
//            result.put("count", points.size());
//
//            callback.invoke(result);
//        } catch (Exception e) {
//            try {
//                JSONObject result = new JSONObject();
//                result.put("code", -1);
//                result.put("msg", "获取失败: " + e.getMessage());
//                callback.invoke(result);
//            } catch (JSONException je) {
//                je.printStackTrace();
//            }
//        }
//    }
//}
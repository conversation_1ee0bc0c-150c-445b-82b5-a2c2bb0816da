package io.dcloud.uniplugin.form.utils;

import android.text.TextUtils;
import android.util.Log;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import io.dcloud.uniplugin.form.field.FieldFile;
import io.dcloud.uniplugin.model.FormConfig;
import io.dcloud.uniplugin.model.FormData;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 * FormData适配器类，用于处理表单数据和字段文件之间的转换
 */
public class FormDataAdapter {
    private static final String TAG = "FormDataAdapter";

    /**
     * 将FormData中的文件列表(List<String>)转换为FieldFile列表(List<FieldFile>)
     * @param formData 表单数据
     * @return 转换后的字段文件映射
     */
    public static Map<String, List<FieldFile>> convertFilesToFieldFiles(FormData formData) {
        Map<String, List<FieldFile>> result = new HashMap<>();
        
        if (formData == null) {
            Log.e(TAG, "formData为空，无法转换文件");
            return result;
        }
        
        Map<String, List<String>> files = formData.getFiles();
        if (files == null || files.isEmpty()) {
            Log.w(TAG, "formData中没有文件数据");
            return result;
        }
        
        for (Map.Entry<String, List<String>> entry : files.entrySet()) {
            String fieldId = entry.getKey();
            List<String> filePaths = entry.getValue();
            
            if (filePaths == null || filePaths.isEmpty()) {
                continue;
            }
            
            List<FieldFile> fieldFiles = new ArrayList<>();
            for (String path : filePaths) {
                if (!TextUtils.isEmpty(path)) {
                    // 根据文件路径创建FieldFile对象
                    FieldFile fieldFile = new FieldFile(path);
                    fieldFiles.add(fieldFile);
                }
            }
            
            if (!fieldFiles.isEmpty()) {
                result.put(fieldId, fieldFiles);
            }
        }
        return result;
    }
    
    /**
     * 将FieldFile列表(List<FieldFile>)转换为字符串列表(List<String>)
     * @param fieldFiles 字段文件映射
     * @return 转换后的文件路径映射
     */
    public static Map<String, List<String>> convertFieldFilesToStrings(Map<String, List<FieldFile>> fieldFiles) {
        Map<String, List<String>> result = new HashMap<>();
        
        if (fieldFiles == null || fieldFiles.isEmpty()) {
            return result;
        }
        
        for (Map.Entry<String, List<FieldFile>> entry : fieldFiles.entrySet()) {
            String fieldId = entry.getKey();
            List<FieldFile> files = entry.getValue();
            
            if (files == null || files.isEmpty()) {
                continue;
            }
            
            List<String> paths = new ArrayList<>();
            for (FieldFile file : files) {
                if (file != null && file.getPath() != null) {
                    paths.add(file.getPath());
                }
            }
            
            if (!paths.isEmpty()) {
                result.put(fieldId, paths);
            }
        }
        
        return result;
    }
    
    /**
     * 获取FormConfig的空实现
     * @return 空的FormConfig对象
     */
    public static FormConfig getEmptyFormConfig() {
        return new FormConfig();
    }

    /**
     * 从表单数据中提取媒体文件路径
     * @param formData 表单数据
     * @param formId 表单ID
     * @param context 上下文
     * @return 媒体文件映射
     */
    public static Map<String, List<String>> extractMediaPathsFromFormData(JSONObject formData, String formId, android.content.Context context) {
        Map<String, List<String>> mediaFiles = new HashMap<>();
        
        if (formData == null) {
            Log.e(TAG, "表单数据为空，无法提取媒体文件路径");
            return mediaFiles;
        }
        
        try {
            // 检查__files对象是否存在
            if (!formData.has("__files")) {
                Log.d(TAG, "表单数据中不包含__files对象，无法提取媒体文件路径");
                return mediaFiles;
            }
            
            // 从__files对象中获取媒体文件路径
            JSONObject filesData = formData.getJSONObject("__files");
            Iterator<String> keys = filesData.keys();
            
            while (keys.hasNext()) {
                String fieldId = keys.next();
                JSONArray fileArray = filesData.getJSONArray(fieldId);
                List<String> paths = new ArrayList<>();
                
                for (int i = 0; i < fileArray.length(); i++) {
                    if (fileArray.get(i) instanceof String) {
                        String path = fileArray.getString(i);
                        if (path != null && !path.isEmpty()) {
                            paths.add(path);
                            Log.d(TAG, "从__files中提取字段[" + fieldId + "]的文件路径: " + path);
                        }
                    }
                }
                
                if (!paths.isEmpty()) {
                    mediaFiles.put(fieldId, paths);
                    Log.d(TAG, "从__files中提取字段[" + fieldId + "]的" + paths.size() + "个文件路径");
                }
            }
            
            Log.d(TAG, "共从__files中提取" + countTotalFiles(mediaFiles) + "个文件路径，涉及" + mediaFiles.size() + "个字段");
        } catch (Exception e) {
            Log.e(TAG, "提取媒体文件路径失败: " + e.getMessage(), e);
        }
        
        return mediaFiles;
    }
    
    /**
     * 计算映射中文件总数
     */
    private static int countTotalFiles(Map<String, List<String>> fileMap) {
        int count = 0;
        for (List<String> files : fileMap.values()) {
            count += files.size();
        }
        return count;
    }
} 
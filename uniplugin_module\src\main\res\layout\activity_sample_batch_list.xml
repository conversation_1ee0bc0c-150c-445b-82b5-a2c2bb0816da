<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 搜索栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">
                
                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="批次编号:" 
                    android:textSize="14sp"
                    android:gravity="right"
                    android:layout_gravity="center_vertical" />

                <EditText
                    android:id="@+id/editTextBatchCode"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:hint="输入批次编号"
                    android:textSize="14sp"
                    android:inputType="text"
                    android:minHeight="40dp"
                    android:padding="8dp"
                    android:background="@drawable/edit_text_border" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">
                
                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="送样单位:" 
                    android:textSize="14sp"
                    android:gravity="right"
                    android:layout_gravity="center_vertical" />

                <EditText
                    android:id="@+id/editTextSendOrg"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:hint="输入送样单位"
                    android:textSize="14sp"
                    android:inputType="text"
                    android:minHeight="40dp"
                    android:padding="8dp"
                    android:background="@drawable/edit_text_border" />
            </LinearLayout>
            
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">
                
                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="送样人:" 
                    android:textSize="14sp"
                    android:gravity="right"
                    android:layout_gravity="center_vertical" />

                <EditText
                    android:id="@+id/editTextSenderName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:hint="输入送样人"
                    android:textSize="14sp"
                    android:inputType="text"
                    android:minHeight="40dp"
                    android:padding="8dp"
                    android:background="@drawable/edit_text_border" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">
                
                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="接样单位:" 
                    android:textSize="14sp"
                    android:gravity="right"
                    android:layout_gravity="center_vertical" />

                <EditText
                    android:id="@+id/editTextReceiveOrg"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:hint="输入接样单位"
                    android:textSize="14sp"
                    android:inputType="text"
                    android:minHeight="40dp"
                    android:padding="8dp"
                    android:background="@drawable/edit_text_border" />
            </LinearLayout>
            
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">
                
                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="接样人:" 
                    android:textSize="14sp"
                    android:gravity="right"
                    android:layout_gravity="center_vertical" />

                <EditText
                    android:id="@+id/editTextReceiverName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:hint="输入接样人"
                    android:textSize="14sp"
                    android:inputType="text"
                    android:minHeight="40dp"
                    android:padding="8dp"
                    android:background="@drawable/edit_text_border" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:clipToPadding="false"
                    android:padding="8dp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="end">
                
                <Button
                    android:id="@+id/buttonReset"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="重置"
                    android:layout_marginEnd="8dp"
                    style="@style/Widget.AppCompat.Button.Colored" />
                
                <Button
                    android:id="@+id/buttonSearch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="搜索"
                    style="@style/Widget.AppCompat.Button.Colored" />
            </LinearLayout>
        </LinearLayout>

        <!-- 列表和下拉刷新 -->
        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipeRefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <!-- 空状态提示 -->
                <LinearLayout
                    android:id="@+id/layoutEmpty"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="100dp"
                        android:layout_height="100dp"
                        android:src="@android:drawable/ic_dialog_info"
                        android:tint="#757575" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="暂无样品批次数据"
                        android:textSize="16sp" />
                </LinearLayout>

            </FrameLayout>

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    </LinearLayout>

    <!-- 悬浮按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAdd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        app:srcCompat="@android:drawable/ic_input_add"
        app:fabSize="normal" />

</androidx.coordinatorlayout.widget.CoordinatorLayout> 
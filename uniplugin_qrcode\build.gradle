apply plugin:'com.android.library'

android {
    compileSdkVersion 31
    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }

    defaultConfig {
        minSdkVersion 24
        targetSdkVersion 31
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
//    implementation 'com.android.support:appcompat-v7: 28.0.0'
    implementation 'androidx.appcompat:appcompat:1.0.0'
//    implementation 'com.google.android.material:material:1.5.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    compileOnly fileTree(dir: 'libs', include: ['*.jar','*.aar'])

    compileOnly fileTree(dir: '../app/libs', include: ['uniapp-v8-release.aar'])
    implementation 'com.alibaba:fastjson:1.1.46.android'

    implementation 'com.google.zxing:core:3.4.0'
    implementation 'com.journeyapps:zxing-android-embedded:4.2.0'
}
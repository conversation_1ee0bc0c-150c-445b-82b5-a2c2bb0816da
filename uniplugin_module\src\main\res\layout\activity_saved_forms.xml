<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <TextView
        android:id="@+id/textViewTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="本地保存的表单列表"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp"
        android:layout_alignParentTop="true" />

    <ListView
        android:id="@+id/listViewForms"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/textViewTitle"
        android:divider="@android:color/darker_gray"
        android:dividerHeight="0.5dp" />

    <TextView
        android:id="@+id/textViewEmpty"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:text="没有找到本地保存的表单"
        android:textSize="16sp"
        android:visibility="gone" />

</RelativeLayout> 
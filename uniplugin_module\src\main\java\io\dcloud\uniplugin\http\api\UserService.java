package io.dcloud.uniplugin.http.api;

import io.dcloud.uniplugin.model.ApiResponse;
import io.dcloud.uniplugin.model.AppVersionVO;
import io.dcloud.uniplugin.model.AuthPermissionInfoRespVO;
import io.dcloud.uniplugin.model.LoginData;
import io.dcloud.uniplugin.model.LoginRequest;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;

/**
 * API服务接口
 */
public interface UserService {


    /**
     * 获取应用版本信息
     */
    @GET("appsc/app/version")
    Call<ApiResponse<AppVersionVO>> getAppVersion();

    /**
     * 获取公钥
     */
    @GET("system/auth/publickey")
    Call<ApiResponse<String>> getPublicKey();
    
    /**
     * 登录
     */
    @POST("system/auth/login")
    Call<ApiResponse<LoginData>> login(@Body LoginRequest request);

    /**
     * 获取用户权限信息
     * 需要token认证
     */
    @GET("system/auth/get-permission-info")
    Call<ApiResponse<AuthPermissionInfoRespVO>> getPermissionInfo();

    /**
     * 登出
     * 需要token认证
     */
    @POST("system/auth/logout")
    Call<ApiResponse<Boolean>> logout();
}
package io.dcloud.uniplugin.model;

import com.google.gson.annotations.SerializedName;

public class LoginResponse {
    private int code;
    private String msg;
    private Data data;

    public static class Data {
        private String token;
        private UserInfo user;
        @SerializedName("tyspRoles")
        private String[] roles;

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public UserInfo getUser() {
            return user;
        }

        public void setUser(UserInfo user) {
            this.user = user;
        }

        public String[] getRoles() {
            return roles;
        }

        public void setRoles(String[] roles) {
            this.roles = roles;
        }
    }

    public static class UserInfo {
        private String mcid;
        private String name;
        private String mc;
        private String gsddm;

        public String getMcid() {
            return mcid;
        }

        public void setMcid(String mcid) {
            this.mcid = mcid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getMc() {
            return mc;
        }

        public void setMc(String mc) {
            this.mc = mc;
        }

        public String getGsddm() {
            return gsddm;
        }

        public void setGsddm(String gsddm) {
            this.gsddm = gsddm;
        }
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }
} 
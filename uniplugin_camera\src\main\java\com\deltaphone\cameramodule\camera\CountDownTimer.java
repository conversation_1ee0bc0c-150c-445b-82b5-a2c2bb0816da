package com.deltaphone.cameramodule.camera;

import android.os.Handler;
import android.util.Log;
import android.widget.TextView;

import java.util.Locale;
import java.util.Timer;
import java.util.TimerTask;

public abstract class CountDownTimer {
    int timeLeftInMillis;
    Timer timer;
    private TextView countdownTextView;
    private final Handler handler = new Handler();

    public CountDownTimer(int timeLeftInMillis,TextView textView) {
        this.timeLeftInMillis = timeLeftInMillis;
        timer = new Timer();
        countdownTextView = textView;
    }
    public void start() {
        if (timer != null) {
            timer.cancel();
        }
        timer = new Timer();
        timer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                timeLeftInMillis = timeLeftInMillis - 1000;
                if (timeLeftInMillis <= 0) {
                    onFinish();
                } else {
                    Log.d("videoDown", "run: "+timeLeftInMillis);
                    updateCountdownText(getTimeLeftString(timeLeftInMillis));
                }
            }
        }, 0, 1000);
    }

    public void onFinish() {    }

    public void stop(){
        if (timer != null) {
            timer.cancel();
        }
        updateCountdownText("00:00");
        handler.removeCallbacksAndMessages(null);
    }
    private String getTimeLeftString(int timeLeftInMillis) {
        long minutes = (timeLeftInMillis / 1000) / 60;
        long seconds = (timeLeftInMillis / 1000) % 60;
        return String.format(Locale.getDefault(), "%02d:%02d", minutes, seconds);
    }

    private void updateCountdownText(String countdownText) {
        handler.post(new Runnable() {
            @Override
            public void run() {
                if (countdownTextView != null) {
                    countdownTextView.setText(countdownText);
                }
            }
        });
    }
}


package com.android.UniPlugin;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.documentfile.provider.DocumentFile;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

import io.dcloud.PandoraEntry;

public class NewPandoraEntry extends PandoraEntry {
    private static final int REQUEST_CODE = 1024;
    private final String TAG = "Jason";
    private File file;

    //private AlertDialog alertDialog;//全局化 ， 此方法在UI线程中使用
    //private Context context;
    @Override
    public void onCreate(Bundle savedInstanceState) {
        Log.d("测试","进来了");
        super.onCreate(savedInstanceState);
        
//        // 初始化全局异常处理器
//        try {
//            GlobalExceptionHandler.init(this);
//            Log.i(TAG, "全局异常处理器已在NewPandoraEntry中初始化");
//        } catch (Exception e) {
//            Log.e(TAG, "初始化全局异常处理器失败", e);
//        }
//
        try {
            requestPermission();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void requestPermission() throws IOException {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // 先判断有没有权限
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED &&
                    ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED) {
                Log.d("测试","有权限");
                createSDCardDir();
            } else {
                Log.d("测试","没权限不提示");
                ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE}, REQUEST_CODE);
            }
        }
            // 请求写入外部存储权限
//            if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
//                ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, 1);
//            }

// 打开文件管理器以选择根目录
//            Intent intent = new Intent(Intent.ACTION_OPEN_DOCUMENT_TREE);
//            startActivityForResult(intent, 2);


    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_CODE) {
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED &&
                    ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED) {
                Log.d("测试","我要创建了");
                    createSDCardDir();
                Log.d("测试","我创建了");
            } else {
                Toast.makeText(this, "存储权限获取失败", Toast.LENGTH_SHORT).show();
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
       super.onActivityResult(requestCode, resultCode, data);
//        if (requestCode == REQUEST_CODE && Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
//            if (Environment.isExternalStorageManager()) {
//                createSDCardDir();
//            } else {
//                Toast.makeText(this, "存储权限获取失败", Toast.LENGTH_SHORT).show();
//            }
//        }
        if (requestCode == 2 && resultCode == Activity.RESULT_OK) {
            // 获取用户授权的根目录
            Uri treeUri = data.getData();

            // 创建名为"MyFolder"的新文件夹
            DocumentFile rootDirectory = DocumentFile.fromTreeUri(this, treeUri);
            DocumentFile myFolder = rootDirectory.createDirectory("MyFolder");

            // 获取新文件夹的URI并将文件写入其中
            if (myFolder != null) {
                Uri myFolderUri = myFolder.getUri();
                File file = new File(myFolderUri.getPath() + "/example.txt");
                try {
                    FileWriter writer = new FileWriter(file);
                    writer.write("Hello, world!");
                    writer.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public void createSDCardDir() {

        String folderName = "TRSPGISData"; // 要创建的文件夹名称

        Log.d("路径测试",getSDPath(this) + File.separator + folderName);
        File folder = new File(getSDPath(this) + File.separator + folderName); // 创建文件夹对象
        if (!folder.exists()) { // 如果文件夹不存在，则创建它
            folder.mkdirs();
        }
        //File folder = new File(Environment.getExternalStorageDirectory().getAbsolutePath() + "/TRSPGISData");
        if (!folder.exists()) {
            if (folder.mkdirs()) {
                createOtherDir();
                Toast.makeText(this, "创建文件成功", Toast.LENGTH_SHORT).show();
            }
        } else {
            //Toast.makeText(this, "文件已存在！", Toast.LENGTH_SHORT).show();
            createOtherDir();
            Toast.makeText(this, "文件已存在！", Toast.LENGTH_SHORT).show();
        }
    }

    public void createOtherDir(){
        File root = android.os.Environment.getExternalStorageDirectory();
        File trsPGISDataDir = new File(root + "/TRSPGISData");

        if (trsPGISDataDir.exists()) {
            // 如果TRSPGISData文件夹存在，创建TRSPSqliteData和TRSPCameraData文件夹
            File trsSqliteDataDir = new File(trsPGISDataDir + "/TRSPSqliteData");
            File trsCameraDataDir = new File(trsPGISDataDir + "/TRSPCameraData");

            if (!trsSqliteDataDir.exists()) {
                boolean mkdirSqlite = trsSqliteDataDir.mkdirs();
                if (!mkdirSqlite){
                    Log.e("createDir", "创建TRSPSqliteData文件夹失败！");
                }
            }

            if (!trsCameraDataDir.exists()) {
                boolean mkdirCamera = trsCameraDataDir.mkdirs();
                if (!mkdirCamera){
                    Log.e("createDir", "创建TRSPCameraData文件夹失败！");
                }
            }
        } else {
            Log.e("createDir", "TRSPGISData文件夹不存在！");
        }
    }

    public static String getSDPath(Context context) {
        File sdDir = null;
        boolean sdCardExist = Environment.getExternalStorageState().equals(
                Environment.MEDIA_MOUNTED);// 判断sd卡是否存在
        if (sdCardExist) {
            if (Build.VERSION.SDK_INT >= 29) {
                //Android10之后
                File[] externalDirs = context.getExternalFilesDirs(null);
                if (externalDirs.length > 0) {
                    String rootPath = externalDirs[0].getAbsolutePath();
                    rootPath = rootPath.substring(0, rootPath.indexOf("/Android/"));
                    //Toast.makeText(context, rootPath, Toast.LENGTH_SHORT).show();
                    sdDir  = new File(rootPath);
                }
            } else {
                sdDir = Environment.getExternalStorageDirectory();// 获取SD卡根目录
            }
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
//                File[] externalDirs = context.getExternalFilesDirs(null);
//                if (externalDirs.length > 0) {
//                    return externalDirs[0].getAbsolutePath();
//                }
//            } else {
//                return  Environment.getExternalStorageDirectory();
//            }
            return sdDir.toString();
        } else {
            sdDir = Environment.getRootDirectory();// 获取跟目录
        }
        return sdDir.toString();
    }
    /**
     * 判断权限申请结果
     *
     * @param grantResults
     * @return
     */

    private boolean hasAllPermissionsGranted(@NonNull int[] grantResults) {

        for (int grantResult : grantResults) {

            if (grantResult == PackageManager.PERMISSION_DENIED) {//PERMISSION_GRANTED 授予

                return false;

            }

        }

        return true;

    }

}

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical" >


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <!--横向布局-->
        <TextView
            android:layout_width="175dp"
            android:layout_height="20dp"
            android:id="@+id/chooseLayer"
            android:layout_marginTop="10dp"
            android:text="请选择属性查询的图层"
            android:background="@color/white"
            />
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="0dp"
            android:orientation="horizontal" >
            <Button
                android:id="@+id/callexit"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:background="@drawable/close" />
        </LinearLayout>

    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal" >
        <Spinner
            android:layout_width="150dp"
            android:layout_height="30dp"
            android:spinnerMode="dialog"
            android:id="@+id/spinner3"
            android:popupBackground="@drawable/shape_for_custom_spinner"
            android:background="@drawable/selector_for_custom_spinner"
            />


    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal" >

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="vertical" >

            <TextView
                android:id="@+id/identifyContent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                />
        </LinearLayout>


    </LinearLayout>

</LinearLayout>
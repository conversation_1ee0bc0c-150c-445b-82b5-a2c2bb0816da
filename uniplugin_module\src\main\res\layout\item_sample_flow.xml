<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 批次编号 -->
        <TextView
            android:id="@+id/textViewBatchCode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="批次编号：P44098120240008"
            android:textColor="#333333"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <!-- 批次类型 -->
        <TextView
            android:id="@+id/textViewBsm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="批次类型：县级"
            android:textColor="#666666"
            android:textSize="14sp"
            android:layout_marginBottom="8dp" />

        <!-- 项目名称 -->
        <TextView
            android:id="@+id/textViewBatchName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="项目名称：2023年度茂名市高州市石鼓镇西基山村补充耕地项目"
            android:textColor="#333333"
            android:textSize="14sp"
            android:layout_marginBottom="8dp" />

        <!-- 送样信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="4dp">
            
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="送样人："
                android:textColor="#666666"
                android:textSize="14sp" />
                
            <TextView
                android:id="@+id/textViewSenderName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="张三"
                android:textColor="#666666"
                android:textSize="14sp" />
        </LinearLayout>

        <!-- 送样单位 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="4dp">
            
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="送样单位："
                android:textColor="#666666"
                android:textSize="14sp" />
                
            <TextView
                android:id="@+id/textViewSendOrg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="某某单位"
                android:textColor="#666666"
                android:textSize="14sp" />
        </LinearLayout>

        <!-- 配送方式 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">
            
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="配送方式："
                android:textColor="#666666"
                android:textSize="14sp" />
                
            <TextView
                android:id="@+id/textViewDeliveryType"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="物流"
                android:textColor="#666666"
                android:textSize="14sp" />
        </LinearLayout>

        <!-- 底部信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!-- 状态 -->
            <TextView
                android:id="@+id/textViewBatchState"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="待流转"
                android:textColor="#1971AC"
                android:textSize="14sp" />

            <!-- 创建时间 -->
            <TextView
                android:id="@+id/textViewCreateTime"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="2023-06-01 12:30:45"
                android:textColor="#999999"
                android:textSize="12sp"
                android:gravity="end" />
        </LinearLayout>
        
        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp"
            android:gravity="end">
            
            <!-- 确定寄送按钮 -->
            <Button
                android:id="@+id/buttonConfirmShipment"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="确定寄送"
                android:textSize="12sp"
                android:background="@drawable/button_primary"
                android:textColor="#FFFFFF"
                android:layout_marginEnd="8dp" />
                
            <!-- 编辑按钮 -->
            <Button
                android:id="@+id/buttonEdit"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="编辑"
                android:textSize="12sp"
                android:background="@drawable/button_outline"
                android:textColor="#1971AC"
                android:layout_marginEnd="8dp" />
                
            <!-- 删除按钮 -->
            <Button
                android:id="@+id/buttonDelete"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="删除"
                android:textSize="12sp"
                android:background="@drawable/button_danger"
                android:textColor="#FFFFFF" />
        </LinearLayout>

        <!-- 兼容原有成员变量，保持隐藏 -->
        <TextView
            android:id="@+id/textViewSampleCount"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />
    </LinearLayout>
</androidx.cardview.widget.CardView> 
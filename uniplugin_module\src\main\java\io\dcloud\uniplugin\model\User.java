package io.dcloud.uniplugin.model;

/**
 * 用户数据模型类
 */
public class User {
    private Long id;
    private String username;
    private String password;
    private String nickname;
    private String role;
    private String lastLoginTime;
    private String salt;
    private String permissions;
    private String gsddm;
    private String gsdmc;


    public User() {
    }

    public User(String username) {
        this.username = username;
    }

    public User(Long id, String username,String nickname, String role) {
        this.id = id;
        this.username = username;
        this.nickname = nickname;
        this.role = role;
    }

    public String getUsername() {
        return username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(String lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    public void setUsername(String username) {
        this.username = username;
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }



    public String getNickname() {
        return nickname;
    }

    public void setNickname(String name) {
        this.nickname = name;
    }



    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }
    
    public String getSalt() {
        return salt;
    }
    
    public void setSalt(String salt) {
        this.salt = salt;
    }
    
    public String getPermissions() {
        return permissions;
    }
    
    public void setPermissions(String permissions) {
        this.permissions = permissions;
    }
    
    public String getGsddm() {
        return gsddm;
    }
    
    public void setGsddm(String gsddm) {
        this.gsddm = gsddm;
    }
    
    public String getGsdmc() {
        return gsdmc;
    }
    
    public void setGsdmc(String gsdmc) {
        this.gsdmc = gsdmc;
    }
} 
package com.deltaphone.cameramodule.camera;

import android.content.Context;
import android.os.Build;
import android.os.Environment;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.channels.FileChannel;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class FileUtils {
    public static String getTimeStampFileName(int model) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());
        String timeStamp = dateFormat.format(new Date());
        String fileName = null;
        if(model == 0){
            fileName = "IMG_" + timeStamp + ".jpg";
        }else if(model == 1){
            fileName = "VIDEO_" + timeStamp +".mp4";
        }else if(model == 2){
            fileName = "QM_" + timeStamp + ".jpg";
        }
        return fileName;
    }


    /**
     * 获取路径
     * @param context
     * @return
     */
    public static String getSDPath(Context context) {
        File sdDir = null;
        boolean sdCardExist = Environment.getExternalStorageState().equals(
                Environment.MEDIA_MOUNTED);// 判断sd卡是否存在
        if (sdCardExist) {
            if (Build.VERSION.SDK_INT >= 29) {
                //Android10之后
                File[] externalDirs = context.getExternalFilesDirs(null);
                if (externalDirs.length > 0) {
                    String rootPath = externalDirs[0].getAbsolutePath();
                    rootPath = rootPath.substring(0, rootPath.indexOf("/Android/"));
//                    Toast.makeText(context, rootPath, Toast.LENGTH_SHORT).show();
                    sdDir  = new File(rootPath);
                }
            } else {
                sdDir = Environment.getExternalStorageDirectory();// 获取SD卡根目录
            }
            return sdDir.toString();
        } else {
            sdDir = Environment.getRootDirectory();// 获取跟目录
        }
        return sdDir.toString();
    }

    /**
     * 将指定路径的图片文件复制到指定文件夹中
     * @param srcPath 源图片路径
     * @param dstPath 目标文件夹路径
     * @throws IOException
     */
    public static void copyImageFile(String srcPath, String dstPath) {
        File srcFile = new File(srcPath);
        File dstFolder = new File(dstPath);

        if (!dstFolder.exists()) {
            dstFolder.mkdirs();
        }

        File dstFile = new File(dstFolder, srcFile.getName());
        FileChannel srcChannel = null;
        FileChannel dstChannel = null;

        try {
            srcChannel = new FileInputStream(srcFile).getChannel();
            dstChannel = new FileOutputStream(dstFile).getChannel();
            dstChannel.transferFrom(srcChannel, 0, srcChannel.size());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (srcChannel != null) {
                    srcChannel.close();
                }
                if (dstChannel != null) {
                    dstChannel.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 计算指定文件夹的大小
     * @param folder
     * @return
     */
    public static long getFolderSize(File folder) {
        long size = 0;
        if (folder.isDirectory()) {
            File[] files = folder.listFiles();
            for (int i = 0; i < files.length; i++) {
                if (files[i].isDirectory()) {
                    size += getFolderSize(files[i]);
                } else {
                    size += files[i].length();
                }
            }
        } else {
            size += folder.length();
        }
        return size;
    }

    /**
     * 删除指定文件夹内的文件
     * @param folderPath
     */
    public static void deleteFiles(String folderPath) {
        File folder = new File(folderPath);
        if (folder.isDirectory()) {
            File[] files = folder.listFiles();
            for (int i = 0; i < files.length; i++) {
                if (files[i].isDirectory()) {
                    deleteFiles(files[i].getPath());
                } else {
                    files[i].delete();
                }
            }
            folder.delete();
        }
    }

    /**
     * 只删除地图数据
     * @param folderPath 地图文件夹的路径
     */
    public static void deleteMapData(String folderPath){
        File folder = new File(folderPath);
        File[] files = folder.listFiles();

        // 遍历地图文件夹中的所有文件和文件夹
        for (File file : files) {
            // 判断是否是文件
            if (file.isFile()) {
                // 获取文件名
                String fileName = file.getName();

                // 判断是否是地图数据
                if (fileName.equals("dem.tpk") || fileName.equals("image.tpk")|| fileName.equals("layers.mmpk")) {
                    // 删除文件
                    file.delete();
                }
            }
        }
    }
}

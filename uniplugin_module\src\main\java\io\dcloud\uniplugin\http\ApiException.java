package io.dcloud.uniplugin.http;

public class ApiException extends RuntimeException {
    private int code;
    private String msg;

    public ApiException(int code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    public ApiException(BaseResponse<?> response) {
        super(response.getMsg());
        this.code = response.getCode();
        this.msg = response.getMsg();
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
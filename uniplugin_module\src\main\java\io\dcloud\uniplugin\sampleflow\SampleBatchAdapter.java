package io.dcloud.uniplugin.sampleflow;

import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import io.dcloud.uniplugin.model.YplzBatch;
import uni.dcloud.io.uniplugin_module.R;

/**
 * 样品批次列表适配器
 */
public class SampleBatchAdapter extends RecyclerView.Adapter<SampleBatchAdapter.ViewHolder> {
    
    private Context context;
    private List<YplzBatch> batchList;
    private OnBatchClickListener listener;
    
    public interface OnBatchClickListener {
        void onBatchClick(YplzBatch batch);
    }
    
    public SampleBatchAdapter(Context context, List<YplzBatch> batchList) {
        this.context = context;
        this.batchList = batchList;
    }
    
    public void setOnBatchClickListener(OnBatchClickListener listener) {
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_sample_batch, parent, false);
        return new ViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        YplzBatch batch = batchList.get(position);
        
        // 设置批次编号和名称
        holder.textViewBatchCode.setText("批次编号：" + (batch.getBatchCode() != null ? batch.getBatchCode() : ""));
        holder.textViewBatchName.setText(batch.getBatchName() != null ? batch.getBatchName() : "未命名批次");
        
        // 设置送样单位和送样人
        holder.textViewSendOrg.setText("送样单位：" + (batch.getSendOrganization() != null ? batch.getSendOrganization() : ""));
        holder.textViewSender.setText("送样人：" + (batch.getSenderName() != null ? batch.getSenderName() : ""));
        
        // 设置接样单位和接样人
        holder.textViewReceiveOrg.setText("接样单位：" + (batch.getReceiveOrganization() != null ? batch.getReceiveOrganization() : ""));
        holder.textViewReceiver.setText("接样人：" + (batch.getReceiverName() != null ? batch.getReceiverName() : ""));
        
        // 设置样品数量
        holder.textViewSampleCount.setText("样品数量：" + (batch.getSampleNumber() != null ? batch.getSampleNumber() : "0"));
        
        // 设置创建时间
        if (batch.getCreateTime() != null) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
                String formattedTime = sdf.format(new Date(batch.getCreateTime()));
                holder.textViewCreateTime.setText(formattedTime);
            } catch (Exception e) {
                holder.textViewCreateTime.setText("");
            }
        } else {
            holder.textViewCreateTime.setText("");
        }
        
        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onBatchClick(batch);
            } else {
                // 默认点击行为：跳转到新增样品流转批次页面
                Intent intent = new Intent(context, CreateSampleFlowActivity.class);
                intent.putExtra("batchId", batch.getId());
                context.startActivity(intent);
            }
        });
    }
    
    @Override
    public int getItemCount() {
        return batchList != null ? batchList.size() : 0;
    }
    
    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView textViewBatchCode;
        TextView textViewBatchName;
        TextView textViewSendOrg;
        TextView textViewSender;
        TextView textViewReceiveOrg;
        TextView textViewReceiver;
        TextView textViewSampleCount;
        TextView textViewCreateTime;
        
        ViewHolder(View itemView) {
            super(itemView);
            
            textViewBatchCode = itemView.findViewById(R.id.textViewBatchCode);
            textViewBatchName = itemView.findViewById(R.id.textViewBatchName);
            textViewSendOrg = itemView.findViewById(R.id.textViewSendOrg);
            textViewSender = itemView.findViewById(R.id.textViewSender);
            textViewReceiveOrg = itemView.findViewById(R.id.textViewReceiveOrg);
            textViewReceiver = itemView.findViewById(R.id.textViewReceiver);
            textViewSampleCount = itemView.findViewById(R.id.textViewSampleCount);
            textViewCreateTime = itemView.findViewById(R.id.textViewCreateTime);
        }
    }
} 
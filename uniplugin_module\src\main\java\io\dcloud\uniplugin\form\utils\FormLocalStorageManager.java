package io.dcloud.uniplugin.form.utils;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import io.dcloud.uniplugin.db.DatabaseConstants;
import io.dcloud.uniplugin.db.DatabaseHelper;
import io.dcloud.uniplugin.form.field.FieldFile;

/**
 * 表单本地存储管理器，负责将表单数据保存到本地和从本地加载表单数据
 */
public class FormLocalStorageManager {
    private static final String TAG = "FormLocalStorageMgr";

    // 表单数据表
    private static final String TABLE_FORM_DATA = "form_data";
    private static final String COLUMN_ID = "id";
    private static final String COLUMN_FORM_ID = "form_id";
    private static final String COLUMN_FORM_NAME = "form_name";
    private static final String COLUMN_SAVE_TIME = "save_time";
    private static final String COLUMN_FORM_DATA = "form_data";
    private static final String COLUMN_DATA_HASH = "data_hash";
    private static final String COLUMN_BSM = "bsm";

    // 创建表单数据表的SQL语句
    private static final String CREATE_TABLE_FORM_DATA = "CREATE TABLE IF NOT EXISTS " + TABLE_FORM_DATA + "("
            + COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT,"
            + COLUMN_FORM_ID + " TEXT NOT NULL,"
            + COLUMN_FORM_NAME + " TEXT,"
            + COLUMN_SAVE_TIME + " INTEGER,"
            + COLUMN_FORM_DATA + " TEXT NOT NULL,"
            + COLUMN_DATA_HASH + " TEXT,"
            + COLUMN_BSM + " TEXT"
            + ");";

    private Context context;
    private String formId;
    private DatabaseHelper dbHelper;
    private String bsm;

    /**
     * 构造函数
     * @param context 上下文
     * @param formId 表单ID
     */
    public FormLocalStorageManager(Context context, String formId) {
        this(context, formId, null);
    }

    /**
     * 构造函数
     * @param context 上下文
     * @param formId 表单ID
     * @param bsm 业务识别码
     */
    public FormLocalStorageManager(Context context, String formId, String bsm) {
        this.context = context;
        this.formId = formId;
        this.bsm = bsm;

        // 初始化数据库
        this.dbHelper = DatabaseHelper.getInstance(context);

        // 确保表单数据表存在
        ensureTableExists();
    }

    /**
     * 确保表单数据表存在
     */
    private void ensureTableExists() {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        try {
            // 检查表是否存在
            Cursor tableCheck = db.rawQuery(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                    new String[]{TABLE_FORM_DATA}
            );

            boolean tableExists = false;
            if (tableCheck != null) {
                tableExists = tableCheck.getCount() > 0;
                tableCheck.close();
            }

            if (tableExists) {
                // 检查BSM列是否存在
                Cursor columnCheck = db.rawQuery(
                        "PRAGMA table_info(" + TABLE_FORM_DATA + ")",
                        null
                );

                boolean bsmColumnExists = false;
                if (columnCheck != null) {
                    int nameIndex = columnCheck.getColumnIndex("name");
                    while (columnCheck.moveToNext()) {
                        if (nameIndex != -1 && COLUMN_BSM.equals(columnCheck.getString(nameIndex))) {
                            bsmColumnExists = true;
                            break;
                        }
                    }
                    columnCheck.close();
                }

                // 如果BSM列不存在，添加它
                if (!bsmColumnExists) {
                    try {
                        db.execSQL("ALTER TABLE " + TABLE_FORM_DATA + " ADD COLUMN " + COLUMN_BSM + " TEXT");
                        Log.d(TAG, "向表单数据表添加BSM列");
                    } catch (Exception e) {
                        Log.e(TAG, "添加BSM列失败: " + e.getMessage(), e);
                    }
                }
            } else {
                // 创建表
                db.execSQL(CREATE_TABLE_FORM_DATA);
                Log.d(TAG, "表单数据表创建成功");
            }
        } catch (Exception e) {
            Log.e(TAG, "创建表单数据表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存表单数据到本地数据库
     * @param formData 表单数据JSON对象
     * @param fieldFiles 字段文件映射
     * @return 是否保存成功
     */
    public boolean saveFormData(JSONObject formData, Map<String, List<FieldFile>> fieldFiles) {
        try {
            if (formData == null) {
                Log.e(TAG, "保存表单数据失败: 表单数据为空");
                return false;
            }

            // 添加保存时间
            long saveTime = System.currentTimeMillis();
            formData.put("saveTime", saveTime);

            // 如果没有表单名称，添加一个默认名称
            if (!formData.has("formName")) {
                formData.put("formName", "表单 " + formId);
            }

            // 记录表单字段
            Iterator<String> keys = formData.keys();
            StringBuilder fieldLog = new StringBuilder();
            int fieldCount = 0;
            while (keys.hasNext()) {
                String key = keys.next();
                if (!key.startsWith("__") && !key.equals("saveTime") && !key.equals("formName")) {
                    fieldCount++;
                    fieldLog.append(key).append(", ");
                }
            }
            Log.d(TAG, "正在保存表单数据，包含" + fieldCount + "个字段: " + fieldLog.toString());

            // 清除表单数据中可能存在的旧文件路径，防止删除文件后仍然保留
            if (formData.has("__files")) {
                JSONObject oldFiles = formData.getJSONObject("__files");
                Iterator<String> fileKeys = oldFiles.keys();
                while (fileKeys.hasNext()) {
                    String fieldId = fileKeys.next();
                    // 清除formData中该字段原来的值，后面会用新值替代
                    if (formData.has(fieldId)) {
                        formData.remove(fieldId);
                        Log.d(TAG, "清除表单数据中字段 " + fieldId + " 的旧值");
                    }
                }
            }

            // 对所有将要保存的字段文件，也确保清除表单数据中的值
            for (String fieldId : fieldFiles.keySet()) {
                if (formData.has(fieldId)) {
                    formData.remove(fieldId);
                    Log.d(TAG, "清除表单数据中字段 " + fieldId + " 的现有值");
                }
            }

            // 保存文件到__files对象中
            JSONObject filesData = new JSONObject();
            if (fieldFiles != null && !fieldFiles.isEmpty()) {
                saveFormFilesDirectly(formData, fieldFiles, filesData);
                // 将文件信息添加到表单数据中
                formData.put("__files", filesData);
            }

            // 计算并添加数据哈希值，用于验证
            String dataHash = calculateDataHash(formData);
            formData.put("__hash", dataHash);

            // 保存表单数据到数据库
            SQLiteDatabase db = dbHelper.getWritableDatabase();
            ContentValues values = new ContentValues();

            values.put(COLUMN_FORM_ID, formId);
            values.put(COLUMN_FORM_NAME, formData.optString("formName", "表单 " + formId));
            values.put(COLUMN_SAVE_TIME, saveTime);
            values.put(COLUMN_FORM_DATA, formData.toString());
            values.put(COLUMN_DATA_HASH, dataHash);

            // 保存BSM值
            if (bsm != null && !bsm.isEmpty()) {
                values.put(COLUMN_BSM, bsm);
                Log.d(TAG, "保存表单数据时包含BSM值: " + bsm);
            } else if (formData.has("pjdybh") && !formData.isNull("pjdybh")) {
                String formBsm = formData.optString("pjdybh");
                if (!formBsm.isEmpty()) {
                    values.put(COLUMN_BSM, formBsm);
                    Log.d(TAG, "从表单数据中获取并保存BSM值: " + formBsm);
                }
            }

            // 检查是否已存在相同form_id的记录
            Cursor cursor = db.query(
                    TABLE_FORM_DATA,
                    new String[]{COLUMN_ID},
                    COLUMN_FORM_ID + "=?",
                    new String[]{formId},
                    null, null, null
            );

            long result;
            if (cursor != null && cursor.moveToFirst()) {
                // 存在记录，进行更新
                int id = cursor.getInt(cursor.getColumnIndex(COLUMN_ID));
                result = db.update(
                        TABLE_FORM_DATA,
                        values,
                        COLUMN_ID + "=?",
                        new String[]{String.valueOf(id)}
                );
                Log.d(TAG, "更新已存在的表单数据记录: " + formId);
            } else {
                // 不存在记录，插入新记录
                result = db.insert(TABLE_FORM_DATA, null, values);
                Log.d(TAG, "插入新的表单数据记录: " + formId);
            }

            if (cursor != null) {
                cursor.close();
            }

            boolean success = result != -1;
            if (success) {
                String saveTimeStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                        .format(new Date(saveTime));
                Log.d(TAG, "表单数据已保存到数据库: " + formId + "，保存时间: " + saveTimeStr +
                        "，数据哈希值: " + dataHash);
                
                // 如果保存成功且有BSM值，更新sampling_points表中的has_local_data字段
                String currentBsm = bsm;
                if (currentBsm == null || currentBsm.isEmpty()) {
                    currentBsm = formData.optString("pjdybh", "");
                }
                
                if (!currentBsm.isEmpty()) {
                    updateHasLocalData(context, currentBsm);
                }
            } else {
                Log.e(TAG, "保存表单数据失败: 数据库操作结果为 " + result);
            }

            return success;
        } catch (Exception e) {
            Log.e(TAG, "保存表单数据失败: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 直接保存表单文件
     * @param formData 表单数据JSON对象
     * @param fieldFiles 字段文件映射
     * @param filesData 文件数据JSON对象
     */
    private void saveFormFilesDirectly(JSONObject formData, Map<String, List<FieldFile>> fieldFiles, JSONObject filesData) throws JSONException {
        if (fieldFiles == null) {
            Log.d(TAG, "字段文件映射为空，创建空的__files对象");
            return;
        }

        Log.d(TAG, "开始保存表单字段文件，字段数: " + fieldFiles.size());

        // 清除表单数据中可能存在的旧文件路径，防止删除文件后仍然保留
        if (formData.has("__files")) {
            JSONObject oldFiles = formData.getJSONObject("__files");
            Iterator<String> fileKeys = oldFiles.keys();
            while (fileKeys.hasNext()) {
                String fieldId = fileKeys.next();
                // 清除formData中该字段原来的值，后面会用新值替代
                if (formData.has(fieldId)) {
                    formData.remove(fieldId);
                    Log.d(TAG, "清除表单数据中字段 " + fieldId + " 的旧值");
                }
            }
        }

        // 对所有将要保存的字段文件，也确保清除表单数据中的值
        for (String fieldId : fieldFiles.keySet()) {
            if (formData.has(fieldId)) {
                formData.remove(fieldId);
                Log.d(TAG, "清除表单数据中字段 " + fieldId + " 的现有值");
            }
        }

        // 记录所有处理过的字段，用于日志
        StringBuilder processedFields = new StringBuilder("处理的媒体字段: ");
        
        // 查找可能在表单数据根级别的媒体字段
        Set<String> mediaFieldIds = new HashSet<>();
        for (String fieldId : fieldFiles.keySet()) {
            List<FieldFile> files = fieldFiles.get(fieldId);
            // 只处理有文件的字段
            if (files != null && !files.isEmpty()) {
                mediaFieldIds.add(fieldId);
                processedFields.append(fieldId).append(", ");
                
                // 即使没有文件，也要确保__files中有该字段的空数组
                if (!filesData.has(fieldId)) {
                    filesData.put(fieldId, new JSONArray());
                    Log.d(TAG, "为字段[" + fieldId + "]创建空的文件数组");
                }
            }
        }
        
        Log.d(TAG, processedFields.toString());

        int totalFiles = 0;
        for (Map.Entry<String, List<FieldFile>> entry : fieldFiles.entrySet()) {
            String fieldId = entry.getKey();
            List<FieldFile> files = entry.getValue();

            // 创建文件数组，即使文件列表为空也创建
            JSONArray fileArray = new JSONArray();
            filesData.put(fieldId, fileArray);
            
            // 如果文件列表为空，确保formData中清除该字段的值
            if (files == null || files.isEmpty()) {
                Log.w(TAG, "字段[" + fieldId + "]的文件列表为空，确保清除相关值");
                if (formData.has(fieldId)) {
                    formData.remove(fieldId);
                }
                continue;
            }

            // 使用HashSet去重
            Set<String> uniquePaths = new HashSet<>();
            
            // 打印字段文件信息，帮助调试
            Log.d(TAG, "字段[" + fieldId + "]有" + files.size() + "个文件");
            
            // 判断是否是照片字段
            boolean isPhotoField = fieldId.toLowerCase().contains("photo") || 
                                  fieldId.toLowerCase().contains("picture") || 
                                  fieldId.toLowerCase().contains("image") || 
                                  fieldId.toLowerCase().endsWith("_photos");
            
            // 统一处理所有类型的文件
            for (FieldFile file : files) {
                if (file != null && file.getPath() != null) {
                    String filePath = file.getPath();
                    File fileObj = new File(filePath);
                    
                    if (fileObj.exists()) {
                        if (!uniquePaths.contains(filePath)) {
                            // 创建包含文件信息的JSON对象，而不仅仅是文件路径
                            JSONObject fileInfo = new JSONObject();
                            fileInfo.put("path", filePath);
                            
                            // 只为照片类型的文件保存位置和方位角信息
                            boolean isPhotoFile = isPhotoField || 
                                                filePath.toLowerCase().endsWith(".jpg") || 
                                                filePath.toLowerCase().endsWith(".jpeg") || 
                                                filePath.toLowerCase().endsWith(".png");
                            
                            if (isPhotoFile) {
                                // 检查是否有位置和方位角信息，如果有则一并保存
                                Double latitude = file.getLatitude();
                                Double longitude = file.getLongitude();
                                Float direction = file.getDirection();
                                
                                // 记录有效的位置信息标志
                                boolean hasValidLocation = false;
                                
                                // 无论值是否为0，都保存位置信息，以确保数据完整性
                                if (latitude != null) {
                                    fileInfo.put("latitude", latitude);
                                    // 只有当经纬度不为0.0时，才认为是有效的位置信息
                                    if (latitude != 0.0) {
                                        hasValidLocation = true;
                                    }
                                }
                                if (longitude != null) {
                                    fileInfo.put("longitude", longitude);
                                    // 只有当经纬度不为0.0时，才认为是有效的位置信息
                                    if (longitude != 0.0) {
                                        hasValidLocation = true;
                                    }
                                }
                                if (direction != null) {
                                    fileInfo.put("direction", direction);
                                }
                                
                                // 详细记录位置信息日志
                                Log.d(TAG, "处理照片 " + filePath + " 的位置信息:");
                                Log.d(TAG, "- 纬度: " + (latitude != null ? latitude : "未设置") + 
                                           (latitude != null && latitude != 0.0 ? " [有效]" : " [无效或为0]"));
                                Log.d(TAG, "- 经度: " + (longitude != null ? longitude : "未设置") + 
                                           (longitude != null && longitude != 0.0 ? " [有效]" : " [无效或为0]"));
                                Log.d(TAG, "- 方位角: " + (direction != null ? direction : "未设置"));
                                
                                // 输出详细日志
                                if (hasValidLocation) {
                                    Log.d(TAG, "照片 " + filePath + " 包含有效的位置信息");
                                } else {
                                    Log.d(TAG, "照片 " + filePath + " 的位置信息无效或为0");
                                }
                            } else {
                                Log.d(TAG, "文件不是照片类型，不保存位置信息: " + filePath);
                            }
                            
                            // 保存文件信息到数组
                            fileArray.put(fileInfo);
                            uniquePaths.add(filePath);
                            totalFiles++;
                            
                            Log.d(TAG, "添加文件: " + filePath + "，大小：" + fileObj.length() + " 字节");
                        } else {
                            Log.d(TAG, "文件已存在于列表中，跳过: " + filePath);
                        }
                    } else {
                        Log.w(TAG, "文件不存在，跳过: " + filePath);
                    }
                }
            }
            
            // 如果字段有有效文件，更新formData中的字段值
            if (fileArray.length() > 0) {
                // 第一个文件作为字段的主要值
                try {
                    JSONObject firstFile = fileArray.getJSONObject(0);
                    String firstFilePath = firstFile.getString("path");
                    formData.put(fieldId, firstFilePath);
                    Log.d(TAG, "设置字段[" + fieldId + "]的值为第一个文件路径: " + firstFilePath);
                } catch (Exception e) {
                    Log.e(TAG, "设置字段值出错: " + e.getMessage());
                }
            }
        }
        
        Log.d(TAG, "共保存" + totalFiles + "个文件");
    }

    /**
     * 从本地数据库加载表单数据
     * @return 表单数据JSON对象，如果不存在则返回null
     */
    public JSONObject loadFormData() {
        try {
            SQLiteDatabase db = dbHelper.getReadableDatabase();

            Cursor cursor = db.query(
                    TABLE_FORM_DATA,
                    new String[]{COLUMN_FORM_DATA},
                    COLUMN_FORM_ID + "=?",
                    new String[]{formId},
                    null, null, null
            );

            JSONObject formData = null;
            if (cursor != null && cursor.moveToFirst()) {
                String formDataStr = cursor.getString(cursor.getColumnIndex(COLUMN_FORM_DATA));
                formData = new JSONObject(formDataStr);

                // 验证数据完整性
//                boolean isValid = validateDataHash(formData);

//                // 记录表单字段
//                Iterator<String> keys = formData.keys();
//                StringBuilder fieldLog = new StringBuilder();
//                int fieldCount = 0;
//                while (keys.hasNext()) {
//                    String key = keys.next();
//                    if (!key.startsWith("__") && !key.equals("saveTime") && !key.equals("formName")) {
//                        fieldCount++;
//                        fieldLog.append(key).append(", ");
//                    }
//                }
//
//                long saveTime = formData.optLong("saveTime", 0);
//                String saveTimeStr = "未知";
//                if (saveTime > 0) {
//                    saveTimeStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
//                            .format(new Date(saveTime));
//                }

//                Log.d(TAG, "从数据库加载表单数据: " + formId +
//                        "，保存时间: " + saveTimeStr +
//                        "，包含" + fieldCount + "个字段: " + fieldLog.toString() +
//                        "，数据完整性: " + (isValid ? "正常" : "可能损坏"));
//
//                if (!isValid) {
//                    Log.w(TAG, "表单数据哈希值验证失败，数据可能已损坏");
//                }
            } else {
                Log.w(TAG, "找不到本地保存的表单数据: " + formId);
            }

            if (cursor != null) {
                cursor.close();
            }

            return formData;
        } catch (Exception e) {
            Log.e(TAG, "加载表单数据失败: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从表单数据加载文件信息
     * @param formData 表单数据JSON对象
     * @return 字段文件映射
     */
    public Map<String, List<FieldFile>> loadFormFiles(JSONObject formData) {
        Map<String, List<FieldFile>> fieldFiles = new HashMap<>();
        
        if (formData == null) {
            Log.e(TAG, "表单数据为空，无法加载文件");
            return fieldFiles;
        }
        
        try {
            // 确保__files对象存在
            if (!formData.has("__files")) {
                Log.w(TAG, "表单数据中不包含__files对象，无法加载文件");
                return fieldFiles;
            }
            
            // 从__files对象中获取文件信息
            JSONObject filesData = formData.getJSONObject("__files");
            
            // 使用loadFormFilesDirectly方法处理文件数据
            return loadFormFilesDirectly(formData, filesData);
            
        } catch (Exception e) {
            Log.e(TAG, "加载表单文件失败: " + e.getMessage(), e);
        }
        
        return fieldFiles;
    }
    
    // 视频字段关键词
    private static final String[] VIDEO_FIELD_KEYWORDS = {"video", "视频"};
    
    // 图片字段关键词
    private static final String[] PHOTO_FIELD_KEYWORDS = {"photo", "image", "pic", "picture", "照片", "图片"};
    
    // 签名字段关键词
    private static final String[] SIGNATURE_FIELD_KEYWORDS = {"signature", "sign", "签名"};
    
    /**
     * 根据字段ID推断媒体类型，使用更加动态的方式
     * @param fieldId 字段ID
     * @return 媒体类型
     */
    private String inferMediaTypeFromFieldId(String fieldId) {
        if (fieldId == null) {
            return "file";
        }
        
        // 转换为小写进行比较
        String fieldIdLower = fieldId.toLowerCase();
        
        // 检查是否包含视频相关关键词
        for (String keyword : VIDEO_FIELD_KEYWORDS) {
            if (fieldIdLower.contains(keyword)) {
                return "video";
            }
        }
        
        // 检查是否包含照片相关关键词
        for (String keyword : PHOTO_FIELD_KEYWORDS) {
            if (fieldIdLower.contains(keyword)) {
                return "image";
            }
        }
        
        // 检查是否以_photos结尾
        if (fieldIdLower.endsWith("_photos")) {
            return "image";
        }
        
        // 检查是否包含签名相关关键词
        for (String keyword : SIGNATURE_FIELD_KEYWORDS) {
            if (fieldIdLower.contains(keyword)) {
                return "signature";
            }
        }
        
        // 默认返回通用文件类型
        return "file";
    }

    /**
     * 获取所有已保存的表单ID列表
     * @param context 上下文
     * @return 表单信息列表，每个表单包含id、名称和保存时间
     */
    public static List<Map<String, Object>> getSavedForms(Context context) {
        List<Map<String, Object>> formList = new ArrayList<>();
        try {
            DatabaseHelper dbHelper = DatabaseHelper.getInstance(context);
            SQLiteDatabase db = dbHelper.getReadableDatabase();

            // 检查表是否存在
            Cursor tableCheck = db.rawQuery(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                    new String[]{TABLE_FORM_DATA}
            );

            boolean tableExists = false;
            if (tableCheck != null) {
                tableExists = tableCheck.getCount() > 0;
                tableCheck.close();
            }

            if (!tableExists) {
                Log.w(TAG, "表单数据表不存在");
                return formList;
            }

            Cursor cursor = db.query(
                    TABLE_FORM_DATA,
                    new String[]{COLUMN_ID, COLUMN_FORM_ID, COLUMN_FORM_NAME, COLUMN_SAVE_TIME, COLUMN_BSM},
                    null, null, null, null,
                    COLUMN_SAVE_TIME + " DESC"
            );

            if (cursor != null) {
                int bsmColumnIndex = cursor.getColumnIndex(COLUMN_BSM);

                while (cursor.moveToNext()) {
                    int id = cursor.getInt(cursor.getColumnIndex(COLUMN_ID));
                    String formId = cursor.getString(cursor.getColumnIndex(COLUMN_FORM_ID));
                    String formName = cursor.getString(cursor.getColumnIndex(COLUMN_FORM_NAME));
                    long saveTime = cursor.getLong(cursor.getColumnIndex(COLUMN_SAVE_TIME));

                    Map<String, Object> formInfo = new HashMap<>();
                    formInfo.put("id", id);
                    formInfo.put("formId", formId);
                    formInfo.put("formName", formName);
                    formInfo.put("saveTime", saveTime);
                    formInfo.put("saveTimeStr", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                            .format(new Date(saveTime)));

                    // 添加BSM值（如果存在）
                    if (bsmColumnIndex != -1 && !cursor.isNull(bsmColumnIndex)) {
                        String bsm = cursor.getString(bsmColumnIndex);
                        if (bsm != null && !bsm.isEmpty()) {
                            formInfo.put("bsm", bsm);
                        }
                    }

                    formList.add(formInfo);
                }
                cursor.close();
            }

            Log.d(TAG, "从数据库加载了" + formList.size() + "个已保存的表单");
        } catch (Exception e) {
            Log.e(TAG, "获取已保存的表单列表失败: " + e.getMessage(), e);
        }
        return formList;
    }

    /**
     * 根据BSM值获取表单
     * @param context 上下文
     * @param bsm 业务识别码
     * @return 表单信息，如果不存在则返回null
     */
    public static Map<String, Object> getFormByBsm(Context context, String bsm) {
        if (bsm == null || bsm.isEmpty()) {
            Log.e(TAG, "BSM值为空，无法查询表单");
            return null;
        }

        try {
            DatabaseHelper dbHelper = DatabaseHelper.getInstance(context);
            SQLiteDatabase db = dbHelper.getReadableDatabase();

            // 检查表是否存在
            Cursor tableCheck = db.rawQuery(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                    new String[]{TABLE_FORM_DATA}
            );

            boolean tableExists = false;
            if (tableCheck != null) {
                tableExists = tableCheck.getCount() > 0;
                tableCheck.close();
            }

            if (!tableExists) {
                Log.w(TAG, "表单数据表不存在");
                return null;
            }

            Cursor cursor = db.query(
                    TABLE_FORM_DATA,
                    new String[]{COLUMN_ID, COLUMN_FORM_ID, COLUMN_FORM_NAME, COLUMN_SAVE_TIME, COLUMN_BSM, COLUMN_FORM_DATA},
                    COLUMN_BSM + "=?",
                    new String[]{bsm},
                    null, null,
                    COLUMN_SAVE_TIME + " DESC", // 按时间降序，获取最新的
                    "1" // 限制只返回一条记录
            );

            Map<String, Object> formInfo = null;
            if (cursor != null && cursor.moveToFirst()) {
                int id = cursor.getInt(cursor.getColumnIndex(COLUMN_ID));
                String formId = cursor.getString(cursor.getColumnIndex(COLUMN_FORM_ID));
                String formName = cursor.getString(cursor.getColumnIndex(COLUMN_FORM_NAME));
                long saveTime = cursor.getLong(cursor.getColumnIndex(COLUMN_SAVE_TIME));
                String formDataStr = cursor.getString(cursor.getColumnIndex(COLUMN_FORM_DATA));

                formInfo = new HashMap<>();
                formInfo.put("id", id);
                formInfo.put("formId", formId);
                formInfo.put("formName", formName);
                formInfo.put("saveTime", saveTime);
                formInfo.put("saveTimeStr", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                        .format(new Date(saveTime)));
                formInfo.put("bsm", bsm);

                try {
                    JSONObject formData = new JSONObject(formDataStr);
                    formInfo.put("formData", formData);
                } catch (JSONException e) {
                    Log.e(TAG, "解析表单数据JSON失败: " + e.getMessage(), e);
                }

                Log.d(TAG, "根据BSM成功查询到表单数据: bsm=" + bsm + ", formId=" + formId);
            } else {
                Log.w(TAG, "未找到匹配BSM值的表单: " + bsm);
            }

            if (cursor != null) {
                cursor.close();
            }

            return formInfo;
        } catch (Exception e) {
            Log.e(TAG, "根据BSM查询表单失败: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 通过数据库记录ID删除本地保存的表单数据
     * @param context 上下文
     * @param id 数据库记录ID
     * @return 是否删除成功
     */
    public static boolean deleteFormDataById(Context context, int id) {
        try {
            DatabaseHelper dbHelper = DatabaseHelper.getInstance(context);
            SQLiteDatabase db = dbHelper.getWritableDatabase();

            int rowsDeleted = db.delete(
                    TABLE_FORM_DATA,
                    COLUMN_ID + "=?",
                    new String[]{String.valueOf(id)}
            );

            boolean success = rowsDeleted > 0;
            if (success) {
                Log.d(TAG, "已删除数据库中的表单数据，ID: " + id);
            } else {
                Log.w(TAG, "删除表单数据失败: 没有找到匹配的记录，ID=" + id);
            }

            return success;
        } catch (Exception e) {
            Log.e(TAG, "删除表单数据失败: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 计算数据哈希值，用于验证数据完整性
     * @param formData 表单数据
     * @return 哈希值字符串
     */
    private String calculateDataHash(JSONObject formData) {
        if (formData == null) {
            return "";
        }

        try {
            // 创建一个副本，排除哈希值字段
            JSONObject tempData = new JSONObject(formData.toString());
            if (tempData.has("__hash")) {
                tempData.remove("__hash");
            }

            // 计算哈希值
            String jsonStr = tempData.toString();
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(jsonStr.getBytes("UTF-8"));

            // 将字节数组转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();
        } catch (Exception e) {
            Log.e(TAG, "计算数据哈希值失败: " + e.getMessage(), e);
            return "";
        }
    }

//    /**
//     * 验证数据哈希值
//     * @param formData 表单数据
//     * @return 是否验证通过
//     */
//    private boolean validateDataHash(JSONObject formData) {
//        if (formData == null || !formData.has("__hash")) {
//            return false;
//        }
//
//        try {
//            String storedHash = formData.getString("__hash");
//            String calculatedHash = calculateDataHash(formData);
//
//            return storedHash.equals(calculatedHash);
//        } catch (Exception e) {
//            Log.e(TAG, "验证数据哈希值失败: " + e.getMessage(), e);
//            return false;
//        }
//    }

    /**
     * 更新样点的本地数据标志
     * 在表单保存成功后，更新对应BSM的样点has_local_data字段为1
     * @param context 上下文
     * @param bsm 业务识别码
     */
    public static void updateHasLocalData(Context context, String bsm) {
        if (context == null || bsm == null || bsm.isEmpty()) {
            Log.e(TAG, "更新样点本地数据标志失败: 无效的参数");
            return;
        }
        
        SQLiteDatabase db = null;
        try {
            DatabaseHelper dbHelper = DatabaseHelper.getInstance(context);
            db = dbHelper.getWritableDatabase();
            
            // 准备更新数据
            ContentValues values = new ContentValues();
            values.put(DatabaseConstants.COLUMN_HAS_LOCAL_DATA, 1); // 设置has_local_data为1
            
            // 执行更新
            int affected = db.update(
                DatabaseConstants.TABLE_DDC_POINTS,  // 表名
                values,                                  // 更新的值
                DatabaseConstants.COLUMN_DDC_PJDY_BSM + " = ?",  // WHERE子句
                new String[]{bsm}                       // WHERE参数
            );
            
            if (affected > 0) {
                Log.d(TAG, "已成功更新样点 " + bsm + " 的" + DatabaseConstants.COLUMN_HAS_LOCAL_DATA + "为1，受影响行数: " + affected);
            } else {
                Log.w(TAG, "未找到BSM为 " + bsm + " 的样点记录，无法更新" + DatabaseConstants.COLUMN_HAS_LOCAL_DATA);
            }
        } catch (Exception e) {
            Log.e(TAG, "更新样点本地数据标志时出错: " + e.getMessage(), e);
        }
    }

    private Map<String, List<FieldFile>> loadFormFilesDirectly(JSONObject formData, JSONObject filesData) {
        Map<String, List<FieldFile>> fieldFiles = new HashMap<>();
        
        try {
            if (filesData == null || filesData.length() == 0) {
                Log.d(TAG, "没有文件数据需要加载");
                return fieldFiles;
            }
            
            Log.d(TAG, "开始加载字段文件，字段数量: " + filesData.length());
            
            // 遍历所有字段
            Iterator<String> keys = filesData.keys();
            while (keys.hasNext()) {
                String fieldId = keys.next();
                JSONArray fileArray = filesData.getJSONArray(fieldId);
                List<FieldFile> files = new ArrayList<>();
                fieldFiles.put(fieldId, files);
                
                Log.d(TAG, "加载字段[" + fieldId + "]的文件，数量: " + fileArray.length());
                
                for (int i = 0; i < fileArray.length(); i++) {
                    // 检查是否是简单的路径字符串还是包含额外信息的JSONObject
                    Object fileEntry = fileArray.get(i);
                    String filePath;
                    double latitude = 0.0;
                    double longitude = 0.0;
                    float direction = 0.0f;
                    
                    if (fileEntry instanceof JSONObject) {
                        // 获取完整的文件信息包括路径和位置数据
                        JSONObject fileInfo = (JSONObject) fileEntry;
                        filePath = fileInfo.getString("path");
                        
                        // 获取位置和方位角信息（如果有）
                        if (fileInfo.has("latitude")) {
                            latitude = fileInfo.getDouble("latitude");
                        }
                        if (fileInfo.has("longitude")) {
                            longitude = fileInfo.getDouble("longitude");
                        }
                        if (fileInfo.has("direction")) {
                            direction = (float) fileInfo.getDouble("direction");
                        }
                        
                        if (latitude != 0 || longitude != 0 || direction != 0) {
                            Log.d(TAG, "从缓存加载文件位置信息: 纬度=" + latitude + 
                                  ", 经度=" + longitude + 
                                  ", 方位角=" + direction);
                        }
                    } else {
                        // 旧版格式，只有文件路径
                        filePath = fileEntry.toString();
                    }
                    
                    // 检查文件是否存在
                    File fileObj = new File(filePath);
                    if (fileObj.exists()) {
                        // 检测媒体类型
                        String mediaType = inferMediaTypeFromFieldId(fieldId);
                        if (mediaType == null) {
                            mediaType = inferMediaTypeFromFilePath(filePath);
                        }
                        
                        // 创建FieldFile对象，包含位置信息
                        FieldFile file = new FieldFile();
                        file.setPath(filePath);
                        file.setMediaType(mediaType);
                        file.setLatitude(latitude);
                        file.setLongitude(longitude);
                        file.setDirection(direction);
                        files.add(file);
                        
                        Log.d(TAG, "加载文件: " + filePath + "，类型: " + mediaType + "，大小: " + fileObj.length() + " 字节");
                    } else {
                        Log.w(TAG, "文件不存在，跳过: " + filePath);
                    }
                }
                
                // 如果字段没有有效文件，那么表单数据中可能需要清除该字段的值
                if (files.isEmpty() && formData.has(fieldId)) {
                    formData.remove(fieldId);
                    Log.d(TAG, "字段[" + fieldId + "]没有有效文件，从表单数据中移除");
                }
                
                // 如果有文件，确保表单数据中有该字段的值（通常是第一个文件的路径）
                if (!files.isEmpty() && !formData.has(fieldId)) {
                    formData.put(fieldId, files.get(0).getPath());
                    Log.d(TAG, "字段[" + fieldId + "]有文件但表单数据中未设置，设置为第一个文件的路径");
                }
            }
            
            int totalFiles = 0;
            for (List<FieldFile> fileList : fieldFiles.values()) {
                totalFiles += fileList.size();
            }
            Log.d(TAG, "共加载了" + totalFiles + "个文件，涉及" + fieldFiles.size() + "个字段");
            
        } catch (Exception e) {
            Log.e(TAG, "加载表单文件时出错: " + e.getMessage(), e);
        }
        
        return fieldFiles;
    }

    /**
     * 从文件路径推断媒体类型
     * @param filePath 文件路径
     * @return 媒体类型
     */
    private String inferMediaTypeFromFilePath(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return "file";
        }
        
        String lowerPath = filePath.toLowerCase();
        
        // 判断图片类型
        if (lowerPath.endsWith(".jpg") || lowerPath.endsWith(".jpeg") || 
            lowerPath.endsWith(".png") || lowerPath.endsWith(".gif") || 
            lowerPath.endsWith(".webp")) {
            return "image";
        }
        
        // 判断视频类型
        if (lowerPath.endsWith(".mp4") || lowerPath.endsWith(".3gp") || 
            lowerPath.endsWith(".avi") || lowerPath.endsWith(".mov") || 
            lowerPath.endsWith(".mkv") || lowerPath.endsWith(".wmv")) {
            return "video";
        }
        
        // 判断签名类型
        if (lowerPath.contains("signature") || lowerPath.contains("sign_") || 
            lowerPath.contains("_sign") || lowerPath.contains("_signature")) {
            return "signature";
        }
        
        // 默认为文件类型
        return "file";
    }
}
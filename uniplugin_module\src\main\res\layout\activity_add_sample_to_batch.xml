<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="io.dcloud.uniplugin.sampleflow.AddSampleToBatchActivity">

    <!-- 批次信息 -->
    <TextView
        android:id="@+id/textViewBatchInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#F5F5F5"
        android:padding="12dp"
        android:textSize="16sp"
        android:textStyle="bold"
        tools:text="批次: 测试批次 (ID: 123)" />

    <!-- 已添加样品列表 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_margin="8dp"
        app:cardCornerRadius="4dp"
        app:cardElevation="2dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#E0E0E0"
                android:padding="8dp"
                android:text="已添加样品"
                android:textSize="16sp"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewSamples"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="8dp"
                tools:listitem="@layout/item_sample" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- 添加样品表单 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        app:cardCornerRadius="4dp"
        app:cardElevation="2dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="8dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="添加新样品"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- 样品名称 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:hint="样品名称">

                <EditText
                    android:id="@+id/editTextSampleName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- 样品类型 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:hint="样品类型">

                <EditText
                    android:id="@+id/editTextSampleType"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- 接样单位 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:hint="接样单位">

                <Spinner
                    android:id="@+id/spinnerReceiveOrg"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:minHeight="48dp" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- 样品数量 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:hint="样品数量">

                <EditText
                    android:id="@+id/editTextSampleQuantity"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number"
                    android:maxLines="1"
                    android:text="1" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- 备注 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:hint="备注">

                <EditText
                    android:id="@+id/editTextSampleRemark"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textMultiLine"
                    android:maxLines="3" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- 添加样品按钮 -->
            <Button
                android:id="@+id/buttonAddSample"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:backgroundTint="#2196F3"
                android:text="添加到列表"
                style="@style/Widget.AppCompat.Button.Colored" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- 提交按钮 -->
    <Button
        android:id="@+id/buttonSubmit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:backgroundTint="#4CAF50"
        android:text="提交样品"
        style="@style/Widget.AppCompat.Button.Colored" />

</LinearLayout> 
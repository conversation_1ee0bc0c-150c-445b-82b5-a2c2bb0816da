<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.chy.map"
    android:versionCode="1"
    android:versionName="1.0"
    tools:ignore="MissingLeanbackLauncher" >

    <uses-sdk
        android:minSdkVersion="23"
        android:targetSdkVersion="28" />
    <!-- 增加文件存储和访问摄像头的权限 -->
    <uses-feature
        android:name="android.software.leanback"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application>
        <activity
            android:name="com.chy.map.com.abdu.qrcode.MainActivity"
            android:label="土壤三普生物调查"
            android:largeHeap="true"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />

        <!-- android:theme="@android:style/Theme.DeviceDefault.Light.NoActionBar" -->
    </application>

</manifest>
package io.dcloud.uniplugin.enums;

public enum SharedPreferencesEnum {
    USER_INFO_PREF("user_info"),
    LOGIN_PREFS("LoginPrefs"),
    USER_ID("userId"),
    ACCESS_TOKEN("access_token"),
    REFRESH_TOKEN("refreshToken"),
    EXPIRE_TIME("expireTime"),
    PERMISSION_INFO("permissionInfo"),
    IS_OFFLINE_LOGIN("isOfflineLogin");



    public final String value; // 直接暴露公共字段

    SharedPreferencesEnum(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return value;
    }
}

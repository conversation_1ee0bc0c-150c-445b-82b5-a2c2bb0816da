package io.dcloud.uniplugin.form.utils;

import android.app.Activity;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import io.dcloud.uniplugin.form.SignatureView;
import io.dcloud.uniplugin.form.field.FieldFile;
import io.dcloud.uniplugin.form.media.MediaHandler;
import io.dcloud.uniplugin.model.FormFieldConfig;
import uni.dcloud.io.uniplugin_module.R;

/**
 * 表单按钮管理器，负责处理表单中各种按钮的设置和事件监听
 */
public class FormButtonManager {
    private static final String TAG = "FormButtonManager";
    private static final int REQUEST_PERMISSION_CAMERA = 2002;
    
    private Activity activity;
    private Map<String, View> formViews;
    private List<FormFieldConfig> fieldList;
    private Map<String, List<FieldFile>> fieldFiles;
    private MediaHandler mediaHandler;
    
    // 添加成员变量来保存pjdybh和xmmc
    private String pjdybh;
    private String xmmc;
    
    /**
     * 构造函数
     * @param activity 活动
     * @param formViews 表单视图
     * @param fieldList 字段列表
     * @param fieldFiles 字段文件
     * @param mediaHandler 媒体处理器
     */
    public FormButtonManager(Activity activity, Map<String, View> formViews, 
                             List<FormFieldConfig> fieldList, 
                             Map<String, List<FieldFile>> fieldFiles,
                             MediaHandler mediaHandler) {
        this.activity = activity;
        this.formViews = formViews;
        this.fieldList = fieldList;
        this.fieldFiles = fieldFiles;
        this.mediaHandler = mediaHandler;
    }
    
    /**
     * 设置表单中所有按钮的点击监听器
     * @param pjdybh
     * @param xmmc
     */
    public void setupFormButtonListeners(String pjdybh, String xmmc) {
        // 保存参数到成员变量，如果传入的不为null则更新，否则保持原值
        if (pjdybh != null) {
            this.pjdybh = pjdybh;
        }
        if (xmmc != null) {
            this.xmmc = xmmc;
        }
        
        Log.d(TAG, "设置表单按钮监听器 - pjdybh: " + this.pjdybh + ", xmmc: " + this.xmmc);
        
//        //Log.d(TAG, "开始设置表单中所有按钮的点击监听器");
        
        // 遍历所有字段
        for (FormFieldConfig field : fieldList) {
            if (field == null) continue;
            
            String fieldId = field.getFieldId();
            String fieldType = field.getFieldType();
            View fieldView = formViews.get(fieldId);
            
            if (fieldView == null) {
//                Log.w(TAG, "找不到字段视图: " + fieldId);
                continue;
            }
            
            // 根据字段类型设置相应的按钮监听器
            if (FormFieldConfig.TYPE_FILE.equals(fieldType) || 
                FormFieldConfig.TYPE_PHOTO.equals(fieldType) || 
                FormFieldConfig.TYPE_VIDEO.equals(fieldType)) {
                setupMediaButtonListeners(fieldView, fieldId);
                
                // 记录详细日志，用于调试视频类型字段
                if (FormFieldConfig.TYPE_VIDEO.equals(fieldType)) {
//                    //Log.d(TAG, "为视频类型字段设置媒体按钮监听器: " + fieldId);
                    
                    // 获取拍摄按钮
                    Button btnCamera = fieldView.findViewById(R.id.btnCamera);
                    
                    if (btnCamera != null) {
//                        //Log.d(TAG, "视频字段的拍摄按钮文本: " + btnCamera.getText());
                    }
                }
            } else if (FormFieldConfig.TYPE_SIGNATURE.equals(fieldType)) {
                setupSignatureButtonListeners(fieldView, fieldId);
            }
        }
    }
    
    /**
     * 设置媒体按钮的点击监听器
     * @param fieldView 字段视图
     * @param fieldId 字段ID
     */
    private void setupMediaButtonListeners(View fieldView, String fieldId) {
        // 获取字段对象，用于判断类型
        FormFieldConfig fieldConfig = null;
        for (FormFieldConfig field : fieldList) {
            if (field != null && fieldId.equals(field.getFieldId())) {
                fieldConfig = field;
                break;
            }
        }
        
        // 如果找不到字段配置，返回
        if (fieldConfig == null) {
//            Log.w(TAG, "找不到字段配置: " + fieldId);
            return;
        }
        
        // 判断是否为视频类型
        boolean isVideo = FormFieldConfig.TYPE_VIDEO.equals(fieldConfig.getFieldType());
//        //Log.d(TAG, "设置媒体按钮监听器: fieldId=" + fieldId + ", isVideo=" + isVideo);
        
        Button btnCamera = fieldView.findViewById(R.id.btnCamera);
        
        if (btnCamera != null) {
            //Log.d(TAG, "设置" + (isVideo ? "录制视频" : "拍照") + "按钮点击监听器: " + fieldId);
            btnCamera.setOnClickListener(v -> {
                //Log.d(TAG, (isVideo ? "录制视频" : "拍照") + "按钮被点击: " + fieldId);
                Log.d(TAG, "点击拍照按钮 - 当前pjdybh: " + this.pjdybh + ", xmmc: " + this.xmmc);
                
                // 检查相机权限
                if (checkCameraPermission()) {
                    UIUtils.showToast(activity, "正在打开相机...");
                    if (mediaHandler != null) {
                        if (isVideo) {
                            // 录制视频 - 不需要pjdybh和xmmc参数
                            mediaHandler.recordVideo(activity, fieldId);
                        } else {
                            // 拍照 - 使用成员变量中保存的值
                            mediaHandler.takePicture(activity, fieldId, this.pjdybh, this.xmmc);
                        }
                    }
                } else {
                    // 请求相机权限
                    requestCameraPermission();
                }
            });
        }
    }
    
    /**
     * 设置签名按钮的点击监听器
     * @param fieldView 字段视图
     * @param fieldId 字段ID
     */
    private void setupSignatureButtonListeners(View fieldView, String fieldId) {
        //Log.d(TAG, "设置签名按钮监听器，字段ID: " + fieldId);
        
        // 获取签名视图和按钮
        SignatureView signatureView = fieldView.findViewById(R.id.signatureView);
        Button btnClear = fieldView.findViewById(R.id.btnClear);
        Button btnConfirm = fieldView.findViewById(R.id.btnConfirm);
        
        if (signatureView == null) {
            Log.e(TAG, "签名视图为空");
            return;
        }
        
        if (btnClear == null) {
            Log.e(TAG, "清除按钮为空");
        } else {
            // 设置清除按钮点击监听器
            //Log.d(TAG, "设置清除按钮点击监听器");
            btnClear.setOnClickListener(v -> {
                //Log.d(TAG, "清除按钮被点击");
                
                // 清除签名
                if (signatureView != null) {
                    signatureView.clear();
                    //Log.d(TAG, "清除签名");
                }
            });
        }
        
        if (btnConfirm == null) {
            Log.e(TAG, "确认按钮为空");
        } else {
            // 设置确认按钮点击监听器
            //Log.d(TAG, "设置确认按钮点击监听器");
            btnConfirm.setOnClickListener(v -> {
                //Log.d(TAG, "确认按钮被点击");
                
                // 检查是否已签名
                if (signatureView != null && signatureView.isSigned()) {
                    //Log.d(TAG, "已签名，准备保存签名");
                    Bitmap bitmap = signatureView.getSignatureBitmap();
                    String path = FormUtils.saveSignatureImage(activity, bitmap, fieldId);
                    
                    if (path != null) {
                        //Log.d(TAG, "签名保存成功: " + path);
                        UIUtils.showToast(activity, "签名已保存");
                        
                        // 统一使用与照片、视频相同的方式管理文件
                        // 删除旧的字段文件代码，改为使用通用的媒体处理方式
                        
                        // 确保文件存在并可访问
                        File file = new File(path);
                        if (!file.exists()) {
                            Log.e(TAG, "创建的签名文件不存在: " + path);
                            return;
                        }
                        
                        // 先从fieldFiles中移除该字段，以便完全重置
                        if (fieldFiles.containsKey(fieldId)) {
                            fieldFiles.remove(fieldId);
                        }
                        
                        // 添加新签名文件
                        List<FieldFile> files = new ArrayList<>();
                        // 明确设置类型为signature
                        FieldFile fieldFile = new FieldFile(path, "signature");
                        files.add(fieldFile);
                        fieldFiles.put(fieldId, files);
                        
                        // 隐藏签名面板，显示已保存的签名
                        ViewParent signaturePanelParent = fieldView.findViewById(R.id.signatureView).getParent();
                        if (signaturePanelParent instanceof View) {
                            View signaturePanel = (View) signaturePanelParent;
                            signaturePanel.setVisibility(View.GONE);
                            //Log.d(TAG, "已隐藏签名面板");
                        }
                        
                        // 使用ImageView显示签名图片
                        android.widget.ImageView signatureImage = fieldView.findViewById(R.id.signatureImage);
                        if (signatureImage != null) {
                            signatureImage.setVisibility(View.VISIBLE);
                            signatureImage.setImageBitmap(signatureView.getSignatureBitmap());
                            // 设置标签以便我们可以稍后找到文件路径
                            signatureImage.setTag(path);
                            //Log.d(TAG, "签名图片已显示在ImageView中");
                            
                            // 显示签名标签
                            View savedSignatureLabel = fieldView.findViewById(R.id.savedSignatureLabel);
                            if (savedSignatureLabel != null) {
                                savedSignatureLabel.setVisibility(View.VISIBLE);
                                //Log.d(TAG, "已显示签名标签");
                            }
                        } else {
                            Log.e(TAG, "未找到ImageView，无法显示签名图片");
                        }
                        
                        //Log.d(TAG, "签名已保存: " + path);
                        
                        // 通知上层组件需要更新文件列表
                        if (mediaHandler != null) {
                            // 使用与照片、视频相同的回调方式通知更新
                            mediaHandler.notifyMediaCallbacks(fieldId, path);
                        }
                    } else {
                        UIUtils.showToast(activity, "保存签名失败");
                    }
                } else {
                    UIUtils.showToast(activity, "请先签名");
                }
            });
        }

        // 设置签名图片点击事件，允许用户重新签名
        android.widget.ImageView signatureImage = fieldView.findViewById(R.id.signatureImage);
        if (signatureImage != null) {
            signatureImage.setOnClickListener(v -> {
                //Log.d(TAG, "签名图片被点击，显示操作选项");
                
                // 显示操作选项对话框
                android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(activity);
                builder.setTitle("签名操作");
                builder.setItems(new String[]{"查看签名", "重新签名"}, (dialog, which) -> {
                    if (which == 0) {
                        // 查看签名 - 以全屏方式显示
                        List<FieldFile> files = fieldFiles.get(fieldId);
                        if (files != null && !files.isEmpty()) {
                            FieldFile signatureFile = files.get(0);
                            if (signatureFile != null && signatureFile.exists()) {
                                // 创建一个大图查看对话框
                                android.app.AlertDialog.Builder imageDialog = new android.app.AlertDialog.Builder(activity);
                                ImageView fullImage = new ImageView(activity);
                                fullImage.setImageBitmap(BitmapFactory.decodeFile(signatureFile.getPath()));
                                fullImage.setAdjustViewBounds(true);
                                
                                imageDialog.setView(fullImage);
                                imageDialog.setPositiveButton("关闭", null);
                                imageDialog.show();
                            }
                        }
                    } else {
                        // 重新签名 - 显示签名区域，隐藏签名图片
                        // 显示签名面板
                        ViewParent signaturePanelParent = signatureView.getParent();
                        if (signaturePanelParent instanceof View) {
                            View signaturePanel = (View) signaturePanelParent;
                            signaturePanel.setVisibility(View.VISIBLE);
                            //Log.d(TAG, "已显示签名面板");
                        }
                        
                        // 查找并显示按钮区域
                        View buttonLayout = null;
                        for (int i = 0; i < ((ViewGroup) fieldView).getChildCount(); i++) {
                            View child = ((ViewGroup) fieldView).getChildAt(i);
                            if (child instanceof LinearLayout && 
                                    child.findViewById(R.id.btnClear) != null) {
                                buttonLayout = child;
                                buttonLayout.setVisibility(View.VISIBLE);
                                break;
                            }
                        }
                        
                        // 隐藏签名图片
                        signatureImage.setVisibility(View.GONE);
                        
                        // 隐藏签名标签
                        View savedSignatureLabel = fieldView.findViewById(R.id.savedSignatureLabel);
                        if (savedSignatureLabel != null) {
                            savedSignatureLabel.setVisibility(View.GONE);
                        }
                        
                        // 清空之前的签名
                        signatureView.clear();
                    }
                });
                builder.show();
            });
        }
    }
    
    /**
     * 检查相机权限
     * @return 是否有相机权限
     */
    private boolean checkCameraPermission() {
        return ContextCompat.checkSelfPermission(activity, android.Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED;
    }
    
    /**
     * 请求相机权限
     */
    private void requestCameraPermission() {
        //Log.d(TAG, "请求相机权限");
        ActivityCompat.requestPermissions(activity, new String[]{android.Manifest.permission.CAMERA}, REQUEST_PERMISSION_CAMERA);
    }
    
    /**
     * 检查存储权限
     * @return 是否有存储权限
     */
    private boolean checkStoragePermission() {
        return ContextCompat.checkSelfPermission(activity, android.Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED &&
               ContextCompat.checkSelfPermission(activity, android.Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED;
    }
    
    /**
     * 请求存储权限
     */
    private void requestStoragePermission() {
        //Log.d(TAG, "请求存储权限");
        ActivityCompat.requestPermissions(activity, 
            new String[]{
                android.Manifest.permission.READ_EXTERNAL_STORAGE, 
                android.Manifest.permission.WRITE_EXTERNAL_STORAGE
            }, 
            REQUEST_PERMISSION_CAMERA);
    }
} 
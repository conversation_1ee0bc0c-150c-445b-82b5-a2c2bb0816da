package io.dcloud.uniplugin;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.ContextThemeWrapper;
import android.widget.ImageButton;


@SuppressLint("AppCompatCustomView")
public class MyImageButton extends ImageButton {
    public MyImageButton(Context context) {
        super(context);
    }

    public MyImageButton(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public MyImageButton(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void setImageButtonStyle(int styleResId) {
        if (getContext() instanceof ContextThemeWrapper) {
            ((ContextThemeWrapper) getContext()).setTheme(styleResId);
        }
    }
}

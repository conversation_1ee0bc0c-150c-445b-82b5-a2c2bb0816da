<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@android:color/white">

    <TextView
        android:id="@+id/textViewTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="请签名"
        android:textSize="18sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#CCCCCC" />

    <io.dcloud.uniplugin.view.SignatureView
        android:id="@+id/signatureView"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout_marginTop="16dp"
        android:background="#F5F5F5" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="16dp">

        <Button
            android:id="@+id/buttonClear"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="清除"
            android:textColor="@android:color/black"
            android:background="#E0E0E0"
            android:layout_marginEnd="4dp" />

        <Button
            android:id="@+id/buttonCancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="取消"
            android:textColor="@android:color/white"
            android:background="#F44336"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp" />

        <Button
            android:id="@+id/buttonConfirm"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="确认"
            android:textColor="@android:color/white"
            android:background="#4CAF50"
            android:layout_marginStart="4dp" />
    </LinearLayout>

</LinearLayout> 
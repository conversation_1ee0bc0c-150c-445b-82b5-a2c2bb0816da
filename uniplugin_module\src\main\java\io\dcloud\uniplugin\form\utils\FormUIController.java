package io.dcloud.uniplugin.form.utils;

import android.app.Activity;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;

import androidx.appcompat.app.AlertDialog;

/**
 * 表单UI控制器，负责表单界面的各种UI控制
 */
public class FormUIController {
    private static final String TAG = "FormUIController";
    
    private Activity activity;
    private ViewGroup formContainer;
    private ProgressBar progressBar;
    private Button buttonSubmit;
    private Button buttonSaveOffline;
    private AlertDialog loadingDialog;
    
    /**
     * 构造函数
     * @param activity 活动
     * @param formContainer 表单容器
     * @param progressBar 进度条
     * @param buttonSubmit 提交按钮
     * @param buttonSaveOffline 离线保存按钮
     */
    public FormUIController(Activity activity, ViewGroup formContainer, 
                           ProgressBar progressBar, Button buttonSubmit, 
                           Button buttonSaveOffline) {
        this.activity = activity;
        this.formContainer = formContainer;
        this.progressBar = progressBar;
        this.buttonSubmit = buttonSubmit;
        this.buttonSaveOffline = buttonSaveOffline;
    }
    
    /**
     * 显示/隐藏加载进度
     * @param show 是否显示
     */
    public void showLoading(boolean show) {
        if (progressBar != null) {
            progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
            
            // 禁用表单和按钮
            if (formContainer != null) {
                formContainer.setEnabled(!show);
                formContainer.setAlpha(show ? 0.5f : 1.0f);
            }
            
            if (buttonSubmit != null) {
                if (!show) {
                    buttonSubmit.setEnabled(true);
                    buttonSubmit.setAlpha(1.0f);
                } else {
                    buttonSubmit.setEnabled(false);
                    buttonSubmit.setAlpha(0.5f);
                }
            }
            
            if (buttonSaveOffline != null) {
                if (!show) {
                    buttonSaveOffline.setEnabled(true);
                    buttonSaveOffline.setAlpha(1.0f);
                } else {
                    buttonSaveOffline.setEnabled(false);
                    buttonSaveOffline.setAlpha(0.5f);
                }
            }
            
            Log.d(TAG, "showLoading: " + (show ? "显示" : "隐藏") + "加载进度条，按钮状态: " + 
                    (show ? "禁用" : "启用"));
        }
    }
    
    /**
     * 设置表单是否可编辑
     * @param enabled true表示可编辑，false表示不可编辑
     */
    public void setFormEnabled(boolean enabled) {
        // 如果有位置限制但不在范围内，则禁用表单
        if (!enabled) {
//            UIUtils.showToast(activity, "当前位置不在指定范围内，表单已禁用");
            Log.w(TAG, "当前位置不在指定范围内，表单已禁用");
        } else {
            Log.d(TAG, "当前位置在指定范围内，表单已启用");
        }
        
        // 设置表单元素是否可用
        if (formContainer != null) {
            // 注意：不再设置整个表单容器的alpha值，保持正常显示
            for (int i = 0; i < formContainer.getChildCount(); i++) {
                View child = formContainer.getChildAt(i);
                setViewEnabledRecursive(child, enabled);
            }
        }
        
        // 位置在范围外时，按钮也需要禁用
        if (buttonSubmit != null) {
            buttonSubmit.setEnabled(enabled);
            buttonSubmit.setAlpha(enabled ? 1.0f : 0.5f);
        }
        
        if (buttonSaveOffline != null) {
            buttonSaveOffline.setEnabled(enabled);
            buttonSaveOffline.setAlpha(enabled ? 1.0f : 0.5f);
        }
    }
    
    /**
     * 设置表单只读但允许提交按钮可用
     * 这用于待提交表单在位置不在围栏内的特殊情况
     * @param enableSaveOfflineButton 是否同时启用"保存到本地"按钮
     */
    public void setFormReadOnlyButSubmittable(boolean enableSaveOfflineButton) {
        Log.d(TAG, "设置表单只读但允许提交" + (enableSaveOfflineButton ? "和保存到本地" : ""));
        
        // 设置表单元素为不可编辑
        if (formContainer != null) {
            for (int i = 0; i < formContainer.getChildCount(); i++) {
                View child = formContainer.getChildAt(i);
                setViewEnabledRecursive(child, false);
            }
        }
        
        // 确保提交按钮可用
        if (buttonSubmit != null) {
            buttonSubmit.setEnabled(true);
            buttonSubmit.setAlpha(1.0f);
            Log.d(TAG, "确保提交按钮可用");
        }
        
        // 根据参数决定是否启用"保存到本地"按钮
        if (buttonSaveOffline != null) {
            buttonSaveOffline.setEnabled(enableSaveOfflineButton);
            buttonSaveOffline.setAlpha(enableSaveOfflineButton ? 1.0f : 0.5f);
            Log.d(TAG, "保存到本地按钮状态: " + (enableSaveOfflineButton ? "可用" : "禁用"));
        }
    }
    
//    /**
//     * 设置表单只读但允许提交按钮可用
//     * 这是向后兼容的重载方法，默认同时启用"保存到本地"按钮
//     */
//    public void setFormReadOnlyButSubmittable() {
//        setFormReadOnlyButSubmittable(true);
//    }
//
//    /**
//     * 更新提交按钮状态
//     * @param isComplete 表单是否完整
//     * @param isOfflineForm 是否为离线表单
//     */
//    public void updateSubmitButtonState(boolean isComplete, boolean isOfflineForm) {
//        Log.d(TAG, "更新提交按钮状态: isComplete=" + isComplete);
//
//        // 如果表单已完成，禁用提交按钮
//        buttonSubmit.setEnabled(!isComplete);
//        buttonSubmit.setText(isComplete ? "已提交" : "提交");
//        buttonSubmit.setAlpha(isComplete ? 0.5f : 1.0f);
//
//        // 如果是离线表单，隐藏离线保存按钮
//        boolean shouldHideSaveOfflineButton = isComplete || isOfflineForm;
//
//        buttonSaveOffline.setVisibility(shouldHideSaveOfflineButton ? View.GONE : View.VISIBLE);
//
//        // 确保离线保存按钮始终可点击
//        buttonSaveOffline.setEnabled(true);
//        buttonSaveOffline.setClickable(true);
//        buttonSaveOffline.setAlpha(1.0f);
//    }
    
    /**
     * 确保表单中的所有按钮都能正确响应点击事件
     */
    public void ensureFormButtonsClickable() {
        Log.d(TAG, "确保所有按钮可点击");
        
        // 确保提交按钮可点击
        buttonSubmit.setEnabled(true);
        buttonSubmit.setClickable(true);
        buttonSubmit.setAlpha(1.0f);
        Log.d(TAG, "确保提交按钮可点击");
        
        // 确保离线保存按钮可点击
        buttonSaveOffline.setEnabled(true);
        buttonSaveOffline.setClickable(true);
        buttonSaveOffline.setAlpha(1.0f);
        Log.d(TAG, "确保离线保存按钮可点击");
        
        // 确保表单容器中的所有按钮可点击
        disableInterceptTouchEvent(formContainer);
    }
    
//    /**
//     * 确保按钮可点击
//     * @param view 按钮视图
//     */
//    public void ensureButtonClickable(View view) {
//        if (view instanceof ViewGroup) {
//            ViewGroup viewGroup = (ViewGroup) view;
//
//            // 递归处理子视图
//            for (int i = 0; i < viewGroup.getChildCount(); i++) {
//                View child = viewGroup.getChildAt(i);
//                ensureButtonClickable(child);
//            }
//        }
//
//        // 如果是按钮，确保其可点击性
//        if (view instanceof Button) {
//            Button button = (Button) view;
//
//            // 记录按钮信息
//            Log.d(TAG, "确保按钮可点击: " + (button.getText() != null ? button.getText() : "未命名") +
//                    ", ID: " + button.getId() +
//                    ", 当前可点击性: " + button.isClickable() +
//                    ", 当前启用状态: " + button.isEnabled());
//
//            // 确保按钮可点击
//            button.setClickable(true);
//            button.setEnabled(true);
//            button.setFocusable(true);
//
//            // 设置按钮的长按监听器，用于调试
//            button.setOnLongClickListener(v -> {
//                String buttonText = button.getText() != null ? button.getText().toString() : "未命名";
//                Log.d(TAG, "按钮被长按: " + buttonText);
//                UIUtils.showToast(activity, "按钮被长按: " + buttonText);
//
//                // 闪烁按钮以显示响应
//                button.setAlpha(0.5f);
//                button.postDelayed(() -> button.setAlpha(1.0f), 200);
//
//                return true; // 返回true表示消费了长按事件
//            });
//        }
//    }
    
    /**
     * 专门控制提交按钮的启用状态
     * @param enabled 是否启用
     * @param showToast 是否显示Toast提示
     */
    public void setSubmitEnabled(boolean enabled, boolean showToast) {
        if (buttonSubmit != null) {
            buttonSubmit.setEnabled(enabled);
            buttonSubmit.setAlpha(enabled ? 1.0f : 0.5f);
            
            Log.d(TAG, "设置提交按钮状态: " + (enabled ? "启用" : "禁用"));
            
            // 如果禁用提交按钮，可以显示一个提示
//            if (!enabled && showToast) {
//                UIUtils.showToast(activity, "当前位置不在有效范围内，无法提交");
//            }
        } else {
            Log.w(TAG, "提交按钮为null，无法设置状态");
        }
    }
    
    /**
     * 专门控制提交按钮的启用状态，默认显示Toast提示
     * @param enabled 是否启用
     */
    public void setSubmitEnabled(boolean enabled) {
        setSubmitEnabled(enabled, true);
    }
    
    /**
     * 递归设置视图启用状态
     * @param view 视图
     * @param enabled 是否启用
     */
    private void setViewEnabledRecursive(View view, boolean enabled) {
        view.setEnabled(enabled);
        
        // 只对输入控件设置透明度，其他视图保持正常显示
        boolean isInputControl = view instanceof android.widget.EditText ||
                               view instanceof android.widget.Spinner ||
                               view instanceof android.widget.CheckBox ||
                               view instanceof android.widget.RadioButton ||
                               view instanceof android.widget.Button ||
                               view instanceof io.dcloud.uniplugin.form.SignatureView;
        
        if (isInputControl) {
            view.setAlpha(enabled ? 1.0f : 0.7f);
        }
        
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                View child = viewGroup.getChildAt(i);
                setViewEnabledRecursive(child, enabled);
            }
        }
    }
    
    /**
     * 禁用视图及其子视图的触摸事件拦截
     * @param view 要禁用的视图
     */
    private void disableInterceptTouchEvent(View view) {
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            viewGroup.setOnTouchListener((v, event) -> false);
            
            // 递归处理子视图
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                View child = viewGroup.getChildAt(i);
                disableInterceptTouchEvent(child);
            }
        }
    }
}
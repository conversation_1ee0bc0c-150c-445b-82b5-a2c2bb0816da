<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context="io.dcloud.uniplugin.fileUpload.FileUploadActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <EditText
            android:id="@+id/editTextYdbh"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="样点编号"
            android:inputType="text"
            android:maxLines="1"
            android:layout_marginEnd="8dp"/>

        <Spinner
            android:id="@+id/spinnerZt"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"/>
    </LinearLayout>

    <Button
        android:id="@+id/buttonTakePhoto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="拍照"
        android:layout_marginBottom="20dp"
        android:backgroundTint="#1971AC" />

    <TextView
        android:id="@+id/textViewSelectedFile"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="未选择文件"
        android:layout_marginBottom="10dp"
        android:visibility="gone"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="已选择的照片/文件:"
        android:layout_marginBottom="5dp"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewImages"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginBottom="10dp"/>

    <EditText
        android:id="@+id/editTextDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="请输入描述信息"
        android:minHeight="100dp"
        android:gravity="top"
        android:padding="8dp"
        android:background="@android:drawable/editbox_background"
        android:layout_marginBottom="20dp"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/buttonSaveOffline"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="离线保存"
            android:layout_marginEnd="8dp"
            android:backgroundTint="#1971AC" />

        <Button
            android:id="@+id/buttonUpload"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="上传"
            android:layout_marginStart="8dp"
            android:backgroundTint="#1971AC" />
    </LinearLayout>

</LinearLayout> 
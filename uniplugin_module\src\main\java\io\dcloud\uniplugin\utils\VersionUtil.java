package io.dcloud.uniplugin.utils;

/**
 * 版本比较工具类
 * 用于比较两个版本号的大小
 */
public class VersionUtil {
    
    /**
     * 比较两个版本号的大小
     * 版本号格式如：1.0.0, 1.0.1, 1.1.0, 1.1.2-2-gcasdkjalsd 等
     * 只比较主版本号部分（如1.0.0），忽略后缀部分（如-2-gcasdkjalsd）
     * 
     * @param version1 第一个版本号
     * @param version2 第二个版本号
     * @return 如果version1大于version2返回1，相等返回0，小于返回-1
     */
    public static int compareVersions(String version1, String version2) {
        // 移除版本号前缀（如果有）
        version1 = removePrefix(version1);
        version2 = removePrefix(version2);
        
        // 移除版本号后缀（如果有）
        version1 = removeSuffix(version1);
        version2 = removeSuffix(version2);
        
        // 分割版本号
        String[] v1Parts = version1.split("\\.");
        String[] v2Parts = version2.split("\\.");
        
        // 逐位比较版本号
        int length = Math.max(v1Parts.length, v2Parts.length);
        for (int i = 0; i < length; i++) {
            int v1 = (i < v1Parts.length) ? parseInt(v1Parts[i]) : 0;
            int v2 = (i < v2Parts.length) ? parseInt(v2Parts[i]) : 0;
            
            if (v1 > v2) {
                return 1;
            } else if (v1 < v2) {
                return -1;
            }
        }
        
        // 版本号相等
        return 0;
    }
    
    /**
     * 移除版本号前缀（如V, v等）
     */
    private static String removePrefix(String version) {
        if (version == null || version.isEmpty()) {
            return "0";
        }
        
        // 移除前缀V或v
        if (version.startsWith("V") || version.startsWith("v")) {
            version = version.substring(1);
        }
        
        return version;
    }
    
    /**
     * 移除版本号后缀（如-2-gcasdkjalsd等）
     */
    private static String removeSuffix(String version) {
        if (version == null || version.isEmpty()) {
            return "0";
        }
        
        // 查找第一个非数字和非点的字符位置
        int suffixIndex = -1;
        for (int i = 0; i < version.length(); i++) {
            char c = version.charAt(i);
            if (c != '.' && (c < '0' || c > '9')) {
                suffixIndex = i;
                break;
            }
        }
        
        // 如果找到后缀，则移除
        if (suffixIndex > 0) {
            version = version.substring(0, suffixIndex);
        }
        
        return version;
    }
    
    /**
     * 安全解析整数，如果解析失败则返回0
     */
    private static int parseInt(String str) {
        try {
            return Integer.parseInt(str);
        } catch (NumberFormatException e) {
            return 0;
        }
    }
}
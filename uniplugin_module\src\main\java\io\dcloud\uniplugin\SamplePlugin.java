package io.dcloud.uniplugin;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.alibaba.fastjson.JSONObject;

import io.dcloud.feature.uniapp.annotation.UniJSMethod;
import io.dcloud.feature.uniapp.bridge.UniJSCallback;
import io.dcloud.feature.uniapp.common.UniModule;
import io.dcloud.uniplugin.sampleflow.SampleBatchListActivity;
import io.dcloud.uniplugin.sampleflow.SampleFlowListActivity;

/**
 * 样品相关插件模块
 */
public class SamplePlugin extends UniModule {
    private static final String TAG = "SamplePlugin";
    
    /**
     * 获取Activity实例
     * @return Activity实例
     */
    private Activity getActivity() {
        Context context = mUniSDKInstance.getContext();
        if (context instanceof Activity) {
            return (Activity) context;
        }
        Log.e(TAG, "Context不是Activity实例");
        return null;
    }
    
    /**
     * 打开样品流转清单页面
     */
    @UniJSMethod(uiThread = true)
    public void openSampleFlowList() {
        Activity activity = getActivity();
        if (activity != null) {
            Intent intent = new Intent(activity, SampleFlowListActivity.class);
            activity.startActivity(intent);
        } else {
            Log.e(TAG, "无法打开样品流转清单页面：Context不是Activity实例");
        }
    }
    
    /**
     * 打开样品批次列表页面
     */
    @UniJSMethod(uiThread = true)
    public void openSampleBatchList() {
        Activity activity = getActivity();
        if (activity != null) {
            Intent intent = new Intent(activity, SampleBatchListActivity.class);
            activity.startActivity(intent);
        } else {
            Log.e(TAG, "无法打开样品批次列表页面：Context不是Activity实例");
        }
    }
    
    /**
     * 打开样品流转清单页面(带回调)
     */
    @UniJSMethod(uiThread = true)
    public void openSampleFlowListWithCallback(JSONObject options, UniJSCallback callback) {
        Activity activity = getActivity();
        if (activity != null) {
            Intent intent = new Intent(activity, SampleFlowListActivity.class);
            activity.startActivityForResult(intent, 1001);
            
            if (callback != null) {
                JSONObject result = new JSONObject();
                result.put("code", "success");
                result.put("message", "正在打开样品流转清单页面");
                callback.invoke(result);
            }
        } else {
            Log.e(TAG, "无法打开样品流转清单页面：Context不是Activity实例");
            if (callback != null) {
                JSONObject result = new JSONObject();
                result.put("code", "error");
                result.put("message", "无法打开样品流转清单页面");
                callback.invoke(result);
            }
        }
    }
    
    /**
     * 打开样品批次列表页面(带回调)
     */
    @UniJSMethod(uiThread = true)
    public void openSampleBatchListWithCallback(JSONObject options, UniJSCallback callback) {
        Activity activity = getActivity();
        if (activity != null) {
            Intent intent = new Intent(activity, SampleBatchListActivity.class);
            activity.startActivityForResult(intent, 1002);
            
            if (callback != null) {
                JSONObject result = new JSONObject();
                result.put("code", "success");
                result.put("message", "正在打开样品批次列表页面");
                callback.invoke(result);
            }
        } else {
            Log.e(TAG, "无法打开样品批次列表页面：Context不是Activity实例");
            if (callback != null) {
                JSONObject result = new JSONObject();
                result.put("code", "error");
                result.put("message", "无法打开样品批次列表页面");
                callback.invoke(result);
            }
        }
    }
} 
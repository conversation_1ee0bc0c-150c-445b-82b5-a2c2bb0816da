package io.dcloud.uniplugin;

import android.Manifest;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import io.dcloud.uniplugin.db.DatabaseHelper;
import io.dcloud.uniplugin.enums.SharedPreferencesEnum;
import io.dcloud.uniplugin.http.RetrofitManager;
import io.dcloud.uniplugin.model.ApiResponse;
import io.dcloud.uniplugin.model.AppVersionVO;
import io.dcloud.uniplugin.model.AuthPermissionInfoRespVO;
import io.dcloud.uniplugin.model.LoginData;
import io.dcloud.uniplugin.model.LoginRequest;
import io.dcloud.uniplugin.model.User;
import io.dcloud.uniplugin.utils.AesEncryptUtil;
import io.dcloud.uniplugin.utils.AppDownloadManager;
import io.dcloud.uniplugin.utils.FileUtils;
import io.dcloud.uniplugin.utils.NetworkUtil;
import io.dcloud.uniplugin.utils.PasswordUtil;
import io.dcloud.uniplugin.utils.RsaUtils;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import uni.dcloud.io.uniplugin_module.R;

/**
 * 登录活动类
 */
public class LoginActivity extends AppCompatActivity {
    private static final String TAG = "LoginActivity";
    
    private EditText editTextUsername;
    private EditText editTextPassword;
    private CheckBox checkBoxRememberPassword;
    private Button buttonLogin;
    private TextView textViewVersion;
//    private UserSessionManager sessionManager;
    
    // 权限请求相关
    private static final int PERMISSION_REQUEST_CODE = 1001;
    
    // 加密密钥
    private static final String ENCRYPT_KEY = "gdspGDSB123GJ.gj456";
    
    // SharedPreferences 存储键
    private static final String KEY_USERNAME = "HCmobile";
    private static final String KEY_PASSWORD = "HCpassw";
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 初始化全局异常处理器
        try {
            GlobalExceptionHandler.init(this);
            Log.i(TAG, "全局异常处理器已在LoginActivity中初始化");
        } catch (Exception e) {
            Log.e(TAG, "初始化全局异常处理器失败", e);
        }
        
        setContentView(R.layout.activity_login);
        
        // 请求必要权限并创建必要的文件夹
        requestPermissions();
        
        // 检查用户是否已登录
        if (isUserLoggedIn()) {
            // 清除SharedPreferences中的用户信息
            SharedPreferences sp = getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, MODE_PRIVATE);
            sp.edit().clear().apply();
        }

        // 检查是否有新的版本，是否需要强制更新
        checkAppVersion();
        
        // 初始化视图
        editTextUsername = findViewById(R.id.editTextUsername);
        editTextPassword = findViewById(R.id.editTextPassword);
        checkBoxRememberPassword = findViewById(R.id.checkBoxRememberPassword);
        buttonLogin = findViewById(R.id.buttonLogin);
        textViewVersion = findViewById(R.id.textViewVersion);
        
        // 设置版本号
        try {
            String versionName = getPackageManager().getPackageInfo(getPackageName(), 0).versionName;
            textViewVersion.setText("当前版本：" + versionName);
        } catch (Exception e) {
            Log.e(TAG, "获取版本号失败: " + e.getMessage());
            textViewVersion.setText("当前版本：1.0.0");
        }
        
        // 加载保存的用户名和密码
        loadSavedCredentials();
        
        // 设置登录按钮点击事件
        buttonLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                handleLogin();
            }
        });
    }
    
    /**
     * 请求必要权限
     */
    private void requestPermissions() {
        // 检查是否已经拥有所需的权限
        boolean hasStoragePermission = ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED &&
                                      ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED;
        boolean hasCameraPermission = ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED;
        boolean hasLocationPermission = ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED;
        
        // 准备请求的权限列表
        List<String> permissionsToRequest = new ArrayList<>();
        
        if (!hasStoragePermission) {
            permissionsToRequest.add(Manifest.permission.READ_EXTERNAL_STORAGE);
            permissionsToRequest.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
        }
        
        if (!hasCameraPermission) {
            permissionsToRequest.add(Manifest.permission.CAMERA);
        }
        
        if (!hasLocationPermission) {
            permissionsToRequest.add(Manifest.permission.ACCESS_FINE_LOCATION);
            permissionsToRequest.add(Manifest.permission.ACCESS_COARSE_LOCATION);
        }
        
        // 如果有需要请求的权限，发起请求
        if (!permissionsToRequest.isEmpty()) {
            ActivityCompat.requestPermissions(this, 
                    permissionsToRequest.toArray(new String[0]), 
                    PERMISSION_REQUEST_CODE);
        } else {
            // 已有所有权限，创建目录
            createAppDirectories();
        }
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_REQUEST_CODE) {
            boolean allGranted = true;
            
            // 检查所有请求的权限是否都被授予
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }
            
            if (allGranted) {
                // 所有权限都获取成功，创建文件夹
                createAppDirectories();
            } else {
                // 有权限被拒绝
                Toast.makeText(this, "部分权限被拒绝，可能导致应用功能受限", Toast.LENGTH_LONG).show();
                
                // 即使权限被拒绝，也尝试创建文件夹
                if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED) {
                    createAppDirectories();
                }
            }
        }
    }
    
    /**
     * 创建应用所需的文件夹
     */
    private void createAppDirectories() {
        FileUtils.createAppDirectories(this);
    }
    
    /**
     * 检查用户是否已登录
     */
    private boolean isUserLoggedIn() {
        SharedPreferences sp = getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, MODE_PRIVATE);
        String token = sp.getString("token", "");


        // 如果有token
        return !TextUtils.isEmpty(token);
    }
    
    /**
     * 加载保存的用户名和密码
     */
    private void loadSavedCredentials() {
        //从LoginPrefs中获取
        SharedPreferences prefs = getSharedPreferences(SharedPreferencesEnum.LOGIN_PREFS.value, MODE_PRIVATE);
        String savedUsername = prefs.getString(KEY_USERNAME, "");
        String savedEncryptedPassword = prefs.getString(KEY_PASSWORD, "");
        
        if (!TextUtils.isEmpty(savedUsername)) {
            editTextUsername.setText(savedUsername);
            if (!TextUtils.isEmpty(savedEncryptedPassword)) {
                try {
                    String decryptedPassword = AesEncryptUtil.decrypt(savedEncryptedPassword, ENCRYPT_KEY);
                    editTextPassword.setText(decryptedPassword);
                    checkBoxRememberPassword.setChecked(true);
                } catch (Exception e) {
                    Log.e(TAG, "密码解密失败: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * 保存用户名和密码
     */
    private void saveCredentials(String username, String password) {
        // 保存到登录记忆SharedPreferences
        SharedPreferences prefs = getSharedPreferences(SharedPreferencesEnum.LOGIN_PREFS.value, MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        
        editor.putString(KEY_USERNAME, username);
        
        if (checkBoxRememberPassword.isChecked()) {
            try {
                String encryptedPassword = AesEncryptUtil.encrypt(password, ENCRYPT_KEY);
                editor.putString(KEY_PASSWORD, encryptedPassword);
            } catch (Exception e) {
                Log.e(TAG, "密码加密失败: " + e.getMessage());
            }
        } else {
            editor.remove(KEY_PASSWORD);
        }
        editor.apply();
    }
    
    /**
     * 处理登录操作
     */
    private void handleLogin() {
        // 获取用户输入
        String username = editTextUsername.getText().toString().trim();
        String password = editTextPassword.getText().toString().trim();
        
        // 验证输入
        if (TextUtils.isEmpty(username)) {
            editTextUsername.setError("请输入账号");
            editTextUsername.requestFocus();
            return;
        }
        
        if (TextUtils.isEmpty(password)) {
            editTextPassword.setError("请输入密码");
            editTextPassword.requestFocus();
            return;
        }
        
        // 禁用登录按钮并更改文本，防止重复提交
        String originalBtnText = buttonLogin.getText().toString();
        buttonLogin.setText("登录中...");
        buttonLogin.setEnabled(false);
        
        // 显示加载提示
//        Toast.makeText(this, "登录中，请耐心等待...", Toast.LENGTH_SHORT).show();
        
        // 检查网络状态
        if (NetworkUtil.isNetworkAvailable(this)) {
            // 有网络，正常登录
            getPublicKeyAndLogin(username, password);
        } else {
            // 无网络，提示是否需要离线登录
            showOfflineLoginDialog(username, password);
            // 恢复按钮状态
            buttonLogin.setText(originalBtnText);
            buttonLogin.setEnabled(true);
        }
    }
    
    /**
     * 获取公钥并执行登录
     */
    private void getPublicKeyAndLogin(String username, String password) {
//        showLoading("正在登录...");
        
        RetrofitManager.getInstance(this)
                .getApiService()
                .getPublicKey()
                .enqueue(new Callback<ApiResponse<String>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<String>> call, Response<ApiResponse<String>> response) {
                        if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                            String publicKey = response.body().getData();
                            if (!TextUtils.isEmpty(publicKey)) {
                                try {
                                    // 使用公钥加密密码
                                    String encryptedPassword = RsaUtils.encrypt(password, publicKey);
                                    if (encryptedPassword==null|| encryptedPassword.equals("")){
                                        showToast("解密失败");
                                        // 恢复登录按钮状态
                                        buttonLogin.setText("登录");
                                        buttonLogin.setEnabled(true);
                                        return;
                                    }
                                    // 执行登录请求
                                    login(username, password, encryptedPassword);
                                } catch (Exception e) {
                                    hideLoading();
                                    showToast("加密失败: " + e.getMessage());
                                    // 恢复登录按钮状态
                                    buttonLogin.setText("登录");
                                    buttonLogin.setEnabled(true);
                                }
                            } else {
                                hideLoading();
                                showToast("获取公钥失败: 公钥为空");
                                // 恢复登录按钮状态
                                buttonLogin.setText("登录");
                                buttonLogin.setEnabled(true);
                            }
                        } else {
                            hideLoading();
                            String errorMsg = response.body() != null ? response.body().getMsg() : "获取公钥失败";
                            showToast(errorMsg);
                            // 恢复登录按钮状态
                            buttonLogin.setText("登录");
                            buttonLogin.setEnabled(true);
                        }
                    }
                    
                    @Override
                    public void onFailure(Call<ApiResponse<String>> call, Throwable t) {
                        hideLoading();
//                        showToast("网络请求失败: " + t.getMessage());
                        showOfflineLoginDialog(username, password);
                        // 恢复登录按钮状态
                        buttonLogin.setText("登录");
                        buttonLogin.setEnabled(true);
                    }
                });
    }
    
    /**
     * 执行登录请求
     */
    private void login(String username, String password, String encryptedPassword) {
        LoginRequest loginRequest = new LoginRequest(username, encryptedPassword);
        
        RetrofitManager.getInstance(this)
                .getApiService()
                .login(loginRequest)
                .enqueue(new Callback<ApiResponse<LoginData>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<LoginData>> call, Response<ApiResponse<LoginData>> response) {
                        hideLoading();
                        
                        if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                            LoginData loginData = response.body().getData();


                            if (loginData != null) {
                                // 保存登录凭证
                                saveCredentials(username, password);

                                // 保存用户的token信息SharedPreferences
                                saveUserInfoToSharedPreferences(loginData);

                                // 获取用户权限信息
                                fetchPermissionInfo(loginData,username, password);
                                
                                // 注意：不在这里恢复按钮状态，因为还需要继续获取权限信息
                            } else {
                                showToast("登录响应数据为空");
                                // 恢复登录按钮状态
                                buttonLogin.setText("登录");
                                buttonLogin.setEnabled(true);
                            }
                        } else {
                            String errorMsg = response.body() != null ? response.body().getMsg() : "登录失败";
                            showToast(errorMsg);
                            // 恢复登录按钮状态
                            buttonLogin.setText("登录");
                            buttonLogin.setEnabled(true);
                        }
                    }
                    
                    @Override
                    public void onFailure(Call<ApiResponse<LoginData>> call, Throwable t) {
                        hideLoading();
                        showToast("错误提示: " + t.getMessage());
                        // 恢复登录按钮状态
                        buttonLogin.setText("登录");
                        buttonLogin.setEnabled(true);
                    }
                });
    }
    
    /**
     * 获取用户权限信息
     */
    private void fetchPermissionInfo(LoginData loginData, String username, String password) {
        
        RetrofitManager.getInstance(this)
                .getApiService()
                .getPermissionInfo()
                .enqueue(new Callback<ApiResponse<AuthPermissionInfoRespVO>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<AuthPermissionInfoRespVO>> call, Response<ApiResponse<AuthPermissionInfoRespVO>> response) {
                        if (response.isSuccessful() && response.body() != null) {
                            AuthPermissionInfoRespVO permissionInfo = response.body().getData();
                            
                            if (permissionInfo != null) {
                                Set<String> roles = permissionInfo.getRoles();

                                if (roles==null||roles.size()==0){
                                    showToast("该用户角色为空，请联系管理员增加角色");
                                    // 恢复登录按钮状态
                                    buttonLogin.setText("登录");
                                    buttonLogin.setEnabled(true);
                                    return;
                                }

                                if (!roles.contains("cyd") && !roles.contains("sysry")){
                                    showToast("该用户不存在可登录的角色");
                                    // 恢复登录按钮状态
                                    buttonLogin.setText("登录");
                                    buttonLogin.setEnabled(true);
                                    return;
                                }

//                                // 保存完整的权限信息到SharedPreferences
                                savePermissionInfoToSharedPreferences(permissionInfo);

                                saveUserInfoToDB(permissionInfo,username,password);
                                
                                // 如果是采样人员APP角色跳转到主页面
                                if (roles.contains("cyd")) {
                                    navigateToMainActivity();
                                }else{
                                    //跳转到检测实验室的主页面
                                    navigateToSYSMainActivity();
                                }


                                // 注意：不需要恢复按钮状态，因为已经跳转到主页面
                            } else {
                                Log.e(TAG, "权限信息为空");
                                // 恢复登录按钮状态
                                buttonLogin.setText("登录");
                                buttonLogin.setEnabled(true);
                            }
                        } else {
                            // 获取权限信息失败，但登录已成功，仍然可以继续
                            Log.e(TAG, "获取权限信息失败: " + (response.errorBody() != null ? "错误响应" : "响应为空"));
                            // 恢复登录按钮状态
                            buttonLogin.setText("登录");
                            buttonLogin.setEnabled(true);
                        }
                    }
                    
                    @Override
                    public void onFailure(Call<ApiResponse<AuthPermissionInfoRespVO>> call, Throwable t) {
                        // 获取权限信息失败，但登录已成功，仍然可以继续
                        Log.e(TAG, "获取权限信息失败: " + t.getMessage());
//                        handlePermissionInfoFailure(ApiResponse<LoginData>);
                        // 恢复登录按钮状态
                        buttonLogin.setText("登录");
                        buttonLogin.setEnabled(true);
                    }
                });
    }

    private void saveUserInfoToDB(AuthPermissionInfoRespVO permissionInfoRespVO, String username, String password) {
        try {
            // 获取数据库帮助类实例
            DatabaseHelper dbHelper = DatabaseHelper.getInstance(this);
            
            // 检查用户是否已存在
            User existingUser = dbHelper.getUserByUsername(username);
            
            // 生成随机盐值
            String salt = PasswordUtil.generateSalt();
            
            // 使用MD5加盐加密密码
            String encryptedPassword = PasswordUtil.encryptPassword(password, salt);
            
            // 将权限集合转换为JSON字符串
            Gson gson = new Gson();
            String permissionsJson = "";
            if (permissionInfoRespVO != null) {
                permissionsJson = gson.toJson(permissionInfoRespVO);
            }
            
            // 获取当前时间作为最后登录时间
            String currentTime = String.valueOf(System.currentTimeMillis());
            
            // 获取用户角色
            String role = "";
            if (!(permissionInfoRespVO != null && !permissionInfoRespVO.getRoles().isEmpty())  ) {
                role = String.join(",", permissionInfoRespVO.getRoles());
            }
            
            // 获取用户信息
            String gsddm = "";
            String gsdmc = "";
            String nickname = "";
            
            if (permissionInfoRespVO.getUser() != null) {
                AuthPermissionInfoRespVO.UserVO userVO = permissionInfoRespVO.getUser();
                nickname = userVO.getNickname();
                gsddm = userVO.getGsddm();
                gsdmc = userVO.getGsdmc();
            }
            
            if (existingUser != null) {
                // 更新现有用户信息
                existingUser.setPassword(encryptedPassword);
                existingUser.setSalt(salt);
                existingUser.setNickname(nickname);
                existingUser.setRole(role);
                existingUser.setLastLoginTime(currentTime);
                existingUser.setPermissions(permissionsJson);
                existingUser.setGsddm(gsddm);
                existingUser.setGsdmc(gsdmc);
                
                dbHelper.updateUser(existingUser);
                Log.d(TAG, "用户信息已更新到数据库");
            } else {
                // 创建新用户
                User newUser = new User();
                newUser.setId(permissionInfoRespVO.getUser().getId());
                newUser.setUsername(username);
                newUser.setPassword(encryptedPassword);
                newUser.setSalt(salt);
                newUser.setNickname(nickname);
                newUser.setRole(role);
                newUser.setLastLoginTime(currentTime);
                newUser.setPermissions(permissionsJson);
                newUser.setGsddm(gsddm);
                newUser.setGsdmc(gsdmc);
                
                long userId = dbHelper.addUser(newUser);
                Log.d(TAG, "新用户已添加到数据库，ID: " + userId);
            }
            
            // 同时保存到SharedPreferences中，用于离线登录
/*            SharedPreferences sp = getSharedPreferences(PREF_NAME, MODE_PRIVATE);
            SharedPreferences.Editor editor = sp.edit();
            editor.putString("username", username);
            editor.putString("password", password); // 保存原始密码用于离线登录验证*/
//            editor.apply();
        } catch (Exception e) {
            Log.e(TAG, "保存用户信息到数据库失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    

    
    /**
     * 保存完整的权限信息到SharedPreferences
     */
    private void savePermissionInfoToSharedPreferences(AuthPermissionInfoRespVO permissionInfo) {
        try {
            SharedPreferences sp = getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, MODE_PRIVATE);
            SharedPreferences.Editor editor = sp.edit();
            
            // 使用Gson将整个权限信息对象转换为JSON字符串
            Gson gson = new Gson();
            String permissionInfoJson = gson.toJson(permissionInfo);
            
            // 保存JSON字符串
            editor.putString(SharedPreferencesEnum.PERMISSION_INFO.value, permissionInfoJson);
            editor.putBoolean(SharedPreferencesEnum.IS_OFFLINE_LOGIN.value,false);
            editor.apply();
            
            Log.d(TAG, "权限信息已保存到SharedPreferences");
        } catch (Exception e) {
            Log.e(TAG, "保存权限信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 保存用户信息到SharedPreferences
     */
    /**
     * 检查应用版本
     */
    private void checkAppVersion() {
        RetrofitManager.getInstance(this)
                .getApiService()
                .getAppVersion()
                .enqueue(new Callback<ApiResponse<AppVersionVO>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<AppVersionVO>> call, Response<ApiResponse<AppVersionVO>> response) {
                        if (response.isSuccessful() && response.body() != null && response.body().getData() != null) {
                            AppVersionVO versionInfo = response.body().getData();
                            try {
                                String currentVersion = getPackageManager().getPackageInfo(getPackageName(), 0).versionName;
                                // 使用VersionUtil比较版本号
                                int compareResult = io.dcloud.uniplugin.utils.VersionUtil.compareVersions(versionInfo.getVersion(), currentVersion);
                                if (compareResult > 0) {
                                    // 远程版本大于本地版本，显示更新对话框
                                    showUpdateDialog(versionInfo);
                                }
                            } catch (PackageManager.NameNotFoundException e) {
                                Log.e(TAG, "获取当前版本号失败: " + e.getMessage());
                            }
                        }
                    }

                    @Override
                    public void onFailure(Call<ApiResponse<AppVersionVO>> call, Throwable t) {
                        Log.e(TAG, "检查版本更新失败: " + t.getMessage());
                    }
                });
    }

    /**
     * 显示更新对话框
     */
    private void showUpdateDialog(AppVersionVO versionInfo) {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("发现新版本");
        builder.setMessage("当前有新版本 " + versionInfo.getVersion() + " 可用，是否更新？");

        // 如果是强制更新，不显示取消按钮
        if (versionInfo.isSfqzgx()) {
            builder.setCancelable(false);
        } else {
            builder.setNegativeButton("稍后再说", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    dialog.dismiss();
                }
            });
        }

        builder.setPositiveButton("立即更新", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                // 使用应用内下载器下载新版本
                downloadAndInstallApk(versionInfo.getAppUrl(), versionInfo.isSfqzgx());
            }
        });

        AlertDialog dialog = builder.create();
        dialog.show();
    }
    
    /**
     * 下载并安装APK
     * @param downloadUrl 下载地址
     * @param isForceUpdate 是否强制更新
     */
    private void downloadAndInstallApk(String downloadUrl, boolean isForceUpdate) {
        // 显示下载进度对话框
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("正在下载");
        builder.setCancelable(!isForceUpdate);
        
        // 添加进度条
        final android.widget.ProgressBar progressBar = new android.widget.ProgressBar(this, null, android.R.attr.progressBarStyleHorizontal);
        progressBar.setMax(100);
        progressBar.setProgress(0);
        builder.setView(progressBar);
        
        final AlertDialog progressDialog = builder.create();
        progressDialog.show();
        
        // 创建下载管理器
        AppDownloadManager downloadManager = new AppDownloadManager(this);
        downloadManager.setDownloadStatusListener(new AppDownloadManager.DownloadStatusListener() {
            @Override
            public void onDownloadComplete(Uri fileUri) {
                progressDialog.dismiss();
                Toast.makeText(LoginActivity.this, "下载完成，正在安装...", Toast.LENGTH_SHORT).show();
                if (isForceUpdate) {
                    // 如果是强制更新，安装后关闭应用
                    finish();
                }
            }
            
            @Override
            public void onDownloadFailed(String error) {
                progressDialog.dismiss();
                Toast.makeText(LoginActivity.this, "下载失败: " + error, Toast.LENGTH_LONG).show();
                if (isForceUpdate) {
                    // 如果是强制更新但下载失败，提示用户重试
                    new AlertDialog.Builder(LoginActivity.this)
                            .setTitle("下载失败")
                            .setMessage("应用更新下载失败，请重试")
                            .setPositiveButton("重试", (dialog, which) -> {
                                downloadAndInstallApk(downloadUrl, isForceUpdate);
                            })
                            .setCancelable(false)
                            .show();
                }
            }
            
            @Override
            public void onDownloadProgress(int progress) {
                progressBar.setProgress(progress);
            }
        });
        
        // 开始下载
        downloadManager.downloadApk(downloadUrl, "广东省补充耕地质量验收APP更新", "正在下载新版本");
    }

    private void saveUserInfoToSharedPreferences(LoginData loginData) {
        try {
            SharedPreferences sp = getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, MODE_PRIVATE);
            SharedPreferences.Editor editor = sp.edit();
            
            // 保存用户ID和token,刷新token和过期时间
            editor.putLong(SharedPreferencesEnum.USER_ID.value, loginData.getUserId());
            editor.putString(SharedPreferencesEnum.ACCESS_TOKEN.value, loginData.getAccessToken());
            editor.putString(SharedPreferencesEnum.REFRESH_TOKEN.value, loginData.getRefreshToken());
            editor.putLong(SharedPreferencesEnum.EXPIRE_TIME.value, loginData.getExpiresTime());
            editor.apply();
        } catch (Exception e) {
            Log.e(TAG, "保存用户信息失败: " + e.getMessage());
        }
    }


    /**
     * 显示离线登录对话框
     */
    private void showOfflineLoginDialog(final String username, final String password) {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("网络连接失败");
        builder.setMessage("当前无网络连接，是否需要进行离线登录？");
        builder.setPositiveButton("确定", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                // 用户点击确定，进行离线登录
                offlineLogin(username, password);
            }
        });
        builder.setNegativeButton("取消", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                // 用户点击取消，不进行登录
                dialog.dismiss();
                // 恢复登录按钮状态
                buttonLogin.setText("登录");
                buttonLogin.setEnabled(true);
            }
        });
        builder.setCancelable(false); // 防止用户通过点击对话框外部或返回键取消对话框
        builder.show();
    }


    /**
     * 离线登录
     */
    private void offlineLogin(String username, String password) {
        try {
            // 从数据库中获取用户信息
            DatabaseHelper dbHelper = DatabaseHelper.getInstance(this);
            User user = dbHelper.getUserByUsername(username);
            
            if (user != null) {
                // 验证密码
                String salt = user.getSalt();
                String encryptedPassword = user.getPassword();
                
                if (salt != null && encryptedPassword != null) {
                    // 使用MD5加盐验证密码
                    boolean isPasswordValid = PasswordUtil.verifyPassword(password, salt, encryptedPassword);
                    
                    if (isPasswordValid) {
                        // 更新最后登录时间
                        user.setLastLoginTime(String.valueOf(System.currentTimeMillis()));
                        dbHelper.updateUserLoginInfo(user);
                        
                        // 保存用户名和密码
                        saveCredentials(username, password);
                        // 登录成功
                        showToast("离线登录成功");
                        SharedPreferences sp = getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, MODE_PRIVATE);
                        SharedPreferences.Editor editor = sp.edit();
                        // 保存JSON字符串
                        editor.putLong(SharedPreferencesEnum.USER_ID.value, user.getId());
                        editor.putString(SharedPreferencesEnum.PERMISSION_INFO.value, user.getPermissions());
                        editor.putBoolean(SharedPreferencesEnum.IS_OFFLINE_LOGIN.value,true);
                        editor.apply();

                        // 根据角色决定跳转到不同的主页面
                        Gson gson = new Gson();
                        try {
                            AuthPermissionInfoRespVO permissionInfo = gson.fromJson(user.getPermissions(), AuthPermissionInfoRespVO.class);
                            if (permissionInfo != null && permissionInfo.getRoles() != null) {
                                Set<String> roles = permissionInfo.getRoles();
                                if (roles.contains("cyd")) {
                                    // 如果是采样人员，跳转到采样主页面
                                    navigateToMainActivity();
                                } else if (roles.contains("sysry")) {
                                    // 如果是实验室人员，跳转到实验室主页面
                                    navigateToSYSMainActivity();
                                } else {
                                    // 默认跳转到采样主页面
                                    navigateToMainActivity();
                                }
                                return;
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "解析权限信息失败: " + e.getMessage());
                        }
                        // 解析失败时默认跳转到采样主页面
                        navigateToMainActivity();
                        return;
                    }
                }
            }
            
            // 登录失败
            showToast("用户名或密码错误，离线登录失败");
            // 恢复登录按钮状态
            buttonLogin.setText("登录");
            buttonLogin.setEnabled(true);
            
        } catch (Exception e) {
            Log.e(TAG, "离线登录失败: " + e.getMessage());
            showToast("离线登录失败: " + e.getMessage());
            // 恢复登录按钮状态
            buttonLogin.setText("登录");
            buttonLogin.setEnabled(true);
        }
    }
    
    /**
     * 跳转到主页面
     */
    private void navigateToMainActivity() {
        Intent intent = new Intent(LoginActivity.this, HomeActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    /**
     * 跳转到检测实验室主页面
     */
    private void navigateToSYSMainActivity() {
        Intent intent = new Intent(LoginActivity.this, LabHomeActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }
    
//    /**
//     * 显示加载提示
//     */
//    private void showLoading(String message) {
//        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
//    }
    
    /**
     * 隐藏加载提示
     */
    private void hideLoading() {
        // 可以在这里实现隐藏加载提示的逻辑
    }
    
    /**
     * 显示提示信息
     */
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
}
<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <option name="testRunner" value="GRADLE" />
        <option name="distributionType" value="DEFAULT_WRAPPED" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="gradleJvm" value="Android Studio default JDK" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$" />
            <option value="$PROJECT_DIR$/app" />
            <option value="$PROJECT_DIR$/uniplugin_camera" />
            <option value="$PROJECT_DIR$/uniplugin_component" />
            <option value="$PROJECT_DIR$/uniplugin_module" />
            <option value="$PROJECT_DIR$/uniplugin_printQr" />
            <option value="$PROJECT_DIR$/uniplugin_qrcode" />
            <option value="$PROJECT_DIR$/uniplugin_richalert" />
            <option value="$PROJECT_DIR$/uniplugin_swdc" />
          </set>
        </option>
      </GradleProjectSettings>
    </option>
  </component>
</project>
package io.dcloud.uniplugin.http.api;

import io.dcloud.uniplugin.model.ApiResponse;
import io.dcloud.uniplugin.model.YPLZ;
import io.dcloud.uniplugin.model.YplzPageResponse;
import io.dcloud.uniplugin.model.YplzqdCreateRequest;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * 样品流转API服务接口
 */
public interface SampleFlowService {

    
    /**
     * 创建样品流转清单
     */
    @POST("bcgd/sample/yplzqd/create")
    Call<ApiResponse<Long>> createSampleFlow(@Body YplzqdCreateRequest request);
    
    /**
     * 获取样品列表
     */
    @GET("bcgd/sample/yplzqd/page")
    Call<ApiResponse<YplzPageResponse<YPLZ>>> getSampleListByBatchId(
        @Query("pageNo") Integer pageNo,
        @Query("pageSize") Integer pageSize,
        @Query("sampleBatchId") Long sampleBatchId,
        @Query("sampleCode") String sampleCode,
        @Query("sampleName") String sampleName,
        @Query("sampleType") String sampleType,
        @Query("status") Long status,
        @Query("supplementProjectName") String supplementProjectName
    );
    
    /**
     * 更新样品
     */
    @POST("bcgd/sample/yplzqd/update")
    Call<ApiResponse<Boolean>> updateSample(@Body YPLZ sample);
    
    /**
     * 删除样品
     */
    @GET("bcgd/sample/yplzqd/delete")
    Call<ApiResponse<Boolean>> deleteSample(@Query("id") Long id);
} 
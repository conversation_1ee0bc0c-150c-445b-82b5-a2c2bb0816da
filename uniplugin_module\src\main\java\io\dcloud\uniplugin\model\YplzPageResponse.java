package io.dcloud.uniplugin.model;

import java.util.List;

/**
 * 样品批次分页响应模型
 * @param <T> 列表项类型
 */
public class YplzPageResponse<T> {
    /**
     * 当前页码
     */
    private Long pageNo;
    
    /**
     * 每页条数
     */
    private Long pageSize;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 总页数
     */
    private Long totalPages;
    
    /**
     * 数据记录列表
     */
    private List<T> list;

    public Long getPageNo() {
        return pageNo;
    }

    public void setPageNo(Long pageNo) {
        this.pageNo = pageNo;
    }

    public Long getPageSize() {
        return pageSize;
    }

    public void setPageSize(Long pageSize) {
        this.pageSize = pageSize;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Long getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(Long totalPages) {
        this.totalPages = totalPages;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    // 计算总页数
    public Long getPages() {
        if (total == null || pageSize == null || pageSize == 0) {
            return 0L;
        }
        return (total + pageSize - 1) / pageSize;
    }
} 
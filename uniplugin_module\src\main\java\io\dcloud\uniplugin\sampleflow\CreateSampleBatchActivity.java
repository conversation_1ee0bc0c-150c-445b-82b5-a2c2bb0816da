package io.dcloud.uniplugin.sampleflow;

import android.annotation.SuppressLint;
import android.app.ProgressDialog;
import android.content.SharedPreferences;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewParent;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.bumptech.glide.Glide;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.dcloud.uniplugin.enums.SharedPreferencesEnum;
import io.dcloud.uniplugin.http.RetrofitManager;
import io.dcloud.uniplugin.http.api.FileUploadService;
import io.dcloud.uniplugin.model.ApiResponse;
import io.dcloud.uniplugin.model.DetectionOrg;
import io.dcloud.uniplugin.model.ProjectVo;
import io.dcloud.uniplugin.model.YplzBatch;
import io.dcloud.uniplugin.model.YplzCreateRequest;
import io.dcloud.uniplugin.view.SignatureView;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import uni.dcloud.io.uniplugin_module.R;

/**
 * 创建/编辑样品批次页面
 */
public class CreateSampleBatchActivity extends AppCompatActivity {

    private static final String TAG = "CreateSampleBatchActivity";

    private Spinner spinnerProject; // 项目选择
    private Spinner spinnerBatchType; // 批次类型选择
    private EditText editTextSendOrg;
    private EditText editTextSenderName;
    private EditText editTextSenderPhone;
    private EditText editTextDeliveryType;
    private EditText editTextDeliveryMessage; // 运送信息
    private Spinner spinnerReceiveOrg; // 接样单位（检测机构）下拉选择
    private Button buttonCancel;
    private Button buttonSubmit;
    private Button buttonClearSignature;
    private Button buttonConfirmSignature;
    private Button buttonRenewSignature;
    private SignatureView signatureViewSender;
    private TextView textViewSignatureStatus;
    private LinearLayout layoutSignaturePreview;
    private FrameLayout layoutSignatureArea;
    private LinearLayout layoutSignatureButtons;
    private ImageView imageViewSignaturePreview;
    private TextView textViewSignatureUrl;
    
    // 送样信息输入区域
    private LinearLayout layoutSenderInfo;

    private String signatureUrl; // 保存签名图片URL
    private FileUploadService fileUploadService;
    private ProgressDialog progressDialog;
    
    private boolean isEditMode = false; // 是否为编辑模式
    private Long batchId; // 编辑模式下的批次ID
    
    // 检测机构相关
    private List<DetectionOrg> detectionOrgList = new ArrayList<>();
    private Map<String, Long> orgNameIdMap = new HashMap<>();
    private Long selectedOrgId; // 选中的检测机构ID
    private String selectedOrgName; // 选中的检测机构名称
    
    // 项目相关
    private List<ProjectVo> projectList = new ArrayList<>();
    private Map<String, String> projectNameNumberMap = new HashMap<>(); // 项目名称到编号的映射
    private String selectedProjectNumber; // 选中的项目编号
    private String selectedProjectName; // 选中的项目名称
    
    // 批次类型相关
    private Integer selectedBatchType; // 选中的批次类型（县0/市1/省2）
    private String selectedBatchTypeName; // 选中的批次类型名称

    @SuppressLint("ClickableViewAccessibility")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_create_sample_batch);

        // 检查是否为编辑模式
        if (getIntent() != null) {
            isEditMode = getIntent().getBooleanExtra("isEdit", false);
            if (isEditMode) {
                batchId = getIntent().getLongExtra("batchId", -1);
                
                // 如果ID无效，退出编辑模式
                if (batchId == -1) {
                    isEditMode = false;
                    Toast.makeText(this, "无效的批次ID", Toast.LENGTH_SHORT).show();
                    finish();
                    return;
                }
            }
        }

        // 设置ActionBar标题和返回按钮
        setTitle(isEditMode ? "编辑样品批次" : "创建样品批次");
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }

        // 初始化文件上传服务
        fileUploadService = new FileUploadService(this);
        
        // 初始化进度对话框
        progressDialog = new ProgressDialog(this);
        progressDialog.setMessage("正在上传签名...");
        progressDialog.setCancelable(false);

        initViews();
        setupListeners();
        
        // 设置签名视图的父容器为可点击，防止滚动冲突
        ViewParent parentView = findViewById(R.id.signatureViewSender).getParent();
        if (parentView instanceof View) {
            View signatureContainer = (View) parentView;
            signatureContainer.setOnTouchListener((v, event) -> {
                // 拦截父视图的触摸事件，但允许子视图处理
                v.onTouchEvent(event);
                return true;
            });
        }
        
        // 初始化UI状态：显示签名区域，隐藏预览区域
        showSignatureArea(true);
        layoutSignaturePreview.setVisibility(View.GONE);
        updateSignatureStatus("请在下方区域签名");
        
        // 无论是否为编辑模式，都隐藏送样信息（由后台自动填写）
        layoutSenderInfo.setVisibility(View.GONE);
        
        // 预填充表单数据（编辑模式）
        if (isEditMode) {
            fillFormWithData();
            buttonSubmit.setText("更新批次"); // 更改提交按钮文本
        }
        
        // 加载检测机构列表
        loadDetectionOrgs();
        
        // 加载项目列表
        loadProjectList();
        
        // 设置批次类型下拉框
        setupBatchTypeSpinner();
    }

    /**
     * 加载项目列表
     */
    private void loadProjectList() {
        RetrofitManager.getInstance(this)
                .getDccyService()
                .getProjectList()
                .enqueue(new Callback<ApiResponse<List<ProjectVo>>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<List<ProjectVo>>> call, Response<ApiResponse<List<ProjectVo>>> response) {
                        if (response.isSuccessful() && response.body() != null && response.body().getData() != null) {
                            projectList = response.body().getData();
                            Log.d(TAG, "项目列表加载成功，共 " + projectList.size() + " 个项目");
                            setupProjectSpinner();
                        } else {
                            Toast.makeText(CreateSampleBatchActivity.this, "获取项目列表失败", Toast.LENGTH_SHORT).show();
                            Log.e(TAG, "获取项目列表失败: " + (response.body() != null ? response.body().getMsg() : "响应为空"));
                        }
                    }

                    @Override
                    public void onFailure(Call<ApiResponse<List<ProjectVo>>> call, Throwable t) {
                        Toast.makeText(CreateSampleBatchActivity.this, "网络请求失败: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                        Log.e(TAG, "加载项目列表失败", t);
                    }
                });
    }
    
    /**
     * 设置项目下拉框
     */
    private void setupProjectSpinner() {
        List<String> projectNames = new ArrayList<>();
        projectNames.add("请选择项目");
        projectNameNumberMap.clear();
        
        for (ProjectVo project : projectList) {
            String displayName = project.getProjectName() + " (" + project.getProjectNumber() + ")";
            projectNames.add(displayName);
            projectNameNumberMap.put(displayName, project.getProjectNumber());
            Log.d(TAG, "添加项目: " + displayName + " -> " + project.getProjectNumber());
        }
        
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, 
                android.R.layout.simple_spinner_item, projectNames);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerProject.setAdapter(adapter);
        
        // 如果已经有预设的项目编号，进行选择
        if (!TextUtils.isEmpty(selectedProjectNumber)) {
            selectProjectByNumber(selectedProjectNumber);
        }
    }
    
    /**
     * 根据项目编号选择对应的项目
     */
    private void selectProjectByNumber(String projectNumber) {
        if (TextUtils.isEmpty(projectNumber)) {
            Log.d(TAG, "传入的项目编号为空");
            return;
        }
        
        if (projectList == null || projectList.isEmpty()) {
            // 如果项目列表为空，等项目列表加载完后再设置
            Log.d(TAG, "项目列表为空，暂存项目编号: " + projectNumber);
            return;
        }
        
        Log.d(TAG, "尝试在" + projectList.size() + "个项目中查找编号: " + projectNumber);
        boolean found = false;
        
        for (int i = 0; i < projectList.size(); i++) {
            ProjectVo project = projectList.get(i);
            if (projectNumber.equals(project.getProjectNumber())) {
                int position = i + 1; // +1 是因为第一项是"请选择项目"
                String displayName = project.getProjectName() + " (" + project.getProjectNumber() + ")";
                Log.d(TAG, "找到匹配的项目: " + displayName + ", 位置: " + position);
                spinnerProject.setSelection(position);
                selectedProjectNumber = projectNumber;
                selectedProjectName = displayName;
                found = true;
                break;
            }
        }
        
        if (!found) {
            Log.e(TAG, "未找到编号为 " + projectNumber + " 的项目");
        }
    }
    
    /**
     * 设置批次类型下拉框
     */
    private void setupBatchTypeSpinner() {
        List<String> batchTypeNames = new ArrayList<>();
        batchTypeNames.add("请选择批次类型");
        batchTypeNames.add("县级");
        batchTypeNames.add("市级");
        batchTypeNames.add("省级");
        
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, 
                android.R.layout.simple_spinner_item, batchTypeNames);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerBatchType.setAdapter(adapter);
        
        // 如果已经有预设的批次类型，进行选择
        if (selectedBatchType != null) {
            selectBatchTypeByValue(selectedBatchType);
        }
        
        Log.d(TAG, "批次类型下拉框设置完成");
    }
    
    /**
     * 根据批次类型值选择对应的选项
     * @param batchType 批次类型值（0=县级, 1=市级, 2=省级）
     */
    private void selectBatchTypeByValue(Integer batchType) {
        if (batchType == null || batchType < 0 || batchType > 2) {
            Log.d(TAG, "传入的批次类型值无效: " + batchType);
            return;
        }
        
        int position = batchType + 1; // +1 是因为第一项是"请选择批次类型"
        spinnerBatchType.setSelection(position);
        selectedBatchType = batchType;
        
        String[] typeNames = {"县级", "市级", "省级"};
        selectedBatchTypeName = typeNames[batchType];
        
        Log.d(TAG, "选择批次类型: " + selectedBatchTypeName + " (值: " + selectedBatchType + ", 位置: " + position + ")");
    }

    /**
     * 加载检测机构列表
     */
    private void loadDetectionOrgs() {
        RetrofitManager.getInstance(this)
                .getLabService()
                .getAllDetectionOrgs()
                .enqueue(new Callback<ApiResponse<List<DetectionOrg>>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<List<DetectionOrg>>> call, Response<ApiResponse<List<DetectionOrg>>> response) {
                        if (response.isSuccessful() && response.body() != null && response.body().getData() != null) {
                            detectionOrgList = response.body().getData();
                            Log.d(TAG, "检测机构列表加载成功，共 " + detectionOrgList.size() + " 个机构");
                            setupOrgSpinner();
                            
                            // 如果是编辑模式，设置选中的机构
                            if (isEditMode && getIntent() != null) {
                                Long orgId = getIntent().getLongExtra("receiveOrganizationId", -1);
                                Log.d(TAG, "编辑模式下的接样单位ID: " + orgId);
                                if (orgId != -1) {
                                    selectOrgById(orgId);
                                }
                            }
                        } else {
                            Toast.makeText(CreateSampleBatchActivity.this, "获取检测机构列表失败", Toast.LENGTH_SHORT).show();
                            Log.e(TAG, "获取检测机构列表失败: " + (response.body() != null ? response.body().getMsg() : "响应为空"));
                        }
                    }

                    @Override
                    public void onFailure(Call<ApiResponse<List<DetectionOrg>>> call, Throwable t) {
                        Toast.makeText(CreateSampleBatchActivity.this, "网络请求失败: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                        Log.e(TAG, "加载检测机构失败", t);
                    }
                });
    }
    
    /**
     * 设置检测机构下拉框
     */
    private void setupOrgSpinner() {
        List<String> orgNames = new ArrayList<>();
        orgNames.add("请选择检测机构");
        orgNameIdMap.clear();
        
        for (DetectionOrg org : detectionOrgList) {
            orgNames.add(org.getName());
            orgNameIdMap.put(org.getName(), org.getId());
            Log.d(TAG, "添加检测机构: " + org.getName() + " (ID: " + org.getId() + ")");
        }
        
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, 
                android.R.layout.simple_spinner_item, orgNames);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerReceiveOrg.setAdapter(adapter);
        
        // 如果已经有选中的机构ID，重新选择
        if (selectedOrgId != null) {
            selectOrgById(selectedOrgId);
        }
    }
    
    /**
     * 根据ID选择对应的检测机构
     */
    private void selectOrgById(Long orgId) {
        if (orgId == null || orgId == -1) {
            Log.d(TAG, "传入的机构ID无效: " + orgId);
            return;
        }
        
        // 先保存ID，以免后续需要使用
        selectedOrgId = orgId;
        
        if (detectionOrgList == null || detectionOrgList.isEmpty()) {
            // 如果列表为空，等机构列表加载完后再设置
            Log.d(TAG, "检测机构列表为空，暂存ID: " + orgId);
            return;
        }
        
        Log.d(TAG, "尝试在" + detectionOrgList.size() + "个机构中查找ID: " + orgId);
        boolean found = false;
        
        for (int i = 0; i < detectionOrgList.size(); i++) {
            DetectionOrg org = detectionOrgList.get(i);
            if (org.getId().equals(orgId)) {
                int position = i + 1; // +1 是因为第一项是"请选择检测机构"
                Log.d(TAG, "找到匹配的检测机构: " + org.getName() + ", 位置: " + position);
                spinnerReceiveOrg.setSelection(position);
                selectedOrgId = orgId;
                selectedOrgName = org.getName();
                found = true;
                break;
            }
        }
        
        if (!found) {
            Log.e(TAG, "未找到ID为 " + orgId + " 的检测机构");
        }
    }

    /**
     * 预填充表单数据
     */
    private void fillFormWithData() {
        // 从Intent获取传递的数据
        if (getIntent() != null) {
            // 设置表单数据
            String batchCode = getIntent().getStringExtra("batchCode"); // 获取批次编号（项目编号）
            Integer batchType = getIntent().getIntExtra("batchType", -1); // 获取批次类型
            signatureUrl = getIntent().getStringExtra("senderSignature");
            String deliveryType = getIntent().getStringExtra("deliveryType");
            String deliveryMessage = getIntent().getStringExtra("deliveryMessage");
            Long receiveOrgId = getIntent().getLongExtra("receiveOrganizationId", -1);
            
            Log.d(TAG, "填充表单数据 - 批次编号: " + batchCode + ", 批次类型: " + batchType + ", 接样单位ID: " + receiveOrgId);
            
            // 填充表单
            
            if (!TextUtils.isEmpty(deliveryType)) {
                editTextDeliveryType.setText(deliveryType);
            }
            
            if (!TextUtils.isEmpty(deliveryMessage)) {
                editTextDeliveryMessage.setText(deliveryMessage);
            }
            
            // 如果已有签名，显示签名预览
            if (!TextUtils.isEmpty(signatureUrl)) {
                showSignaturePreview(signatureUrl);
                showSignatureArea(false);
                updateSignatureStatus("已有签名");
            }
            
            // 设置接样单位ID（会在loadDetectionOrgs完成后进行选择）
            if (receiveOrgId != -1) {
                selectedOrgId = receiveOrgId;
                Log.d(TAG, "预存接样单位ID: " + receiveOrgId);
            }
            
            // 设置项目编号（会在loadProjectList完成后进行选择）
            if (!TextUtils.isEmpty(batchCode)) {
                selectedProjectNumber = batchCode;
                Log.d(TAG, "预存项目编号: " + batchCode);
            }
            
            // 设置批次类型（会在setupBatchTypeSpinner完成后进行选择）
            if (batchType != -1) {
                selectedBatchType = batchType;
                Log.d(TAG, "预存批次类型: " + batchType);
                // 如果批次类型下拉框已经设置完成，立即选择
                selectBatchTypeByValue(batchType);
            }
        }
    }

    private void initViews() {
        spinnerProject = findViewById(R.id.spinnerProject);
        spinnerBatchType = findViewById(R.id.spinnerBatchType);
        editTextSendOrg = findViewById(R.id.editTextSendOrg);
        editTextSenderName = findViewById(R.id.editTextSenderName);
        editTextSenderPhone = findViewById(R.id.editTextSenderPhone);
        editTextDeliveryType = findViewById(R.id.editTextDeliveryType);
        editTextDeliveryMessage = findViewById(R.id.editTextDeliveryMessage);
        spinnerReceiveOrg = findViewById(R.id.spinnerReceiveOrg);
        buttonCancel = findViewById(R.id.buttonCancel);
        buttonSubmit = findViewById(R.id.buttonSubmit);
        buttonClearSignature = findViewById(R.id.buttonClearSignature);
        buttonConfirmSignature = findViewById(R.id.buttonConfirmSignature);
        buttonRenewSignature = findViewById(R.id.buttonRenewSignature);
        signatureViewSender = findViewById(R.id.signatureViewSender);
        textViewSignatureStatus = findViewById(R.id.textViewSignatureStatus);
        layoutSignaturePreview = findViewById(R.id.layoutSignaturePreview);
        layoutSignatureArea = findViewById(R.id.layoutSignatureArea);
        layoutSignatureButtons = findViewById(R.id.layoutSignatureButtons);
        imageViewSignaturePreview = findViewById(R.id.imageViewSignaturePreview);
        textViewSignatureUrl = findViewById(R.id.textViewSignatureUrl);
        layoutSenderInfo = findViewById(R.id.layoutSenderInfo);
    }

    private void setupListeners() {
        // 取消按钮
        buttonCancel.setOnClickListener(v -> finish());

        // 提交按钮
        buttonSubmit.setOnClickListener(v -> {
            if (isEditMode) {
                updateBatch();
            } else {
                submitBatch();
            }
        });

        // 清除签名按钮
        buttonClearSignature.setOnClickListener(v -> {
            if (signatureViewSender != null) {
                try {
                    // 检查签名视图尺寸是否有效
                    if (signatureViewSender.getWidth() > 0 && signatureViewSender.getHeight() > 0) {
                        signatureViewSender.clear();
                        updateSignatureStatus("请在签名区域签名");
                    } else {
                        // 如果尺寸无效，使用post延迟执行清除操作
                        signatureViewSender.post(() -> {
                            try {
                                if (signatureViewSender != null && 
                                    signatureViewSender.getWidth() > 0 && 
                                    signatureViewSender.getHeight() > 0) {
                                    signatureViewSender.clear();
                                    updateSignatureStatus("请在签名区域签名");
                                }
                            } catch (Exception e) {
                                Log.e(TAG, "延迟清除签名失败", e);
                            }
                        });
                    }
                } catch (Exception e) {
                    Log.e(TAG, "清除签名失败", e);
                    Toast.makeText(CreateSampleBatchActivity.this, "清除签名失败，请重试", Toast.LENGTH_SHORT).show();
                }
            }
        });
        
        // 接样单位选择监听
        spinnerReceiveOrg.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position > 0) { // 跳过"请选择检测机构"选项
                    String orgName = (String) parent.getItemAtPosition(position);
                    selectedOrgId = orgNameIdMap.get(orgName);
                    selectedOrgName = orgName;
                    Log.d(TAG, "已选择检测机构: " + selectedOrgName + " (ID: " + selectedOrgId + ")");
                } else {
                    selectedOrgId = null;
                    selectedOrgName = null;
                    Log.d(TAG, "未选择检测机构");
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                selectedOrgId = null;
                selectedOrgName = null;
                Log.d(TAG, "清除检测机构选择");
            }
        });
        
        // 项目选择监听
        spinnerProject.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position > 0) { // 跳过"请选择项目"选项
                    String displayName = (String) parent.getItemAtPosition(position);
                    selectedProjectNumber = projectNameNumberMap.get(displayName);
                    selectedProjectName = displayName;
                    Log.d(TAG, "已选择项目: " + selectedProjectName + " (编号: " + selectedProjectNumber + ")");
                } else {
                    selectedProjectNumber = null;
                    selectedProjectName = null;
                    Log.d(TAG, "未选择项目");
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                selectedProjectNumber = null;
                selectedProjectName = null;
                Log.d(TAG, "清除项目选择");
            }
        });
        
        // 批次类型选择监听
        spinnerBatchType.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position > 0) { // 跳过"请选择批次类型"选项
                    selectedBatchType = position - 1; // 0=县级, 1=市级, 2=省级
                    selectedBatchTypeName = (String) parent.getItemAtPosition(position);
                    Log.d(TAG, "已选择批次类型: " + selectedBatchTypeName + " (值: " + selectedBatchType + ")");
                } else {
                    selectedBatchType = null;
                    selectedBatchTypeName = null;
                    Log.d(TAG, "未选择批次类型");
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                selectedBatchType = null;
                selectedBatchTypeName = null;
                Log.d(TAG, "清除批次类型选择");
            }
        });
        
        // 确认签名按钮
        buttonConfirmSignature.setOnClickListener(v -> {
            if (signatureViewSender == null || signatureViewSender.isEmpty()) {
                Toast.makeText(this, "请先完成签名", Toast.LENGTH_SHORT).show();
                return;
            }
            
            // 获取签名图片并上传
            uploadSignature();
        });
        
        // 重新签名按钮
        buttonRenewSignature.setOnClickListener(v -> {
            // 显示签名区域
            showSignatureArea(true);
            // 隐藏预览区域
            layoutSignaturePreview.setVisibility(View.GONE);
            // 更新状态
            updateSignatureStatus("请在签名区域重新签名");
            
            // 清除旧的URL
            signatureUrl = null;
            
            // 使用post确保在视图可见并测量完成后再清除签名
            signatureViewSender.post(() -> {
                try {
                    // 在UI线程中安全地清除签名
                    if (signatureViewSender != null && signatureViewSender.getWidth() > 0 && signatureViewSender.getHeight() > 0) {
                        signatureViewSender.clear();
                    }
                } catch (Exception e) {
                    Log.e(TAG, "清除签名失败", e);
                    Toast.makeText(CreateSampleBatchActivity.this, "重置签名失败，请重试", Toast.LENGTH_SHORT).show();
                }
            });
        });
    }
    
    /**
     * 上传签名图片
     */
    private void uploadSignature() {
        if (signatureViewSender == null) {
            return;
        }
        
        // 获取签名图片
        Bitmap signatureBitmap = signatureViewSender.getSignatureBitmap();
        if (signatureBitmap == null) {
            Toast.makeText(this, "获取签名图片失败", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 显示进度对话框
        progressDialog.show();
        updateSignatureStatus("正在上传签名...");
        
        // 上传签名图片
        fileUploadService.uploadSignature(signatureBitmap, new FileUploadService.FileUploadCallback() {
            @Override
            public void onSuccess(String fileUrl) {
                // 上传成功
                runOnUiThread(() -> {
                    progressDialog.dismiss();
                    signatureUrl = fileUrl;
                    updateSignatureStatus("签名已上传");
                    showSignaturePreview(fileUrl);
                    Toast.makeText(CreateSampleBatchActivity.this, "签名上传成功", Toast.LENGTH_SHORT).show();
                    
                    // 隐藏签名区域，显示预览区域
                    showSignatureArea(false);
                });
            }

            @Override
            public void onFailure(String errorMsg) {
                // 上传失败
                runOnUiThread(() -> {
                    progressDialog.dismiss();
                    updateSignatureStatus("签名上传失败，请重试");
                    Toast.makeText(CreateSampleBatchActivity.this, "签名上传失败：" + errorMsg, Toast.LENGTH_SHORT).show();
                });
            }
        });
    }
    
    /**
     * 显示签名预览
     * @param fileUrl 签名图片URL
     */
    private void showSignaturePreview(String fileUrl) {
        if (TextUtils.isEmpty(fileUrl)) {
            return;
        }
        
        // 显示签名预览区域
        layoutSignaturePreview.setVisibility(View.VISIBLE);
        
        // 加载签名图片
        Glide.with(this)
                .load(fileUrl)
                .into(imageViewSignaturePreview);
        
        // 保存但不显示URL
        textViewSignatureUrl.setText("URL: " + fileUrl);
    }
    
    /**
     * 控制签名区域的显示和隐藏
     * @param show true显示签名区域，false隐藏签名区域
     */
    private void showSignatureArea(boolean show) {
        if (show) {
            layoutSignatureArea.setVisibility(View.VISIBLE);
            layoutSignatureButtons.setVisibility(View.VISIBLE);
        } else {
            layoutSignatureArea.setVisibility(View.GONE);
            layoutSignatureButtons.setVisibility(View.GONE);
        }
    }
    
    /**
     * 更新签名状态提示
     * @param status 状态文本
     */
    private void updateSignatureStatus(String status) {
        if (textViewSignatureStatus != null) {
            textViewSignatureStatus.setText(status);
        }
    }

    /**
     * 提交样品信息
     */
    private void submitBatch() {
        // 验证必填字段
        if (!validateForm()) {
            return;
        }

        // 构建请求对象
        YplzCreateRequest request = new YplzCreateRequest();
        
        // 设置批次编号（项目编号）
        if (!TextUtils.isEmpty(selectedProjectNumber)) {
            request.setBatchCode(selectedProjectNumber);
            Log.d(TAG, "设置批次编号: " + selectedProjectNumber);
        }
        
        // 设置批次类型
        if (selectedBatchType != null) {
            request.setBatchType(selectedBatchType);
            Log.d(TAG, "设置批次类型: " + selectedBatchTypeName + " (值: " + selectedBatchType + ")");
        }
        
        // 送样信息由后台自动填写，不再需要从界面获取
        
        // 从SharedPreferences获取当前用户ID并设置为senderId
        try {
            SharedPreferences sp = getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, MODE_PRIVATE);
            long userId = sp.getLong(SharedPreferencesEnum.USER_ID.value, -1L);
            if (userId != -1L) {
                request.setSenderId(userId);
                Log.d(TAG, "设置送样人ID: " + userId);
            } else {
                Log.w(TAG, "未找到当前用户ID，使用默认值");
            }
        } catch (Exception e) {
            Log.e(TAG, "获取用户ID时出错", e);
        }
        
        // 设置接样单位信息
        if (selectedOrgId != null) {
            request.setReceiveOrganizationId(selectedOrgId);
            request.setReceiveOrganization(selectedOrgName);
            Log.d(TAG, "设置接样单位: " + selectedOrgName + " (ID: " + selectedOrgId + ")");
        }
        
        // 设置配送信息
        request.setDeliveryType(editTextDeliveryType.getText().toString().trim());
        request.setDeliveryMessage(editTextDeliveryMessage.getText().toString().trim());
        
        // 设置签名URL
        request.setSenderSignature(signatureUrl);

        // 显示进度对话框
        progressDialog.show();

        // 发起网络请求
        RetrofitManager.getInstance(this)
                .getSampleBatchServiceApi()
                .createSampleBatch(request)
                .enqueue(new Callback<ApiResponse<Long>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<Long>> call, Response<ApiResponse<Long>> response) {
                        progressDialog.dismiss();

                        if (response.isSuccessful() && response.body() != null && response.body().getData() != null) {
                            Long sampleId = response.body().getData();
                            if (sampleId != null) {
                                Toast.makeText(CreateSampleBatchActivity.this, "样品信息提交成功", Toast.LENGTH_SHORT).show();
                                
                                // 返回成功结果
                                setResult(RESULT_OK);
                                finish();
                            } else {
                                Toast.makeText(CreateSampleBatchActivity.this, "提交失败：未返回样品ID", Toast.LENGTH_SHORT).show();
                            }
                        } else {
                            String errorMsg = "提交失败";
                            if (response.body() != null) {
                                errorMsg = response.body().getMsg();
                            }
                            Toast.makeText(CreateSampleBatchActivity.this, errorMsg, Toast.LENGTH_SHORT).show();
                        }
                    }

                    @Override
                    public void onFailure(Call<ApiResponse<Long>> call, Throwable t) {
                        progressDialog.dismiss();
                        Toast.makeText(CreateSampleBatchActivity.this, "网络请求失败: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                        Log.e(TAG, "提交样品信息失败", t);
                    }
                });
    }

    /**
     * 更新批次信息
     */
    private void updateBatch() {
        // 验证必填字段
        if (!validateForm()) {
            return;
        }

        // 构建批次对象
        YplzBatch batch = new YplzBatch();
        batch.setId(batchId);
        
        // 设置批次类型
        if (selectedBatchType != null) {
            batch.setBatchType(selectedBatchType);
            Log.d(TAG, "更新批次类型: " + selectedBatchTypeName + " (值: " + selectedBatchType + ")");
        }
        
        // 不再从界面获取送样信息，保留系统原有数据
        
        // 设置接样单位信息
        if (selectedOrgId != null) {
            batch.setReceiveOrganizationId(selectedOrgId);
            batch.setReceiveOrganization(selectedOrgName);
            Log.d(TAG, "更新接样单位: " + selectedOrgName + " (ID: " + selectedOrgId + ")");
        }
        
        batch.setDeliveryType(editTextDeliveryType.getText().toString().trim());
        batch.setDeliveryMessage(editTextDeliveryMessage.getText().toString().trim());
        batch.setSenderSignature(signatureUrl);

        // 显示进度对话框
        progressDialog.setMessage("正在更新批次信息...");
        progressDialog.show();

        // 发起网络请求
        RetrofitManager.getInstance(this)
                .getSampleBatchServiceApi()
                .updateSampleBatch(batch)
                .enqueue(new Callback<ApiResponse<Boolean>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<Boolean>> call, Response<ApiResponse<Boolean>> response) {
                        progressDialog.dismiss();

                        try {
                            if (response.isSuccessful() && response.body() != null) {
                                // 检查响应码是否为0（成功）
                                if (response.body().getCode() == 0) {
                                    // 不管data是什么，只要code=0就认为成功
                                    Toast.makeText(CreateSampleBatchActivity.this, "批次更新成功", Toast.LENGTH_SHORT).show();
                                    
                                    // 返回成功结果
                                    setResult(RESULT_OK);
                                    finish();
                                } else {
                                    // 获取错误信息
                                    String errorMsg = response.body().getMsg();
                                    if (TextUtils.isEmpty(errorMsg)) {
                                        errorMsg = "更新失败：" + response.body().getCode();
                                    }
                                    Toast.makeText(CreateSampleBatchActivity.this, errorMsg, Toast.LENGTH_SHORT).show();
                                }
                            } else {
                                Toast.makeText(CreateSampleBatchActivity.this, "更新失败：网络请求错误", Toast.LENGTH_SHORT).show();
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "处理响应时发生异常", e);
                            Toast.makeText(CreateSampleBatchActivity.this, "更新失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                        }
                    }

                    @Override
                    public void onFailure(Call<ApiResponse<Boolean>> call, Throwable t) {
                        progressDialog.dismiss();
                        Toast.makeText(CreateSampleBatchActivity.this, "网络请求失败: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                        Log.e(TAG, "更新批次信息失败", t);
                    }
                });
    }
    
    /**
     * 验证表单
     */
    private boolean validateForm() {
        // 验证项目选择
        if (TextUtils.isEmpty(selectedProjectNumber)) {
            Toast.makeText(this, "请选择项目", Toast.LENGTH_SHORT).show();
            spinnerProject.requestFocus();
            return false;
        }
        
        // 验证批次类型选择
        if (selectedBatchType == null) {
            Toast.makeText(this, "请选择批次类型", Toast.LENGTH_SHORT).show();
            spinnerBatchType.requestFocus();
            return false;
        }
        
        // 验证签名
        if (TextUtils.isEmpty(signatureUrl)) {
            Toast.makeText(this, "请先完成签名", Toast.LENGTH_SHORT).show();
            return false;
        }
        
        // 验证检测机构
        if (selectedOrgId == null) {
            Toast.makeText(this, "请选择接样单位（检测机构）", Toast.LENGTH_SHORT).show();
            spinnerReceiveOrg.requestFocus();
            return false;
        }
        
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (progressDialog != null && progressDialog.isShowing()) {
            progressDialog.dismiss();
        }
    }
} 
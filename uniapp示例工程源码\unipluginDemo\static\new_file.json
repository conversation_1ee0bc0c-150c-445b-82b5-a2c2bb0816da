{"id": "__UNI__09EF501", "name": "掌上喵", "version": {"name": "1.4.40", "code": 1440}, "description": "", "launch_path": "__uniappview.html", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"OAuth": {"description": "OAuth"}, "Share": {"description": "plus.share.*"}, "VideoPlayer": {"description": "VideoPlayer"}, "Push": {"description": "<PERSON><PERSON>"}, "UniNView": {"description": "UniNView"}, "Stream": {"description": "plus.stream.*"}, "Confusion": {"description": "Resource confusion"}, "Audio": {"description": "plus.audio.*"}, "Accelerometer": {"description": "plus.accelerometer.*"}, "Barcode": {"description": "plus.barcode.*"}, "Cache": {"description": "plus.cache.*"}, "Camera": {"description": "plus.camera.*"}, "Console": {"description": "plus.console.*"}, "Device": {"description": "plus.device.*"}, "Downloader": {"description": "plus.downloader.*"}, "File": {"description": "plus.io.*"}, "Gallery": {"description": "plus.gallery.*"}, "Geolocation": {"description": "plus.geolocation.*"}, "Invocation": {"description": "plus.android.*"}, "NativeObj": {"description": "plus.nativeObj.*"}, "NativeUI": {"description": "plus.nativeUI.*"}, "Navigator": {"description": "plus.navigator.*"}, "Orientation": {"description": "plus.orientation.*"}, "Proximity": {"description": "plus.proximity.*"}, "Runtime": {"description": "plus.runtime.*"}, "Storage": {"description": "plus.storage.*"}, "Uploader": {"description": "plus.uploader.*"}, "Webview": {"description": "plus.webview.*"}, "XMLHttpRequest": {"description": "plus.net.*"}, "Zip": {"description": "plus.zip.*"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": false, "delay": 0}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview", "uni-app": "auto"}, "statusbar": {"immersed": "supportedDevice", "style": "dark", "background": "#F7F7F7"}, "compatible": {"ignoreVersion": true}, "usingComponents": true, "allowsInlineMediaPlayback": true, "safearea": {"background": "#ffffff", "bottom": {"offset": "auto"}}, "uni-app": {"compilerVersion": "2.4.6", "control": "v8", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}}, "tabBar": {"borderStyle": "rgba(0,0,0,0.4)", "backgroundColor": "#ffffff", "list": [{"pagePath": "pages/mall/mall"}, {"pagePath": "pages/navbar/navbar"}, {"pagePath": "pages/material/material"}, {"pagePath": "pages/my/my"}, {"pagePath": "pages/product/rank"}], "selectedColor": "#0062cc", "height": "50px"}, "adid": "129854030308", "channel": ""}}
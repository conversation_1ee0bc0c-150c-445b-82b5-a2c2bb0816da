JvmtiExport can_access_local_variables 0
JvmtiExport can_hotswap_or_post_breakpoint 0
JvmtiExport can_post_on_exceptions 0
# 177 ciObject found
instanceKlass org/codehaus/groovy/runtime/GeneratedLambda
instanceKlass org/codehaus/groovy/ast/ClassHelper
instanceKlass org/codehaus/groovy/classgen/asm/util/TypeUtil
instanceKlass org/gradle/api/initialization/ConfigurableIncludedBuild
instanceKlass org/gradle/internal/metaobject/DynamicInvokeResult
instanceKlass org/codehaus/groovy/runtime/wrappers/Wrapper
instanceKlass groovy/lang/AdaptingMetaClass
instanceKlass groovy/lang/GroovyInterceptable
instanceKlass org/codehaus/groovy/runtime/ArrayUtil
instanceKlass org/codehaus/groovy/runtime/metaclass/ClosureMetaClass$StandardClosureChooser
instanceKlass org/codehaus/groovy/runtime/metaclass/ClosureMetaClass$MethodChooser
instanceKlass org/codehaus/groovy/runtime/callsite/BooleanClosureWrapper
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass com/sun/beans/finder/FinderUtils
instanceKlass com/sun/beans/finder/AbstractFinder
instanceKlass org/gradle/cache/internal/DefaultFileContentCacheFactory$DefaultFileContentCache
instanceKlass org/gradle/internal/jvm/JavaModuleDetector$ModuleInfoLocator
instanceKlass org/gradle/internal/jvm/JavaModuleDetector$$Lambda$362
instanceKlass org/gradle/internal/jvm/JavaModuleDetector$$Lambda$361
instanceKlass org/gradle/cache/internal/FileContentCache
instanceKlass org/gradle/cache/internal/DefaultFileContentCacheFactory
instanceKlass org/gradle/api/internal/model/InstantiatorBackedObjectFactory
instanceKlass org/gradle/process/internal/JavaExecAction
instanceKlass org/gradle/process/internal/ExecHandleBuilder
instanceKlass org/gradle/process/internal/JavaForkOptionsInternal
instanceKlass org/gradle/process/JavaDebugOptions
instanceKlass org/gradle/process/internal/ExecAction
instanceKlass org/gradle/process/internal/DefaultExecActionFactory
instanceKlass org/gradle/api/internal/file/copy/CopySpecInternal
instanceKlass org/gradle/api/internal/file/copy/CopyAction
instanceKlass org/gradle/api/internal/file/copy/FileCopier
instanceKlass org/gradle/api/internal/resources/DefaultResourceHandler
instanceKlass org/gradle/api/internal/resources/DefaultTextResourceFactory
instanceKlass org/gradle/api/internal/resources/DefaultResourceResolver
instanceKlass org/gradle/api/internal/resources/ResourceResolver
instanceKlass org/gradle/api/resources/TextResourceFactory
instanceKlass org/gradle/api/internal/resources/DefaultResourceHandler$Factory$FactoryImpl
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$360
instanceKlass org/gradle/internal/metaobject/DynamicObjectUtil
instanceKlass org/gradle/process/JavaExecSpec
instanceKlass org/gradle/process/JavaForkOptions
instanceKlass org/gradle/process/ExecSpec
instanceKlass org/gradle/process/BaseExecSpec
instanceKlass org/gradle/process/ProcessForkOptions
instanceKlass org/gradle/internal/classpath/Instrumented$$Lambda$359
instanceKlass java/util/stream/Sink$ChainedInt
instanceKlass java/util/stream/Sink$OfInt
instanceKlass java/util/function/IntConsumer
instanceKlass java/util/stream/Nodes$ArrayNode
instanceKlass org/codehaus/groovy/runtime/callsite/CallSiteArray$$Lambda$358
instanceKlass org/codehaus/groovy/runtime/callsite/CallSiteArray$$Lambda$357
instanceKlass org/codehaus/groovy/runtime/callsite/AbstractCallSite
instanceKlass java/util/stream/Streams$RangeIntSpliterator
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$NoOpBuilder
instanceKlass groovy/transform/Internal
instanceKlass org/gradle/process/ExecResult
instanceKlass org/gradle/api/resources/ResourceHandler
instanceKlass org/gradle/plugin/use/internal/PluginRequestCollector
instanceKlass com/google/common/collect/MapMakerInternalMap$AbstractStrongKeyEntry
instanceKlass com/google/common/collect/MapMakerInternalMap$WeakValueEntry
instanceKlass com/google/common/collect/Count
instanceKlass org/gradle/api/internal/initialization/loadercache/DefaultClassLoaderCache$CachedClassLoader
instanceKlass org/gradle/api/internal/initialization/loadercache/DefaultClassLoaderCache$$Lambda$356
instanceKlass org/gradle/groovy/scripts/internal/DefaultScriptCompilationHandler$ClassesDirCompiledScript$$Lambda$355
instanceKlass org/gradle/configuration/DefaultScriptPluginFactory$ScriptPluginImpl$$Lambda$354
instanceKlass org/gradle/groovy/scripts/internal/BuildScriptData
instanceKlass org/gradle/internal/snapshot/AbstractIncompleteFileSystemNode$2$$Lambda$353
instanceKlass org/gradle/internal/snapshot/AbstractIncompleteFileSystemNode$$Lambda$352
instanceKlass org/gradle/internal/snapshot/AbstractIncompleteFileSystemNode$2$$Lambda$351
instanceKlass org/gradle/internal/snapshot/PathUtil$1
instanceKlass org/gradle/internal/snapshot/AbstractStorePathRelationshipHandler
instanceKlass org/gradle/internal/snapshot/AbstractIncompleteFileSystemNode$2
instanceKlass org/gradle/internal/snapshot/AbstractListChildMap
instanceKlass org/gradle/cache/internal/btree/BTreePersistentIndexedCache$DataBlockUpdateResult
instanceKlass org/gradle/api/internal/changedetection/state/CachingFileHasher$FileInfo
instanceKlass org/gradle/internal/io/StreamByteBuffer$StreamByteBufferChunk
instanceKlass org/gradle/cache/internal/DefaultMultiProcessSafePersistentIndexedCache$$Lambda$350
instanceKlass org/gradle/internal/io/StreamByteBuffer
instanceKlass org/gradle/cache/internal/CacheAccessWorker$1
instanceKlass org/gradle/cache/internal/btree/BTreePersistentIndexedCache$Lookup
instanceKlass org/gradle/cache/internal/AsyncCacheAccessDecoratedCache$$Lambda$349
instanceKlass org/gradle/cache/internal/btree/BTreePersistentIndexedCache$IndexEntry
instanceKlass org/gradle/cache/internal/InMemoryDecoratedCache$$Lambda$348
instanceKlass org/gradle/cache/internal/CrossProcessSynchronizingCache$$Lambda$347
instanceKlass org/gradle/cache/internal/DefaultMultiProcessSafePersistentIndexedCache$$Lambda$346
instanceKlass org/gradle/cache/internal/btree/BTreePersistentIndexedCache$IndexRoot
instanceKlass org/gradle/internal/snapshot/SnapshotUtil$1
instanceKlass com/google/common/primitives/Longs
instanceKlass org/gradle/internal/snapshot/AbstractIncompleteFileSystemNode$$Lambda$345
instanceKlass org/gradle/cache/internal/btree/FreeListBlockStore$FreeListEntry
instanceKlass org/gradle/api/internal/initialization/ClassLoaderScopeIdentifier$Id
instanceKlass org/gradle/model/dsl/internal/transform/ClosureCreationInterceptingVerifier
instanceKlass org/gradle/cache/internal/btree/BlockPointer
instanceKlass org/gradle/groovy/scripts/internal/FactoryBackedCompileOperation
instanceKlass org/gradle/groovy/scripts/internal/BuildScriptTransformer$1
instanceKlass org/gradle/cache/internal/btree/ByteInput
instanceKlass org/gradle/groovy/scripts/internal/BuildScriptTransformer
instanceKlass org/gradle/cache/internal/btree/ByteOutput
instanceKlass org/gradle/api/internal/collections/IterationOrderRetainingSetElementSource$1
instanceKlass org/gradle/cache/internal/btree/FreeListBlockStore$2
instanceKlass org/gradle/cache/internal/btree/FreeListBlockStore$1
instanceKlass org/gradle/cache/internal/btree/BTreePersistentIndexedCache$2
instanceKlass org/gradle/cache/internal/btree/BTreePersistentIndexedCache$1
instanceKlass org/gradle/api/internal/plugins/DefaultPluginManager$3
instanceKlass org/gradle/cache/internal/btree/FreeListBlockStore
instanceKlass org/gradle/cache/internal/btree/StateCheckBlockStore
instanceKlass org/gradle/api/plugins/AppliedPlugin
instanceKlass org/gradle/cache/internal/btree/Block
instanceKlass org/gradle/cache/internal/btree/FileBackedBlockStore
instanceKlass org/gradle/api/internal/plugins/ApplyPluginBuildOperationType$Result
instanceKlass org/gradle/api/internal/plugins/DefaultPluginManager
instanceKlass org/gradle/cache/internal/btree/CachingBlockStore
instanceKlass org/gradle/api/internal/plugins/ImperativeOnlyPluginTarget
instanceKlass org/gradle/api/internal/plugins/PluginTarget
instanceKlass org/gradle/cache/internal/btree/KeyHasher
instanceKlass org/gradle/groovy/scripts/internal/DefaultScriptRunnerFactory$ScriptRunnerImpl
instanceKlass org/gradle/cache/internal/btree/BlockStore$Factory
instanceKlass org/gradle/groovy/scripts/internal/CrossBuildInMemoryCachingScriptClassCache$CachedCompiledScript
instanceKlass org/gradle/cache/internal/btree/BlockPayload
instanceKlass org/gradle/cache/internal/btree/BlockStore
instanceKlass org/gradle/internal/classloader/ImplementationHashAware
instanceKlass org/gradle/cache/internal/DefaultMultiProcessSafePersistentIndexedCache$$Lambda$344
instanceKlass org/gradle/groovy/scripts/internal/DefaultScriptCompilationHandler$ClassesDirCompiledScript
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/cache/internal/CacheAccessWorker$2
instanceKlass org/gradle/cache/internal/CacheAccessWorker$FlushOperationsCommand
instanceKlass org/gradle/internal/classpath/DefaultCachedClasspathTransformer$$Lambda$343
instanceKlass org/gradle/cache/internal/CacheAccessWorker$ShutdownOperationsCommand
instanceKlass org/gradle/cache/internal/AsyncCacheAccessDecoratedCache$$Lambda$342
instanceKlass sun/nio/fs/WindowsPath$1
instanceKlass org/gradle/internal/classpath/DefaultCachedClasspathTransformer$$Lambda$341
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/classpath/DefaultCachedClasspathTransformer$$Lambda$340
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/classpath/DefaultCachedClasspathTransformer$$Lambda$339
instanceKlass org/gradle/internal/classpath/DefaultCachedClasspathTransformer$$Lambda$338
instanceKlass org/gradle/internal/classpath/DefaultCachedClasspathTransformer$$Lambda$337
instanceKlass org/gradle/internal/Either
instanceKlass org/gradle/internal/classpath/DefaultCachedClasspathTransformer$$Lambda$336
instanceKlass org/gradle/internal/snapshot/ReadOnlyFileSystemNode$1
instanceKlass org/gradle/internal/snapshot/ChildMap$Entry$$Lambda$335
instanceKlass org/gradle/internal/snapshot/SnapshotUtil$2
instanceKlass org/gradle/internal/snapshot/SnapshotUtil
instanceKlass org/gradle/internal/watch/registry/impl/HierarchicalFileWatcherUpdater$$Lambda$334
instanceKlass org/gradle/internal/watch/vfs/impl/WatchingVirtualFileSystem$$Lambda$333
instanceKlass org/gradle/internal/snapshot/SnapshotHierarchy$SnapshotDiffListener
instanceKlass org/gradle/internal/watch/vfs/impl/WatchingVirtualFileSystem$$Lambda$332
instanceKlass org/gradle/internal/watch/vfs/impl/WatchingVirtualFileSystem$$Lambda$331
instanceKlass org/gradle/internal/snapshot/AbstractIncompleteFileSystemNode$$Lambda$330
instanceKlass org/gradle/internal/watch/registry/SnapshotCollectingDiffListener$$Lambda$329
instanceKlass org/gradle/internal/snapshot/AbstractIncompleteFileSystemNode
instanceKlass org/gradle/internal/snapshot/ChildMap$Entry$PathRelationshipHandler
instanceKlass org/gradle/internal/snapshot/SingletonChildMap
instanceKlass org/gradle/internal/snapshot/PathUtil$$Lambda$328
instanceKlass org/gradle/internal/snapshot/PathUtil$$Lambda$327
instanceKlass org/gradle/internal/snapshot/VfsRelativePath
instanceKlass org/gradle/internal/watch/registry/SnapshotCollectingDiffListener
instanceKlass org/gradle/internal/vfs/impl/AbstractVirtualFileSystem$$Lambda$326
instanceKlass org/gradle/internal/vfs/impl/AbstractVirtualFileSystem$UpdateFunction
instanceKlass org/gradle/internal/vfs/impl/AbstractVirtualFileSystem$$Lambda$325
instanceKlass org/gradle/internal/snapshot/ChildMapFactory
instanceKlass org/gradle/internal/snapshot/DirectorySnapshot$$Lambda$324
instanceKlass org/gradle/internal/snapshot/ChildMap$Entry
instanceKlass org/gradle/internal/snapshot/ChildMap$InvalidationHandler
instanceKlass java/util/Comparator$$Lambda$323
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/snapshot/FileSystemLocationSnapshot$$Lambda$322
instanceKlass org/gradle/internal/snapshot/PathUtil
instanceKlass org/gradle/internal/snapshot/FileSystemLocationSnapshot$$Lambda$321
instanceKlass org/gradle/internal/snapshot/MerkleDirectorySnapshotBuilder$Directory
instanceKlass sun/nio/fs/WindowsFileKey
instanceKlass org/gradle/internal/snapshot/MerkleDirectorySnapshotBuilder
instanceKlass org/gradle/internal/snapshot/RelativePathTracker
instanceKlass org/gradle/internal/RelativePathSupplier
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotterStatistics$CollectingFileVisitor
instanceKlass org/gradle/internal/vfs/impl/DefaultFileSystemAccess$1
instanceKlass org/gradle/internal/file/impl/DefaultFileMetadata$1
instanceKlass org/gradle/internal/file/impl/DefaultFileMetadata
instanceKlass org/gradle/internal/file/FileMetadata
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/NativePlatformBackedFileMetadataAccessor$1
instanceKlass net/rubygrapefruit/platform/internal/WindowsFileTime
instanceKlass net/rubygrapefruit/platform/internal/jni/WindowsFileFunctions
instanceKlass net/rubygrapefruit/platform/internal/WindowsFileStat
instanceKlass org/gradle/internal/snapshot/SnapshottingFilter$1
instanceKlass org/gradle/internal/snapshot/SnapshottingFilter
instanceKlass org/gradle/internal/vfs/impl/DefaultFileSystemAccess$$Lambda$320
instanceKlass org/gradle/internal/vfs/impl/DefaultFileSystemAccess$$Lambda$319
instanceKlass org/gradle/internal/snapshot/SnapshotHierarchy$$Lambda$318
instanceKlass org/gradle/internal/snapshot/SnapshotHierarchy$$Lambda$317
instanceKlass org/gradle/internal/vfs/impl/DefaultFileSystemAccess$$Lambda$316
instanceKlass org/gradle/internal/classpath/DefaultCachedClasspathTransformer$$Lambda$315
instanceKlass org/gradle/internal/classpath/DefaultCachedClasspathTransformer$$Lambda$314
instanceKlass org/gradle/internal/classpath/DefaultCachedClasspathTransformer$$Lambda$313
instanceKlass org/gradle/internal/classpath/DefaultCachedClasspathTransformer$ValueOrTransformProvider
instanceKlass org/gradle/internal/classpath/InstrumentingClasspathFileTransformer
instanceKlass java/lang/invoke/SerializedLambda
instanceKlass org/gradle/internal/classpath/Instrumented
instanceKlass org/gradle/internal/classpath/InstrumentingTransformer
instanceKlass org/gradle/internal/classpath/CompositeTransformer
instanceKlass org/gradle/groovy/scripts/internal/FileCacheBackedScriptClassCompiler$1
instanceKlass org/gradle/cache/internal/DefaultPersistentDirectoryCache$Initializer
instanceKlass org/gradle/groovy/scripts/internal/FileCacheBackedScriptClassCompiler$CompileToCrossBuildCacheAction
instanceKlass org/gradle/groovy/scripts/internal/FileCacheBackedScriptClassCompiler$ProgressReportingInitializer
instanceKlass com/google/common/io/ByteArrayDataInput
instanceKlass com/google/common/io/ByteArrayDataOutput
instanceKlass com/google/common/io/ByteStreams
instanceKlass java/math/MutableBigInteger
instanceKlass org/gradle/groovy/scripts/internal/ScriptCacheKey
instanceKlass org/gradle/internal/scripts/GradleScript
instanceKlass org/gradle/api/Script
instanceKlass org/gradle/groovy/scripts/internal/NoDataCompileOperation
instanceKlass com/google/common/collect/MapMakerInternalMap$StrongValueEntry
instanceKlass org/codehaus/groovy/control/CompilationUnit$SourceUnitOperation
instanceKlass org/codehaus/groovy/control/CompilationUnit$ISourceUnitOperation
instanceKlass org/gradle/groovy/scripts/internal/Permits
instanceKlass org/gradle/plugin/use/internal/PluginUseScriptBlockMetadataCompiler
instanceKlass org/gradle/groovy/scripts/internal/InitialPassStatementTransformer
instanceKlass org/gradle/internal/resource/CachingTextResource
instanceKlass org/gradle/groovy/scripts/DelegatingScriptSource
instanceKlass org/gradle/groovy/scripts/DefaultScriptCompilerFactory$ScriptCompilerImpl
instanceKlass org/gradle/configuration/DefaultScriptTarget
instanceKlass org/gradle/tooling/internal/provider/runner/PluginApplicationTracker$$Lambda$312
instanceKlass org/gradle/util/internal/TextUtil$1
instanceKlass java/util/regex/CharPredicates$$Lambda$311
instanceKlass java/util/regex/CharPredicates$$Lambda$310
instanceKlass org/gradle/util/internal/TextUtil
instanceKlass org/gradle/configuration/BuildOperationScriptPlugin$OperationDetails
instanceKlass org/gradle/configuration/BuildOperationScriptPlugin$1
instanceKlass org/gradle/configuration/internal/DefaultUserCodeApplicationContext$CurrentApplication
instanceKlass org/gradle/configuration/internal/UserCodeApplicationContext$Application
instanceKlass org/gradle/configuration/BuildOperationScriptPlugin$$Lambda$309
instanceKlass org/gradle/configuration/internal/UserCodeApplicationId
instanceKlass org/gradle/configuration/BuildOperationScriptPlugin$2
instanceKlass org/gradle/configuration/ApplyScriptPluginBuildOperationType$Result
instanceKlass org/gradle/configuration/BuildOperationScriptPlugin
instanceKlass org/gradle/configuration/ScriptTarget
instanceKlass org/gradle/configuration/DefaultScriptPluginFactory$ScriptPluginImpl
instanceKlass org/gradle/internal/event/BroadcastDispatch$ActionInvocationHandler
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge$2$$Lambda$308
instanceKlass org/gradle/internal/extensibility/ExtensionsStorage$ExtensionHolder
instanceKlass org/gradle/api/plugins/ExtensionsSchema$ExtensionSchema
instanceKlass org/gradle/api/NamedDomainObjectCollectionSchema$NamedDomainObjectSchema
instanceKlass org/codehaus/groovy/runtime/memoize/StampedCommonCache$$Lambda$307
instanceKlass org/codehaus/groovy/runtime/memoize/EvictableCache$Action
instanceKlass java/util/WeakHashMap$HashIterator
instanceKlass java/util/stream/StreamSpliterators$WrappingSpliterator$$Lambda$306
instanceKlass java/util/function/BooleanSupplier
instanceKlass java/util/stream/StreamSpliterators$WrappingSpliterator$$Lambda$305
instanceKlass jdk/internal/jrtfs/JrtDirectoryStream$1
instanceKlass jdk/internal/jrtfs/JrtFileSystem$$Lambda$304
instanceKlass jdk/internal/jrtfs/JrtFileSystem$$Lambda$303
instanceKlass jdk/internal/jrtfs/JrtDirectoryStream
instanceKlass jdk/internal/jrtfs/JrtFileAttributes
instanceKlass jdk/internal/jimage/ImageReader$SharedImageReader$$Lambda$302
instanceKlass jdk/internal/jimage/ImageReader$SharedImageReader$LocationVisitor
instanceKlass jdk/internal/jrtfs/JrtFileAttributeView
instanceKlass jdk/internal/jimage/ImageReader$Node
instanceKlass jdk/internal/jrtfs/SystemImage$2
instanceKlass jdk/internal/jrtfs/SystemImage$$Lambda$301
instanceKlass jdk/internal/jrtfs/SystemImage
instanceKlass jdk/internal/jrtfs/JrtPath
instanceKlass groovy/grape/GrapeIvy
instanceKlass groovy/grape/GrapeEngine
instanceKlass org/codehaus/groovy/vmplugin/v9/Java9$$Lambda$300
instanceKlass org/codehaus/groovy/vmplugin/v9/Java9$$Lambda$299
instanceKlass org/codehaus/groovy/vmplugin/v9/Java9$$Lambda$298
instanceKlass java/util/stream/Collectors$$Lambda$297
instanceKlass java/util/stream/Collectors$$Lambda$296
instanceKlass java/util/stream/Collectors$$Lambda$295
instanceKlass org/codehaus/groovy/vmplugin/v9/Java9$$Lambda$294
instanceKlass org/codehaus/groovy/vmplugin/v9/Java9$$Lambda$293
instanceKlass org/codehaus/groovy/vmplugin/v9/Java9$$Lambda$292
instanceKlass jdk/nio/zipfs/ZipFileSystem$$Lambda$291
instanceKlass org/codehaus/groovy/vmplugin/v9/ClassFinder$1$$Lambda$290
instanceKlass java/nio/file/Files$3
instanceKlass java/nio/file/FileTreeWalker$Event
instanceKlass jdk/nio/zipfs/ZipDirectoryStream$1
instanceKlass java/nio/file/FileTreeWalker$DirectoryNode
instanceKlass jdk/nio/zipfs/ZipDirectoryStream
instanceKlass java/time/chrono/AbstractChronology
instanceKlass java/time/zone/ZoneOffsetTransition
instanceKlass java/time/zone/ZoneOffsetTransitionRule
instanceKlass java/time/zone/Ser
instanceKlass java/io/Externalizable
instanceKlass java/time/zone/ZoneRulesProvider$1
instanceKlass java/time/zone/ZoneRulesProvider
instanceKlass jdk/nio/zipfs/ZipUtils
instanceKlass jdk/nio/zipfs/ZipFileAttributeView
instanceKlass java/nio/file/attribute/BasicWithKeyFileAttributeView
instanceKlass java/nio/file/FileTreeWalker
instanceKlass java/nio/file/SimpleFileVisitor
instanceKlass jdk/nio/zipfs/ZipFileSystem$END
instanceKlass jdk/nio/zipfs/ZipConstants
instanceKlass sun/nio/fs/WindowsChannelFactory$2
instanceKlass sun/nio/fs/WindowsSecurityDescriptor
instanceKlass jdk/nio/zipfs/ZipPath
instanceKlass jdk/nio/zipfs/ZipCoder
instanceKlass sun/nio/fs/WindowsSecurity
instanceKlass sun/nio/fs/AbstractAclFileAttributeView
instanceKlass java/nio/file/attribute/AclFileAttributeView
instanceKlass java/nio/file/attribute/FileOwnerAttributeView
instanceKlass jdk/nio/zipfs/ZipFileSystem$$Lambda$289
instanceKlass sun/nio/fs/WindowsFileSystemProvider$1
instanceKlass jdk/nio/zipfs/ZipFileSystem$$Lambda$288
instanceKlass java/nio/file/PathMatcher
instanceKlass jdk/nio/zipfs/ZipFileAttributes
instanceKlass jdk/nio/zipfs/ZipFileSystem$IndexNode
instanceKlass sun/nio/fs/WindowsLinkSupport
instanceKlass sun/net/www/protocol/jrt/JavaRuntimeURLConnection$$Lambda$287
instanceKlass java/nio/file/FileStore
instanceKlass java/nio/channels/AsynchronousFileChannel
instanceKlass java/nio/channels/AsynchronousChannel
instanceKlass java/nio/file/spi/FileSystemProvider$1
instanceKlass sun/nio/ch/FileChannelImpl$Closer
instanceKlass sun/nio/fs/WindowsChannelFactory$Flags
instanceKlass sun/nio/fs/WindowsChannelFactory$1
instanceKlass sun/nio/fs/WindowsChannelFactory
instanceKlass sun/nio/fs/WindowsUriSupport
instanceKlass org/codehaus/groovy/vmplugin/v9/ClassFinder
instanceKlass org/apache/groovy/util/Maps
instanceKlass org/codehaus/groovy/GroovyExceptionInterface
instanceKlass org/codehaus/groovy/control/CompilerConfiguration
instanceKlass groovy/lang/GroovyClassLoader$1
instanceKlass org/codehaus/groovy/runtime/memoize/CommonCache
instanceKlass java/util/concurrent/locks/StampedLock$WNode
instanceKlass java/util/concurrent/locks/StampedLock
instanceKlass org/codehaus/groovy/runtime/memoize/StampedCommonCache
instanceKlass org/codehaus/groovy/runtime/memoize/ValueConvertable
instanceKlass org/codehaus/groovy/control/CompilationUnit$IPrimaryClassNodeOperation
instanceKlass org/codehaus/groovy/control/CompilationUnit$PhaseOperation
instanceKlass org/codehaus/groovy/control/CompilationUnit$ClassgenCallback
instanceKlass org/codehaus/groovy/runtime/memoize/UnlimitedConcurrentCache
instanceKlass org/codehaus/groovy/runtime/memoize/EvictableCache
instanceKlass org/codehaus/groovy/ast/expr/MethodCall
instanceKlass org/codehaus/groovy/ast/stmt/LoopingStatement
instanceKlass org/codehaus/groovy/control/messages/Message
instanceKlass org/codehaus/groovy/ast/CodeVisitorSupport
instanceKlass org/codehaus/groovy/ast/GroovyCodeVisitor
instanceKlass org/codehaus/groovy/ast/GroovyClassVisitor
instanceKlass org/codehaus/groovy/transform/ErrorCollecting
instanceKlass org/codehaus/groovy/ast/expr/ExpressionTransformer
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$WriteLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$ReadLock
instanceKlass org/apache/groovy/plugin/GroovyRunnerRegistry
instanceKlass org/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$$Lambda$286
instanceKlass groovy/lang/MetaClassImpl$$Lambda$285
instanceKlass org/codehaus/groovy/runtime/GroovyCategorySupport
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock
instanceKlass org/codehaus/groovy/util/ComplexKeyHashMap$1
instanceKlass org/codehaus/groovy/reflection/CachedClass$1$$Lambda$284
instanceKlass org/codehaus/groovy/reflection/CachedClass$1$$Lambda$283
instanceKlass org/codehaus/groovy/reflection/CachedClass$1$$Lambda$282
instanceKlass org/codehaus/groovy/reflection/CachedClass$1$$Lambda$281
instanceKlass java/beans/SimpleBeanInfo
instanceKlass java/beans/Transient
instanceKlass java/beans/BeanProperty
instanceKlass com/sun/beans/WildcardTypeImpl
instanceKlass com/sun/beans/introspect/PropertyInfo
instanceKlass com/sun/beans/introspect/EventSetInfo
instanceKlass com/sun/beans/TypeResolver
instanceKlass java/beans/MethodRef
instanceKlass com/sun/beans/introspect/MethodInfo$MethodOrder
instanceKlass com/sun/beans/introspect/MethodInfo
instanceKlass com/sun/beans/util/Cache$Ref
instanceKlass com/sun/beans/util/Cache$CacheEntry
instanceKlass com/sun/beans/util/Cache
instanceKlass com/sun/beans/introspect/ClassInfo
instanceKlass javax/swing/SwingContainer
instanceKlass java/beans/JavaBean
instanceKlass java/util/zip/ZipFile$ZipEntryIterator
instanceKlass java/util/jar/JarFile$$Lambda$280
instanceKlass com/sun/beans/finder/ClassFinder
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/ClassSpecializer$Factory$1Var
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass com/sun/beans/finder/InstanceFinder
instanceKlass java/beans/WeakIdentityMap
instanceKlass java/beans/ThreadGroupContext
instanceKlass groovy/lang/MetaClassImpl$$Lambda$279
instanceKlass java/beans/BeanInfo
instanceKlass org/codehaus/groovy/reflection/CachedClass$CachedMethodComparatorWithString
instanceKlass org/codehaus/groovy/runtime/callsite/CallSiteArray
instanceKlass org/codehaus/groovy/util/AbstractConcurrentMapBase
instanceKlass java/lang/reflect/AnnotatedType
instanceKlass org/codehaus/groovy/runtime/MetaClassHelper
instanceKlass org/codehaus/groovy/reflection/CachedClass$2$$Lambda$278
instanceKlass org/codehaus/groovy/reflection/CachedClass$2$$Lambda$277
instanceKlass org/codehaus/groovy/reflection/CachedClass$2$$Lambda$276
instanceKlass org/codehaus/groovy/reflection/CachedClass$2$$Lambda$275
instanceKlass groovy/lang/ExpandoMetaClass$Callable
instanceKlass org/codehaus/groovy/runtime/MethodKey
instanceKlass groovy/lang/ClosureInvokingMethod
instanceKlass org/codehaus/groovy/reflection/CachedClass$2$$Lambda$274
instanceKlass org/codehaus/groovy/runtime/metaclass/MetaMethodIndex$EntryIterator
instanceKlass groovy/lang/MetaClassImpl$$Lambda$273
instanceKlass groovy/lang/MetaClassImpl$$Lambda$272
instanceKlass groovy/lang/MetaClassImpl$$Lambda$271
instanceKlass org/codehaus/groovy/runtime/GeneratedClosure
instanceKlass org/gradle/api/internal/provider/MapPropertyExtensions
instanceKlass org/w3c/dom/Document
instanceKlass org/w3c/dom/UserDataHandler
instanceKlass org/w3c/dom/NamedNodeMap
instanceKlass org/w3c/dom/TypeInfo
instanceKlass org/w3c/dom/Attr
instanceKlass org/w3c/dom/Element
instanceKlass org/w3c/dom/Node
instanceKlass org/w3c/dom/NodeList
instanceKlass org/apache/groovy/xml/extensions/XmlExtensions
instanceKlass java/awt/LayoutManager
instanceKlass javax/swing/ButtonModel
instanceKlass javax/swing/AbstractButton$Handler
instanceKlass javax/swing/event/ChangeListener
instanceKlass javax/swing/Icon
instanceKlass javax/swing/event/TableModelListener
instanceKlass javax/swing/MenuSelectionManager
instanceKlass javax/swing/event/TableColumnModelListener
instanceKlass javax/swing/ListSelectionModel
instanceKlass javax/swing/AncestorNotifier
instanceKlass java/beans/VetoableChangeListener
instanceKlass javax/swing/ArrayTable
instanceKlass javax/swing/TransferHandler$DropLocation
instanceKlass javax/swing/ActionMap
instanceKlass javax/swing/InputMap
instanceKlass javax/swing/InputVerifier
instanceKlass javax/swing/border/Border
instanceKlass javax/swing/event/AncestorListener
instanceKlass java/awt/AWTKeyStroke
instanceKlass javax/swing/plaf/ComponentUI
instanceKlass javax/swing/TransferHandler
instanceKlass java/awt/event/ItemListener
instanceKlass java/awt/peer/ComponentPeer
instanceKlass javax/accessibility/AccessibleStateSet
instanceKlass sun/awt/RequestFocusController
instanceKlass java/awt/im/InputContext
instanceKlass java/awt/im/InputMethodRequests
instanceKlass java/awt/event/InputMethodListener
instanceKlass java/awt/event/MouseWheelListener
instanceKlass java/awt/event/MouseMotionListener
instanceKlass java/awt/event/MouseListener
instanceKlass java/awt/event/KeyListener
instanceKlass java/awt/event/HierarchyBoundsListener
instanceKlass java/awt/event/HierarchyListener
instanceKlass java/awt/event/FocusListener
instanceKlass java/awt/event/ComponentListener
instanceKlass java/awt/image/BufferStrategy
instanceKlass java/awt/BufferCapabilities
instanceKlass java/awt/ImageCapabilities
instanceKlass java/awt/image/ImageProducer
instanceKlass java/awt/Insets
instanceKlass java/awt/Cursor
instanceKlass java/awt/image/ColorModel
instanceKlass sun/awt/ComponentFactory
instanceKlass java/awt/Toolkit
instanceKlass java/awt/dnd/DropTarget
instanceKlass java/awt/dnd/DropTargetListener
instanceKlass java/awt/PointerInfo
instanceKlass sun/java2d/pipe/Region
instanceKlass java/awt/ComponentOrientation
instanceKlass java/awt/GraphicsConfiguration
instanceKlass java/awt/Color
instanceKlass java/awt/Paint
instanceKlass java/awt/Transparency
instanceKlass java/awt/FontMetrics
instanceKlass java/beans/PropertyChangeListener
instanceKlass javax/accessibility/AccessibleContext
instanceKlass java/util/EventObject
instanceKlass java/awt/geom/RectangularShape
instanceKlass java/awt/Shape
instanceKlass java/awt/geom/Dimension2D
instanceKlass java/awt/geom/Point2D
instanceKlass java/awt/Graphics
instanceKlass java/awt/Event
instanceKlass java/awt/Font
instanceKlass java/awt/MenuComponent
instanceKlass java/awt/Image
instanceKlass javax/swing/table/TableColumn
instanceKlass javax/swing/Action
instanceKlass javax/swing/table/AbstractTableModel
instanceKlass javax/swing/ButtonGroup
instanceKlass javax/swing/table/TableModel
instanceKlass javax/swing/MenuElement
instanceKlass javax/swing/tree/TreePath
instanceKlass javax/swing/table/TableColumnModel
instanceKlass javax/swing/tree/DefaultMutableTreeNode
instanceKlass javax/swing/MutableComboBoxModel
instanceKlass javax/swing/ComboBoxModel
instanceKlass javax/swing/AbstractListModel
instanceKlass javax/swing/SwingConstants
instanceKlass java/awt/Component
instanceKlass java/awt/MenuContainer
instanceKlass java/awt/image/ImageObserver
instanceKlass javax/swing/TransferHandler$HasGetTransferHandler
instanceKlass javax/accessibility/Accessible
instanceKlass java/awt/event/ActionListener
instanceKlass javax/swing/event/ListDataListener
instanceKlass java/awt/ItemSelectable
instanceKlass javax/swing/tree/MutableTreeNode
instanceKlass javax/swing/tree/TreeNode
instanceKlass javax/swing/ListModel
instanceKlass org/apache/groovy/swing/extensions/SwingExtensions
instanceKlass java/sql/NClob
instanceKlass java/sql/Blob
instanceKlass java/sql/SQLXML
instanceKlass java/sql/RowId
instanceKlass java/sql/Clob
instanceKlass java/sql/SQLType
instanceKlass java/sql/Statement
instanceKlass java/sql/Array
instanceKlass java/sql/Ref
instanceKlass groovy/sql/GroovyResultSet
instanceKlass java/sql/ResultSet
instanceKlass java/sql/ResultSetMetaData
instanceKlass java/sql/Wrapper
instanceKlass org/apache/groovy/sql/extensions/SqlExtensions
instanceKlass java/nio/file/WatchEvent$Modifier
instanceKlass java/nio/file/WatchKey
instanceKlass java/nio/file/WatchEvent$Kind
instanceKlass java/nio/file/WatchService
instanceKlass org/apache/groovy/dateutil/extensions/DateUtilStaticExtensions
instanceKlass org/apache/groovy/dateutil/extensions/DateUtilExtensions
instanceKlass org/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$DefaultModuleListener$$Lambda$270
instanceKlass org/codehaus/groovy/runtime/metaclass/MethodHelper
instanceKlass java/time/zone/ZoneRules
instanceKlass java/time/chrono/Chronology
instanceKlass java/time/chrono/Era
instanceKlass java/time/format/DateTimeFormatter
instanceKlass java/time/temporal/ValueRange
instanceKlass java/time/temporal/TemporalQuery
instanceKlass java/time/MonthDay
instanceKlass java/time/LocalDateTime
instanceKlass java/time/chrono/ChronoLocalDateTime
instanceKlass java/time/Instant
instanceKlass java/time/OffsetDateTime
instanceKlass java/time/Year
instanceKlass java/time/LocalDate
instanceKlass java/time/chrono/ChronoLocalDate
instanceKlass java/time/LocalTime
instanceKlass java/time/ZonedDateTime
instanceKlass java/time/chrono/ChronoZonedDateTime
instanceKlass java/time/YearMonth
instanceKlass java/time/OffsetTime
instanceKlass java/time/temporal/TemporalAdjuster
instanceKlass java/time/Period
instanceKlass java/time/chrono/ChronoPeriod
instanceKlass java/time/temporal/Temporal
instanceKlass java/time/temporal/TemporalAccessor
instanceKlass java/time/temporal/TemporalField
instanceKlass org/apache/groovy/datetime/extensions/DateTimeStaticExtensions
instanceKlass org/apache/groovy/datetime/extensions/DateTimeExtensions
instanceKlass org/codehaus/groovy/runtime/m12n/ExtensionModule
instanceKlass org/codehaus/groovy/runtime/m12n/PropertiesModuleFactory
instanceKlass org/codehaus/groovy/util/URLStreams
instanceKlass org/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$DefaultModuleListener
instanceKlass org/codehaus/groovy/runtime/m12n/ExtensionModuleScanner
instanceKlass java/util/ResourceBundle$CacheKey
instanceKlass java/time/ZoneId
instanceKlass org/codehaus/groovy/runtime/DefaultGroovyStaticMethods
instanceKlass org/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$$Lambda$269
instanceKlass org/codehaus/groovy/runtime/RangeInfo
instanceKlass java/util/function/ToDoubleFunction
instanceKlass java/util/function/ToLongFunction
instanceKlass java/util/function/DoubleFunction
instanceKlass java/util/function/LongFunction
instanceKlass java/util/stream/DoubleStream
instanceKlass java/util/stream/LongStream
instanceKlass java/util/function/DoublePredicate
instanceKlass java/util/function/LongPredicate
instanceKlass java/util/function/IntPredicate
instanceKlass java/util/OptionalInt
instanceKlass java/util/OptionalLong
instanceKlass java/util/OptionalDouble
instanceKlass org/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$$Lambda$268
instanceKlass org/codehaus/groovy/runtime/NumberAwareComparator
instanceKlass org/codehaus/groovy/runtime/EncodingGroovyMethods
instanceKlass java/lang/ProcessHandle
instanceKlass java/util/concurrent/CompletableFuture
instanceKlass java/util/concurrent/CompletionStage
instanceKlass java/lang/ProcessHandle$Info
instanceKlass org/codehaus/groovy/reflection/CachedClass$CachedMethodComparatorByName
instanceKlass org/codehaus/groovy/reflection/CachedMethod$MyComparator
instanceKlass java/lang/SecurityManager$$Lambda$267
instanceKlass java/lang/SecurityManager$$Lambda$266
instanceKlass java/lang/SecurityManager$$Lambda$265
instanceKlass java/lang/SecurityManager$$Lambda$264
instanceKlass java/lang/SecurityManager$$Lambda$263
instanceKlass java/lang/SecurityManager$$Lambda$262
instanceKlass java/lang/SecurityManager$$Lambda$261
instanceKlass java/lang/SecurityManager$$Lambda$260
instanceKlass java/lang/SecurityManager$$Lambda$259
instanceKlass java/lang/SecurityManager$$Lambda$258
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/SecurityManager$$Lambda$257
instanceKlass org/codehaus/groovy/vmplugin/v9/Java9$$Lambda$256
instanceKlass org/codehaus/groovy/vmplugin/v9/Java9$$Lambda$255
instanceKlass org/codehaus/groovy/vmplugin/v9/Java9$$Lambda$254
instanceKlass java/util/stream/MatchOps$$Lambda$253
instanceKlass java/util/stream/MatchOps$BooleanTerminalSink
instanceKlass java/util/stream/MatchOps$MatchOp
instanceKlass java/util/stream/MatchOps
instanceKlass org/codehaus/groovy/vmplugin/v9/Java9$$Lambda$252
instanceKlass org/codehaus/groovy/vmplugin/v9/Java9$$Lambda$251
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/codehaus/groovy/vmplugin/v9/Java9$$Lambda$250
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/codehaus/groovy/vmplugin/v9/Java9$$Lambda$249
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/codehaus/groovy/vmplugin/v9/Java9$$Lambda$248
instanceKlass java/util/Spliterators$IteratorSpliterator
instanceKlass jdk/internal/module/SystemModules$all
instanceKlass org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport
instanceKlass org/codehaus/groovy/ast/Variable
instanceKlass org/codehaus/groovy/vmplugin/v8/Java8
instanceKlass org/codehaus/groovy/vmplugin/VMPluginFactory$$Lambda$247
instanceKlass org/codehaus/groovy/vmplugin/VMPluginFactory
instanceKlass org/codehaus/groovy/reflection/ReflectionUtils
instanceKlass org/codehaus/groovy/reflection/CachedClass$3$$Lambda$246
instanceKlass org/codehaus/groovy/reflection/CachedClass$3$$Lambda$245
instanceKlass org/codehaus/groovy/reflection/CachedClass$3$$Lambda$244
instanceKlass org/codehaus/groovy/reflection/CachedClass$3$$Lambda$243
instanceKlass org/codehaus/groovy/runtime/memoize/MemoizeCache
instanceKlass org/codehaus/groovy/reflection/CachedClass$3$$Lambda$242
instanceKlass org/codehaus/groovy/reflection/stdclasses/CachedSAMClass$$Lambda$241
instanceKlass org/codehaus/groovy/reflection/stdclasses/CachedSAMClass$$Lambda$240
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass org/codehaus/groovy/reflection/stdclasses/InjectedInvoker
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/MethodHandleImpl$BindCaller
instanceKlass org/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$$Lambda$239
instanceKlass java/util/stream/IntStream
instanceKlass org/codehaus/groovy/transform/trait/Traits$Implemented
instanceKlass org/codehaus/groovy/util/ReferenceType$HardRef
instanceKlass org/codehaus/groovy/util/ManagedReference
instanceKlass org/codehaus/groovy/reflection/ClassInfo$GlobalClassSet
instanceKlass org/apache/groovy/util/SystemUtil
instanceKlass org/codehaus/groovy/reflection/GroovyClassValue
instanceKlass org/codehaus/groovy/reflection/GroovyClassValueFactory
instanceKlass org/codehaus/groovy/reflection/ClassInfo$1
instanceKlass org/codehaus/groovy/reflection/GroovyClassValue$ComputeValue
instanceKlass org/codehaus/groovy/util/ComplexKeyHashMap$Entry
instanceKlass org/codehaus/groovy/util/ComplexKeyHashMap$EntryIterator
instanceKlass org/codehaus/groovy/reflection/ReflectionCache
instanceKlass java/lang/Process
instanceKlass java/util/Timer
instanceKlass java/util/TimerTask
instanceKlass groovy/lang/groovydoc/Groovydoc
instanceKlass groovy/lang/ListWithDefault
instanceKlass groovy/lang/Range
instanceKlass groovy/util/BufferedIterator
instanceKlass java/util/BitSet
instanceKlass org/codehaus/groovy/reflection/GeneratedMetaMethod$DgmMethodRecord
instanceKlass groovy/lang/MetaClassRegistry$MetaClassCreationHandle
instanceKlass org/codehaus/groovy/runtime/m12n/ExtensionModuleRegistry
instanceKlass org/codehaus/groovy/util/Reference
instanceKlass org/codehaus/groovy/util/ReferenceManager
instanceKlass org/codehaus/groovy/util/ReferenceBundle
instanceKlass org/codehaus/groovy/util/ManagedConcurrentLinkedQueue
instanceKlass groovy/lang/MetaClassRegistryChangeEventListener
instanceKlass java/util/EventListener
instanceKlass org/codehaus/groovy/runtime/m12n/ExtensionModuleScanner$ExtensionModuleListener
instanceKlass org/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl
instanceKlass org/codehaus/groovy/runtime/InvokerHelper
instanceKlass org/gradle/internal/extensibility/ExtensionsStorage
instanceKlass org/gradle/api/plugins/ExtraPropertiesExtension
instanceKlass org/gradle/internal/extensibility/DefaultConvention
instanceKlass org/gradle/api/internal/plugins/ExtensionContainerInternal
instanceKlass org/gradle/api/internal/coerce/StringToEnumTransformer
instanceKlass org/codehaus/groovy/runtime/metaclass/MetaMethodIndex
instanceKlass org/codehaus/groovy/vmplugin/VMPlugin
instanceKlass groovyjarjarasm/asm/ClassVisitor
instanceKlass org/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry
instanceKlass org/codehaus/groovy/util/FastArray
instanceKlass org/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header
instanceKlass org/codehaus/groovy/reflection/ClassInfo
instanceKlass org/codehaus/groovy/util/Finalizable
instanceKlass org/codehaus/groovy/reflection/CachedClass
instanceKlass org/codehaus/groovy/ast/ASTNode
instanceKlass org/codehaus/groovy/ast/NodeMetaDataHandler
instanceKlass groovy/lang/groovydoc/GroovydocHolder
instanceKlass groovyjarjarasm/asm/Opcodes
instanceKlass org/codehaus/groovy/util/SingleKeyHashMap$Copier
instanceKlass org/codehaus/groovy/runtime/callsite/CallSite
instanceKlass groovy/lang/MetaClassImpl$MethodIndexAction
instanceKlass org/codehaus/groovy/reflection/ParameterTypes
instanceKlass org/codehaus/groovy/util/ComplexKeyHashMap
instanceKlass groovy/lang/MetaClassImpl
instanceKlass groovy/lang/MutableMetaClass
instanceKlass org/gradle/internal/metaobject/BeanDynamicObject$MetaClassAdapter
instanceKlass org/gradle/api/internal/coerce/PropertySetTransformer
instanceKlass org/gradle/api/internal/coerce/MethodArgumentsTransformer
instanceKlass org/gradle/vcs/VcsMappings
instanceKlass org/gradle/vcs/internal/services/VersionControlServices$VersionControlSettingsServices
instanceKlass org/gradle/plugin/internal/PluginUsePluginServiceRegistry$SettingsScopeServices
instanceKlass org/gradle/internal/service/scopes/SettingsScopeServices$1
instanceKlass org/gradle/initialization/IncludedBuildSpec
instanceKlass org/gradle/vcs/SourceControl
instanceKlass org/gradle/plugin/management/PluginManagementSpec
instanceKlass org/gradle/initialization/DefaultProjectDescriptor
instanceKlass org/gradle/api/initialization/ProjectDescriptor
instanceKlass org/gradle/api/ProjectState
instanceKlass org/gradle/api/attributes/DocsType
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemAttributesDescriber
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport$TargetJvmEnvironmentDisambiguationRules
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport$TargetJvmEnvironmentCompatibilityRules
instanceKlass org/gradle/api/attributes/java/TargetJvmEnvironment
instanceKlass org/gradle/api/internal/attributes/DefaultOrderedDisambiguationRule
instanceKlass org/gradle/api/internal/attributes/DefaultOrderedCompatibilityRule
instanceKlass org/gradle/api/internal/attributes/AttributeMatchingRules
instanceKlass com/google/common/collect/Ordering
instanceKlass org/gradle/api/attributes/java/TargetJvmVersion
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport$BundlingDisambiguationRules
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport$BundlingCompatibilityRules
instanceKlass org/gradle/api/attributes/Bundling
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport$$Lambda$238
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport$LibraryElementsDisambiguationRules
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport$LibraryElementsCompatibilityRules
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport$1
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport$UsageDisambiguationRules
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport$UsageCompatibilityRules
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport
instanceKlass org/gradle/internal/locking/DefaultDependencyLockingHandler$$Lambda$237
instanceKlass org/gradle/internal/locking/DefaultDependencyLockingHandler$$Lambda$236
instanceKlass org/gradle/api/internal/artifacts/DefaultDependencyManagementServices$DependencyResolutionScopeServices$$Lambda$235
instanceKlass org/gradle/internal/locking/DefaultDependencyLockingHandler
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/DefaultRootComponentMetadataBuilder$MetadataHolder
instanceKlass org/gradle/internal/component/local/model/DefaultLocalComponentMetadata
instanceKlass org/gradle/internal/component/local/model/BuildableLocalComponentMetadata
instanceKlass org/gradle/api/internal/artifacts/configurations/DefaultConfigurationContainer$$Lambda$234
instanceKlass org/gradle/api/internal/file/copy/CopySpecSource
instanceKlass org/gradle/api/file/CopySpec
instanceKlass org/gradle/api/file/CopyProcessingSpec
instanceKlass org/gradle/api/file/ContentFilterable
instanceKlass org/gradle/api/file/CopySourceSpec
instanceKlass org/gradle/api/artifacts/ConfigurablePublishArtifact
instanceKlass org/gradle/api/artifacts/PublishArtifact
instanceKlass org/gradle/api/internal/artifacts/dsl/PublishArtifactNotationParserFactory
instanceKlass org/gradle/api/artifacts/Configuration$Namer
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/DefaultRootComponentMetadataBuilder
instanceKlass org/gradle/api/internal/artifacts/configurations/MutationValidator
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/RootComponentMetadataBuilder
instanceKlass org/gradle/api/internal/artifacts/configurations/ConfigurationInternal
instanceKlass org/gradle/internal/deprecation/DeprecatableConfiguration
instanceKlass org/gradle/api/internal/artifacts/configurations/ConfigurationsProvider
instanceKlass org/gradle/api/internal/file/DefaultFileSystemLocation
instanceKlass org/gradle/api/resources/TextResource
instanceKlass org/gradle/internal/locking/LockFileReaderWriter
instanceKlass org/gradle/api/internal/provider/ValidatingValueCollector
instanceKlass org/gradle/api/internal/provider/AbstractCollectionProperty$EmptySupplier
instanceKlass org/gradle/api/internal/provider/DefaultListProperty$1
instanceKlass org/gradle/api/internal/provider/AbstractCollectionProperty$NoValueSupplier
instanceKlass org/gradle/api/internal/provider/CollectionSupplier
instanceKlass org/gradle/internal/locking/LockEntryFilterFactory$$Lambda$233
instanceKlass org/gradle/internal/locking/LockEntryFilterFactory$$Lambda$232
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/LockEntryFilter
instanceKlass org/gradle/internal/locking/LockEntryFilterFactory
instanceKlass org/gradle/internal/locking/DependencyLockingNotationConverter
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/DependencyLockingState
instanceKlass org/gradle/internal/locking/DefaultDependencyLockingProvider
instanceKlass org/gradle/api/internal/attributes/AttributeDesugaring
instanceKlass org/gradle/api/internal/artifacts/transform/ConsumerProvidedVariantFinder
instanceKlass org/gradle/api/internal/artifacts/transform/VariantSelector
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultArtifactTransforms
instanceKlass org/gradle/api/internal/artifacts/ivyservice/DefaultConfigurationResolver$$Lambda$231
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/oldresult/ResolvedConfigurationBuilder
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/DependencyArtifactsVisitor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/DefaultConfigurationResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/projectresult/ResolvedLocalComponentsResult
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ShortCircuitEmptyConfigurationResolver
instanceKlass org/gradle/api/artifacts/result/ResolutionResult
instanceKlass org/gradle/api/internal/artifacts/ResolveContext
instanceKlass org/gradle/api/artifacts/ResolvedConfiguration
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/VisitedArtifactSet
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ErrorHandlingConfigurationResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedArtifactSet$TransformSourceVisitor
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformationNodeFactory
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformedVariantFactory$$Lambda$230
instanceKlass org/gradle/api/internal/artifacts/transform/TransformedProjectArtifactSet
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedArtifactSet$Artifacts
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformedVariantFactory$$Lambda$229
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformedVariantFactory$Factory
instanceKlass org/gradle/api/internal/artifacts/transform/AbstractTransformedArtifactSet
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedArtifactSet
instanceKlass org/gradle/api/internal/artifacts/transform/ExtraExecutionGraphDependenciesResolverFactory
instanceKlass org/gradle/api/internal/artifacts/transform/VariantDefinition
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedVariant
instanceKlass org/gradle/api/internal/artifacts/type/DefaultArtifactTypeRegistry
instanceKlass org/gradle/api/artifacts/transform/TransformParameters$None
instanceKlass org/gradle/api/artifacts/transform/TransformParameters
instanceKlass org/gradle/api/artifacts/transform/TransformAction
instanceKlass org/gradle/api/artifacts/transform/TransformSpec
instanceKlass org/gradle/api/artifacts/transform/VariantTransform
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultVariantTransformRegistry$RecordingRegistration
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultVariantTransformRegistry
instanceKlass org/gradle/api/internal/artifacts/ArtifactTransformRegistration
instanceKlass org/gradle/api/internal/artifacts/transform/Transformer
instanceKlass org/gradle/api/internal/tasks/properties/PropertyVisitor
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformationRegistrationFactory
instanceKlass sun/reflect/annotation/AnnotationParser$$Lambda$228
instanceKlass org/gradle/api/reflect/InjectionPointQualifier
instanceKlass org/gradle/api/internal/tasks/properties/AbstractPropertyNode
instanceKlass org/gradle/api/internal/tasks/properties/bean/RuntimeBeanNodeFactory
instanceKlass org/gradle/api/internal/tasks/properties/DefaultPropertyWalker
instanceKlass org/gradle/api/internal/tasks/properties/DefaultTypeMetadataStore$$Lambda$227
instanceKlass org/gradle/api/internal/tasks/properties/DefaultTypeMetadataStore$$Lambda$226
instanceKlass org/gradle/api/internal/tasks/properties/TypeMetadata
instanceKlass org/gradle/internal/reflect/PropertyMetadata
instanceKlass org/gradle/api/internal/tasks/properties/DefaultTypeMetadataStore
instanceKlass org/gradle/api/internal/tasks/properties/TypeMetadataStore
instanceKlass org/gradle/api/internal/tasks/properties/InspectionSchemeFactory$InspectionSchemeImpl
instanceKlass org/gradle/api/internal/tasks/properties/InspectionScheme
instanceKlass org/apache/commons/lang/builder/HashCodeBuilder
instanceKlass com/google/common/base/Equivalence$Wrapper
instanceKlass org/gradle/internal/reflect/Methods
instanceKlass org/gradle/internal/reflect/annotations/impl/DefaultTypeAnnotationMetadataStore$$Lambda$225
instanceKlass org/gradle/internal/service/scopes/ExecutionGlobalServices$$Lambda$224
instanceKlass org/gradle/internal/scripts/ScriptOrigin
instanceKlass org/gradle/api/internal/PolymorphicDomainObjectContainerInternal
instanceKlass org/gradle/api/ExtensiblePolymorphicDomainObjectContainer
instanceKlass org/gradle/api/internal/rules/NamedDomainObjectFactoryRegistry
instanceKlass org/gradle/util/internal/ConfigureUtil$WrappedConfigureAction
instanceKlass org/gradle/util/internal/ClosureBackedAction
instanceKlass org/gradle/internal/reflect/AnnotationCategory$1
instanceKlass org/gradle/work/NormalizeLineEndings
instanceKlass org/gradle/api/tasks/IgnoreEmptyDirectories
instanceKlass org/gradle/api/tasks/Optional
instanceKlass org/gradle/api/tasks/PathSensitive
instanceKlass org/gradle/api/tasks/CompileClasspath
instanceKlass org/gradle/api/tasks/Classpath
instanceKlass org/gradle/api/tasks/SkipWhenEmpty
instanceKlass org/gradle/work/Incremental
instanceKlass org/gradle/work/DisableCachingByDefault
instanceKlass org/gradle/api/artifacts/transform/CacheableTransform
instanceKlass org/gradle/api/tasks/CacheableTask
instanceKlass org/gradle/internal/reflect/annotations/impl/DefaultTypeAnnotationMetadataStore$1
instanceKlass org/gradle/internal/reflect/validation/TypeValidationContext
instanceKlass org/gradle/internal/reflect/annotations/TypeAnnotationMetadata
instanceKlass org/gradle/internal/reflect/annotations/impl/DefaultTypeAnnotationMetadataStore
instanceKlass org/gradle/internal/service/scopes/ExecutionGlobalServices$$Lambda$223
instanceKlass org/gradle/api/internal/AbstractTask
instanceKlass org/gradle/api/internal/TaskInternal
instanceKlass org/gradle/internal/service/scopes/ExecutionGlobalServices$$Lambda$222
instanceKlass org/gradle/internal/execution/caching/CachingDisabledReason
instanceKlass org/gradle/internal/execution/DeferredExecutionHandler
instanceKlass org/gradle/internal/execution/UnitOfWork
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformerInvocationFactory
instanceKlass org/gradle/api/internal/artifacts/DefaultDependencyManagementServices$ArtifactTransformResolutionGradleUserHomeServices$1
instanceKlass org/gradle/internal/execution/workspace/impl/DefaultImmutableWorkspaceProvider$$Lambda$221
instanceKlass org/gradle/internal/execution/workspace/impl/DefaultImmutableWorkspaceProvider
instanceKlass org/gradle/cache/ManualEvictionInMemoryCache
instanceKlass org/gradle/cache/internal/DefaultCrossBuildInMemoryCacheFactory$CrossBuildCacheRetainingDataFromPreviousBuild
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementGradleUserHomeScopeServices$$Lambda$220
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/Try
instanceKlass org/gradle/internal/snapshot/impl/ImplementationSnapshotSerializer
instanceKlass org/gradle/internal/execution/history/impl/FileSystemSnapshotSerializer
instanceKlass org/gradle/internal/execution/history/impl/FileCollectionFingerprintSerializer
instanceKlass org/gradle/internal/execution/history/AfterPreviousExecutionState
instanceKlass org/gradle/internal/execution/history/impl/DefaultExecutionHistoryStore
instanceKlass org/gradle/api/internal/changedetection/state/DefaultExecutionHistoryCacheAccess
instanceKlass org/gradle/internal/execution/ExecutionResult
instanceKlass org/gradle/internal/execution/UnitOfWork$ExecutionRequest
instanceKlass org/gradle/internal/execution/steps/ExecuteStep
instanceKlass org/gradle/internal/execution/steps/BeforeExecutionContext
instanceKlass org/gradle/internal/execution/steps/AfterPreviousExecutionContext
instanceKlass org/gradle/internal/execution/steps/WorkspaceContext
instanceKlass org/gradle/internal/execution/steps/RemovePreviousOutputsStep
instanceKlass org/gradle/internal/execution/steps/ResolveInputChangesStep
instanceKlass org/gradle/internal/execution/steps/TimeoutStep
instanceKlass org/gradle/internal/execution/steps/CreateOutputsStep
instanceKlass org/gradle/internal/execution/steps/CurrentSnapshotResult
instanceKlass org/gradle/internal/execution/steps/StoreExecutionStateStep
instanceKlass org/gradle/internal/execution/steps/BroadcastChangingOutputsStep
instanceKlass org/gradle/internal/execution/steps/SkipUpToDateStep
instanceKlass org/gradle/internal/execution/history/changes/IncrementalInputProperties
instanceKlass org/gradle/internal/execution/fingerprint/InputFingerprinter$InputVisitor
instanceKlass org/gradle/internal/execution/steps/ResolveChangesStep
instanceKlass org/gradle/internal/execution/steps/CachingResult
instanceKlass org/gradle/internal/execution/ExecutionEngine$Result
instanceKlass org/gradle/internal/execution/steps/UpToDateResult
instanceKlass org/gradle/internal/execution/steps/SnapshotResult
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$NoOpCachingStateStep
instanceKlass org/gradle/internal/reflect/validation/ValidationProblemBuilder
instanceKlass org/gradle/internal/execution/steps/ValidateStep
instanceKlass org/gradle/internal/execution/history/BeforeExecutionState
instanceKlass org/gradle/internal/execution/history/ExecutionState
instanceKlass org/gradle/internal/execution/UnitOfWork$ImplementationVisitor
instanceKlass org/gradle/internal/execution/steps/Result
instanceKlass org/gradle/internal/execution/steps/BuildOperationStep
instanceKlass org/gradle/internal/execution/steps/LoadExecutionStateStep
instanceKlass org/gradle/internal/execution/steps/AssignWorkspaceStep
instanceKlass org/gradle/internal/execution/steps/IdentityCacheStep
instanceKlass org/gradle/internal/execution/steps/IdentityContext
instanceKlass org/gradle/internal/execution/steps/ExecutionRequestContext
instanceKlass org/gradle/internal/execution/steps/Context
instanceKlass org/gradle/internal/execution/steps/IdentifyStep
instanceKlass org/gradle/internal/execution/ExecutionEngine$Request
instanceKlass org/gradle/internal/execution/impl/DefaultExecutionEngine
instanceKlass org/gradle/internal/id/UniqueId$1
instanceKlass com/google/common/base/Ascii
instanceKlass com/google/common/io/BaseEncoding$Alphabet
instanceKlass com/google/common/io/BaseEncoding
instanceKlass org/gradle/internal/id/UniqueId
instanceKlass org/gradle/internal/execution/timeout/Timeout
instanceKlass org/gradle/internal/execution/timeout/impl/DefaultTimeoutHandler
instanceKlass org/gradle/internal/execution/history/impl/DefaultOverlappingOutputDetector
instanceKlass org/gradle/internal/execution/UnitOfWork$OutputVisitor
instanceKlass org/gradle/internal/execution/DefaultOutputSnapshotter
instanceKlass org/gradle/internal/execution/fingerprint/impl/DefaultInputFingerprinter
instanceKlass org/gradle/internal/execution/fingerprint/impl/DefaultFileCollectionFingerprinterRegistry$$Lambda$219
instanceKlass org/gradle/internal/execution/fingerprint/impl/DefaultFileCollectionFingerprinterRegistry
instanceKlass org/gradle/api/internal/changedetection/state/CachingFileSystemLocationSnapshotHasher
instanceKlass org/gradle/api/internal/changedetection/state/LineEndingNormalizingInputStreamHasher
instanceKlass org/gradle/api/tasks/CompileClasspathNormalizer
instanceKlass org/gradle/api/tasks/ClasspathNormalizer
instanceKlass org/gradle/internal/fingerprint/IgnoredPathInputNormalizer
instanceKlass org/gradle/internal/fingerprint/NameOnlyInputNormalizer
instanceKlass org/gradle/internal/fingerprint/RelativePathInputNormalizer
instanceKlass org/gradle/internal/execution/fingerprint/impl/DefaultFileNormalizationSpec
instanceKlass org/gradle/internal/execution/fingerprint/FileNormalizationSpec
instanceKlass org/gradle/internal/fingerprint/AbsolutePathInputNormalizer
instanceKlass org/gradle/api/tasks/FileNormalizer
instanceKlass org/gradle/internal/fingerprint/impl/FileCollectionFingerprinterRegistrations$$Lambda$218
instanceKlass org/gradle/internal/fingerprint/impl/FingerprinterRegistration
instanceKlass org/gradle/internal/fingerprint/impl/FileCollectionFingerprinterRegistrations$$Lambda$217
instanceKlass org/gradle/internal/fingerprint/FileSystemLocationFingerprint
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass org/gradle/internal/fingerprint/impl/FileCollectionFingerprinterRegistrations$$Lambda$216
instanceKlass org/gradle/internal/snapshot/FileSystemLocationSnapshot$FileSystemLocationSnapshotVisitor
instanceKlass org/gradle/internal/fingerprint/impl/FileCollectionFingerprinterRegistrations$1
instanceKlass org/gradle/api/internal/changedetection/state/LineEndingNormalizingFileSystemLocationSnapshotHasher$1
instanceKlass org/gradle/api/internal/changedetection/state/LineEndingNormalizingFileSystemLocationSnapshotHasher
instanceKlass org/gradle/internal/fingerprint/hashing/FileSystemLocationSnapshotHasher$1
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$215
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$214
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$213
instanceKlass com/google/common/collect/RangeGwtSerializationDependencies
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$212
instanceKlass com/google/common/collect/ImmutableRangeSet$Builder
instanceKlass com/google/common/collect/SortedIterable
instanceKlass com/google/common/collect/AbstractRangeSet
instanceKlass com/google/common/collect/RangeSet
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$211
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$210
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$209
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$208
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$207
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$206
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$205
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$204
instanceKlass com/google/common/collect/CollectCollectors
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass org/gradle/internal/fingerprint/impl/FileCollectionFingerprinterRegistrations$$Lambda$203
instanceKlass org/gradle/internal/normalization/java/ApiClassExtractor$$Lambda$202
instanceKlass org/gradle/internal/normalization/java/ApiMemberWriterFactory
instanceKlass org/gradle/internal/normalization/java/impl/ApiMemberWriter
instanceKlass org/gradle/internal/normalization/java/ApiClassExtractor
instanceKlass org/gradle/api/internal/changedetection/state/AbiExtractingClasspathResourceHasher
instanceKlass org/gradle/internal/fingerprint/classpath/CompileClasspathFingerprinter
instanceKlass org/gradle/internal/fingerprint/hashing/FileSystemLocationSnapshotHasher
instanceKlass org/gradle/api/internal/changedetection/state/SplitResourceSnapshotterCacheService
instanceKlass org/gradle/internal/execution/history/changes/ChangeVisitor
instanceKlass org/gradle/internal/execution/history/changes/InputFileChanges
instanceKlass org/gradle/internal/execution/history/changes/ExecutionStateChanges
instanceKlass org/gradle/internal/execution/history/changes/ChangeContainer
instanceKlass org/gradle/internal/execution/history/changes/DefaultExecutionStateChangeDetector
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/GradlePluginVariantsSupport$TargetGradleVersionDisambiguationRule
instanceKlass org/gradle/api/internal/attributes/DefaultCompatibilityRuleChain$ExceptionHandler
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/GradlePluginVariantsSupport$TargetGradleVersionCompatibilityRule
instanceKlass org/gradle/api/attributes/AttributeCompatibilityRule
instanceKlass org/gradle/api/attributes/plugin/GradlePluginApiVersion
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/GradlePluginVariantsSupport
instanceKlass org/gradle/api/internal/attributes/DefaultDisambiguationRuleChain$ExceptionHandler
instanceKlass org/gradle/internal/action/DefaultConfigurableRules
instanceKlass org/gradle/internal/action/ConfigurableRules
instanceKlass org/gradle/api/artifacts/CacheableRule
instanceKlass org/gradle/internal/snapshot/impl/AbstractArraySnapshot
instanceKlass org/gradle/internal/snapshot/impl/AbstractScalarValueSnapshot
instanceKlass org/gradle/api/internal/DefaultActionConfiguration
instanceKlass org/gradle/internal/action/DefaultConfigurableRule
instanceKlass org/gradle/internal/action/InstantiatingAction
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/PlatformSupport$$Lambda$201
instanceKlass org/gradle/api/ActionConfiguration
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/PlatformSupport$ComponentCategoryDisambiguationRule
instanceKlass org/gradle/api/internal/ReusableAction
instanceKlass org/gradle/api/attributes/AttributeDisambiguationRule
instanceKlass org/gradle/api/internal/attributes/MultipleCandidatesResult
instanceKlass org/gradle/api/attributes/MultipleCandidatesDetails
instanceKlass org/gradle/api/internal/attributes/DefaultDisambiguationRuleChain
instanceKlass org/gradle/internal/action/InstantiatingAction$ExceptionHandler
instanceKlass org/gradle/api/internal/attributes/DefaultCompatibilityRuleChain
instanceKlass java/util/concurrent/LinkedBlockingDeque$Node
instanceKlass java/lang/management/MemoryUsage
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectionEvent
instanceKlass org/gradle/api/attributes/CompatibilityRuleChain
instanceKlass org/gradle/api/attributes/DisambiguationRuleChain
instanceKlass org/gradle/api/internal/attributes/DefaultAttributeMatchingStrategy
instanceKlass org/gradle/api/internal/attributes/CompatibilityCheckResult
instanceKlass org/gradle/api/attributes/CompatibilityCheckDetails
instanceKlass org/gradle/api/internal/attributes/DefaultAttributesSchema$MergedSchema
instanceKlass org/gradle/api/internal/attributes/DefaultAttributesSchema$DefaultAttributeMatcher
instanceKlass org/gradle/api/internal/attributes/AttributeDescriber
instanceKlass org/gradle/api/attributes/AttributeMatchingStrategy
instanceKlass org/gradle/internal/component/model/AttributeSelectionSchema
instanceKlass org/gradle/internal/component/model/AttributeMatcher
instanceKlass org/gradle/internal/component/model/ComponentAttributeMatcher
instanceKlass org/gradle/api/internal/attributes/DefaultAttributesSchema
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/store/StoreSet
instanceKlass org/gradle/api/internal/artifacts/DefaultGlobalDependencyResolutionRules$CompositeDependencySubstitutionRules
instanceKlass org/gradle/api/internal/artifacts/DefaultGlobalDependencyResolutionRules
instanceKlass org/gradle/api/artifacts/ComponentModuleMetadataDetails
instanceKlass org/gradle/api/artifacts/ComponentModuleMetadata
instanceKlass org/gradle/api/internal/artifacts/dsl/ComponentModuleMetadataContainer
instanceKlass org/gradle/api/internal/artifacts/dsl/ModuleReplacementsData
instanceKlass org/gradle/api/internal/artifacts/DefaultDependencyManagementServices$DependencyResolutionScopeServices$$Lambda$200
instanceKlass org/gradle/api/capabilities/CapabilitiesMetadata
instanceKlass org/gradle/internal/component/external/model/AbstractStatelessDerivationStrategy
instanceKlass org/gradle/api/internal/artifacts/dsl/MetadataRuleWrapper
instanceKlass org/gradle/api/internal/notations/ComponentIdentifierParserFactory
instanceKlass org/gradle/api/artifacts/DependencyConstraintMetadata
instanceKlass org/gradle/api/internal/catalog/parser/StrictVersionParser
instanceKlass org/gradle/api/internal/notations/DependencyStringNotationConverter
instanceKlass org/gradle/api/internal/notations/DependencyMetadataNotationParser
instanceKlass org/gradle/api/internal/artifacts/repositories/resolver/AbstractDependencyImpl
instanceKlass org/gradle/api/artifacts/DirectDependencyMetadata
instanceKlass org/gradle/api/artifacts/DependencyMetadata
instanceKlass org/gradle/internal/rules/DefaultRuleActionAdapter
instanceKlass org/gradle/api/artifacts/maven/PomModuleDescriptor
instanceKlass org/gradle/api/artifacts/ivy/IvyModuleDescriptor
instanceKlass org/gradle/internal/rules/DefaultRuleActionValidator
instanceKlass org/gradle/api/internal/artifacts/dsl/ComponentMetadataRuleContainer
instanceKlass org/gradle/internal/action/ConfigurableRule
instanceKlass org/gradle/internal/rules/RuleAction
instanceKlass org/gradle/api/internal/artifacts/dsl/SpecConfigurableRule
instanceKlass org/gradle/internal/rules/SpecRuleAction
instanceKlass org/gradle/internal/rules/RuleActionAdapter
instanceKlass org/gradle/internal/rules/RuleActionValidator
instanceKlass org/gradle/api/internal/artifacts/ComponentMetadataProcessor
instanceKlass org/gradle/api/attributes/Category$Impl
instanceKlass org/gradle/api/attributes/Category
instanceKlass org/gradle/internal/resolve/caching/ComponentMetadataRuleExecutor$$Lambda$199
instanceKlass org/gradle/internal/resolve/caching/ComponentMetadataRuleExecutor$$Lambda$198
instanceKlass org/gradle/internal/component/model/ConfigurationMetadata
instanceKlass org/gradle/internal/component/external/model/AbstractRealisedModuleResolveMetadataSerializationHelper
instanceKlass org/gradle/internal/component/external/model/VirtualComponentIdentifier
instanceKlass org/gradle/internal/component/external/model/ModuleComponentResolveMetadata
instanceKlass org/gradle/internal/resolve/caching/DesugaringAttributeContainerSerializer
instanceKlass org/gradle/api/internal/artifacts/DefaultDependencyManagementServices$DependencyResolutionScopeServices$$Lambda$197
instanceKlass com/google/common/collect/MapMakerInternalMap$StrongKeyDummyValueEntry$Helper
instanceKlass org/gradle/api/internal/collections/DefaultPendingSource
instanceKlass org/gradle/api/Namer$Comparator
instanceKlass org/gradle/api/internal/collections/SortedSetElementSource
instanceKlass org/gradle/api/Named$Namer
instanceKlass org/gradle/internal/instantiation/generator/InjectUtil
instanceKlass org/gradle/api/reflect/TypeOf
instanceKlass org/gradle/internal/instantiation/generator/Jsr330ConstructorSelector$$Lambda$196
instanceKlass org/gradle/internal/instantiation/generator/Jsr330ConstructorSelector$CachedConstructor
instanceKlass groovy/lang/Buildable
instanceKlass groovy/lang/Writable
instanceKlass org/gradle/internal/management/DefaultDependencyResolutionManagement$$Lambda$195
instanceKlass org/gradle/internal/lazy/Lazy$$Lambda$194
instanceKlass org/gradle/internal/lazy/Lazy$Factory
instanceKlass org/gradle/internal/lazy/LockingLazy
instanceKlass org/gradle/api/internal/provider/ValueSupplier$Present
instanceKlass org/gradle/api/internal/provider/ValueSupplier$Missing
instanceKlass org/gradle/api/internal/provider/ValueSupplier$Value
instanceKlass org/gradle/api/internal/provider/Providers
instanceKlass org/gradle/api/internal/provider/ValueSanitizers$4
instanceKlass org/gradle/api/internal/provider/ValueSanitizers$3
instanceKlass org/gradle/api/internal/provider/ValueSanitizers$2
instanceKlass org/gradle/api/internal/provider/ValueSanitizers$1
instanceKlass org/gradle/api/internal/provider/ValueCollector
instanceKlass org/gradle/api/internal/provider/ValueSanitizer
instanceKlass org/gradle/api/internal/provider/ValueSanitizers
instanceKlass org/gradle/api/internal/provider/AbstractProperty$FinalizationState
instanceKlass org/gradle/internal/management/DefaultDependencyResolutionManagement$ComponentMetadataRulesRegistar
instanceKlass org/gradle/internal/Describables$AbstractDescribable
instanceKlass org/gradle/internal/Describables
instanceKlass org/apache/groovy/util/BeanUtils
instanceKlass groovy/lang/MetaProperty
instanceKlass javax/annotation/meta/TypeQualifier
instanceKlass org/gradle/api/initialization/dsl/VersionCatalogBuilder
instanceKlass org/gradle/api/reflect/HasPublicType
instanceKlass org/gradle/internal/lazy/Lazy
instanceKlass org/gradle/api/initialization/resolve/MutableVersionCatalogContainer
instanceKlass org/gradle/internal/management/DefaultDependencyResolutionManagement
instanceKlass org/gradle/api/internal/artifacts/DefaultArtifactRepositoryContainer$$Lambda$193
instanceKlass org/gradle/api/internal/artifacts/DefaultArtifactRepositoryContainer$$Lambda$192
instanceKlass org/gradle/api/internal/collections/AbstractIterationOrderRetainingElementSource$RealizedElementCollectionIterator
instanceKlass org/gradle/api/internal/DefaultNamedDomainObjectCollection$UnfilteredIndex
instanceKlass org/gradle/api/internal/DefaultDomainObjectCollection$1
instanceKlass org/gradle/api/internal/collections/DefaultCollectionEventRegister
instanceKlass org/gradle/api/internal/collections/ListElementSource$1
instanceKlass org/gradle/api/internal/provider/Collector
instanceKlass org/gradle/api/internal/collections/AbstractIterationOrderRetainingElementSource
instanceKlass org/gradle/api/internal/artifacts/DefaultArtifactRepositoryContainer$RepositoryNamer
instanceKlass java/lang/SafeVarargs
instanceKlass com/google/common/reflect/Types$WildcardTypeImpl
instanceKlass java/util/StringJoiner
instanceKlass sun/reflect/generics/tree/ArrayTypeSignature
instanceKlass sun/reflect/generics/tree/IntSignature
instanceKlass java/lang/Class$EnclosingMethodInfo
instanceKlass com/google/common/reflect/Types$ClassOwnership$1LocalClass
instanceKlass com/google/common/reflect/Types$ParameterizedTypeImpl
instanceKlass com/google/common/reflect/Types$1
instanceKlass com/google/common/reflect/Types
instanceKlass com/google/common/reflect/TypeResolver$TypeVariableKey
instanceKlass com/google/common/reflect/TypeResolver$TypeTable
instanceKlass com/google/common/reflect/TypeResolver
instanceKlass com/google/common/reflect/TypeVisitor
instanceKlass org/gradle/api/internal/collections/CollectionFilter
instanceKlass org/gradle/api/artifacts/repositories/RepositoryContentDescriptor
instanceKlass org/gradle/api/artifacts/repositories/InclusiveRepositoryContentDescriptor
instanceKlass org/gradle/api/artifacts/repositories/IvyArtifactRepository
instanceKlass org/gradle/api/artifacts/repositories/FlatDirectoryArtifactRepository
instanceKlass org/gradle/api/artifacts/repositories/MavenArtifactRepository
instanceKlass org/gradle/api/artifacts/repositories/MetadataSupplierAware
instanceKlass org/gradle/api/artifacts/repositories/AuthenticationSupported
instanceKlass org/gradle/api/artifacts/repositories/UrlArtifactRepository
instanceKlass org/gradle/api/internal/collections/IndexedElementSource
instanceKlass org/gradle/api/NamedDomainObjectCollectionSchema
instanceKlass org/gradle/api/Rule
instanceKlass org/gradle/api/NamedDomainObjectProvider
instanceKlass org/gradle/api/internal/DefaultNamedDomainObjectCollection$Index
instanceKlass org/gradle/api/internal/collections/ElementSource
instanceKlass org/gradle/api/internal/collections/PendingSource
instanceKlass org/gradle/api/internal/collections/CollectionEventRegister
instanceKlass org/gradle/api/internal/WithEstimatedSize
instanceKlass org/gradle/internal/metaobject/PropertyMixIn
instanceKlass org/gradle/internal/metaobject/MethodMixIn
instanceKlass org/gradle/api/internal/artifacts/dsl/RepositoryHandlerInternal
instanceKlass org/gradle/internal/component/external/model/CapabilityInternal
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/parser/GradleModuleMetadataParser
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/MavenVersionSelectorScheme
instanceKlass org/gradle/internal/component/external/model/ModuleDependencyMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/parser/PomParent
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/parser/AbstractModuleDescriptorParser
instanceKlass org/gradle/api/artifacts/repositories/AuthenticationContainer
instanceKlass org/gradle/api/artifacts/repositories/ArtifactRepository
instanceKlass org/gradle/api/internal/artifacts/repositories/DefaultBaseRepositoryFactory
instanceKlass org/gradle/api/internal/provider/CredentialsProviderFactory
instanceKlass org/gradle/api/provider/ValueSourceSpec
instanceKlass org/gradle/api/file/FileContents
instanceKlass org/gradle/api/internal/provider/DefaultProviderFactory
instanceKlass org/gradle/api/internal/provider/ValueSourceProviderFactory$Listener
instanceKlass org/gradle/api/provider/ValueSourceParameters$None
instanceKlass org/gradle/api/provider/ValueSourceParameters
instanceKlass org/gradle/api/provider/ValueSource
instanceKlass org/gradle/internal/isolated/IsolationScheme
instanceKlass org/gradle/api/internal/provider/ValueSourceProviderFactory$Listener$ObtainedValue
instanceKlass org/gradle/api/internal/provider/DefaultValueSourceProviderFactory
instanceKlass org/gradle/api/internal/DefaultCollectionCallbackActionDecorator
instanceKlass org/apache/ivy/util/MessageLogger
instanceKlass org/gradle/api/internal/artifacts/ivyservice/DefaultIvyContextManager
instanceKlass org/gradle/internal/resource/local/CompositeLocallyAvailableResourceFinder
instanceKlass org/gradle/internal/resource/local/ivy/PatternBasedLocallyAvailableResourceFinder$1
instanceKlass org/gradle/api/internal/artifacts/repositories/resolver/AbstractResourcePattern
instanceKlass org/gradle/internal/resource/local/LocallyAvailableResourceFinderSearchableFileStoreAdapter$$Lambda$191
instanceKlass org/gradle/internal/resource/local/ivy/LocallyAvailableResourceFinderFactory$$Lambda$190
instanceKlass org/gradle/internal/resource/local/LocallyAvailableResourceCandidates
instanceKlass org/gradle/internal/resource/local/AbstractLocallyAvailableResourceFinder
instanceKlass org/gradle/internal/resource/local/ivy/LocallyAvailableResourceFinderFactory$$Lambda$189
instanceKlass org/gradle/api/internal/artifacts/repositories/resolver/ResourcePattern
instanceKlass org/gradle/internal/resource/local/ivy/LocallyAvailableResourceFinderFactory
instanceKlass org/gradle/internal/resource/local/FileResourceListener
instanceKlass org/gradle/api/internal/artifacts/repositories/transport/RepositoryTransport
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolutionstrategy/ExternalResourceCachePolicy
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$188
instanceKlass org/gradle/internal/resource/local/LocallyAvailableExternalResource
instanceKlass org/gradle/internal/resource/ExternalResource
instanceKlass org/gradle/internal/resource/local/FileResourceConnector
instanceKlass org/gradle/api/internal/artifacts/mvnsettings/DefaultLocalMavenRepositoryLocator$CurrentSystemPropertyAccess
instanceKlass org/gradle/api/internal/artifacts/mvnsettings/DefaultLocalMavenRepositoryLocator$SystemPropertyAccess
instanceKlass org/gradle/api/internal/artifacts/mvnsettings/DefaultLocalMavenRepositoryLocator
instanceKlass org/gradle/api/internal/artifacts/mvnsettings/DefaultMavenFileLocations
instanceKlass org/apache/maven/settings/io/SettingsReader
instanceKlass org/apache/maven/settings/building/SettingsBuildingRequest
instanceKlass org/gradle/api/internal/artifacts/mvnsettings/DefaultMavenSettingsProvider
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/CapabilitiesConflictHandler$Resolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/ModuleConflictHandler
instanceKlass org/gradle/internal/resolve/resolver/ResolveContextToComponentResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/dependencysubstitution/DependencySubstitutionApplicator
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/CapabilitiesConflictHandler
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/ConflictHandler
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/DependencyGraphVisitor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/DefaultArtifactDependencyResolver
instanceKlass org/gradle/api/artifacts/result/ComponentSelectionDescriptor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/CachingComponentSelectionDescriptorFactory
instanceKlass org/gradle/internal/resolve/caching/CrossBuildCachingRuleExecutor$AnySerializer
instanceKlass org/gradle/internal/resolve/caching/ComponentMetadataSupplierRuleExecutor$$Lambda$187
instanceKlass org/gradle/internal/resolve/caching/CrossBuildCachingRuleExecutor$EntryValidator
instanceKlass org/gradle/internal/resolve/caching/CrossBuildCachingRuleExecutor$CachedEntry
instanceKlass org/gradle/api/internal/artifacts/configurations/dynamicversion/CachePolicy
instanceKlass org/gradle/internal/resolve/caching/ComponentMetadataSupplierRuleExecutor$$Lambda$186
instanceKlass org/gradle/api/artifacts/ModuleVersionIdentifier
instanceKlass org/gradle/api/artifacts/ResolvedModuleVersion
instanceKlass org/gradle/internal/resolve/caching/ImplicitInputRecorder
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$1
instanceKlass org/gradle/api/artifacts/ComponentMetadata
instanceKlass org/gradle/api/internal/artifacts/ModuleVersionIdentifierSerializer
instanceKlass org/gradle/api/internal/artifacts/DefaultComponentSelectorConverter
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/simple/DefaultExcludeNothing
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/specs/ExcludeNothing
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/factories/Unions
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/factories/Intersections
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/specs/GroupSetExclude
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/specs/ModuleSetExclude
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/simple/DefaultExcludeFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/factories/DelegatingExcludeFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/factories/CachingExcludeFactory$ConcurrentCache
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/factories/CachingExcludeFactory$MergeCaches
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/specs/ExcludeSpec
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/factories/ExcludeFactory
instanceKlass org/gradle/api/internal/artifacts/component/DefaultComponentIdentifierFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ChangingValueDependencyResolutionListener$1
instanceKlass org/gradle/api/artifacts/component/ModuleComponentSelector
instanceKlass org/gradle/api/internal/artifacts/configurations/dynamicversion/Expiry
instanceKlass org/gradle/api/artifacts/component/ModuleComponentIdentifier
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ChangingValueDependencyResolutionListener
instanceKlass org/gradle/api/artifacts/result/ResolvedArtifactResult
instanceKlass org/gradle/api/artifacts/result/ArtifactResult
instanceKlass org/gradle/api/internal/artifacts/MetadataResolutionContext
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ConnectionFailureRepositoryDisabler
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$2
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/verification/DependencyVerificationOverride$$Lambda$185
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/api/internal/artifacts/configurations/ResolutionStrategyInternal
instanceKlass org/gradle/api/artifacts/ResolutionStrategy
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$184
instanceKlass org/gradle/api/internal/artifacts/verification/signatures/SignatureVerificationService
instanceKlass org/gradle/security/internal/PublicKeyService
instanceKlass org/gradle/internal/resource/connector/ResourceConnectorSpecification
instanceKlass org/gradle/api/internal/artifacts/verification/signatures/DefaultSignatureVerificationServiceFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/ResolvedArtifactCaches
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/artifacts/InMemoryModuleArtifactCache
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$183
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$182
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$181
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$180
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$179
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/artifacts/DefaultModuleArtifactCache$CachedArtifactSerializer
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/artifacts/DefaultModuleArtifactCache$ArtifactAtRepositoryKeySerializer
instanceKlass org/gradle/api/internal/artifacts/metadata/ModuleComponentFileArtifactIdentifierSerializer
instanceKlass org/gradle/internal/component/local/model/ComponentFileArtifactIdentifier
instanceKlass org/gradle/api/internal/artifacts/metadata/ComponentArtifactIdentifierSerializer
instanceKlass org/gradle/internal/component/external/model/DefaultModuleComponentArtifactIdentifier
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/artifacts/CachedArtifact
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/artifacts/CachedArtifacts
instanceKlass org/gradle/api/internal/artifacts/ModuleComponentSelectorSerializer
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/ModuleMetadataSerializer
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/ModuleMetadataStore
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/ModuleMetadataCache$CachedMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/dynamicversions/ModuleVersionsCache$CachedModuleVersionList
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$178
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/component/model/ModuleSources
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ModuleDescriptorHashCodec
instanceKlass org/gradle/api/internal/artifacts/repositories/metadata/MetadataFileSource
instanceKlass org/gradle/internal/component/model/PersistentModuleSource
instanceKlass org/gradle/internal/component/model/ModuleSource
instanceKlass org/gradle/api/internal/artifacts/repositories/metadata/DefaultMetadataFileSourceCodec
instanceKlass org/gradle/internal/component/model/PersistentModuleSource$Codec
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$177
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$176
instanceKlass org/gradle/api/internal/filestore/DefaultArtifactIdentifierFileStore$$Lambda$175
instanceKlass org/gradle/internal/component/external/model/ModuleComponentArtifactIdentifier
instanceKlass org/gradle/api/artifacts/component/ComponentArtifactIdentifier
instanceKlass org/gradle/api/internal/filestore/DefaultArtifactIdentifierFileStore$1
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$174
instanceKlass org/gradle/internal/resource/cached/CachedExternalResource
instanceKlass org/gradle/internal/resource/metadata/ExternalResourceMetaData
instanceKlass org/gradle/internal/resource/cached/DefaultCachedExternalResourceIndex$CachedExternalResourceSerializer
instanceKlass org/gradle/internal/resource/cached/CachedItem
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ArtifactCachesProvider$$Lambda$173
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$172
instanceKlass org/gradle/internal/resource/local/LocallyAvailableResource
instanceKlass org/gradle/internal/resource/local/DefaultPathKeyFileStore
instanceKlass org/gradle/internal/resource/cached/DefaultExternalResourceFileStore$$Lambda$171
instanceKlass org/gradle/api/Namer
instanceKlass org/gradle/internal/resource/cached/DefaultExternalResourceFileStore$1
instanceKlass org/gradle/internal/resource/local/GroupedAndNamedUniqueFileStore$Grouper
instanceKlass org/gradle/internal/resource/local/PathKeyFileStore
instanceKlass org/gradle/internal/hash/ChecksumHasher
instanceKlass org/gradle/internal/hash/DefaultChecksumService
instanceKlass org/gradle/internal/component/external/descriptor/Configuration
instanceKlass org/gradle/internal/component/model/IvyArtifactName
instanceKlass org/gradle/internal/component/external/model/ivy/MutableIvyModuleResolveMetadata
instanceKlass org/gradle/api/internal/artifacts/repositories/metadata/DefaultMavenImmutableAttributesFactory
instanceKlass org/gradle/internal/component/external/model/maven/MutableMavenModuleResolveMetadata
instanceKlass org/gradle/internal/component/external/model/MutableModuleComponentResolveMetadata
instanceKlass org/gradle/api/internal/artifacts/repositories/metadata/MavenImmutableAttributesFactory
instanceKlass org/gradle/internal/component/external/model/PreferJavaRuntimeVariant$PreferJarVariantUsageDisambiguationRule
instanceKlass org/gradle/internal/component/external/model/PreferJavaRuntimeVariant$PreferRuntimeVariantUsageDisambiguationRule
instanceKlass org/gradle/api/attributes/LibraryElements$Impl
instanceKlass org/gradle/api/attributes/Usage$Impl
instanceKlass org/gradle/model/internal/type/ClassTypeWrapper
instanceKlass org/gradle/model/internal/type/TypeWrapper
instanceKlass org/gradle/model/internal/type/ModelType
instanceKlass org/gradle/model/internal/inspect/FormattingValidationProblemCollector
instanceKlass org/gradle/api/attributes/LibraryElements
instanceKlass org/gradle/api/attributes/Usage
instanceKlass org/gradle/api/internal/attributes/EmptySchema$DoNothingDisambiguationRule
instanceKlass org/gradle/api/internal/attributes/EmptySchema$DoNothingCompatibilityRule
instanceKlass org/gradle/api/internal/attributes/DisambiguationRule
instanceKlass org/gradle/api/internal/attributes/CompatibilityRule
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ModuleComponentRepository
instanceKlass org/gradle/api/internal/tasks/properties/annotations/InputPropertyAnnotationHandler
instanceKlass org/gradle/api/internal/tasks/properties/annotations/NoOpPropertyAnnotationHandler
instanceKlass org/gradle/api/internal/tasks/properties/PropertyValue
instanceKlass org/gradle/api/internal/tasks/properties/annotations/NestedBeanAnnotationHandler
instanceKlass org/gradle/api/internal/tasks/properties/annotations/DestroysPropertyAnnotationHandler
instanceKlass org/gradle/api/internal/tasks/properties/annotations/LocalStatePropertyAnnotationHandler
instanceKlass org/gradle/api/internal/artifacts/transform/CacheableTransformTypeAnnotationHandler
instanceKlass org/gradle/api/internal/tasks/properties/annotations/CacheableTaskTypeAnnotationHandler
instanceKlass org/gradle/api/internal/tasks/properties/annotations/DisableCachingByDefaultTypeAnnotationHandler
instanceKlass org/gradle/vcs/internal/services/VersionControlServices$VcsResolverFactory
instanceKlass org/gradle/vcs/internal/resolver/OncePerBuildInvocationVcsVersionWorkingDirResolver
instanceKlass org/gradle/vcs/internal/resolver/DefaultVcsVersionWorkingDirResolver
instanceKlass org/gradle/vcs/internal/VersionRef
instanceKlass org/gradle/vcs/internal/resolver/PersistentVcsMetadataCache$VersionRefSerializer
instanceKlass org/gradle/cache/internal/CleanupActionFactory$BuildOperationCacheCleanupDecorator
instanceKlass org/gradle/internal/resource/local/ModificationTimeFileAccessTimeJournal
instanceKlass org/gradle/vcs/internal/VersionControlRepositoryConnection
instanceKlass org/gradle/vcs/internal/VersionControlSystem
instanceKlass org/gradle/vcs/internal/services/DefaultVersionControlRepositoryFactory
instanceKlass org/gradle/vcs/internal/DefaultVcsMappingsStore
instanceKlass org/gradle/vcs/internal/DefaultVcsMappingFactory
instanceKlass org/gradle/vcs/internal/services/DefaultVersionControlSpecFactory
instanceKlass org/gradle/internal/typeconversion/CharSequenceNotationConverter
instanceKlass org/gradle/api/internal/notations/ModuleIdentifierNotationConverter
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/DefaultLocalComponentRegistry
instanceKlass org/gradle/internal/component/local/model/LocalComponentMetadata
instanceKlass org/gradle/internal/component/local/model/LocalComponentArtifactMetadata
instanceKlass org/gradle/composite/internal/IncludedBuildDependencyMetadataBuilder
instanceKlass org/gradle/composite/internal/LocalComponentInAnotherBuildProvider
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/DefaultProjectLocalComponentProvider
instanceKlass org/gradle/internal/model/CalculatedValueContainerFactory$$Lambda$170
instanceKlass org/gradle/api/internal/tasks/NodeExecutionContext
instanceKlass org/gradle/api/artifacts/Configuration
instanceKlass org/gradle/api/attributes/HasConfigurableAttributes
instanceKlass org/gradle/api/internal/artifacts/configurations/ConfigurationInternal$VariantVisitor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/DefaultLocalComponentMetadataBuilder
instanceKlass org/gradle/internal/component/local/model/LocalFileDependencyMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/DefaultLocalConfigurationMetadataBuilder
instanceKlass org/gradle/util/internal/WrapUtil
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/DefaultDependencyDescriptorFactory
instanceKlass org/gradle/internal/component/model/LocalOriginDependencyMetadata
instanceKlass org/gradle/internal/component/model/ForcingDependencyMetadata
instanceKlass org/gradle/internal/component/model/DependencyMetadata
instanceKlass org/gradle/api/artifacts/Dependency
instanceKlass org/gradle/internal/component/model/ExcludeMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/DefaultExcludeRuleConverter
instanceKlass org/gradle/internal/resource/transport/sftp/SftpConnectorFactory
instanceKlass com/jcraft/jsch/HostKeyRepository
instanceKlass com/jcraft/jsch/Logger
instanceKlass org/gradle/internal/resource/transport/sftp/LockableSftpClient
instanceKlass org/gradle/internal/resource/transport/sftp/SftpClientFactory$SftpClientCreator
instanceKlass org/gradle/internal/resource/transport/aws/s3/S3ConnectorFactory
instanceKlass org/gradle/internal/resource/transport/gcp/gcs/GcsConnectorFactory
instanceKlass org/gradle/internal/resource/transfer/ExternalResourceConnector
instanceKlass org/gradle/internal/resource/transfer/ExternalResourceAccessor
instanceKlass org/gradle/internal/resource/transfer/ExternalResourceLister
instanceKlass org/gradle/internal/resource/transfer/ExternalResourceUploader
instanceKlass org/gradle/internal/resource/transport/http/HttpConnectorFactory
instanceKlass org/gradle/internal/resource/transport/http/HttpClientHelper$Factory$$Lambda$169
instanceKlass org/gradle/internal/resource/transport/http/HttpClientHelper
instanceKlass org/gradle/internal/resource/transport/http/HttpSettings
instanceKlass org/gradle/internal/resource/transport/http/DefaultSslContextFactory
instanceKlass org/gradle/internal/resource/transport/file/FileConnectorFactory
instanceKlass org/gradle/internal/resource/UriTextResource$UriResourceLocation
instanceKlass org/gradle/api/internal/initialization/DefaultScriptHandler
instanceKlass org/gradle/api/internal/artifacts/DefaultDependencyManagementServices$DefaultDependencyResolutionServices
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformedVariantFactory$VariantKey
instanceKlass org/gradle/api/internal/artifacts/transform/TransformationNodeFactory
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformedVariantFactory
instanceKlass org/gradle/api/internal/artifacts/dsl/DefaultComponentModuleMetadataHandler
instanceKlass org/gradle/api/internal/artifacts/repositories/DefaultUrlArtifactRepository$Factory
instanceKlass org/gradle/api/internal/artifacts/transform/TransformedVariantFactory
instanceKlass org/gradle/api/internal/artifacts/ComponentModuleMetadataProcessor
instanceKlass org/gradle/api/internal/artifacts/ComponentMetadataProcessorFactory
instanceKlass org/gradle/api/internal/artifacts/dsl/DefaultComponentMetadataHandler
instanceKlass org/gradle/api/internal/artifacts/dsl/ComponentMetadataHandlerInternal
instanceKlass org/gradle/api/artifacts/dsl/DependencyLockingHandler
instanceKlass org/gradle/api/artifacts/dsl/DependencyHandler
instanceKlass org/gradle/api/artifacts/dsl/ComponentModuleMetadataHandler
instanceKlass org/gradle/api/artifacts/dsl/ComponentMetadataHandler
instanceKlass org/gradle/api/artifacts/dsl/ArtifactHandler
instanceKlass org/gradle/api/artifacts/dsl/DependencyConstraintHandler
instanceKlass org/gradle/api/internal/artifacts/configurations/ConfigurationContainerInternal
instanceKlass org/gradle/api/artifacts/ConfigurationContainer
instanceKlass org/gradle/api/internal/artifacts/RepositoriesSupplier
instanceKlass org/gradle/api/artifacts/dsl/RepositoryHandler
instanceKlass org/gradle/api/artifacts/ArtifactRepositoryContainer
instanceKlass org/gradle/api/NamedDomainObjectList
instanceKlass org/gradle/api/internal/artifacts/transform/MutableTransformationWorkspaceServices
instanceKlass org/gradle/internal/file/ReservedFileSystemLocation
instanceKlass org/gradle/api/file/ProjectLayout
instanceKlass org/gradle/api/internal/artifacts/transform/TransformationRegistrationFactory
instanceKlass org/gradle/api/internal/artifacts/query/ArtifactResolutionQueryFactory
instanceKlass org/gradle/api/internal/artifacts/BaseRepositoryFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/parser/MetaDataParser
instanceKlass org/gradle/api/internal/artifacts/ConfigurationResolver
instanceKlass org/gradle/api/internal/artifacts/transform/ArtifactTransforms
instanceKlass org/gradle/api/internal/artifacts/GlobalDependencyResolutionRules
instanceKlass org/gradle/internal/component/external/model/VariantDerivationStrategy
instanceKlass org/gradle/api/internal/artifacts/type/ArtifactTypeRegistry
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/DependencyLockingProvider
instanceKlass org/gradle/api/internal/artifacts/VariantTransformRegistry
instanceKlass org/gradle/api/internal/artifacts/transform/TransformerInvocationFactory
instanceKlass org/gradle/api/internal/tasks/TaskResolver
instanceKlass org/gradle/api/internal/artifacts/ArtifactPublicationServices
instanceKlass org/gradle/api/internal/artifacts/DefaultDependencyManagementServices$DependencyResolutionScopeServices
instanceKlass org/gradle/api/internal/artifacts/DefaultDependencyManagementServices$ArtifactTransformResolutionGradleUserHomeServices
instanceKlass org/gradle/internal/model/CalculatedModelValue
instanceKlass org/gradle/api/internal/initialization/RootScriptDomainObjectContext
instanceKlass org/gradle/internal/resource/ResourceLocation
instanceKlass org/gradle/internal/resource/UriTextResource
instanceKlass org/gradle/groovy/scripts/TextResourceScriptSource
instanceKlass org/gradle/initialization/BuildOperationSettingsProcessor$2$1
instanceKlass org/gradle/initialization/EvaluateSettingsBuildOperationType$Details
instanceKlass org/gradle/initialization/BuildOperationSettingsProcessor$2
instanceKlass org/gradle/initialization/DirectoryInitScriptFinder
instanceKlass org/gradle/initialization/CompositeInitScriptFinder
instanceKlass org/gradle/initialization/InitScriptFinder
instanceKlass org/gradle/initialization/DefaultGradleProperties
instanceKlass org/gradle/initialization/DefaultGradlePropertiesController$Loaded
instanceKlass org/gradle/initialization/DefaultSettingsLoader
instanceKlass org/gradle/initialization/SettingsAttachingSettingsLoader
instanceKlass org/gradle/internal/composite/CommandLineIncludedBuildSettingsLoader
instanceKlass org/gradle/internal/composite/ChildBuildRegisteringSettingsLoader
instanceKlass org/gradle/internal/composite/CompositeBuildSettingsLoader
instanceKlass org/gradle/initialization/InitScriptHandlingSettingsLoader
instanceKlass org/gradle/initialization/GradlePropertiesHandlingSettingsLoader
instanceKlass org/gradle/initialization/BuildOperationFiringSettingsPreparer$LoadBuild$1
instanceKlass org/gradle/initialization/LoadBuildBuildOperationType$Details
instanceKlass org/gradle/internal/operations/DefaultBuildOperationExecutor$$Lambda$168
instanceKlass org/gradle/initialization/BuildOperationFiringSettingsPreparer$LoadBuild
instanceKlass org/gradle/internal/build/DefaultBuildLifecycleController$$Lambda$167
instanceKlass org/gradle/internal/build/DefaultBuildLifecycleController$$Lambda$166
instanceKlass org/gradle/internal/buildtree/DefaultBuildTreeLifecycleController$$Lambda$165
instanceKlass org/gradle/internal/buildtree/DefaultBuildTreeLifecycleController$$Lambda$164
instanceKlass org/gradle/internal/build/ExecutionResult
instanceKlass org/gradle/tooling/internal/provider/runner/BuildModelActionRunner$ModelCreateAction
instanceKlass org/gradle/internal/logging/format/TersePrettyDurationFormatter
instanceKlass org/gradle/internal/buildevents/BuildResultLogger
instanceKlass org/gradle/util/internal/TreeVisitor
instanceKlass org/gradle/internal/buildevents/BuildExceptionReporter
instanceKlass org/gradle/internal/logging/format/DurationFormatter
instanceKlass org/gradle/internal/buildevents/BuildLogger
instanceKlass org/gradle/api/internal/tasks/execution/statistics/TaskExecutionStatisticsEventAdapter
instanceKlass org/gradle/tooling/internal/provider/FileSystemWatchingBuildActionRunner$1
instanceKlass org/gradle/internal/watch/options/FileSystemWatchingSettingsFinalizedProgressDetails
instanceKlass org/gradle/api/internal/tasks/testing/operations/ExecuteTestBuildOperationType$Result
instanceKlass org/gradle/api/internal/tasks/compile/CompileJavaBuildOperationType$Result
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge$Finished
instanceKlass org/gradle/internal/operations/OperationFinishEvent
instanceKlass org/gradle/internal/watch/vfs/impl/WatchingVirtualFileSystem$1$1
instanceKlass org/gradle/internal/watch/vfs/BuildStartedFileSystemWatchingBuildOperationType$Result
instanceKlass java/util/stream/SortedOps$RefSortingSink$$Lambda$163
instanceKlass org/gradle/internal/watch/registry/impl/WatchedHierarchies$FilterAlreadyCoveredSnapshotsVisitor
instanceKlass org/gradle/internal/watch/registry/impl/CheckIfNonEmptySnapshotVisitor
instanceKlass java/util/stream/ForEachOps$ForEachOp
instanceKlass java/util/stream/ForEachOps
instanceKlass org/gradle/internal/watch/registry/impl/WatchedHierarchies$$Lambda$162
instanceKlass org/gradle/internal/watch/registry/impl/WatchedHierarchies$$Lambda$161
instanceKlass java/util/stream/SortedOps
instanceKlass java/util/Comparator$$Lambda$160
instanceKlass org/gradle/internal/watch/registry/impl/WatchedHierarchies$$Lambda$159
instanceKlass java/util/function/ToIntFunction
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/watch/registry/impl/WatchedHierarchies$$Lambda$158
instanceKlass java/util/ArrayDeque$DeqSpliterator
instanceKlass org/gradle/internal/watch/registry/impl/WatchableHierarchies$$Lambda$157
instanceKlass org/gradle/internal/watch/vfs/impl/WatchingVirtualFileSystem$$Lambda$156
instanceKlass org/gradle/internal/watch/registry/impl/DefaultFileWatcherRegistry$$Lambda$155
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/watch/registry/impl/DefaultFileWatcherRegistry$$Lambda$154
instanceKlass org/gradle/internal/watch/registry/impl/DefaultFileWatcherRegistry$MutableFileWatchingStatistics
instanceKlass net/rubygrapefruit/platform/file/FileWatchEvent$Handler
instanceKlass org/gradle/internal/watch/registry/FileWatcherRegistry$FileWatchingStatistics
instanceKlass org/gradle/internal/watch/registry/impl/DefaultFileWatcherRegistry
instanceKlass org/gradle/internal/snapshot/SnapshotHierarchy$SnapshotVisitor
instanceKlass org/gradle/internal/watch/registry/impl/WatchedHierarchies
instanceKlass org/gradle/internal/watch/registry/impl/WindowsFileWatcherRegistryFactory$$Lambda$153
instanceKlass org/gradle/internal/watch/registry/impl/HierarchicalFileWatcherUpdater$MovedHierarchyHandler
instanceKlass org/gradle/internal/watch/registry/impl/HierarchicalFileWatcherUpdater$FileSystemLocationToWatchValidator$$Lambda$152
instanceKlass org/gradle/internal/watch/registry/impl/HierarchicalFileWatcherUpdater$FileSystemLocationToWatchValidator
instanceKlass org/gradle/internal/watch/registry/impl/HierarchicalFileWatcherUpdater
instanceKlass org/gradle/internal/snapshot/FileSystemSnapshotHierarchyVisitor
instanceKlass org/gradle/internal/watch/registry/impl/WatchableHierarchies
instanceKlass net/rubygrapefruit/platform/internal/jni/AbstractFileEventFunctions$NativeFileWatcher
instanceKlass net/rubygrapefruit/platform/file/FileWatchEvent
instanceKlass net/rubygrapefruit/platform/internal/jni/AbstractFileEventFunctions$NativeFileWatcherCallback
instanceKlass org/gradle/internal/watch/vfs/impl/WatchingVirtualFileSystem$3
instanceKlass org/gradle/internal/watch/vfs/BuildStartedFileSystemWatchingBuildOperationType$Details$1
instanceKlass org/gradle/internal/watch/vfs/BuildStartedFileSystemWatchingBuildOperationType$Details
instanceKlass org/gradle/internal/watch/vfs/FileSystemWatchingStatistics
instanceKlass org/gradle/internal/watch/vfs/impl/WatchingVirtualFileSystem$1
instanceKlass org/gradle/internal/watch/vfs/impl/WatchingVirtualFileSystem$$Lambda$151
instanceKlass org/gradle/api/tasks/util/internal/CachingPatternSpecFactory$CachingSpec
instanceKlass org/gradle/api/internal/file/RelativePathSpec
instanceKlass org/gradle/api/internal/file/pattern/AnythingMatcher
instanceKlass org/gradle/api/internal/file/pattern/FixedPatternStep
instanceKlass org/gradle/api/internal/file/pattern/HasSuffixPatternStep
instanceKlass org/gradle/api/internal/file/pattern/HasPrefixPatternStep
instanceKlass org/gradle/api/internal/file/pattern/HasPrefixAndSuffixPatternStep
instanceKlass org/gradle/api/internal/file/pattern/AnyWildcardPatternStep
instanceKlass org/gradle/api/internal/file/pattern/PatternStep
instanceKlass org/gradle/api/internal/file/pattern/PatternStepFactory
instanceKlass org/gradle/api/internal/file/pattern/FixedStepPathMatcher
instanceKlass org/gradle/api/internal/file/pattern/GreedyPathMatcher
instanceKlass org/gradle/api/internal/file/pattern/EndOfPathMatcher
instanceKlass org/gradle/api/internal/file/pattern/PatternMatcher
instanceKlass org/gradle/api/internal/file/pattern/PathMatcher
instanceKlass org/gradle/api/internal/file/pattern/PatternMatcherFactory
instanceKlass org/gradle/api/tasks/util/internal/CachingPatternSpecFactory$1
instanceKlass org/gradle/api/tasks/util/internal/CachingPatternSpecFactory$SpecKey
instanceKlass org/gradle/launcher/exec/RootBuildLifecycleBuildActionExecutor$$Lambda$150
instanceKlass org/gradle/internal/watch/vfs/impl/WatchingVirtualFileSystem$$Lambda$149
instanceKlass java/util/function/UnaryOperator
instanceKlass org/gradle/execution/taskgraph/DefaultTaskExecutionGraph
instanceKlass org/gradle/api/specs/Specs$2
instanceKlass org/gradle/api/specs/Specs$1
instanceKlass org/gradle/api/specs/Specs
instanceKlass org/gradle/execution/plan/FailureCollector
instanceKlass org/gradle/execution/plan/DefaultNodeValidator
instanceKlass org/gradle/execution/plan/DefaultExecutionPlan
instanceKlass org/gradle/internal/graph/CachingDirectedGraphWalker$GraphWithEmptyEdges
instanceKlass org/gradle/api/internal/tasks/CachingTaskDependencyResolveContext$TaskGraphImpl
instanceKlass org/gradle/internal/graph/DirectedGraphWithEdgeValues
instanceKlass org/gradle/internal/graph/CachingDirectedGraphWalker
instanceKlass org/gradle/internal/graph/DirectedGraph
instanceKlass org/gradle/api/internal/tasks/AbstractTaskDependencyResolveContext
instanceKlass org/gradle/api/internal/tasks/TaskDependencyResolveContext
instanceKlass org/gradle/configuration/internal/DefaultListenerBuildOperationDecorator
instanceKlass org/gradle/execution/plan/DefaultPlanExecutor
instanceKlass org/gradle/execution/plan/TaskNodeFactory$DefaultTypeOriginInspectorFactory
instanceKlass org/gradle/api/internal/tasks/TaskExecutionContext
instanceKlass org/gradle/internal/snapshot/EmptyChildMap
instanceKlass org/gradle/internal/collect/PersistentList
instanceKlass org/gradle/internal/snapshot/ChildMap$StoreHandler
instanceKlass org/gradle/internal/snapshot/ChildMap$NodeHandler
instanceKlass org/gradle/execution/plan/ValuedVfsHierarchy
instanceKlass org/gradle/execution/plan/ExecutionNodeAccessHierarchy$AbstractNodeAccessVisitor
instanceKlass org/gradle/execution/plan/ValuedVfsHierarchy$ValueVisitor
instanceKlass org/gradle/execution/plan/ExecutionNodeAccessHierarchy
instanceKlass org/gradle/internal/build/ExportedTaskNode
instanceKlass org/gradle/internal/build/DefaultBuildWorkGraph
instanceKlass org/gradle/internal/buildtree/DefaultBuildTreeLifecycleController
instanceKlass org/gradle/internal/buildtree/BuildTreeLifecycleController
instanceKlass org/gradle/internal/buildtree/DefaultBuildTreeModelCreator
instanceKlass org/gradle/internal/buildtree/BuildTreeModelCreator
instanceKlass org/gradle/internal/buildtree/DefaultBuildTreeWorkPreparer
instanceKlass org/gradle/internal/buildtree/BuildTreeWorkPreparer
instanceKlass org/gradle/internal/buildtree/DefaultBuildTreeFinishExecutor
instanceKlass org/gradle/internal/buildtree/DefaultBuildTreeWorkExecutor
instanceKlass org/gradle/internal/buildtree/BuildOperationFiringBuildTreeWorkExecutor
instanceKlass org/gradle/execution/SelectedTaskExecutionAction
instanceKlass org/gradle/execution/DryRunBuildExecutionAction
instanceKlass org/gradle/execution/BuildExecutionAction
instanceKlass org/gradle/execution/DefaultBuildWorkExecutor
instanceKlass org/gradle/execution/UndefinedBuildWorkExecutor
instanceKlass org/gradle/execution/BuildOperationFiringBuildWorkerExecutor
instanceKlass org/gradle/internal/build/DefaultBuildWorkPreparer
instanceKlass org/gradle/internal/taskgraph/CalculateTaskGraphBuildOperationType$TaskIdentity
instanceKlass org/gradle/internal/build/BuildOperationFiringBuildWorkPreparer
instanceKlass org/gradle/initialization/internal/InternalBuildFinishedListener
instanceKlass org/gradle/BuildResult
instanceKlass org/gradle/internal/build/DefaultBuildLifecycleController
instanceKlass org/gradle/initialization/VintageBuildModelController
instanceKlass org/gradle/initialization/DefaultTaskExecutionPreparer
instanceKlass org/gradle/execution/BuildExecutionContext
instanceKlass org/gradle/execution/DefaultBuildConfigurationActionExecuter
instanceKlass org/gradle/execution/TaskNameResolvingBuildConfigurationAction
instanceKlass org/gradle/execution/DefaultTasksBuildExecutionAction
instanceKlass org/gradle/execution/BuildConfigurationAction
instanceKlass org/gradle/execution/commandline/CommandLineTaskConfigurer
instanceKlass org/gradle/api/internal/tasks/options/OptionValueNotationParserFactory
instanceKlass org/gradle/initialization/DefaultSettingsPreparer
instanceKlass org/gradle/initialization/BuildOperationFiringSettingsPreparer$1
instanceKlass org/gradle/initialization/LoadBuildBuildOperationType$Result
instanceKlass org/gradle/initialization/BuildOperationFiringSettingsPreparer
instanceKlass org/gradle/initialization/SettingsLoader
instanceKlass org/gradle/initialization/DefaultSettingsLoaderFactory
instanceKlass org/gradle/internal/id/LongIdGenerator
instanceKlass org/gradle/configuration/DefaultInitScriptProcessor
instanceKlass org/gradle/initialization/SettingsFactory
instanceKlass org/gradle/initialization/ScriptEvaluatingSettingsProcessor
instanceKlass org/gradle/initialization/SettingsEvaluatedCallbackFiringSettingsProcessor
instanceKlass org/gradle/initialization/RootBuildCacheControllerSettingsProcessor
instanceKlass org/gradle/initialization/BuildOperationSettingsProcessor$1
instanceKlass org/gradle/initialization/EvaluateSettingsBuildOperationType$Result
instanceKlass org/gradle/initialization/BuildOperationSettingsProcessor
instanceKlass org/gradle/internal/resource/TextResource
instanceKlass org/gradle/internal/resource/DefaultTextFileResourceLoader
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/UnknownProjectFinder
instanceKlass org/gradle/api/internal/initialization/ScriptHandlerInternal
instanceKlass org/gradle/api/initialization/dsl/ScriptHandler
instanceKlass org/gradle/api/internal/initialization/DefaultScriptHandlerFactory
instanceKlass org/gradle/api/internal/initialization/DefaultScriptClassPathResolver
instanceKlass org/gradle/configuration/ScriptPluginFactorySelector$1
instanceKlass org/gradle/configuration/ScriptPluginFactorySelector$ProviderInstantiator
instanceKlass org/gradle/configuration/ScriptPluginFactorySelector
instanceKlass org/gradle/groovy/scripts/internal/StatementTransformer
instanceKlass org/gradle/groovy/scripts/internal/CompileOperation
instanceKlass org/gradle/groovy/scripts/Transformer
instanceKlass org/gradle/configuration/project/DefaultCompileOperationFactory
instanceKlass org/gradle/plugin/use/internal/PluginDependencyResolutionServices$DefaultPluginRepositoriesProvider
instanceKlass org/gradle/plugin/use/resolve/internal/PluginResolutionResult
instanceKlass org/gradle/plugin/use/internal/DefaultPluginRequestApplicator
instanceKlass org/gradle/cache/internal/DefaultCrossBuildInMemoryCacheFactory$DefaultClassMap$$Lambda$148
instanceKlass org/gradle/plugin/management/internal/DefaultPluginResolutionStrategy
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/CachingVersionSelectorScheme
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/VersionSelector
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/DefaultVersionSelectorScheme
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/Version
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/StaticVersionComparator
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/DefaultVersionComparator
instanceKlass org/gradle/plugin/use/resolve/service/internal/ClientInjectedClasspathPluginResolver$$Lambda$147
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/plugin/internal/PluginUsePluginServiceRegistry$BuildScopeServices$1
instanceKlass org/gradle/plugin/use/internal/PluginRepositoriesProvider
instanceKlass org/gradle/api/internal/artifacts/Module
instanceKlass org/gradle/internal/service/scopes/BuildScopeServices$DependencyMetaDataProviderImpl
instanceKlass org/gradle/api/internal/artifacts/DefaultDependencyManagementServices
instanceKlass org/gradle/api/internal/file/AbstractFileResolver$2
instanceKlass org/apache/commons/io/FilenameUtils
instanceKlass org/gradle/internal/typeconversion/NotationConverterToNotationParserAdapter$ResultImpl
instanceKlass org/gradle/util/internal/DeferredUtil
instanceKlass org/gradle/api/internal/plugins/PluginDescriptorLocator
instanceKlass org/gradle/api/internal/plugins/PluginImplementation
instanceKlass org/gradle/api/internal/plugins/DefaultPluginRegistry
instanceKlass org/gradle/api/internal/plugins/PotentialPlugin
instanceKlass org/gradle/model/internal/inspect/ModelRuleSourceDetector$1
instanceKlass com/google/common/collect/MapMakerInternalMap$StrongKeyWeakValueEntry$Helper
instanceKlass org/gradle/api/internal/initialization/ClassLoaderScopeIdentifier
instanceKlass org/gradle/api/internal/initialization/AbstractClassLoaderScope
instanceKlass org/gradle/api/internal/initialization/loadercache/ClassLoaderId
instanceKlass org/gradle/initialization/ClassLoaderScopeId
instanceKlass org/gradle/initialization/DefaultClassLoaderScopeRegistry
instanceKlass org/gradle/api/internal/initialization/loadercache/DefaultClassLoaderCache$ClassLoaderSpec
instanceKlass org/gradle/api/internal/initialization/loadercache/DefaultClassLoaderCache
instanceKlass org/gradle/composite/internal/plugins/CompositeBuildPluginResolverContributor$CompositeBuildPluginResolver
instanceKlass org/gradle/composite/internal/plugins/CompositeBuildPluginResolverContributor
instanceKlass org/gradle/internal/composite/DefaultBuildIncluder
instanceKlass org/gradle/plugin/management/internal/autoapply/DefaultAutoAppliedPluginHandler
instanceKlass org/gradle/plugin/management/internal/SingletonPluginRequests
instanceKlass java/util/DualPivotQuicksort
instanceKlass org/gradle/plugin/use/internal/DefaultPluginId
instanceKlass org/gradle/plugin/use/PluginId
instanceKlass org/gradle/plugin/management/internal/autoapply/AutoAppliedGradleEnterprisePlugin
instanceKlass org/gradle/plugin/management/internal/DefaultPluginRequest
instanceKlass org/gradle/api/internal/artifacts/DefaultModuleVersionSelector
instanceKlass org/gradle/api/artifacts/ModuleVersionSelector
instanceKlass org/gradle/api/internal/artifacts/DefaultModuleIdentifier
instanceKlass org/gradle/plugin/management/internal/PluginRequestInternal
instanceKlass org/gradle/plugin/management/PluginRequest
instanceKlass org/gradle/plugin/management/internal/autoapply/DefaultAutoAppliedPluginRegistry
instanceKlass org/gradle/workers/internal/DefaultWorkResult
instanceKlass org/gradle/api/tasks/WorkResult
instanceKlass org/gradle/internal/work/ConditionalExecutionQueue
instanceKlass org/gradle/plugin/use/resolve/internal/PluginResolver
instanceKlass org/gradle/groovy/scripts/internal/BuildScopeInMemoryCachingScriptClassCompiler
instanceKlass org/gradle/groovy/scripts/ScriptCompiler
instanceKlass org/gradle/groovy/scripts/DefaultScriptCompilerFactory
instanceKlass org/gradle/groovy/scripts/ScriptRunner
instanceKlass org/gradle/groovy/scripts/internal/DefaultScriptRunnerFactory
instanceKlass org/gradle/groovy/scripts/internal/BuildOperationBackedScriptCompilationHandler$1
instanceKlass org/gradle/internal/scripts/CompileScriptBuildOperationType$Result
instanceKlass org/gradle/groovy/scripts/internal/BuildOperationBackedScriptCompilationHandler
instanceKlass org/gradle/groovy/scripts/internal/DefaultScriptCompilationHandler$NoOpGroovyResourceLoader
instanceKlass groovy/lang/GroovyResourceLoader
instanceKlass org/gradle/groovy/scripts/internal/CompiledScript
instanceKlass com/google/common/base/AbstractIterator$1
instanceKlass com/google/common/base/AbstractIterator
instanceKlass com/google/common/base/Splitter$1
instanceKlass com/google/common/base/CharMatcher
instanceKlass com/google/common/base/Splitter$Strategy
instanceKlass com/google/common/base/Splitter
instanceKlass org/gradle/configuration/DefaultImportsReader$2
instanceKlass com/google/common/io/Java8Compatibility
instanceKlass com/google/common/io/LineBuffer
instanceKlass com/google/common/io/LineReader
instanceKlass com/google/common/io/CharStreams
instanceKlass org/gradle/configuration/DefaultImportsReader$1
instanceKlass com/google/common/io/Resources
instanceKlass org/gradle/configuration/DefaultImportsReader
instanceKlass org/gradle/configuration/ScriptPlugin
instanceKlass org/gradle/api/Plugin
instanceKlass org/gradle/configuration/internal/DefaultUserCodeApplicationContext
instanceKlass org/gradle/api/internal/artifacts/DefaultBuildIdentifier
instanceKlass org/gradle/composite/internal/CompositeBuildClassPathInitializer
instanceKlass org/gradle/composite/internal/IncludedBuildController
instanceKlass org/gradle/composite/internal/DefaultIncludedBuildControllers
instanceKlass org/gradle/api/tasks/TaskContainer
instanceKlass org/gradle/api/PolymorphicDomainObjectContainer
instanceKlass org/gradle/api/tasks/TaskCollection
instanceKlass org/gradle/execution/TaskSelectionResult
instanceKlass org/gradle/execution/TaskNameResolver
instanceKlass org/gradle/execution/ExcludedTaskFilteringProjectsPreparer
instanceKlass org/gradle/configuration/DefaultProjectsPreparer
instanceKlass org/gradle/configuration/BuildTreePreparingProjectsPreparer
instanceKlass org/gradle/configuration/BuildOperationFiringProjectsPreparer$1
instanceKlass org/gradle/initialization/ConfigureBuildBuildOperationType$Result
instanceKlass org/gradle/configuration/BuildOperationFiringProjectsPreparer
instanceKlass org/gradle/initialization/ModelConfigurationListener
instanceKlass org/gradle/initialization/InstantiatingBuildLoader
instanceKlass org/gradle/initialization/ProjectPropertySettingBuildLoader
instanceKlass org/gradle/initialization/NotifyingBuildLoader$$Lambda$146
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/initialization/NotifyingBuildLoader$1
instanceKlass org/gradle/initialization/LoadProjectsBuildOperationType$Result$Project
instanceKlass org/gradle/initialization/NotifyProjectsLoadedBuildOperationType$Result
instanceKlass org/gradle/initialization/NotifyingBuildLoader
instanceKlass org/gradle/initialization/DefaultGradlePropertiesController$SharedGradleProperties
instanceKlass org/gradle/initialization/DefaultGradlePropertiesController$NotLoaded
instanceKlass org/gradle/initialization/DefaultGradlePropertiesController$State
instanceKlass org/gradle/initialization/DefaultGradlePropertiesController
instanceKlass org/gradle/initialization/DefaultGradlePropertiesLoader
instanceKlass kotlin/SafePublicationLazyImpl$Companion
instanceKlass kotlin/SafePublicationLazyImpl
instanceKlass kotlin/reflect/jvm/internal/pcollections/MapEntry
instanceKlass kotlin/reflect/jvm/internal/ReflectProperties$Val$1
instanceKlass kotlin/reflect/jvm/internal/ReflectProperties$Val
instanceKlass kotlin/reflect/jvm/internal/ReflectProperties
instanceKlass kotlin/text/Regex$Companion
instanceKlass kotlin/text/Regex
instanceKlass kotlin/jvm/internal/DefaultConstructorMarker
instanceKlass kotlin/reflect/jvm/internal/KDeclarationContainerImpl$Companion
instanceKlass kotlin/reflect/jvm/internal/KClassifierImpl
instanceKlass kotlin/reflect/jvm/internal/pcollections/ConsPStack
instanceKlass kotlin/reflect/jvm/internal/pcollections/IntTree
instanceKlass kotlin/reflect/jvm/internal/pcollections/IntTreePMap
instanceKlass kotlin/reflect/jvm/internal/pcollections/HashPMap
instanceKlass kotlin/reflect/jvm/internal/KClassCacheKt
instanceKlass kotlin/reflect/jvm/internal/KPropertyImpl$Companion
instanceKlass kotlin/reflect/jvm/internal/KCallableImpl
instanceKlass kotlin/reflect/jvm/internal/KTypeParameterOwnerImpl
instanceKlass kotlin/reflect/jvm/internal/KDeclarationContainerImpl
instanceKlass kotlin/jvm/internal/ClassBasedDeclarationContainer
instanceKlass kotlin/reflect/KMutableProperty2
instanceKlass kotlin/reflect/KMutableProperty0
instanceKlass kotlin/reflect/KTypeParameter
instanceKlass kotlin/reflect/KType
instanceKlass kotlin/reflect/KProperty2
instanceKlass kotlin/reflect/KClass
instanceKlass kotlin/reflect/KMutableProperty1
instanceKlass kotlin/reflect/KMutableProperty
instanceKlass kotlin/reflect/KProperty1
instanceKlass kotlin/jvm/functions/Function1
instanceKlass kotlin/jvm/internal/ReflectionFactory
instanceKlass kotlin/reflect/KClassifier
instanceKlass kotlin/jvm/internal/Reflection
instanceKlass kotlin/jvm/internal/CallableReference$NoReceiver
instanceKlass kotlin/reflect/KProperty$Getter
instanceKlass kotlin/reflect/KFunction
instanceKlass kotlin/reflect/KProperty$Accessor
instanceKlass kotlin/reflect/KDeclarationContainer
instanceKlass kotlin/jvm/internal/CallableReference
instanceKlass kotlin/reflect/KProperty0
instanceKlass kotlin/reflect/KProperty
instanceKlass kotlin/reflect/KCallable
instanceKlass kotlin/reflect/KAnnotatedElement
instanceKlass org/gradle/kotlin/dsl/tooling/builders/BuildSrcClassPathModeConfigurationAction
instanceKlass org/gradle/initialization/buildsrc/GroovyBuildSrcProjectConfigurationAction
instanceKlass org/gradle/configuration/project/PluginsProjectConfigureActions
instanceKlass org/gradle/api/internal/InternalAction
instanceKlass org/gradle/configuration/project/ProjectConfigureAction
instanceKlass org/gradle/initialization/buildsrc/BuildSrcProjectConfigurationAction
instanceKlass org/gradle/initialization/buildsrc/BuildSrcBuildListenerFactory
instanceKlass org/gradle/initialization/buildsrc/BuildSourceBuilder$1
instanceKlass org/gradle/initialization/buildsrc/BuildBuildSrcBuildOperationType$Result
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$BuildSessionServices$$Lambda$145
instanceKlass org/gradle/api/internal/changedetection/state/SplitFileHasher
instanceKlass org/gradle/api/internal/provider/DefaultConfigurationTimeBarrier
instanceKlass org/gradle/api/internal/AbstractMutationGuard
instanceKlass org/gradle/api/internal/project/BuildOperationCrossProjectConfigurator
instanceKlass org/gradle/api/internal/WithMutationGuard
instanceKlass org/gradle/internal/concurrent/CompositeStoppable$2
instanceKlass org/gradle/internal/execution/WorkValidationContext
instanceKlass org/gradle/internal/execution/WorkValidationContext$TypeOriginInspector
instanceKlass org/gradle/configurationcache/ConfigurationCacheIO
instanceKlass org/gradle/configurationcache/ConfigurationCacheHost
instanceKlass org/gradle/configurationcache/DefaultConfigurationCache$Host
instanceKlass org/gradle/cache/internal/FileContentCacheFactory$Calculator
instanceKlass org/gradle/language/nativeplatform/internal/incremental/sourceparser/CachingCSourceParser
instanceKlass org/gradle/language/nativeplatform/internal/incremental/sourceparser/CSourceParser
instanceKlass org/gradle/language/nativeplatform/internal/incremental/DefaultCompilationStateCacheFactory
instanceKlass org/gradle/language/nativeplatform/internal/incremental/CompilationStateCacheFactory
instanceKlass org/gradle/internal/scan/config/BuildScanConfig
instanceKlass org/gradle/internal/scan/config/BuildScanConfig$Attributes
instanceKlass org/gradle/internal/enterprise/impl/legacy/LegacyGradleEnterprisePluginCheckInService
instanceKlass org/gradle/internal/scan/eob/BuildScanEndOfBuildNotifier
instanceKlass org/gradle/internal/scan/config/BuildScanConfigProvider
instanceKlass org/gradle/internal/enterprise/impl/legacy/DefaultBuildScanScopeIds
instanceKlass org/gradle/internal/scan/scopeids/BuildScanScopeIds
instanceKlass org/gradle/internal/enterprise/GradleEnterprisePluginCheckInResult
instanceKlass org/gradle/internal/enterprise/impl/DefautGradleEnterprisePluginCheckInService
instanceKlass org/gradle/internal/enterprise/GradleEnterprisePluginCheckInService
instanceKlass org/gradle/internal/enterprise/impl/DefaultGradleEnterprisePluginConfig
instanceKlass org/gradle/internal/enterprise/impl/DefaultGradleEnterprisePluginBuildState
instanceKlass org/gradle/internal/enterprise/impl/DefaultGradleEnterprisePluginServiceRef
instanceKlass org/gradle/internal/enterprise/GradleEnterprisePluginBuildState
instanceKlass org/gradle/internal/enterprise/GradleEnterprisePluginConfig
instanceKlass org/gradle/internal/enterprise/GradleEnterprisePluginEndOfBuildListener$BuildResult
instanceKlass org/gradle/internal/enterprise/GradleEnterprisePluginServiceRef
instanceKlass org/gradle/internal/enterprise/impl/DefaultGradleEnterprisePluginAdapter
instanceKlass org/gradle/internal/enterprise/core/GradleEnterprisePluginAdapter
instanceKlass org/gradle/initialization/DefaultJdkToolsInitializer
instanceKlass org/gradle/api/internal/tasks/compile/incremental/analyzer/CachingClassDependenciesAnalyzer
instanceKlass org/gradle/api/internal/tasks/compile/incremental/classpath/CachingClassSetAnalyzer
instanceKlass org/gradle/api/internal/tasks/compile/incremental/IncrementalCompilerFactory
instanceKlass org/gradle/api/internal/tasks/compile/incremental/analyzer/ClassDependenciesAnalyzer
instanceKlass org/gradle/api/internal/tasks/compile/incremental/classpath/ClassSetAnalyzer
instanceKlass org/gradle/api/internal/tasks/CompileServices$GradleScopeCompileServices
instanceKlass org/gradle/language/java/artifact/JavadocArtifact
instanceKlass org/gradle/language/java/internal/JavaLanguagePluginServiceRegistry$JavaGradleScopeServices
instanceKlass org/gradle/api/internal/artifacts/transform/TransformationNodeDependencyResolver
instanceKlass org/gradle/api/internal/artifacts/DependencyServices$DependencyManagementGradleServices
instanceKlass org/gradle/kotlin/dsl/accessors/PluginAccessorClassPathGenerator
instanceKlass org/gradle/kotlin/dsl/accessors/ProjectAccessorsClassPathGenerator
instanceKlass org/gradle/kotlin/dsl/accessors/GradleScopeServices
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/execution/OutputChangeListener
instanceKlass org/gradle/internal/execution/history/OutputFilesRepository
instanceKlass org/gradle/internal/service/scopes/ExecutionGradleServices
instanceKlass org/gradle/caching/internal/controller/BuildCacheController
instanceKlass org/gradle/caching/internal/origin/OriginMetadataFactory
instanceKlass org/gradle/caching/internal/packaging/impl/TarPackerFileSystemSupport
instanceKlass org/gradle/caching/internal/controller/BuildCacheCommandFactory
instanceKlass org/gradle/caching/internal/packaging/BuildCacheEntryPacker
instanceKlass org/gradle/caching/internal/packaging/impl/FilePermissionAccess
instanceKlass org/gradle/caching/internal/BuildCacheServices$3
instanceKlass org/gradle/internal/service/scopes/GradleScopeServices$$Lambda$144
instanceKlass org/gradle/api/execution/TaskExecutionGraphListener
instanceKlass org/gradle/api/services/internal/BuildServiceRegistryInternal
instanceKlass org/gradle/execution/commandline/CommandLineTaskParser
instanceKlass org/gradle/api/internal/tasks/options/OptionReader
instanceKlass org/gradle/execution/plan/TaskNodeDependencyResolver
instanceKlass org/gradle/execution/plan/WorkNodeExecutor
instanceKlass org/gradle/execution/plan/LocalTaskNodeExecutor
instanceKlass org/gradle/execution/plan/NodeExecutor
instanceKlass org/gradle/execution/plan/WorkNodeDependencyResolver
instanceKlass org/gradle/execution/plan/DependencyResolver
instanceKlass org/gradle/api/internal/tasks/WorkDependencyResolver
instanceKlass org/gradle/execution/plan/PlanExecutor
instanceKlass org/gradle/execution/plan/ExecutionNodeAccessHierarchies
instanceKlass org/gradle/execution/plan/TaskDependencyResolver
instanceKlass org/gradle/execution/plan/TaskNodeFactory
instanceKlass org/gradle/api/execution/TaskExecutionListener
instanceKlass org/gradle/execution/BuildConfigurationActionExecuter
instanceKlass org/gradle/internal/execution/BuildOutputCleanupRegistry
instanceKlass org/gradle/initialization/TaskExecutionPreparer
instanceKlass org/gradle/execution/plan/ExecutionPlan
instanceKlass org/gradle/execution/plan/NodeValidator
instanceKlass org/gradle/execution/BuildWorkExecutor
instanceKlass org/gradle/internal/ImmutableActionSet
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ObjectCreationDetails
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$InvokeConstructorStrategy
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$GeneratedClassImpl$GeneratedConstructorImpl
instanceKlass org/gradle/internal/instantiation/generator/ClassGenerator$GeneratedConstructor
instanceKlass org/gradle/internal/instantiation/generator/ClassGenerator$SerializationConstructor
instanceKlass java/lang/invoke/MethodHandles$Lookup$$Lambda$143
instanceKlass jdk/internal/org/objectweb/asm/ClassReader
instanceKlass org/objectweb/asm/Handler
instanceKlass org/objectweb/asm/Attribute
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$ReturnTypeEntry
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$142
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$141
instanceKlass com/google/common/collect/LinkedHashMultimap$ValueSet$1
instanceKlass com/google/common/collect/AbstractMapBasedMultimap$WrappedCollection$WrappedIterator
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$140
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$139
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$138
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$137
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$136
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$135
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$134
instanceKlass org/gradle/internal/reflect/JavaReflectionUtil
instanceKlass org/gradle/model/internal/asm/AsmClassGeneratorUtils
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$133
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$132
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$131
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$MethodCodeBody
instanceKlass org/objectweb/asm/Edge
instanceKlass org/objectweb/asm/Label
instanceKlass org/objectweb/asm/Frame
instanceKlass org/objectweb/asm/ByteVector
instanceKlass org/objectweb/asm/Symbol
instanceKlass org/objectweb/asm/SymbolTable
instanceKlass org/objectweb/asm/MethodVisitor
instanceKlass org/objectweb/asm/FieldVisitor
instanceKlass org/objectweb/asm/ModuleVisitor
instanceKlass org/objectweb/asm/AnnotationVisitor
instanceKlass org/objectweb/asm/RecordComponentVisitor
instanceKlass org/gradle/model/internal/asm/AsmClassGenerator
instanceKlass org/gradle/internal/DisplayName
instanceKlass org/gradle/internal/instantiation/generator/ManagedObjectFactory
instanceKlass org/gradle/util/internal/ConfigureUtil
instanceKlass org/gradle/internal/metaobject/AbstractDynamicObject
instanceKlass org/gradle/api/plugins/Convention
instanceKlass org/gradle/api/plugins/ExtensionContainer
instanceKlass org/gradle/internal/metaobject/DynamicObject
instanceKlass org/gradle/internal/metaobject/PropertyAccess
instanceKlass org/gradle/internal/metaobject/MethodAccess
instanceKlass org/gradle/internal/extensibility/ConventionAwareHelper
instanceKlass org/gradle/api/internal/HasConvention
instanceKlass org/gradle/api/internal/IConventionAware
instanceKlass org/gradle/api/internal/GeneratedSubclass
instanceKlass org/gradle/api/internal/ConventionMapping
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl
instanceKlass javax/annotation/Nullable
instanceKlass java/lang/module/ModuleDescriptor$Builder$$Lambda$130
instanceKlass jdk/internal/module/Checks
instanceKlass java/lang/module/ModuleDescriptor$Builder
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$$Lambda$129
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass jdk/internal/HotSpotIntrinsicCandidate
instanceKlass java/lang/Deprecated
instanceKlass org/gradle/api/internal/DynamicObjectAware
instanceKlass org/gradle/internal/extensibility/NoConventionMapping
instanceKlass sun/reflect/annotation/AnnotationParser$$Lambda$128
instanceKlass sun/reflect/generics/tree/Wildcard
instanceKlass sun/reflect/generics/tree/BottomSignature
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$MethodMetadata
instanceKlass org/gradle/internal/reflect/PropertyAccessor
instanceKlass org/gradle/internal/reflect/PropertyMutator
instanceKlass org/gradle/internal/reflect/JavaPropertyReflectionUtil
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$PropertyMetadata
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$ClassMetadata
instanceKlass org/gradle/configuration/ConfigurationTargetIdentifier
instanceKlass org/gradle/api/plugins/PluginContainer
instanceKlass org/gradle/api/plugins/PluginCollection
instanceKlass org/gradle/internal/reflect/MutablePropertyDetails
instanceKlass java/beans/Introspector$1
instanceKlass jdk/internal/misc/JavaBeansAccess
instanceKlass java/beans/FeatureDescriptor
instanceKlass com/sun/beans/WeakCache
instanceKlass java/beans/Introspector
instanceKlass org/gradle/internal/reflect/MethodSet$MethodKey
instanceKlass org/gradle/api/internal/initialization/ClassLoaderScope
instanceKlass org/gradle/api/internal/plugins/PluginManagerInternal
instanceKlass org/gradle/api/services/BuildServiceRegistry
instanceKlass org/gradle/api/internal/SettingsInternal
instanceKlass org/gradle/api/initialization/Settings
instanceKlass org/gradle/execution/taskgraph/TaskExecutionGraphInternal
instanceKlass org/gradle/api/internal/plugins/DefaultObjectConfigurationAction
instanceKlass org/gradle/api/plugins/ObjectConfigurationAction
instanceKlass groovy/lang/GroovyObjectSupport
instanceKlass groovy/lang/GroovyCallable
instanceKlass org/gradle/internal/MutableActionSet
instanceKlass org/gradle/api/execution/TaskExecutionGraph
instanceKlass org/gradle/api/plugins/PluginManager
instanceKlass org/gradle/internal/reflect/PropertyDetails
instanceKlass org/gradle/internal/reflect/MutableClassDetails
instanceKlass org/gradle/internal/reflect/ClassDetails
instanceKlass org/gradle/internal/reflect/ClassInspector
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$ClassGenerationVisitor
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassInspectionVisitorImpl
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$InjectionAnnotationValidator
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$DisabledAnnotationValidator
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$ClassValidator
instanceKlass com/google/common/collect/LinkedHashMultimap$ValueSetLink
instanceKlass org/gradle/internal/reflect/MethodSet
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$ClassGenerationHandler
instanceKlass org/gradle/api/internal/project/AbstractPluginAware
instanceKlass org/gradle/internal/build/DefaultBuildLifecycleControllerFactory$1
instanceKlass org/gradle/internal/featurelifecycle/ScriptUsageLocationReporter
instanceKlass kotlin/SynchronizedLazyImpl
instanceKlass org/gradle/util/internal/GUtil$1
instanceKlass org/gradle/util/Path
instanceKlass org/gradle/internal/build/DefaultPublicBuildPath
instanceKlass org/gradle/profile/BuildProfileServices$1$1
instanceKlass org/gradle/api/HasImplicitReceiver
instanceKlass org/gradle/api/NonExtensible
instanceKlass org/gradle/internal/build/DefaultBuildLifecycleControllerFactory$GradleModelProvider
instanceKlass org/gradle/internal/build/BuildModelController
instanceKlass org/gradle/api/internal/project/CrossProjectModelAccess
instanceKlass org/gradle/configurationcache/ConfigurationCacheServices$BuildScopeServicesProvider
instanceKlass kotlin/coroutines/Continuation
instanceKlass org/gradle/configurationcache/fingerprint/ConfigurationCacheFingerprintController
instanceKlass org/gradle/configurationcache/RelevantProjectsRegistry
instanceKlass org/gradle/configurationcache/SystemPropertyAccessListener
instanceKlass org/gradle/internal/classpath/Instrumented$Listener
instanceKlass org/gradle/configurationcache/ConfigurationCacheProblemsListenerManagerAction
instanceKlass org/gradle/configurationcache/initialization/ConfigurationCacheBuildEnablement
instanceKlass org/gradle/profile/ProfileListener
instanceKlass org/gradle/profile/ProfileEventAdapter
instanceKlass org/gradle/api/internal/artifacts/transform/ArtifactTransformListener
instanceKlass org/gradle/initialization/BuildCompletionListener
instanceKlass org/gradle/api/artifacts/DependencyResolutionListener
instanceKlass org/gradle/execution/taskgraph/TaskListenerInternal
instanceKlass org/gradle/api/ProjectEvaluationListener
instanceKlass org/gradle/internal/service/scopes/BuildScopeListenerManagerAction
instanceKlass org/gradle/profile/BuildProfileServices$1
instanceKlass org/gradle/vcs/internal/resolver/VcsDependencyResolver
instanceKlass org/gradle/vcs/internal/resolver/VcsVersionWorkingDirResolver
instanceKlass org/gradle/vcs/internal/services/VersionControlServices$VersionControlBuildServices
instanceKlass org/gradle/tooling/provider/model/ToolingModelBuilder
instanceKlass org/gradle/language/cpp/internal/tooling/ToolingNativeServices$ToolingModelRegistration
instanceKlass org/gradle/authentication/aws/AwsImAuthentication
instanceKlass org/gradle/internal/resource/transport/aws/s3/S3ResourcesPluginServiceRegistry$AuthenticationSchemeAction
instanceKlass org/gradle/nativeplatform/toolchain/internal/metadata/CompilerMetaDataProvider
instanceKlass org/gradle/nativeplatform/toolchain/internal/metadata/CompilerMetaDataProviderFactory
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/nativeplatform/internal/resolve/NativeDependencyResolver
instanceKlass org/gradle/nativeplatform/internal/resolve/LibraryBinaryLocator
instanceKlass org/gradle/nativeplatform/internal/resolve/NativeDependencyResolverServices
instanceKlass org/gradle/language/cpp/internal/NativeDependencyCache
instanceKlass org/gradle/ide/xcode/internal/xcodeproj/GidGenerator
instanceKlass org/gradle/ide/xcode/internal/services/XcodeServices$GlobalIdGeneratorServices
instanceKlass org/gradle/plugins/ide/internal/configurer/UniqueProjectNameProvider
instanceKlass org/gradle/plugins/ide/internal/tooling/ToolingModelServices$BuildScopeToolingServices
instanceKlass org/gradle/composite/internal/CompositeBuildServices$CompositeBuildBuildScopeServices
instanceKlass org/gradle/caching/http/internal/HttpBuildCacheServiceServices$$Lambda$127
instanceKlass org/apache/http/HttpRequest
instanceKlass org/apache/http/HttpMessage
instanceKlass org/gradle/caching/http/internal/HttpBuildCacheRequestCustomizer
instanceKlass org/gradle/caching/http/internal/DefaultHttpBuildCacheServiceFactory
instanceKlass org/gradle/caching/BuildCacheServiceFactory
instanceKlass org/gradle/caching/configuration/AbstractBuildCache
instanceKlass org/gradle/caching/configuration/BuildCache
instanceKlass org/gradle/caching/configuration/internal/DefaultBuildCacheServiceRegistration
instanceKlass org/gradle/maven/MavenPomArtifact
instanceKlass org/gradle/maven/MavenModule
instanceKlass org/gradle/api/publish/maven/internal/publisher/MavenDuplicatePublicationTracker
instanceKlass org/gradle/api/publish/maven/internal/publisher/MavenPublishers
instanceKlass org/gradle/api/publish/maven/internal/dependencies/VersionRangeMapper
instanceKlass org/gradle/api/publish/maven/internal/MavenPublishServices$ComponentRegistrationAction
instanceKlass org/gradle/api/publish/internal/validation/DuplicatePublicationTracker
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/DefaultProjectDependencyPublicationResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/ProjectDependencyPublicationResolver
instanceKlass org/gradle/jvm/toolchain/internal/LocationListInstallationSupplier
instanceKlass org/gradle/jvm/toolchain/internal/EnvironmentVariableListInstallationSupplier
instanceKlass org/gradle/jvm/toolchain/internal/AutoDetectingInstallationSupplier
instanceKlass org/gradle/jvm/toolchain/internal/InstallationSupplier
instanceKlass org/gradle/jvm/toolchain/internal/JavaInstallationRegistry
instanceKlass org/gradle/jvm/toolchain/install/internal/JdkCacheDirectory
instanceKlass org/gradle/language/base/artifact/SourcesArtifact
instanceKlass org/gradle/jvm/JvmLibrary
instanceKlass org/gradle/platform/base/Library
instanceKlass org/gradle/language/jvm/internal/JvmPluginServiceRegistry$ComponentRegistrationAction
instanceKlass org/gradle/ivy/IvyDescriptorArtifact
instanceKlass org/gradle/api/component/Artifact
instanceKlass org/gradle/api/internal/component/DefaultComponentTypeRegistry$DefaultComponentTypeRegistration
instanceKlass org/gradle/ivy/IvyModule
instanceKlass org/gradle/api/component/Component
instanceKlass org/gradle/api/internal/component/ComponentTypeRegistration
instanceKlass org/gradle/api/internal/component/DefaultComponentTypeRegistry
instanceKlass org/gradle/api/publish/ivy/internal/publisher/IvyPublisher
instanceKlass org/gradle/api/publish/ivy/internal/IvyServices$BuildServices
instanceKlass org/gradle/api/internal/resolve/ProjectModelResolver
instanceKlass org/gradle/platform/base/internal/registry/ComponentModelBaseServiceRegistry$BuildScopeServices
instanceKlass org/gradle/plugin/use/resolve/internal/PluginResolverContributor
instanceKlass org/gradle/plugin/use/internal/PluginResolverFactory
instanceKlass org/gradle/plugin/use/internal/PluginDependencyResolutionServices
instanceKlass org/gradle/plugin/management/internal/PluginResolutionStrategyInternal
instanceKlass org/gradle/plugin/management/PluginResolutionStrategy
instanceKlass org/gradle/plugin/use/resolve/service/internal/ClientInjectedClasspathPluginResolver
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/ProjectFinder
instanceKlass org/gradle/api/internal/artifacts/DependencyResolutionServices
instanceKlass org/gradle/plugin/management/internal/autoapply/AutoAppliedPluginRegistry
instanceKlass org/gradle/plugin/internal/PluginUsePluginServiceRegistry$BuildScopeServices
instanceKlass org/gradle/authentication/http/HttpHeaderAuthentication
instanceKlass org/gradle/authentication/http/DigestAuthentication
instanceKlass org/gradle/internal/authentication/AbstractAuthentication
instanceKlass org/gradle/internal/authentication/AuthenticationInternal
instanceKlass org/gradle/authentication/http/BasicAuthentication
instanceKlass org/gradle/authentication/Authentication
instanceKlass org/gradle/internal/authentication/DefaultAuthenticationSchemeRegistry
instanceKlass org/gradle/internal/resource/transport/http/HttpResourcesPluginServiceRegistry$AuthenticationSchemeAction
instanceKlass org/gradle/internal/component/model/ComponentResolveMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/ProjectArtifactSetResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/ProjectArtifactResolver
instanceKlass org/gradle/internal/component/external/model/ModuleComponentArtifactMetadata
instanceKlass org/gradle/internal/component/model/ComponentArtifactMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ResolverProviderFactory
instanceKlass org/gradle/internal/management/DependencyResolutionManagementInternal
instanceKlass org/gradle/api/initialization/resolve/DependencyResolutionManagement
instanceKlass org/gradle/internal/resource/cached/AbstractCachedIndex
instanceKlass org/gradle/internal/resource/local/LocallyAvailableResourceFinder
instanceKlass org/gradle/internal/resource/local/GroupedAndNamedUniqueFileStore
instanceKlass org/gradle/internal/verifier/HttpRedirectVerifier
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/ModuleRepositoryCacheProvider
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/StartParameterResolutionOverride
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/verification/DependencyVerificationOverride
instanceKlass org/gradle/api/internal/filestore/DefaultArtifactIdentifierFileStore$Factory
instanceKlass org/gradle/internal/resource/cached/DefaultExternalResourceFileStore$Factory
instanceKlass org/gradle/internal/resolve/caching/CrossBuildCachingRuleExecutor
instanceKlass org/gradle/internal/resolve/caching/CachingRuleExecutor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/VersionParser
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/ProjectDependencyResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ComponentResolvers
instanceKlass org/gradle/internal/resolve/resolver/OriginArtifactSelector
instanceKlass org/gradle/internal/resolve/resolver/ArtifactResolver
instanceKlass org/gradle/internal/resolve/resolver/DependencyToComponentIdResolver
instanceKlass org/gradle/internal/resolve/resolver/ComponentMetaDataResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ResolveIvyFactory
instanceKlass org/gradle/internal/resource/TextUriResourceLoader$Factory
instanceKlass org/gradle/api/internal/artifacts/repositories/transport/RepositoryTransportFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/ModuleExclusions
instanceKlass org/gradle/api/internal/runtimeshaded/RuntimeShadedJarFactory
instanceKlass org/gradle/api/internal/artifacts/DefaultProjectDependencyFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/FileStoreAndIndexProvider
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/ModuleRepositoryCaches
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/ModuleSourcesSerializer
instanceKlass org/gradle/api/internal/artifacts/repositories/metadata/IvyMutableModuleMetadataFactory
instanceKlass org/gradle/api/internal/artifacts/repositories/metadata/MavenMutableModuleMetadataFactory
instanceKlass org/gradle/api/internal/artifacts/repositories/metadata/MutableModuleMetadataFactory
instanceKlass org/gradle/util/internal/SimpleMapInterner
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/ComponentSelectionDescriptorFactory
instanceKlass org/gradle/initialization/DependenciesAccessors
instanceKlass org/gradle/api/internal/filestore/ArtifactIdentifierFileStore
instanceKlass org/gradle/api/internal/artifacts/verification/signatures/SignatureVerificationServiceFactory
instanceKlass org/gradle/internal/resource/TextUriResourceLoader
instanceKlass org/gradle/api/internal/artifacts/repositories/resolver/ExternalResourceAccessor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/VersionSelectorScheme
instanceKlass org/gradle/api/internal/artifacts/ComponentSelectorConverter
instanceKlass org/gradle/api/internal/artifacts/mvnsettings/MavenSettingsProvider
instanceKlass org/gradle/api/internal/artifacts/mvnsettings/MavenFileLocations
instanceKlass org/gradle/internal/execution/steps/DeferredExecutionAwareStep
instanceKlass org/gradle/internal/execution/steps/Step
instanceKlass org/gradle/internal/resource/cached/CachedExternalResourceIndex
instanceKlass org/gradle/internal/resource/cached/ExternalResourceFileStore
instanceKlass org/gradle/internal/resource/local/FileStoreSearcher
instanceKlass org/gradle/internal/resource/local/FileStore
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/ProjectPublicationRegistry
instanceKlass org/gradle/api/internal/artifacts/ArtifactDependencyResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/LocalComponentRegistry
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/RepositoryDisabler
instanceKlass org/gradle/api/internal/artifacts/mvnsettings/LocalMavenRepositoryLocator
instanceKlass org/gradle/api/internal/artifacts/component/ComponentIdentifierFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/AttributeContainerSerializer
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/VersionComparator
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/dynamicversions/AbstractModuleVersionsCache
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/dynamicversions/ModuleVersionsCache
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/AbstractModuleMetadataCache
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/ModuleMetadataCache
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/artifacts/AbstractArtifactsCache
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/artifacts/ModuleArtifactsCache
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/artifacts/ModuleArtifactCache
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/kotlin/dsl/provider/KotlinScriptEvaluator
instanceKlass org/gradle/internal/execution/ExecutionEngine
instanceKlass kotlin/jvm/functions/Function2
instanceKlass org/gradle/kotlin/dsl/provider/PluginRequestsHandler
instanceKlass org/gradle/plugin/management/internal/autoapply/AutoAppliedPluginHandler
instanceKlass org/gradle/plugin/use/internal/PluginRequestApplicator
instanceKlass org/gradle/kotlin/dsl/provider/KotlinScriptClassPathProvider
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/DependencyFactory
instanceKlass org/gradle/kotlin/dsl/provider/ClassPathModeExceptionCollector
instanceKlass org/gradle/kotlin/dsl/provider/BuildServices
instanceKlass org/gradle/kotlin/dsl/concurrent/AsyncIOScopeFactory
instanceKlass org/gradle/kotlin/dsl/concurrent/BuildServices
instanceKlass org/gradle/caching/configuration/internal/BuildCacheConfigurationInternal
instanceKlass org/gradle/caching/configuration/BuildCacheConfiguration
instanceKlass org/gradle/caching/configuration/internal/BuildCacheServiceRegistration
instanceKlass org/gradle/caching/local/internal/DirectoryBuildCacheFileStoreFactory
instanceKlass org/gradle/caching/internal/BuildCacheServices$2
instanceKlass org/gradle/groovy/scripts/ScriptSource
instanceKlass org/gradle/api/internal/project/ProjectFactory
instanceKlass org/gradle/api/internal/project/IProjectFactory
instanceKlass org/gradle/execution/TaskPathProjectEvaluator
instanceKlass org/gradle/api/internal/file/DefaultArchiveOperations
instanceKlass org/gradle/api/file/ArchiveOperations
instanceKlass org/gradle/api/internal/file/DefaultFileSystemOperations
instanceKlass org/gradle/api/file/FileSystemOperations
instanceKlass org/gradle/internal/resource/LocalBinaryResource
instanceKlass org/gradle/internal/resource/ReadableContent
instanceKlass org/gradle/api/resources/internal/ReadableResourceInternal
instanceKlass org/gradle/internal/resource/Resource
instanceKlass org/gradle/api/resources/ReadableResource
instanceKlass org/gradle/api/resources/Resource
instanceKlass org/gradle/api/internal/file/delete/DeleteSpecInternal
instanceKlass org/gradle/api/file/DeleteSpec
instanceKlass org/gradle/api/internal/file/DefaultFileOperations
instanceKlass org/gradle/api/internal/file/FileOperations
instanceKlass org/gradle/process/internal/DefaultExecOperations
instanceKlass org/gradle/process/ExecOperations
instanceKlass org/gradle/internal/service/scopes/BuildScopeServices$$Lambda$126
instanceKlass org/gradle/tooling/provider/model/internal/BuildScopeToolingModelBuilderRegistryAction
instanceKlass org/gradle/api/internal/project/ProjectInternal
instanceKlass org/gradle/model/internal/registry/ModelRegistryScope
instanceKlass org/gradle/api/internal/DomainObjectContext
instanceKlass org/gradle/api/internal/file/HasScriptServices
instanceKlass org/gradle/api/internal/project/ProjectIdentifier
instanceKlass org/gradle/api/Project
instanceKlass org/gradle/api/internal/initialization/ScriptClassPathInitializer
instanceKlass org/gradle/api/internal/GradleInternal
instanceKlass org/gradle/api/internal/plugins/PluginAwareInternal
instanceKlass org/gradle/tooling/provider/model/internal/DefaultToolingModelBuilderRegistry
instanceKlass org/gradle/tooling/provider/model/internal/ToolingModelBuilderLookup
instanceKlass org/gradle/tooling/provider/model/ToolingModelBuilderRegistry
instanceKlass org/gradle/api/internal/project/DefaultProjectRegistry
instanceKlass org/gradle/api/internal/resources/DefaultResourceHandler$Factory
instanceKlass org/gradle/api/internal/resources/ApiTextResourceAdapter$Factory
instanceKlass org/gradle/api/internal/tasks/TaskStatistics
instanceKlass org/gradle/initialization/InitScriptHandler
instanceKlass org/gradle/groovy/scripts/internal/FileCacheBackedScriptClassCompiler
instanceKlass org/gradle/api/provider/ProviderFactory
instanceKlass org/gradle/configuration/DefaultScriptPluginFactory
instanceKlass org/gradle/initialization/buildsrc/BuildSourceBuilder
instanceKlass org/gradle/execution/ProjectConfigurer
instanceKlass org/gradle/groovy/scripts/internal/DefaultScriptCompilationHandler
instanceKlass org/gradle/api/internal/properties/GradleProperties
instanceKlass org/gradle/internal/service/scopes/BuildScopeServiceRegistryFactory
instanceKlass org/gradle/internal/service/scopes/ServiceRegistryFactory
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementServices
instanceKlass org/gradle/api/internal/plugins/PluginInspector
instanceKlass org/gradle/internal/build/BuildWorkPreparer
instanceKlass org/gradle/execution/TaskSelector
instanceKlass org/gradle/api/internal/project/taskfactory/ITaskFactory
instanceKlass org/gradle/internal/actor/ActorFactory
instanceKlass org/gradle/initialization/BuildLoader
instanceKlass org/gradle/groovy/scripts/internal/ScriptCompilationHandler
instanceKlass org/gradle/initialization/IGradlePropertiesLoader
instanceKlass org/gradle/initialization/GradlePropertiesController
instanceKlass org/gradle/initialization/ProjectDescriptorRegistry
instanceKlass org/gradle/api/internal/project/ProjectRegistry
instanceKlass org/gradle/internal/resource/TextFileResourceLoader
instanceKlass org/gradle/configuration/InitScriptProcessor
instanceKlass org/gradle/api/internal/project/IsolatedAntBuilder
instanceKlass org/gradle/initialization/SettingsLoaderFactory
instanceKlass org/gradle/groovy/scripts/ScriptCompilerFactory
instanceKlass org/gradle/groovy/scripts/internal/ScriptClassCompiler
instanceKlass org/gradle/configuration/ScriptPluginFactory
instanceKlass org/gradle/api/internal/provider/ValueSourceProviderFactory
instanceKlass org/gradle/internal/build/PublicBuildPath
instanceKlass org/gradle/initialization/ProjectAccessListener
instanceKlass org/gradle/configuration/project/ProjectEvaluator
instanceKlass org/gradle/api/internal/tasks/userinput/BuildScanUserInputHandler
instanceKlass org/gradle/api/internal/project/ProjectTaskLister
instanceKlass org/gradle/initialization/ProjectAccessHandler
instanceKlass org/gradle/api/internal/initialization/ScriptClassPathResolver
instanceKlass org/gradle/configuration/ProjectsPreparer
instanceKlass org/gradle/api/internal/component/ComponentTypeRegistry
instanceKlass org/gradle/api/internal/artifacts/configurations/DependencyMetaDataProvider
instanceKlass org/gradle/initialization/SettingsProcessor
instanceKlass org/gradle/api/internal/plugins/PluginRegistry
instanceKlass org/gradle/initialization/SettingsPreparer
instanceKlass org/gradle/internal/build/BuildIncluder
instanceKlass org/gradle/groovy/scripts/internal/ScriptRunnerFactory
instanceKlass org/gradle/internal/operations/logging/BuildOperationLoggerFactory
instanceKlass org/gradle/api/invocation/BuildInvocationDetails
instanceKlass org/gradle/internal/authentication/AuthenticationSchemeRegistry
instanceKlass org/gradle/configuration/CompileOperationFactory
instanceKlass org/gradle/api/internal/initialization/ScriptHandlerFactory
instanceKlass org/gradle/internal/composite/IncludedBuildInternal
instanceKlass org/gradle/api/initialization/IncludedBuild
instanceKlass org/gradle/internal/build/BuildWorkGraph
instanceKlass org/gradle/internal/buildtree/BuildTreeFinishExecutor
instanceKlass org/gradle/internal/buildtree/BuildTreeWorkExecutor
instanceKlass org/gradle/api/invocation/Gradle
instanceKlass org/gradle/api/plugins/PluginAware
instanceKlass org/gradle/api/artifacts/component/ProjectComponentIdentifier
instanceKlass org/gradle/api/artifacts/component/ComponentIdentifier
instanceKlass org/gradle/internal/build/AbstractBuildState
instanceKlass org/gradle/internal/Actions$NullAction
instanceKlass org/gradle/internal/Actions
instanceKlass org/gradle/plugin/management/internal/PluginRequests$EmptyPluginRequests
instanceKlass org/gradle/plugin/management/internal/PluginRequests
instanceKlass org/gradle/api/internal/BuildDefinition
instanceKlass org/gradle/internal/buildtree/ProblemReportingBuildActionRunner$$Lambda$125
instanceKlass org/gradle/launcher/exec/ChainingBuildActionRunner
instanceKlass org/gradle/internal/buildtree/ProblemReportingBuildActionRunner
instanceKlass org/gradle/launcher/exec/BuildOutcomeReportingBuildActionRunner
instanceKlass org/gradle/tooling/internal/provider/FileSystemWatchingBuildActionRunner
instanceKlass org/gradle/launcher/exec/BuildCompletionNotifyingBuildActionRunner
instanceKlass org/gradle/launcher/exec/RootBuildLifecycleBuildActionExecutor
instanceKlass org/gradle/initialization/exception/StackTraceSanitizingExceptionAnalyser
instanceKlass org/gradle/internal/exceptions/FailureResolutionAware
instanceKlass org/gradle/initialization/exception/DefaultExceptionAnalyser
instanceKlass org/gradle/internal/scripts/ScriptExecutionListener
instanceKlass org/gradle/initialization/exception/MultipleBuildFailuresExceptionAnalyser
instanceKlass org/gradle/configurationcache/problems/ConfigurationCacheProblems$PostBuildProblemsHandler
instanceKlass org/gradle/configurationcache/problems/ConfigurationCacheProblemsSummary
instanceKlass org/gradle/configurationcache/problems/ConfigurationCacheReport$State
instanceKlass org/gradle/internal/buildtree/DeprecationsReporter
instanceKlass org/gradle/internal/build/NestedRootBuild
instanceKlass org/gradle/api/artifacts/component/BuildIdentifier
instanceKlass org/gradle/composite/internal/DefaultIncludedBuildRegistry
instanceKlass org/gradle/api/internal/artifacts/ivyservice/dependencysubstitution/DependencySubstitutionsInternal
instanceKlass org/gradle/api/artifacts/DependencySubstitutions
instanceKlass org/gradle/composite/internal/IncludedBuildDependencySubstitutionsBuilder
instanceKlass org/gradle/internal/typeconversion/CompositeNotationConverter
instanceKlass org/gradle/api/capabilities/Capability
instanceKlass org/gradle/api/internal/artifacts/dsl/CapabilityNotationParserFactory
instanceKlass org/gradle/api/internal/project/ProjectState
instanceKlass org/gradle/internal/model/ModelContainer
instanceKlass org/gradle/api/internal/attributes/UsageCompatibilityHandler
instanceKlass java/util/Comparator$$Lambda$124
instanceKlass org/gradle/api/internal/attributes/DefaultImmutableAttributes$$Lambda$123
instanceKlass org/gradle/api/attributes/Attribute
instanceKlass org/gradle/api/internal/attributes/DefaultImmutableAttributes
instanceKlass org/gradle/api/internal/attributes/AttributeValue
instanceKlass org/gradle/api/internal/attributes/ImmutableAttributes
instanceKlass org/gradle/api/internal/attributes/AttributeContainerInternal
instanceKlass org/gradle/api/attributes/AttributeContainer
instanceKlass org/gradle/api/attributes/HasAttributes
instanceKlass org/gradle/internal/isolation/Isolatable
instanceKlass org/gradle/internal/snapshot/impl/DefaultValueSnapshotter$IsolatableVisitor
instanceKlass org/gradle/internal/snapshot/ValueSnapshot
instanceKlass org/gradle/internal/hash/Hashable
instanceKlass org/gradle/internal/snapshot/impl/DefaultValueSnapshotter$ValueSnapshotVisitor
instanceKlass org/gradle/internal/snapshot/impl/DefaultValueSnapshotter$ValueVisitor
instanceKlass com/google/common/cache/LocalCache$StrongValueReference
instanceKlass org/gradle/api/internal/provider/ManagedFactories$ProviderManagedFactory
instanceKlass org/gradle/api/internal/provider/ManagedFactories$PropertyManagedFactory
instanceKlass org/gradle/api/internal/provider/ManagedFactories$MapPropertyManagedFactory
instanceKlass org/gradle/api/internal/provider/ManagedFactories$ListPropertyManagedFactory
instanceKlass org/gradle/api/internal/provider/AbstractMinimalProvider
instanceKlass org/gradle/api/internal/provider/CollectionPropertyInternal
instanceKlass org/gradle/api/internal/provider/CollectionProviderInternal
instanceKlass org/gradle/api/internal/provider/PropertyInternal
instanceKlass org/gradle/internal/state/OwnerAware
instanceKlass org/gradle/api/internal/provider/HasConfigurableValueInternal
instanceKlass org/gradle/api/internal/provider/ProviderInternal
instanceKlass org/gradle/api/internal/provider/ValueSupplier
instanceKlass org/gradle/api/internal/provider/ManagedFactories$SetPropertyManagedFactory
instanceKlass org/gradle/api/internal/file/ManagedFactories$DirectoryPropertyManagedFactory
instanceKlass org/gradle/api/internal/file/ManagedFactories$DirectoryManagedFactory
instanceKlass org/gradle/api/internal/file/ManagedFactories$RegularFilePropertyManagedFactory
instanceKlass org/gradle/api/internal/file/ManagedFactories$RegularFileManagedFactory
instanceKlass org/gradle/api/internal/file/collections/ManagedFactories$ConfigurableFileCollectionManagedFactory
instanceKlass org/gradle/internal/state/DefaultManagedFactoryRegistry
instanceKlass org/gradle/internal/classloader/ConfigurableClassLoaderHierarchyHasher
instanceKlass org/gradle/internal/classloader/DefaultClassLoaderFactory
instanceKlass org/gradle/api/internal/initialization/loadercache/DefaultClasspathHasher
instanceKlass javax/annotation/meta/TypeQualifierDefault
instanceKlass javax/annotation/Nonnull
instanceKlass org/gradle/api/NonNullApi
instanceKlass org/gradle/api/internal/changedetection/state/PropertiesFileAwareClasspathResourceHasher$$Lambda$122
instanceKlass org/gradle/api/internal/changedetection/state/PropertiesFileAwareClasspathResourceHasher$$Lambda$121
instanceKlass org/gradle/internal/fingerprint/FileCollectionFingerprint$1
instanceKlass org/gradle/internal/fingerprint/impl/EmptyCurrentFileCollectionFingerprint
instanceKlass org/gradle/internal/fingerprint/DirectorySensitivity$$Lambda$120
instanceKlass org/gradle/internal/fingerprint/DirectorySensitivity$$Lambda$119
instanceKlass org/gradle/api/internal/changedetection/state/ZipHasher$$Lambda$118
instanceKlass org/gradle/api/internal/changedetection/state/ZipHasher$HashingExceptionReporter
instanceKlass org/gradle/internal/snapshot/AbstractFileSystemLocationSnapshot
instanceKlass org/gradle/internal/snapshot/FileSystemLeafSnapshot
instanceKlass org/gradle/internal/fingerprint/hashing/ZipEntryContext
instanceKlass org/gradle/api/internal/file/archive/ZipInput
instanceKlass org/gradle/api/internal/changedetection/state/ZipHasher
instanceKlass org/gradle/api/internal/changedetection/state/IgnoringResourceHasher
instanceKlass org/gradle/api/internal/changedetection/state/MetaInfAwareClasspathResourceHasher
instanceKlass org/gradle/api/internal/changedetection/state/PropertiesFileAwareClasspathResourceHasher$$Lambda$117
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/api/internal/changedetection/state/PropertiesFileAwareClasspathResourceHasher
instanceKlass org/gradle/api/internal/changedetection/state/LineEndingNormalizingResourceHasher$1
instanceKlass org/gradle/api/internal/changedetection/state/LineEndingNormalizingResourceHasher
instanceKlass org/gradle/internal/snapshot/RelativePathTrackingFileSystemSnapshotHierarchyVisitor
instanceKlass org/gradle/internal/fingerprint/CurrentFileCollectionFingerprint
instanceKlass org/gradle/internal/fingerprint/FileCollectionFingerprint
instanceKlass org/gradle/internal/fingerprint/impl/AbstractFingerprintingStrategy
instanceKlass org/gradle/api/internal/changedetection/state/RuntimeClasspathResourceHasher
instanceKlass org/gradle/api/internal/changedetection/state/PropertiesFileFilter
instanceKlass org/gradle/api/internal/changedetection/state/ResourceEntryFilter$1
instanceKlass org/gradle/api/internal/changedetection/state/ResourceEntryFilter
instanceKlass org/gradle/api/internal/changedetection/state/ResourceFilter$1
instanceKlass org/gradle/api/internal/changedetection/state/ResourceFilter
instanceKlass org/gradle/internal/fingerprint/FingerprintingStrategy
instanceKlass org/gradle/internal/fingerprint/impl/AbstractFileCollectionFingerprinter
instanceKlass org/gradle/internal/fingerprint/impl/DefaultFileCollectionSnapshotter
instanceKlass org/gradle/internal/fingerprint/impl/DefaultGenericFileTreeSnapshotter
instanceKlass org/gradle/api/internal/changedetection/state/CachingResourceHasher
instanceKlass org/gradle/internal/fingerprint/hashing/ResourceHasher
instanceKlass org/gradle/internal/fingerprint/hashing/ZipEntryContextHasher
instanceKlass org/gradle/internal/fingerprint/hashing/RegularFileSnapshotContextHasher
instanceKlass org/gradle/internal/fingerprint/hashing/ConfigurableNormalizer
instanceKlass org/gradle/api/internal/changedetection/state/DefaultResourceSnapshotterCacheService
instanceKlass org/gradle/internal/typeconversion/NotationParserBuilder$LazyDisplayName
instanceKlass org/gradle/internal/typeconversion/JustReturningParser
instanceKlass org/gradle/api/artifacts/VersionConstraint
instanceKlass org/gradle/internal/typeconversion/TypedNotationConverter
instanceKlass org/gradle/internal/typeconversion/CrossBuildCachingNotationConverter
instanceKlass org/gradle/api/internal/artifacts/DefaultImmutableModuleIdentifierFactory
instanceKlass org/gradle/composite/internal/DefaultBuildableCompositeBuildContext
instanceKlass org/gradle/tooling/internal/provider/serialization/DefaultPayloadClassLoaderRegistry$DetailsToClassLoaderTransformer
instanceKlass org/gradle/tooling/internal/provider/serialization/DefaultPayloadClassLoaderRegistry$ClassLoaderToDetailsTransformer
instanceKlass org/gradle/tooling/internal/provider/serialization/DefaultPayloadClassLoaderRegistry
instanceKlass org/gradle/tooling/internal/provider/serialization/ClassLoaderDetails
instanceKlass org/gradle/tooling/internal/provider/serialization/SerializeMap
instanceKlass org/gradle/tooling/internal/provider/serialization/DeserializeMap
instanceKlass org/gradle/tooling/internal/provider/serialization/WellKnownClassLoaderRegistry
instanceKlass java/io/ObjectInput
instanceKlass java/io/ObjectStreamConstants
instanceKlass java/io/ObjectOutput
instanceKlass org/gradle/tooling/internal/provider/serialization/ModelClassLoaderFactory
instanceKlass org/gradle/tooling/internal/provider/serialization/DaemonSidePayloadClassLoaderFactory
instanceKlass org/gradle/internal/file/impl/SingleDepthFileAccessTracker
instanceKlass org/gradle/cache/internal/SingleDepthFilesFinder
instanceKlass org/gradle/cache/internal/UnusedVersionsCacheCleanup$1
instanceKlass org/gradle/cache/internal/AbstractCacheCleanup
instanceKlass org/gradle/cache/internal/CompositeCleanupAction$Builder
instanceKlass org/gradle/cache/internal/CompositeCleanupAction
instanceKlass org/gradle/internal/classpath/ClasspathFileTransformer
instanceKlass org/gradle/internal/classpath/CachedClasspathTransformer$Transform
instanceKlass org/gradle/internal/classpath/DefaultCachedClasspathTransformer
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$GradleUserHomeServices$3
instanceKlass java/util/function/Predicate$$Lambda$116
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotter$DefaultExcludes$$Lambda$115
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotter$DefaultExcludes$$Lambda$114
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotter$DefaultExcludes$$Lambda$113
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotter$DefaultExcludes$$Lambda$112
instanceKlass java/util/function/Predicate$$Lambda$111
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotter$DefaultExcludes$EndMatcher
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotter$DefaultExcludes$StartMatcher
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotter$DefaultExcludes
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotter$1
instanceKlass java/nio/file/FileVisitor
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotter$SymbolicLinkMapping
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotter
instanceKlass com/google/common/util/concurrent/Striped$1
instanceKlass com/google/common/util/concurrent/Striped$6
instanceKlass java/util/concurrent/locks/ReadWriteLock
instanceKlass com/google/common/util/concurrent/Striped$5
instanceKlass com/google/common/util/concurrent/Striped
instanceKlass org/gradle/internal/vfs/impl/DefaultFileSystemAccess$StripedProducerGuard
instanceKlass java/nio/file/attribute/PosixFilePermissions$1
instanceKlass java/nio/file/attribute/PosixFilePermissions
instanceKlass org/apache/tools/ant/util/FileUtils
instanceKlass org/apache/tools/ant/taskdefs/condition/Os
instanceKlass org/apache/tools/ant/taskdefs/condition/Condition
instanceKlass org/apache/tools/ant/types/resources/Appendable
instanceKlass org/apache/tools/ant/types/resources/FileProvider
instanceKlass org/apache/tools/ant/types/resources/Touchable
instanceKlass org/apache/tools/ant/ProjectComponent
instanceKlass org/apache/tools/ant/types/ResourceCollection
instanceKlass org/apache/tools/ant/DirectoryScanner
instanceKlass org/apache/tools/ant/types/ResourceFactory
instanceKlass org/apache/tools/ant/types/selectors/SelectorScanner
instanceKlass org/apache/tools/ant/FileScanner
instanceKlass org/gradle/internal/snapshot/FileSystemLocationSnapshot
instanceKlass org/gradle/internal/snapshot/FileSystemSnapshot
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotterStatistics
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$GradleUserHomeServices$$Lambda$110
instanceKlass org/gradle/internal/build/BuildAddedListener
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$GradleUserHomeServices$$Lambda$109
instanceKlass org/gradle/internal/watch/registry/impl/DaemonDocumentationIndex
instanceKlass org/gradle/internal/snapshot/SnapshotHierarchy$NodeDiffListener
instanceKlass org/gradle/internal/watch/registry/FileWatcherRegistry$ChangeHandler
instanceKlass org/gradle/internal/snapshot/MetadataSnapshot
instanceKlass org/gradle/internal/vfs/impl/AbstractVirtualFileSystem
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$GradleUserHomeServices$$Lambda$108
instanceKlass net/rubygrapefruit/platform/internal/jni/AbstractFileEventFunctions$AbstractWatcherBuilder
instanceKlass org/gradle/internal/watch/registry/FileWatcherUpdater
instanceKlass net/rubygrapefruit/platform/file/FileWatcher
instanceKlass org/gradle/internal/watch/registry/FileWatcherRegistry
instanceKlass org/gradle/internal/watch/registry/impl/AbstractFileWatcherRegistryFactory
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$GradleUserHomeServices$$Lambda$107
instanceKlass org/gradle/internal/snapshot/FileSystemNode
instanceKlass org/gradle/internal/snapshot/ChildMap
instanceKlass org/gradle/internal/vfs/impl/DefaultSnapshotHierarchy$1
instanceKlass org/gradle/internal/snapshot/ReadOnlyFileSystemNode
instanceKlass org/gradle/internal/vfs/impl/DefaultSnapshotHierarchy
instanceKlass org/gradle/internal/snapshot/SnapshotHierarchy
instanceKlass org/apache/commons/io/filefilter/IOFileFilter
instanceKlass java/io/FilenameFilter
instanceKlass org/apache/commons/io/FileUtils
instanceKlass com/google/common/io/CharSource
instanceKlass com/google/common/hash/PrimitiveSink
instanceKlass com/google/common/io/Closer$SuppressingSuppressor
instanceKlass com/google/common/io/Closer$Suppressor
instanceKlass com/google/common/io/Closer
instanceKlass com/google/common/io/CharSink
instanceKlass java/io/File$TempDirectory
instanceKlass org/gradle/api/internal/file/temp/TempFiles
instanceKlass org/gradle/internal/watch/vfs/impl/DefaultWatchableFileSystemDetector
instanceKlass net/rubygrapefruit/platform/internal/PosixFileSystems
instanceKlass org/gradle/internal/file/FilePathUtil
instanceKlass org/gradle/internal/file/DefaultFileHierarchySet$Node
instanceKlass org/gradle/internal/file/DefaultFileHierarchySet$HierarchyVisitor
instanceKlass org/gradle/internal/file/DefaultFileHierarchySet$PrefixFileSet
instanceKlass org/gradle/cache/internal/DefaultGlobalCacheLocations
instanceKlass org/gradle/internal/nativeintegration/services/NativeServices$3
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$GradleUserHomeServices$1
instanceKlass org/gradle/internal/file/DefaultFileHierarchySet$EmptyFileHierarchySet
instanceKlass org/gradle/internal/file/FileHierarchySet
instanceKlass org/gradle/internal/file/DefaultFileHierarchySet
instanceKlass org/gradle/internal/hash/DefaultFileHasher
instanceKlass org/gradle/api/internal/changedetection/state/CachingFileHasher
instanceKlass com/google/common/collect/MapMakerInternalMap$WeakKeyDummyValueEntry$Helper
instanceKlass com/google/common/collect/MapMakerInternalMap$InternalEntry
instanceKlass com/google/common/collect/MapMakerInternalMap$1
instanceKlass com/google/common/collect/MapMakerInternalMap$InternalEntryHelper
instanceKlass com/google/common/collect/MapMakerInternalMap$WeakValueReference
instanceKlass com/google/common/collect/Interners$InternerImpl
instanceKlass com/google/common/collect/MapMaker
instanceKlass com/google/common/collect/Interners$InternerBuilder
instanceKlass com/google/common/collect/Interners
instanceKlass org/gradle/internal/hash/HashCode
instanceKlass com/google/common/base/Charsets
instanceKlass org/gradle/internal/hash/Hashing$MessageDigestHasher
instanceKlass org/gradle/internal/hash/Hashing$DefaultHasher
instanceKlass org/gradle/internal/hash/PrimitiveHasher
instanceKlass org/gradle/internal/hash/Hasher
instanceKlass org/gradle/internal/hash/Hashing$MessageDigestHashFunction
instanceKlass org/gradle/internal/hash/HashFunction
instanceKlass org/gradle/internal/hash/Hashing
instanceKlass org/gradle/internal/hash/DefaultStreamHasher
instanceKlass org/gradle/api/internal/changedetection/state/FileTimeStampInspector$$Lambda$106
instanceKlass org/gradle/api/internal/changedetection/state/FileHasherStatistics
instanceKlass org/gradle/api/internal/changedetection/state/DefaultFileAccessTimeJournal$$Lambda$105
instanceKlass org/gradle/cache/internal/DefaultCacheAccess$IndexedCacheEntry
instanceKlass java/net/DatagramPacket$1
instanceKlass org/gradle/cache/internal/locklistener/DefaultFileLockContentionHandler$ContendedAction
instanceKlass org/gradle/cache/internal/locklistener/DefaultFileLockContentionHandler$1
instanceKlass org/gradle/internal/Factories$1
instanceKlass org/gradle/internal/Factories
instanceKlass org/gradle/cache/internal/CrossProcessSynchronizingCache
instanceKlass org/gradle/cache/internal/InMemoryDecoratedCache
instanceKlass org/gradle/cache/internal/InMemoryCacheController
instanceKlass com/google/common/cache/LongAddables$1
instanceKlass com/google/common/cache/Striped64$Cell
instanceKlass com/google/common/cache/Striped64$1
instanceKlass com/google/common/cache/LongAddable
instanceKlass com/google/common/cache/LongAddables
instanceKlass com/google/common/cache/AbstractCache$SimpleStatsCounter
instanceKlass org/gradle/cache/internal/LoggingEvictionListener
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/cache/internal/DefaultInMemoryCacheDecoratorFactory$$Lambda$104
instanceKlass org/gradle/cache/internal/DefaultInMemoryCacheDecoratorFactory$CacheDetails
instanceKlass org/gradle/cache/internal/AsyncCacheAccessDecoratedCache
instanceKlass org/gradle/cache/internal/CacheAccessWorker
instanceKlass org/gradle/cache/internal/DefaultMultiProcessSafePersistentIndexedCache
instanceKlass org/gradle/cache/internal/DefaultCacheAccess$$Lambda$103
instanceKlass org/gradle/cache/internal/btree/BTreePersistentIndexedCache
instanceKlass org/gradle/cache/internal/DefaultInMemoryCacheDecoratorFactory$InMemoryCacheDecorator
instanceKlass org/gradle/cache/PersistentIndexedCacheParameters
instanceKlass org/gradle/api/internal/changedetection/state/DefaultFileAccessTimeJournal
instanceKlass org/gradle/cache/internal/MultiProcessSafeAsyncPersistentIndexedCache
instanceKlass org/gradle/cache/CacheDecorator
instanceKlass org/gradle/cache/internal/DefaultInMemoryCacheDecoratorFactory
instanceKlass org/gradle/cache/CleanupAction
instanceKlass org/gradle/cache/internal/FilesFinder
instanceKlass org/gradle/internal/file/FileAccessTracker
instanceKlass org/gradle/internal/classpath/DefaultClasspathTransformerCacheFactory
instanceKlass org/apache/commons/lang/StringUtils
instanceKlass org/gradle/api/internal/artifacts/ivyservice/DefaultArtifactCaches$$Lambda$102
instanceKlass org/gradle/api/internal/artifacts/ivyservice/WritableArtifactCacheLockingManager
instanceKlass org/gradle/api/internal/artifacts/ivyservice/DefaultArtifactCaches$LateInitWritableArtifactCacheLockingManager
instanceKlass com/google/common/primitives/IntsMethodsForWeb
instanceKlass org/apache/commons/lang/ArrayUtils
instanceKlass org/gradle/cache/internal/CacheVersion
instanceKlass java/lang/Character$CharacterCache
instanceKlass org/gradle/util/internal/DefaultGradleVersion$Stage
instanceKlass org/gradle/cache/internal/CacheVersionMapping$Builder
instanceKlass org/gradle/cache/internal/CacheVersionMapping$1
instanceKlass org/gradle/cache/internal/CacheVersionMapping
instanceKlass org/gradle/api/internal/artifacts/ivyservice/DefaultArtifactCacheMetadata
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementGradleUserHomeScopeServices$$Lambda$101
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ArtifactCacheLockingManager
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ArtifactCacheMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/DefaultArtifactCaches
instanceKlass org/gradle/cache/internal/DefaultCacheFactory$ReferenceTrackingCache
instanceKlass org/gradle/cache/internal/DefaultCacheFactory$DirCacheReference
instanceKlass org/gradle/cache/internal/cacheops/CacheOperationStack
instanceKlass org/gradle/cache/internal/LockOnDemandCrossProcessCacheAccess$ContendedAction
instanceKlass org/gradle/cache/internal/LockOnDemandCrossProcessCacheAccess$UnlockAction
instanceKlass org/gradle/cache/internal/DefaultCacheAccess$1
instanceKlass org/gradle/cache/internal/DefaultCacheAccess$$Lambda$100
instanceKlass org/gradle/cache/internal/DefaultCacheAccess$$Lambda$99
instanceKlass org/gradle/cache/internal/cacheops/CacheAccessOperationsStack
instanceKlass org/gradle/cache/internal/DefaultPersistentDirectoryStore$Cleanup
instanceKlass org/gradle/cache/internal/DefaultPersistentDirectoryStore$1
instanceKlass org/gradle/cache/internal/DefaultPersistentDirectoryStore$2
instanceKlass org/gradle/cache/internal/DefaultCacheAccess$$Lambda$98
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/cache/AsyncCacheAccess
instanceKlass org/gradle/cache/MultiProcessSafePersistentIndexedCache
instanceKlass org/gradle/cache/UnitOfWorkParticipant
instanceKlass org/gradle/cache/PersistentIndexedCache
instanceKlass org/gradle/cache/internal/AbstractCrossProcessCacheAccess
instanceKlass org/gradle/cache/CrossProcessCacheAccess
instanceKlass org/gradle/cache/internal/DefaultCacheAccess
instanceKlass org/gradle/cache/internal/CacheCleanupAction
instanceKlass org/gradle/cache/internal/CacheInitializationAction
instanceKlass org/gradle/cache/internal/CacheCoordinator
instanceKlass org/gradle/cache/internal/DefaultPersistentDirectoryStore
instanceKlass org/gradle/cache/internal/DefaultCacheScopeMapping$1
instanceKlass org/gradle/cache/internal/DefaultCacheRepository$PersistentCacheBuilder
instanceKlass org/gradle/cache/CacheBuilder
instanceKlass org/gradle/cache/internal/DefaultCacheRepository
instanceKlass org/gradle/cache/internal/ReferencablePersistentCache
instanceKlass org/gradle/cache/PersistentCache
instanceKlass org/gradle/cache/CleanableStore
instanceKlass org/gradle/cache/CacheAccess
instanceKlass org/gradle/cache/internal/DefaultCacheFactory
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge$Progress
instanceKlass org/gradle/internal/operations/OperationProgressEvent
instanceKlass org/gradle/initialization/BuildOptionBuildOperationProgressEventsEmitter$1
instanceKlass org/gradle/internal/configurationcache/options/ConfigurationCacheSettingsFinalizedProgressDetails
instanceKlass kotlin/UNINITIALIZED_VALUE
instanceKlass kotlin/UnsafeLazyImpl
instanceKlass kotlin/Lazy
instanceKlass kotlin/LazyKt$WhenMappings
instanceKlass kotlin/LazyKt__LazyJVMKt
instanceKlass org/gradle/configurationcache/extensions/UnsafeLazyKt
instanceKlass kotlin/jvm/internal/Lambda
instanceKlass kotlin/jvm/internal/FunctionBase
instanceKlass kotlin/jvm/functions/Function0
instanceKlass kotlin/Function
instanceKlass org/gradle/internal/scripts/ScriptingLanguages$1
instanceKlass org/gradle/scripts/ScriptingLanguage
instanceKlass org/gradle/internal/scripts/ScriptingLanguages
instanceKlass org/gradle/internal/scripts/DefaultScriptFileResolver
instanceKlass org/gradle/internal/scripts/ScriptFileResolver
instanceKlass org/gradle/initialization/layout/BuildLayoutConfiguration
instanceKlass org/gradle/launcher/exec/BuildTreeLifecycleBuildActionExecutor$$Lambda$97
instanceKlass org/gradle/internal/buildtree/DefaultBuildTreeContext
instanceKlass org/gradle/internal/buildtree/BuildTreeContext
instanceKlass org/gradle/initialization/BuildOptionBuildOperationProgressEventsEmitter
instanceKlass org/gradle/internal/build/BuildLifecycleController
instanceKlass org/gradle/internal/build/DefaultBuildLifecycleControllerFactory
instanceKlass org/gradle/internal/enterprise/core/GradleEnterprisePluginManager
instanceKlass org/gradle/tooling/internal/provider/runner/AbstractClientProvidedBuildActionRunner$ClientAction
instanceKlass org/gradle/tooling/internal/provider/runner/AbstractClientProvidedBuildActionRunner
instanceKlass org/gradle/tooling/internal/provider/runner/TestExecutionRequestActionRunner
instanceKlass org/gradle/tooling/internal/provider/runner/BuildModelActionRunner
instanceKlass org/gradle/tooling/internal/provider/runner/BuildControllerFactory
instanceKlass org/gradle/configurationcache/DefaultConfigurationCache
instanceKlass org/gradle/configurationcache/ConfigurationCacheRepository
instanceKlass org/gradle/configurationcache/BuildTreeConfigurationCache
instanceKlass org/gradle/configurationcache/DefaultBuildTreeLifecycleControllerFactory
instanceKlass org/gradle/internal/buildtree/BuildTreeLifecycleControllerFactory
instanceKlass org/gradle/configurationcache/initialization/DefaultConfigurationCacheProblemsListener
instanceKlass org/gradle/configurationcache/initialization/ConfigurationCacheProblemsListener
instanceKlass org/gradle/api/internal/BuildScopeListenerRegistrationListener
instanceKlass org/gradle/api/internal/tasks/execution/TaskExecutionAccessListener
instanceKlass org/gradle/configurationcache/ConfigurationCacheClassLoaderScopeRegistryListener
instanceKlass org/gradle/internal/buildtree/BuildTreeLifecycleListener
instanceKlass org/gradle/configurationcache/serialization/ScopeLookup
instanceKlass org/gradle/initialization/ClassLoaderScopeRegistryListener
instanceKlass org/gradle/configurationcache/problems/ConfigurationCacheProblems
instanceKlass org/gradle/configurationcache/problems/ConfigurationCacheReport
instanceKlass org/gradle/configurationcache/ConfigurationCacheKey
instanceKlass org/gradle/configurationcache/problems/ProblemsListener
instanceKlass org/gradle/configurationcache/initialization/DefaultInjectedClasspathInstrumentationStrategy
instanceKlass org/gradle/plugin/use/resolve/service/internal/InjectedClasspathInstrumentationStrategy
instanceKlass org/gradle/configurationcache/initialization/ConfigurationCacheStartParameter
instanceKlass org/gradle/configurationcache/BuildTreeListenerManager
instanceKlass org/gradle/vcs/internal/VcsResolver
instanceKlass org/gradle/vcs/internal/resolver/VcsVersionSelectionCache
instanceKlass org/gradle/vcs/internal/VcsMappingsStore
instanceKlass org/gradle/vcs/internal/VcsMappingFactory
instanceKlass org/gradle/vcs/internal/VersionControlSpecFactory
instanceKlass org/gradle/vcs/internal/services/VersionControlServices$VersionControlBuildTreeServices
instanceKlass org/gradle/plugins/ide/internal/IdeArtifactStore
instanceKlass org/gradle/internal/enterprise/impl/legacy/DefaultBuildScanBuildStartedTime
instanceKlass org/gradle/internal/scan/time/BuildScanBuildStartedTime
instanceKlass org/gradle/internal/enterprise/impl/legacy/DefaultBuildScanClock
instanceKlass org/gradle/internal/scan/time/BuildScanClock
instanceKlass org/gradle/internal/enterprise/impl/DefaultGradleEnterprisePluginRequiredServices
instanceKlass org/gradle/internal/enterprise/GradleEnterprisePluginRequiredServices
instanceKlass org/gradle/internal/operations/RunnableBuildOperation
instanceKlass org/gradle/composite/internal/IncludedBuildControllers
instanceKlass org/gradle/composite/internal/DefaultIncludedBuildTaskGraph
instanceKlass org/gradle/composite/internal/IncludedBuildTaskGraph
instanceKlass org/gradle/internal/build/IncludedBuildState
instanceKlass org/gradle/composite/internal/DefaultIncludedBuildFactory
instanceKlass org/gradle/internal/build/BuildLifecycleControllerFactory
instanceKlass org/gradle/internal/build/StandAloneNestedBuild
instanceKlass org/gradle/internal/build/NestedBuildState
instanceKlass org/gradle/internal/build/RootBuildState
instanceKlass org/gradle/internal/build/BuildActionTarget
instanceKlass org/gradle/internal/build/CompositeBuildParticipantBuildState
instanceKlass org/gradle/internal/build/IncludedBuildFactory
instanceKlass org/gradle/composite/internal/BuildStateFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/LocalComponentProvider
instanceKlass org/gradle/api/internal/composite/CompositeBuildContext
instanceKlass org/gradle/api/internal/artifacts/ivyservice/dependencysubstitution/DependencySubstitutionRules
instanceKlass org/gradle/composite/internal/CompositeBuildServices$CompositeBuildTreeScopeServices
instanceKlass org/gradle/api/internal/tasks/compile/processing/AnnotationProcessorDetector
instanceKlass org/gradle/language/java/internal/JavaLanguagePluginServiceRegistry$1
instanceKlass org/gradle/util/internal/BuildCommencedTimeProvider
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/store/ResolutionResultsStoreFactory
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildTreeScopeServices
instanceKlass org/gradle/caching/internal/controller/RootBuildCacheControllerRef
instanceKlass org/gradle/caching/internal/BuildCacheServices$1
instanceKlass org/gradle/internal/build/BuildStateRegistry
instanceKlass org/gradle/internal/buildtree/BuildTreeActionExecutor
instanceKlass org/gradle/tooling/internal/provider/LauncherServices$ToolingBuildTreeScopeServices
instanceKlass org/gradle/api/internal/project/DefaultProjectStateRegistry
instanceKlass org/gradle/api/internal/project/ProjectStateRegistry
instanceKlass org/gradle/api/internal/provider/ConfigurationTimeBarrier
instanceKlass org/gradle/problems/buildtree/ProblemReporter
instanceKlass org/gradle/internal/buildtree/BuildTreeScopeServices
instanceKlass org/gradle/internal/buildtree/BuildTreeState
instanceKlass org/gradle/configurationcache/DefaultBuildTreeModelControllerServices$servicesForBuildTree$1
instanceKlass org/gradle/internal/buildtree/BuildTreeModelControllerServices$Supplier
instanceKlass org/gradle/internal/buildtree/BuildModelParameters
instanceKlass org/gradle/internal/buildtree/RunTasksRequirements
instanceKlass org/gradle/internal/logging/sink/ProgressLogEventGenerator$Operation
instanceKlass org/gradle/internal/logging/progress/DefaultProgressLoggerFactory$ProgressLoggerImpl
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge$Started
instanceKlass org/gradle/api/internal/tasks/RegisterTaskBuildOperationType$Details
instanceKlass org/gradle/api/internal/tasks/RealizeTaskBuildOperationType$Details
instanceKlass org/gradle/api/internal/ExecuteDomainObjectCollectionCallbackBuildOperationType$Details
instanceKlass org/gradle/configuration/internal/ExecuteListenerBuildOperationType$Details
instanceKlass org/gradle/configuration/ApplyScriptPluginBuildOperationType$Details
instanceKlass org/gradle/api/internal/plugins/ApplyPluginBuildOperationType$Details
instanceKlass org/gradle/api/internal/tasks/testing/operations/ExecuteTestBuildOperationType$Details
instanceKlass org/gradle/internal/operations/OperationStartEvent
instanceKlass org/gradle/internal/operations/DefaultBuildOperationExecutor$ListenerAdapter
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationTrackingListener
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$DefaultBuildOperationContext
instanceKlass org/gradle/internal/operations/OperationIdentifier
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$3
instanceKlass org/gradle/internal/operations/BuildOperationMetadata$1
instanceKlass org/gradle/internal/operations/BuildOperationDescriptor$Builder
instanceKlass org/gradle/internal/operations/BuildOperationDescriptor
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$CallableBuildOperationWorker
instanceKlass org/gradle/internal/operations/DefaultBuildOperationExecutor$$Lambda$96
instanceKlass org/gradle/launcher/exec/RunAsBuildOperationBuildActionExecutor$3
instanceKlass org/gradle/internal/operations/notify/BuildOperationProgressNotification
instanceKlass org/gradle/internal/operations/notify/BuildOperationFinishedNotification
instanceKlass org/gradle/internal/operations/notify/BuildOperationStartedNotification
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge$Adapter
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge$RecordingListener
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge$ReplayAndAttachListener
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationListener
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge$State
instanceKlass org/gradle/internal/resources/DefaultResourceLockCoordinationService$AcquireLocks
instanceKlass org/gradle/internal/resources/DefaultResourceLockCoordinationService$2
instanceKlass org/gradle/internal/resources/DefaultResourceLockCoordinationService$DefaultResourceLockState
instanceKlass org/gradle/internal/resources/ResourceLockState
instanceKlass org/gradle/internal/work/DefaultWorkerLeaseService$3
instanceKlass com/google/common/collect/Iterables
instanceKlass org/gradle/launcher/exec/RunAsWorkerThreadBuildActionExecutor$$Lambda$95
instanceKlass org/gradle/internal/buildtree/BuildActionRunner$Result
instanceKlass com/google/common/util/concurrent/AbstractFuture$Failure
instanceKlass com/google/common/util/concurrent/AbstractFuture$Cancellation
instanceKlass com/google/common/util/concurrent/AbstractFuture$SetFuture
instanceKlass com/google/common/util/concurrent/Uninterruptibles
instanceKlass org/gradle/internal/resources/AbstractTrackedResourceLock
instanceKlass org/gradle/internal/work/WorkerLeaseRegistry$WorkerLeaseCompletion
instanceKlass org/gradle/internal/resources/AbstractResourceLockRegistry$3
instanceKlass org/gradle/internal/resources/AbstractResourceLockRegistry$2
instanceKlass com/google/common/base/CommonPattern
instanceKlass com/google/common/base/Platform$JdkPatternCompiler
instanceKlass com/google/common/base/PatternCompiler
instanceKlass com/google/common/base/Platform
instanceKlass com/google/common/base/Stopwatch
instanceKlass com/google/common/util/concurrent/AbstractFuture$Waiter
instanceKlass com/google/common/util/concurrent/AbstractFuture$Listener
instanceKlass com/google/common/util/concurrent/AbstractFuture$UnsafeAtomicHelper$1
instanceKlass sun/misc/Unsafe
instanceKlass com/google/common/util/concurrent/AbstractFuture$AtomicHelper
instanceKlass com/google/common/util/concurrent/internal/InternalFutureFailureAccess
instanceKlass com/google/common/util/concurrent/AbstractFuture$Trusted
instanceKlass com/google/common/util/concurrent/ListenableFuture
instanceKlass org/gradle/internal/resources/AbstractResourceLockRegistry$1
instanceKlass org/gradle/internal/work/DefaultWorkerLeaseService$WorkerLeaseLockRegistry$1
instanceKlass org/gradle/internal/resources/AbstractResourceLockRegistry$ThreadLockDetails
instanceKlass org/gradle/configuration/project/ConfigureProjectBuildOperationType$Details
instanceKlass org/gradle/tooling/internal/protocol/events/InternalProjectConfigurationDescriptor
instanceKlass org/gradle/internal/build/event/types/DefaultOperationDescriptor
instanceKlass org/gradle/tooling/internal/protocol/events/InternalOperationDescriptor
instanceKlass org/gradle/tooling/internal/protocol/events/InternalProjectConfigurationResult
instanceKlass org/gradle/api/internal/tasks/execution/ExecuteTaskBuildOperationDetails
instanceKlass org/gradle/internal/operations/trace/CustomOperationTraceSerialization
instanceKlass org/gradle/api/internal/tasks/execution/ExecuteTaskBuildOperationType$Details
instanceKlass org/gradle/tooling/internal/protocol/events/InternalTaskFailureResult
instanceKlass org/gradle/tooling/internal/protocol/events/InternalTaskSkippedResult
instanceKlass org/gradle/execution/plan/Node
instanceKlass org/gradle/tooling/internal/provider/runner/SubtreeFilteringBuildOperationListener
instanceKlass org/gradle/tooling/internal/provider/runner/CompositeOperationResultPostProcessor
instanceKlass org/gradle/tooling/internal/protocol/events/InternalJavaCompileTaskOperationResult$InternalAnnotationProcessorResult
instanceKlass org/gradle/tooling/internal/protocol/events/InternalTaskCachedResult
instanceKlass org/gradle/tooling/internal/protocol/events/InternalTaskSuccessResult
instanceKlass org/gradle/tooling/internal/protocol/events/InternalJavaCompileTaskOperationResult
instanceKlass org/gradle/tooling/internal/protocol/events/InternalIncrementalTaskResult
instanceKlass org/gradle/tooling/internal/protocol/events/InternalTaskResult
instanceKlass org/gradle/api/internal/tasks/compile/tooling/JavaCompileTaskSuccessResultPostProcessor
instanceKlass org/gradle/tooling/internal/provider/runner/TaskOriginTracker
instanceKlass org/gradle/tooling/internal/protocol/events/InternalBinaryPluginIdentifier
instanceKlass org/gradle/tooling/internal/protocol/events/InternalScriptPluginIdentifier
instanceKlass org/gradle/tooling/internal/protocol/events/InternalPluginIdentifier
instanceKlass org/gradle/tooling/internal/provider/runner/PluginApplicationTracker
instanceKlass org/gradle/tooling/internal/provider/runner/OperationDependenciesResolver
instanceKlass org/gradle/tooling/internal/protocol/events/InternalTestFailureResult
instanceKlass org/gradle/tooling/internal/protocol/events/InternalFailureResult
instanceKlass org/gradle/tooling/internal/protocol/events/InternalTestSkippedResult
instanceKlass org/gradle/tooling/internal/protocol/events/InternalTestSuccessResult
instanceKlass org/gradle/tooling/internal/protocol/events/InternalSuccessResult
instanceKlass org/gradle/internal/build/event/types/AbstractResult
instanceKlass org/gradle/tooling/internal/protocol/events/InternalTestResult
instanceKlass org/gradle/tooling/internal/protocol/events/InternalOperationResult
instanceKlass org/gradle/tooling/internal/protocol/events/InternalOperationFinishedProgressEvent
instanceKlass org/gradle/tooling/internal/protocol/events/InternalOperationStartedProgressEvent
instanceKlass org/gradle/tooling/internal/protocol/events/InternalProgressEvent
instanceKlass org/gradle/api/Task
instanceKlass org/gradle/api/plugins/ExtensionAware
instanceKlass org/gradle/tooling/internal/provider/runner/ClientForwardingTestOperationListener
instanceKlass org/gradle/tooling/internal/provider/runner/ProgressEventConsumer
instanceKlass org/gradle/internal/buildtree/BuildActionModelRequirements
instanceKlass org/gradle/launcher/exec/BuildTreeLifecycleBuildActionExecutor
instanceKlass org/gradle/launcher/exec/RunAsBuildOperationBuildActionExecutor$2
instanceKlass org/gradle/launcher/exec/RunAsBuildOperationBuildActionExecutor$1
instanceKlass org/gradle/launcher/exec/RunBuildBuildOperationType$Result
instanceKlass org/gradle/launcher/exec/RunBuildBuildOperationType$Details
instanceKlass org/gradle/launcher/exec/RunAsBuildOperationBuildActionExecutor
instanceKlass org/gradle/launcher/exec/RunAsWorkerThreadBuildActionExecutor
instanceKlass org/gradle/internal/filewatch/FileWatcherEventListener
instanceKlass org/gradle/execution/CancellableOperationManager
instanceKlass org/gradle/tooling/internal/provider/ContinuousBuildActionExecutor
instanceKlass org/gradle/tooling/internal/provider/SubscribableBuildActionExecutor
instanceKlass org/gradle/BuildAdapter
instanceKlass org/gradle/internal/InternalBuildListener
instanceKlass org/gradle/internal/InternalListener
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge$1
instanceKlass org/gradle/BuildListener
instanceKlass org/gradle/deployment/internal/DefaultDeploymentRegistry$PendingChanges
instanceKlass org/gradle/initialization/ContinuousExecutionGate$GateKeeper
instanceKlass org/gradle/initialization/DefaultContinuousExecutionGate
instanceKlass org/gradle/internal/operations/CallableBuildOperation
instanceKlass org/gradle/initialization/ContinuousExecutionGate
instanceKlass org/gradle/api/file/SourceDirectorySet
instanceKlass org/gradle/api/internal/model/DefaultObjectFactory
instanceKlass org/gradle/api/internal/model/NamedObjectInstantiator$$Lambda$94
instanceKlass org/gradle/internal/state/Managed
instanceKlass com/google/common/base/ExtraObjectsMethodsForWeb
instanceKlass org/gradle/model/internal/inspect/ValidationProblemCollector
instanceKlass org/gradle/api/internal/MutationGuards$1
instanceKlass org/gradle/api/internal/MutationGuard
instanceKlass org/gradle/api/internal/MutationGuards
instanceKlass org/gradle/api/internal/CollectionCallbackActionDecorator$1
instanceKlass org/gradle/api/internal/collections/DefaultDomainObjectCollectionFactory
instanceKlass org/gradle/api/file/Directory
instanceKlass org/gradle/api/file/RegularFile
instanceKlass org/gradle/api/file/FileSystemLocation
instanceKlass org/gradle/api/internal/tasks/DefaultTaskDependencyFactory
instanceKlass org/gradle/api/internal/file/FileCollectionInternal$1
instanceKlass org/gradle/api/internal/file/FileCollectionStructureVisitor
instanceKlass org/gradle/api/file/FileVisitor
instanceKlass org/gradle/api/tasks/TaskDependency
instanceKlass org/gradle/api/internal/file/FileCollectionInternal$Source
instanceKlass org/gradle/api/internal/file/AbstractFileCollection
instanceKlass org/gradle/api/internal/file/FileTreeInternal
instanceKlass org/gradle/api/internal/file/collections/MinimalFileTree
instanceKlass org/gradle/api/internal/file/collections/MinimalFileCollection
instanceKlass org/gradle/api/internal/file/DefaultFileCollectionFactory
instanceKlass org/gradle/internal/exceptions/DiagnosticsVisitor
instanceKlass org/gradle/internal/typeconversion/ErrorHandlingNotationParser
instanceKlass org/gradle/internal/typeconversion/NotationConvertResult
instanceKlass org/gradle/internal/typeconversion/NotationConverterToNotationParserAdapter
instanceKlass org/gradle/internal/typeconversion/TypeInfo
instanceKlass org/gradle/internal/typeconversion/NotationParserBuilder
instanceKlass org/gradle/api/internal/file/FileOrUriNotationConverter
instanceKlass org/gradle/api/internal/file/AbstractFileResolver
instanceKlass org/gradle/api/internal/provider/DefaultPropertyFactory
instanceKlass org/gradle/api/internal/provider/PropertyHost$$Lambda$93
instanceKlass org/gradle/internal/state/ModelObject
instanceKlass org/gradle/api/internal/file/collections/DefaultDirectoryFileTreeFactory
instanceKlass org/gradle/api/tasks/util/internal/PatternSets$PatternSetFactory
instanceKlass org/gradle/api/tasks/util/internal/PatternSets
instanceKlass com/google/common/cache/LocalCache$AbstractReferenceEntry
instanceKlass org/gradle/cache/internal/HeapProportionalCacheSizer
instanceKlass org/gradle/internal/instantiation/generator/DefaultInstantiationScheme$DefaultDeserializationInstantiator
instanceKlass org/gradle/internal/instantiation/InstanceFactory
instanceKlass org/gradle/internal/instantiation/generator/DependencyInjectingInstantiator
instanceKlass javax/inject/Inject
instanceKlass org/gradle/internal/instantiation/DeserializationInstantiator
instanceKlass org/gradle/internal/instantiation/generator/DefaultInstantiationScheme
instanceKlass org/gradle/internal/instantiation/generator/ParamsMatchingConstructorSelector
instanceKlass org/gradle/internal/instantiation/generator/Jsr330ConstructorSelector
instanceKlass com/google/common/collect/ImmutableMultimap$Builder
instanceKlass com/google/common/collect/Multiset
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$$Lambda$92
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$GeneratedClassImpl
instanceKlass org/gradle/internal/instantiation/generator/ClassGenerator$GeneratedClass
instanceKlass org/gradle/cache/internal/DefaultCrossBuildInMemoryCacheFactory$AbstractCrossBuildInMemoryCache
instanceKlass org/gradle/model/internal/asm/ClassGeneratorSuffixRegistry
instanceKlass org/gradle/api/DomainObjectSet
instanceKlass org/gradle/api/NamedDomainObjectContainer
instanceKlass org/gradle/util/Configurable
instanceKlass org/gradle/api/NamedDomainObjectSet
instanceKlass org/gradle/api/NamedDomainObjectCollection
instanceKlass org/gradle/api/DomainObjectCollection
instanceKlass org/gradle/api/file/DirectoryProperty
instanceKlass org/gradle/api/file/RegularFileProperty
instanceKlass org/gradle/api/file/FileSystemLocationProperty
instanceKlass org/gradle/api/provider/Property
instanceKlass org/gradle/api/provider/MapProperty
instanceKlass org/gradle/api/provider/SetProperty
instanceKlass org/gradle/api/provider/ListProperty
instanceKlass org/gradle/api/provider/HasMultipleValues
instanceKlass org/gradle/api/provider/Provider
instanceKlass org/gradle/api/file/ConfigurableFileTree
instanceKlass org/gradle/api/file/DirectoryTree
instanceKlass org/gradle/api/file/FileTree
instanceKlass org/gradle/api/file/ConfigurableFileCollection
instanceKlass org/gradle/api/provider/HasConfigurableValue
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$InstantiationStrategy
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$ClassInspectionVisitor
instanceKlass com/google/common/reflect/TypeCapture
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$UnclaimedPropertyHandler
instanceKlass com/google/common/collect/ListMultimap
instanceKlass com/google/common/collect/AbstractMultimap
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator
instanceKlass org/gradle/internal/instantiation/generator/ClassGenerator
instanceKlass org/gradle/api/internal/tasks/properties/annotations/OutputPropertyRoleAnnotationHandler
instanceKlass org/gradle/internal/instantiation/generator/DefaultInstantiatorFactory$ClassGeneratorBackedManagedFactory
instanceKlass org/gradle/internal/instantiation/InstantiationScheme
instanceKlass org/gradle/internal/instantiation/generator/ConstructorSelector
instanceKlass org/gradle/internal/instantiation/generator/DefaultInstantiatorFactory
instanceKlass org/gradle/cache/internal/CrossBuildInMemoryCache
instanceKlass org/gradle/cache/internal/DefaultCrossBuildInMemoryCacheFactory
instanceKlass org/gradle/internal/filewatch/FileSystemChangeWaiter
instanceKlass org/gradle/internal/filewatch/DefaultFileSystemChangeWaiterFactory
instanceKlass org/gradle/internal/filewatch/DefaultFileWatcherFactory
instanceKlass org/gradle/api/execution/internal/TaskInputsListener
instanceKlass org/gradle/api/execution/internal/DefaultTaskInputsListeners
instanceKlass org/gradle/internal/operations/BuildOperationState
instanceKlass org/gradle/internal/operations/UnmanagedBuildOperationWrapper
instanceKlass org/gradle/internal/operations/DefaultBuildOperationExecutor$$Lambda$91
instanceKlass org/gradle/internal/operations/DefaultBuildOperationExecutor$$Lambda$90
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$TimeSupplier
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$1
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext
instanceKlass org/gradle/internal/operations/BuildOperationContext
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution
instanceKlass org/gradle/internal/operations/BuildOperation
instanceKlass org/gradle/internal/operations/BuildOperationWorker
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListenerFactory
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner
instanceKlass org/gradle/internal/operations/BuildOperationQueue
instanceKlass org/gradle/internal/operations/DefaultBuildOperationQueueFactory
instanceKlass org/gradle/internal/operations/BuildOperationRef
instanceKlass org/gradle/internal/operations/BuildOperationQueue$QueueWorker
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener
instanceKlass org/gradle/internal/operations/DefaultBuildOperationExecutor
instanceKlass java/util/concurrent/atomic/AtomicReferenceArray
instanceKlass com/google/common/cache/LocalCache$LoadingValueReference
instanceKlass com/google/common/cache/RemovalListener
instanceKlass com/google/common/cache/Weigher
instanceKlass com/google/common/base/Equivalence
instanceKlass java/util/function/BiPredicate
instanceKlass com/google/common/base/MoreObjects
instanceKlass com/google/common/cache/LocalCache$1
instanceKlass com/google/common/cache/ReferenceEntry
instanceKlass com/google/common/cache/CacheLoader
instanceKlass com/google/common/cache/LocalCache$LocalManualCache
instanceKlass com/google/common/cache/LocalCache$ValueReference
instanceKlass com/google/common/cache/CacheBuilder$2
instanceKlass com/google/common/cache/CacheStats
instanceKlass com/google/common/base/Suppliers$SupplierOfInstance
instanceKlass com/google/common/base/Suppliers
instanceKlass com/google/common/cache/CacheBuilder$1
instanceKlass com/google/common/cache/AbstractCache$StatsCounter
instanceKlass com/google/common/cache/LoadingCache
instanceKlass com/google/common/cache/Cache
instanceKlass com/google/common/base/Ticker
instanceKlass com/google/common/base/Supplier
instanceKlass com/google/common/cache/CacheBuilder
instanceKlass org/gradle/internal/resources/AbstractResourceLockRegistry$ResourceLockProducer
instanceKlass org/gradle/internal/resources/AbstractResourceLockRegistry
instanceKlass org/gradle/internal/resources/ResourceLockRegistry
instanceKlass org/gradle/internal/work/DefaultWorkerLeaseService$ProjectLockStatisticsImpl
instanceKlass org/gradle/internal/resources/ProjectLockStatistics
instanceKlass org/gradle/internal/work/DefaultWorkerLeaseService$Root
instanceKlass org/gradle/internal/work/DefaultWorkerLeaseService$LeaseHolder
instanceKlass org/gradle/internal/work/WorkerLeaseRegistry$WorkerLease
instanceKlass org/gradle/internal/resources/ResourceLock
instanceKlass org/gradle/internal/work/DefaultWorkerLeaseService
instanceKlass org/gradle/internal/resources/DefaultResourceLockCoordinationService
instanceKlass org/gradle/tooling/internal/provider/runner/ToolingApiBuildEventListenerFactory$1
instanceKlass org/gradle/internal/operations/DefaultBuildOperationListenerManager$ProgressShieldingBuildOperationListener
instanceKlass org/gradle/internal/operations/DefaultBuildOperationAncestryTracker
instanceKlass org/gradle/internal/reflect/AnnotationCategory
instanceKlass org/gradle/language/java/internal/JavaLanguagePluginServiceRegistry$JavaGlobalScopeServices$$Lambda$89
instanceKlass sun/invoke/util/VerifyAccess$1
instanceKlass org/gradle/internal/session/BuildSessionLifecycleListener
instanceKlass org/gradle/tooling/internal/provider/BuildSessionLifecycleBuildActionExecuter$$Lambda$88
instanceKlass org/gradle/launcher/exec/BuildActionResult
instanceKlass org/gradle/internal/session/DefaultBuildSessionContext
instanceKlass org/gradle/internal/session/BuildSessionContext
instanceKlass org/gradle/internal/scopeids/PersistentScopeIdStoreFactory
instanceKlass org/gradle/internal/scopeids/ScopeIdsServices
instanceKlass org/gradle/internal/model/ValueCalculator
instanceKlass org/gradle/internal/model/CalculatedValueContainerFactory
instanceKlass org/gradle/plugin/use/internal/InjectedPluginClasspath
instanceKlass org/gradle/configurationcache/DefaultBuildTreeModelControllerServices
instanceKlass org/gradle/api/artifacts/ModuleIdentifier
instanceKlass org/gradle/vcs/internal/resolver/PersistentVcsMetadataCache
instanceKlass org/gradle/vcs/internal/VcsDirectoryLayout
instanceKlass org/gradle/vcs/internal/VersionControlRepositoryConnectionFactory
instanceKlass org/gradle/vcs/internal/services/VersionControlServices$VersionControlBuildSessionServices
instanceKlass org/gradle/nativeplatform/toolchain/internal/gcc/metadata/SystemLibraryDiscovery
instanceKlass org/gradle/nativeplatform/toolchain/internal/xcode/AbstractLocator
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/WindowsKitInstall
instanceKlass org/gradle/platform/base/internal/toolchain/SearchResult
instanceKlass org/gradle/platform/base/internal/toolchain/ToolSearchResult
instanceKlass com/google/common/collect/SetMultimap
instanceKlass com/google/common/collect/Multimap
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/AbstractWindowsKitComponentLocator
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/UcrtLocator
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/version/SystemPathVersionLocator
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/version/AbstractVisualStudioVersionLocator
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/version/VisualStudioMetaDataProvider
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/version/VswhereVersionLocator
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/version/VisualCppMetadataProvider
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/WindowsSdkLocator
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/VisualStudioLocator
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/WindowsComponentLocator
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/version/VisualStudioVersionLocator
instanceKlass org/gradle/nativeplatform/internal/services/NativeBinaryServices$BuildSessionScopeServices
instanceKlass org/gradle/api/tasks/testing/TestDescriptor
instanceKlass org/gradle/internal/service/scopes/Scopes$Build
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$RegistrationWrapper
instanceKlass org/gradle/api/internal/tasks/testing/operations/TestListenerBuildOperationAdapter
instanceKlass org/gradle/api/internal/tasks/testing/results/TestListenerInternal
instanceKlass org/gradle/api/internal/tasks/testing/operations/TestExecutionBuildOperationBuildSessionScopeServices
instanceKlass org/gradle/api/internal/catalog/DependenciesAccessorsWorkspaceProvider
instanceKlass org/gradle/internal/execution/workspace/WorkspaceProvider
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildSessionScopeServices
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ArtifactVisitor
instanceKlass org/gradle/api/internal/file/FileCollectionInternal
instanceKlass org/gradle/api/internal/tasks/TaskDependencyContainer
instanceKlass org/gradle/api/file/FileCollection
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ArtifactSetToFileCollectionFactory
instanceKlass org/gradle/workers/internal/WorkerExecutionQueueFactory
instanceKlass org/gradle/process/internal/worker/child/WorkerDirectoryProvider
instanceKlass org/gradle/internal/work/ConditionalExecutionQueueFactory
instanceKlass org/gradle/workers/internal/WorkersServices$BuildSessionScopeServices
instanceKlass org/gradle/internal/fingerprint/impl/FileCollectionFingerprinterRegistrations
instanceKlass org/gradle/internal/vfs/impl/DefaultFileSystemAccess
instanceKlass org/gradle/internal/execution/fingerprint/FileCollectionFingerprinterRegistry
instanceKlass org/gradle/internal/execution/fingerprint/InputFingerprinter
instanceKlass org/gradle/internal/execution/OutputSnapshotter
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$BuildSessionServices
instanceKlass org/gradle/internal/buildtree/BuildTreeModelControllerServices
instanceKlass org/gradle/internal/session/BuildSessionActionExecutor
instanceKlass org/gradle/tooling/internal/provider/LauncherServices$ToolingBuildSessionScopeServices
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$CollectionService
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$CollectingVisitor
instanceKlass sun/reflect/generics/tree/VoidDescriptor
instanceKlass org/gradle/initialization/SettingsLocation
instanceKlass org/gradle/cache/internal/CleanupActionFactory
instanceKlass org/gradle/internal/scopeids/id/ScopeId
instanceKlass org/gradle/internal/scopeids/PersistentScopeIdLoader
instanceKlass org/gradle/api/internal/FeaturePreviews
instanceKlass org/gradle/internal/session/BuildSessionScopeServices$CrossBuildFileHashCacheWrapper
instanceKlass org/gradle/initialization/layout/ProjectCacheDir
instanceKlass org/gradle/api/internal/attributes/DefaultImmutableAttributesFactory
instanceKlass org/gradle/api/internal/attributes/ImmutableAttributesFactory
instanceKlass org/gradle/internal/buildevents/BuildStartedTime
instanceKlass org/gradle/deployment/internal/DefaultDeploymentRegistry
instanceKlass org/gradle/internal/filewatch/PendingChangesListener
instanceKlass org/gradle/deployment/internal/DeploymentRegistryInternal
instanceKlass org/gradle/deployment/internal/DeploymentRegistry
instanceKlass org/gradle/internal/filewatch/PendingChangesManager
instanceKlass org/gradle/api/internal/tasks/userinput/UserInputReader
instanceKlass org/gradle/api/internal/tasks/userinput/UserInputHandler
instanceKlass org/gradle/internal/hash/ChecksumService
instanceKlass org/gradle/api/internal/project/CrossProjectConfigurator
instanceKlass org/gradle/internal/work/AsyncWorkTracker
instanceKlass org/gradle/internal/session/BuildSessionScopeServices
instanceKlass org/gradle/internal/service/scopes/DefaultGradleUserHomeScopeServiceRegistry$Services
instanceKlass org/gradle/api/internal/tasks/compile/incremental/cache/UserHomeScopedCompileCaches
instanceKlass org/gradle/api/internal/tasks/compile/incremental/cache/GeneralCompileCaches
instanceKlass org/gradle/api/internal/tasks/CompileServices$UserHomeScopeServices
instanceKlass org/gradle/kotlin/dsl/provider/plugins/DefaultKotlinScriptBasePluginsApplicator
instanceKlass org/gradle/kotlin/dsl/provider/KotlinScriptBasePluginsApplicator
instanceKlass org/gradle/kotlin/dsl/provider/plugins/precompiled/DefaultPrecompiledScriptPluginsSupport
instanceKlass org/gradle/kotlin/dsl/provider/PrecompiledScriptPluginsSupport
instanceKlass org/gradle/kotlin/dsl/provider/plugins/DefaultProjectSchemaProvider
instanceKlass org/gradle/kotlin/dsl/accessors/ProjectSchemaProvider
instanceKlass org/gradle/kotlin/dsl/provider/plugins/GradleUserHomeServices
instanceKlass org/gradle/api/internal/artifacts/transform/ImmutableTransformationWorkspaceServices
instanceKlass org/gradle/api/internal/artifacts/transform/TransformationWorkspaceServices
instanceKlass org/gradle/api/internal/artifacts/ivyservice/DefaultArtifactCaches$WritableArtifactCacheLockingParameters
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ArtifactCachesProvider
instanceKlass org/gradle/internal/execution/history/ExecutionHistoryStore
instanceKlass org/gradle/internal/execution/history/ExecutionHistoryCacheAccess
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementGradleUserHomeScopeServices
instanceKlass org/gradle/workers/internal/ClassLoaderStructureProvider
instanceKlass org/gradle/workers/internal/WorkerDaemonClientsManager
instanceKlass org/gradle/workers/internal/ActionExecutionSpecFactory
instanceKlass org/gradle/workers/internal/WorkersServices$GradleUserHomeServices
instanceKlass org/gradle/kotlin/dsl/provider/KotlinScriptClassloadingCache
instanceKlass org/gradle/kotlin/dsl/provider/GradleUserHomeServices
instanceKlass org/gradle/kotlin/dsl/support/EmbeddedKotlinProvider
instanceKlass org/gradle/kotlin/dsl/support/GradleUserHomeServices
instanceKlass org/gradle/kotlin/dsl/cache/KotlinDslWorkspaceProvider
instanceKlass org/gradle/kotlin/dsl/cache/GradleUserHomeServices
instanceKlass org/gradle/api/internal/changedetection/state/CrossBuildFileHashCache
instanceKlass org/gradle/internal/watch/registry/FileWatcherRegistryFactory
instanceKlass org/gradle/internal/vfs/impl/VfsRootReference
instanceKlass org/gradle/internal/build/BuildState
instanceKlass org/gradle/internal/watch/vfs/impl/LocationsWrittenByCurrentBuild
instanceKlass org/gradle/internal/vfs/FileSystemAccess$WriteListener
instanceKlass org/gradle/api/internal/changedetection/state/ResourceSnapshotterCacheService
instanceKlass org/gradle/internal/fingerprint/classpath/ClasspathFingerprinter
instanceKlass org/gradle/internal/execution/fingerprint/FileCollectionFingerprinter
instanceKlass org/gradle/internal/hash/FileHasher
instanceKlass org/gradle/internal/watch/vfs/WatchableFileSystemDetector
instanceKlass org/gradle/internal/watch/vfs/BuildLifecycleAwareVirtualFileSystem
instanceKlass org/gradle/internal/vfs/VirtualFileSystem
instanceKlass org/gradle/internal/fingerprint/GenericFileTreeSnapshotter
instanceKlass org/gradle/internal/execution/fingerprint/FileCollectionSnapshotter
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$GradleUserHomeServices
instanceKlass org/gradle/tooling/internal/provider/serialization/PayloadSerializer
instanceKlass org/gradle/tooling/internal/provider/serialization/PayloadClassLoaderFactory
instanceKlass org/gradle/tooling/internal/provider/serialization/PayloadClassLoaderRegistry
instanceKlass org/gradle/tooling/internal/provider/LauncherServices$ToolingGradleUserHomeScopeServices
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$InstanceUnpackingVisitor
instanceKlass org/gradle/internal/classpath/ClasspathBuilder$EntryBuilder
instanceKlass org/gradle/internal/classpath/ClasspathEntryVisitor$Entry
instanceKlass org/gradle/cache/internal/DirectoryCleanupAction
instanceKlass org/gradle/cache/CleanupProgressMonitor
instanceKlass org/gradle/cache/internal/GradleUserHomeCleanupService
instanceKlass org/gradle/cache/internal/VersionSpecificCacheDirectoryScanner
instanceKlass org/gradle/cache/internal/UsedGradleVersionsFromGradleUserHomeCaches
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/logging/services/ProgressLoggingBridge
instanceKlass org/gradle/internal/logging/progress/ProgressLogger
instanceKlass org/gradle/internal/logging/progress/DefaultProgressLoggerFactory
instanceKlass org/gradle/internal/operations/DefaultBuildOperationIdFactory
instanceKlass org/gradle/internal/service/scopes/WorkerSharedGlobalScopeServices$$Lambda$87
instanceKlass org/gradle/internal/service/scopes/WorkerSharedGlobalScopeServices$$Lambda$86
instanceKlass java/util/function/LongSupplier
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/file/impl/DefaultDeleter
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/cache/internal/GradleUserHomeCleanupServices
instanceKlass org/gradle/cache/internal/DefaultCacheScopeMapping
instanceKlass org/gradle/cache/internal/CacheRepositoryServices
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/service/scopes/DefaultGradleUserHomeScopeServiceRegistry$1$$Lambda$85
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/snapshot/impl/DefaultValueSnapshotter
instanceKlass org/gradle/internal/isolation/IsolatableFactory
instanceKlass org/gradle/internal/snapshot/ValueSnapshotter
instanceKlass org/gradle/cache/internal/DefaultGeneratedGradleJarCache
instanceKlass org/gradle/cache/internal/GeneratedGradleJarCache
instanceKlass org/gradle/groovy/scripts/internal/CrossBuildInMemoryCachingScriptClassCache
instanceKlass org/gradle/internal/classpath/ClasspathBuilder
instanceKlass org/gradle/internal/classpath/ClasspathWalker
instanceKlass org/gradle/internal/vfs/FileSystemAccess
instanceKlass org/gradle/api/internal/changedetection/state/FileTimeStampInspector
instanceKlass org/gradle/initialization/RootBuildLifecycleListener
instanceKlass org/gradle/cache/internal/UsedGradleVersions
instanceKlass org/gradle/cache/internal/CacheScopeMapping
instanceKlass org/gradle/internal/classloader/ClasspathHasher
instanceKlass org/gradle/cache/CacheRepository
instanceKlass org/gradle/initialization/ClassLoaderScopeRegistryListenerManager
instanceKlass org/gradle/internal/jvm/JavaModuleDetector
instanceKlass org/gradle/process/internal/worker/child/WorkerProcessClassPathProvider
instanceKlass org/gradle/internal/service/scopes/DefaultGradleUserHomeScopeServiceRegistry$1
instanceKlass org/gradle/internal/session/BuildSessionState
instanceKlass org/gradle/internal/operations/trace/SerializedOperation
instanceKlass org/gradle/internal/operations/trace/BuildOperationTrace$1
instanceKlass org/gradle/internal/operations/DefaultBuildOperationListenerManager$1
instanceKlass org/gradle/internal/operations/DefaultBuildOperationListenerManager
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationValve
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationListenerRegistrar
instanceKlass org/gradle/internal/operations/logging/LoggingBuildOperationProgressBroadcaster
instanceKlass org/gradle/internal/operations/trace/BuildOperationTrace
instanceKlass org/gradle/configuration/internal/ListenerBuildOperationDecorator
instanceKlass org/gradle/configuration/internal/UserCodeApplicationContext
instanceKlass org/gradle/internal/work/WorkerLeaseService
instanceKlass org/gradle/internal/resources/ProjectLeaseRegistry
instanceKlass org/gradle/internal/work/WorkerLeaseRegistry
instanceKlass org/gradle/internal/operations/BuildOperationExecutor
instanceKlass org/gradle/internal/operations/BuildOperationRunner
instanceKlass org/gradle/internal/operations/BuildOperationQueueFactory
instanceKlass org/gradle/api/internal/CollectionCallbackActionDecorator
instanceKlass org/gradle/internal/session/CrossBuildSessionState$Services
instanceKlass org/gradle/internal/service/ServiceRegistryBuilder
instanceKlass org/gradle/internal/session/CrossBuildSessionState
instanceKlass org/gradle/tooling/internal/protocol/ModelIdentifier
instanceKlass org/gradle/tooling/internal/protocol/InternalProtocolInterface
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer$3
instanceKlass org/gradle/internal/logging/sink/ProgressLogEventGenerator
instanceKlass org/gradle/internal/logging/console/BuildLogLevelFilterRenderer
instanceKlass org/gradle/launcher/daemon/server/exec/ExecuteBuild$1
instanceKlass org/gradle/initialization/DefaultBuildRequestMetaData
instanceKlass org/gradle/initialization/DefaultBuildRequestContext
instanceKlass org/gradle/launcher/daemon/server/exec/DaemonConnectionBackedEventConsumer
instanceKlass org/gradle/launcher/daemon/server/exec/WatchForDisconnection$1
instanceKlass org/gradle/internal/featurelifecycle/LoggingIncubatingFeatureHandler
instanceKlass org/gradle/util/internal/IncubationLogger
instanceKlass org/gradle/internal/featurelifecycle/FeatureUsage
instanceKlass org/gradle/internal/featurelifecycle/UsageLocationReporter
instanceKlass org/gradle/internal/featurelifecycle/LoggingDeprecatedFeatureHandler
instanceKlass org/gradle/internal/featurelifecycle/FeatureHandler
instanceKlass org/gradle/internal/deprecation/DeprecationMessageBuilder
instanceKlass org/gradle/internal/deprecation/DeprecationLogger
instanceKlass org/gradle/launcher/daemon/server/exec/ForwardClientInput$2
instanceKlass org/gradle/util/internal/StdinSwapper$2
instanceKlass org/gradle/util/internal/StdinSwapper$1
instanceKlass org/gradle/util/internal/Swapper
instanceKlass org/gradle/launcher/daemon/server/exec/ForwardClientInput$1
instanceKlass java/math/MathContext
instanceKlass org/gradle/internal/util/NumberUtil
instanceKlass org/gradle/launcher/daemon/server/exec/LogToClient$AsynchronousLogDispatcher$1
instanceKlass java/util/concurrent/CountDownLatch
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorFactory
instanceKlass java/lang/reflect/AccessibleObject$$Lambda$84
instanceKlass com/google/common/collect/AbstractIterator$1
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$UnmodifiableEntry
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1
instanceKlass org/gradle/launcher/daemon/server/DaemonStateCoordinator$1
instanceKlass org/gradle/launcher/daemon/registry/PersistentDaemonRegistry$5
instanceKlass org/gradle/launcher/daemon/server/exec/StartBuildOrRespondWithBusy$1
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection$CommandQueue$1
instanceKlass org/gradle/launcher/daemon/server/exec/HandleCancel$1
instanceKlass com/google/common/collect/Platform
instanceKlass org/gradle/launcher/daemon/server/api/DaemonCommandExecution
instanceKlass java/lang/Long$LongCache
instanceKlass org/gradle/launcher/exec/DefaultBuildActionParameters
instanceKlass org/gradle/configuration/GradleLauncherMetaData
instanceKlass com/google/common/base/Converter
instanceKlass com/google/common/collect/Maps$EntryTransformer
instanceKlass com/google/common/collect/SortedMapDifference
instanceKlass com/google/common/collect/MapDifference
instanceKlass com/google/common/collect/Maps
instanceKlass com/google/common/collect/AbstractMapEntry
instanceKlass java/util/AbstractMap$SimpleImmutableEntry
instanceKlass com/google/common/collect/ImmutableMap$Builder
instanceKlass com/google/common/collect/BiMap
instanceKlass com/google/common/collect/ImmutableMap
instanceKlass org/gradle/internal/DefaultTaskExecutionRequest
instanceKlass org/gradle/TaskExecutionRequest
instanceKlass com/google/common/collect/CollectPreconditions
instanceKlass org/gradle/internal/buildoption/BuildOption$Value
instanceKlass org/gradle/internal/concurrent/DefaultParallelismConfiguration
instanceKlass org/gradle/internal/logging/DefaultLoggingConfiguration
instanceKlass org/gradle/initialization/BuildLayoutParameters
instanceKlass java/nio/channels/spi/AbstractSelector$1
instanceKlass sun/nio/ch/WindowsSelectorImpl$MapEntry
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection$1
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection$ReceiveQueue
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection$DisconnectQueue
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection$CommandQueue
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection
instanceKlass org/gradle/launcher/daemon/server/api/DaemonConnection
instanceKlass org/gradle/launcher/daemon/server/DefaultIncomingConnectionHandler$ConnectionWorker
instanceKlass org/gradle/launcher/daemon/server/SynchronizedDispatchConnection
instanceKlass org/gradle/internal/serialize/Serializers$StatefulSerializerAdapter$2
instanceKlass org/gradle/internal/serialize/Serializers$StatefulSerializerAdapter$1
instanceKlass org/gradle/internal/remote/internal/inet/SocketInetAddress$Serializer
instanceKlass org/gradle/internal/io/BufferCaster
instanceKlass java/lang/invoke/ConstantBootstraps
instanceKlass java/util/concurrent/atomic/AtomicReferenceFieldUpdater$AtomicReferenceFieldUpdaterImpl$1
instanceKlass java/util/concurrent/atomic/AtomicReferenceFieldUpdater
instanceKlass sun/nio/ch/OptionKey
instanceKlass sun/nio/ch/SocketOptionRegistry$LazyInitialization
instanceKlass sun/nio/ch/SocketOptionRegistry$RegistryKey
instanceKlass sun/nio/ch/SocketOptionRegistry
instanceKlass java/util/stream/Collectors$$Lambda$83
instanceKlass java/util/stream/Collectors$$Lambda$82
instanceKlass java/util/stream/Collectors$$Lambda$81
instanceKlass java/util/stream/Collectors$$Lambda$80
instanceKlass sun/net/ext/ExtendedSocketOptions$$Lambda$79
instanceKlass sun/nio/ch/ExtendedSocketOption$1
instanceKlass sun/nio/ch/ExtendedSocketOption
instanceKlass sun/nio/ch/SocketChannelImpl$DefaultOptionsHolder
instanceKlass jdk/internal/util/ArraysSupport
instanceKlass java/nio/BufferMismatch
instanceKlass sun/nio/ch/IOStatus
instanceKlass sun/nio/ch/Util$BufferCache
instanceKlass sun/nio/ch/PipeImpl$Initializer$LoopbackConnector
instanceKlass sun/nio/ch/PipeImpl$Initializer
instanceKlass java/nio/channels/Pipe
instanceKlass sun/nio/ch/NativeObject
instanceKlass sun/nio/ch/PollArrayWrapper
instanceKlass sun/nio/ch/WindowsSelectorImpl$FinishLock
instanceKlass sun/nio/ch/WindowsSelectorImpl$StartLock
instanceKlass sun/nio/ch/WindowsSelectorImpl$SubSelector
instanceKlass java/nio/channels/SelectionKey
instanceKlass sun/nio/ch/Util$2
instanceKlass sun/nio/ch/Util
instanceKlass java/nio/channels/Selector
instanceKlass java/nio/DirectByteBuffer$Deallocator
instanceKlass org/gradle/internal/remote/internal/KryoBackedMessageSerializer
instanceKlass org/gradle/internal/remote/internal/inet/SocketConnection
instanceKlass org/gradle/launcher/daemon/server/DaemonStateCoordinator$2
instanceKlass org/gradle/internal/serialize/ObjectReader
instanceKlass org/gradle/internal/serialize/ObjectWriter
instanceKlass org/gradle/internal/serialize/Serializers$StatefulSerializerAdapter
instanceKlass org/gradle/internal/serialize/StatefulSerializer
instanceKlass org/gradle/launcher/daemon/server/Daemon$DefaultDaemonExpirationListener
instanceKlass org/gradle/internal/serialize/Serializers
instanceKlass org/gradle/launcher/daemon/server/Daemon$DaemonExpirationPeriodicCheck
instanceKlass org/gradle/internal/remote/internal/RemoteConnection
instanceKlass org/gradle/internal/remote/internal/Connection
instanceKlass org/gradle/internal/dispatch/Receive
instanceKlass org/gradle/internal/remote/internal/MessageSerializer
instanceKlass org/gradle/launcher/daemon/server/DaemonRegistryUnavailableExpirationStrategy
instanceKlass org/gradle/internal/remote/internal/inet/SocketConnectCompletion
instanceKlass org/gradle/internal/remote/internal/ConnectCompletion
instanceKlass org/gradle/internal/event/DefaultListenerManager$ListenerDetails
instanceKlass org/gradle/launcher/daemon/server/health/LowMemoryDaemonExpirationStrategy
instanceKlass org/gradle/process/internal/health/memory/OsMemoryStatusListener
instanceKlass org/gradle/launcher/daemon/server/NotMostRecentlyUsedDaemonExpirationStrategy
instanceKlass java/net/Socket
instanceKlass com/google/common/base/Functions$ConstantFunction
instanceKlass com/google/common/base/Functions
instanceKlass org/gradle/launcher/daemon/server/DaemonIdleTimeoutExpirationStrategy
instanceKlass org/gradle/launcher/daemon/context/DaemonCompatibilitySpec
instanceKlass org/gradle/api/internal/specs/ExplainingSpec
instanceKlass org/gradle/launcher/daemon/server/CompatibleDaemonExpirationStrategy
instanceKlass org/gradle/launcher/daemon/server/expiry/AllDaemonExpirationStrategy
instanceKlass org/gradle/internal/stream/EncodedStream
instanceKlass org/gradle/launcher/daemon/bootstrap/DaemonStartupCommunication
instanceKlass org/gradle/cache/internal/DefaultFileLockManager$DefaultFileLock$3
instanceKlass org/gradle/cache/internal/DefaultFileLockManager$DefaultFileLock$2
instanceKlass org/gradle/cache/internal/DefaultFileLockManager$DefaultFileLock$1
instanceKlass java/io/FileOutputStream$1
instanceKlass org/gradle/internal/remote/internal/inet/SocketInetAddress
instanceKlass org/gradle/internal/serialize/AbstractEncoder
instanceKlass org/gradle/internal/serialize/FlushableEncoder
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistryContent$$Lambda$78
instanceKlass org/gradle/launcher/daemon/registry/DaemonInfo$Serializer
instanceKlass org/gradle/cache/internal/filelock/LockInfo
instanceKlass org/gradle/cache/internal/DefaultFileLockManager$DefaultFileLock$$Lambda$77
instanceKlass org/gradle/cache/internal/filelock/DefaultLockStateSerializer$SequenceNumberLockState
instanceKlass org/gradle/internal/io/IOQuery$Result
instanceKlass org/gradle/cache/internal/filelock/FileLockOutcome
instanceKlass org/gradle/cache/internal/DefaultFileLockManager$DefaultFileLock$4
instanceKlass org/gradle/internal/io/ExponentialBackoff
instanceKlass org/gradle/cache/internal/DefaultFileLockManager$AwaitableFileLockReleasedSignal
instanceKlass org/gradle/cache/FileLockReleasedSignal
instanceKlass org/gradle/cache/internal/filelock/LockInfoSerializer
instanceKlass org/gradle/cache/internal/filelock/LockInfoAccess
instanceKlass org/gradle/cache/internal/filelock/LockStateAccess
instanceKlass org/gradle/cache/internal/filelock/LockFileAccess
instanceKlass org/gradle/cache/internal/filelock/LockState
instanceKlass org/gradle/cache/internal/filelock/DefaultLockStateSerializer
instanceKlass org/gradle/internal/io/IOQuery
instanceKlass org/gradle/cache/FileLock$State
instanceKlass org/gradle/cache/internal/filelock/LockStateSerializer
instanceKlass sun/net/ResourceManager
instanceKlass java/net/DatagramPacket
instanceKlass java/net/DatagramSocket$1
instanceKlass java/net/AbstractPlainDatagramSocketImpl$1
instanceKlass java/net/DatagramSocketImpl
instanceKlass java/net/DefaultDatagramSocketImplFactory
instanceKlass java/net/DatagramSocket
instanceKlass org/gradle/cache/internal/locklistener/FileLockCommunicator
instanceKlass org/gradle/cache/internal/filelock/LockOptionsBuilder
instanceKlass org/gradle/cache/internal/SimpleStateCache$1Updater
instanceKlass org/gradle/cache/internal/FileIntegrityViolationSuppressingPersistentStateCacheDecorator$1
instanceKlass org/gradle/launcher/daemon/registry/PersistentDaemonRegistry$8
instanceKlass org/gradle/launcher/daemon/registry/DaemonInfo
instanceKlass org/gradle/launcher/daemon/context/DaemonConnectDetails
instanceKlass sun/util/resources/provider/NonBaseLocaleDataMetaInfo
instanceKlass sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda$76
instanceKlass sun/util/locale/provider/BaseLocaleDataMetaInfo
instanceKlass sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda$75
instanceKlass sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda$74
instanceKlass sun/util/locale/provider/TimeZoneNameUtility$TimeZoneNameGetter
instanceKlass sun/util/locale/provider/TimeZoneNameUtility
instanceKlass org/gradle/internal/remote/internal/inet/TcpIncomingConnector$1
instanceKlass org/gradle/internal/remote/internal/inet/TcpIncomingConnector$Receiver
instanceKlass org/gradle/internal/remote/internal/inet/MultiChoiceAddress
instanceKlass org/gradle/internal/remote/internal/inet/InetEndpoint
instanceKlass java/util/UUID$Holder
instanceKlass java/util/UUID
instanceKlass sun/net/NetHooks
instanceKlass java/net/InetSocketAddress$InetSocketAddressHolder
instanceKlass java/net/Inet4AddressImpl
instanceKlass org/gradle/internal/remote/internal/inet/InetAddresses
instanceKlass java/net/AbstractPlainSocketImpl$1
instanceKlass java/net/StandardSocketOptions$StdSocketOption
instanceKlass java/net/StandardSocketOptions
instanceKlass java/net/SocketImpl
instanceKlass java/net/SocketOptions
instanceKlass java/net/SocksConsts
instanceKlass java/net/ServerSocket$2
instanceKlass jdk/internal/misc/JavaNetSocketAccess
instanceKlass java/net/ServerSocket
instanceKlass jdk/net/ExtendedSocketOptions$PlatformSocketOptions$1
instanceKlass jdk/net/ExtendedSocketOptions$PlatformSocketOptions
instanceKlass jdk/net/SocketFlow
instanceKlass jdk/net/ExtendedSocketOptions$ExtSocketOption
instanceKlass java/net/SocketOption
instanceKlass jdk/net/ExtendedSocketOptions
instanceKlass sun/net/ext/ExtendedSocketOptions
instanceKlass sun/nio/ch/Net$1
instanceKlass java/net/ProtocolFamily
instanceKlass sun/nio/ch/Net
instanceKlass sun/nio/ch/SelChImpl
instanceKlass sun/nio/ch/DefaultSelectorProvider
instanceKlass java/nio/channels/spi/SelectorProvider$1
instanceKlass java/nio/channels/spi/SelectorProvider
instanceKlass java/nio/channels/NetworkChannel
instanceKlass org/gradle/launcher/daemon/server/DaemonTcpServerConnector$1
instanceKlass org/gradle/launcher/daemon/server/Daemon$5
instanceKlass org/gradle/launcher/daemon/server/DefaultIncomingConnectionHandler
instanceKlass org/gradle/initialization/DefaultBuildCancellationToken
instanceKlass java/util/concurrent/SynchronousQueue$TransferStack$SNode
instanceKlass java/util/concurrent/SynchronousQueue$Transferer
instanceKlass org/gradle/initialization/BuildCancellationToken
instanceKlass org/gradle/launcher/daemon/server/DaemonStateCoordinator
instanceKlass org/gradle/launcher/daemon/server/Daemon$4
instanceKlass org/gradle/launcher/daemon/server/Daemon$3
instanceKlass org/gradle/launcher/daemon/server/Daemon$2
instanceKlass org/gradle/launcher/daemon/server/Daemon$1
instanceKlass org/gradle/launcher/daemon/server/DaemonRegistryUpdater
instanceKlass sun/security/provider/AbstractDrbg$NonceProvider
instanceKlass sun/security/provider/AbstractDrbg$SeederHolder$$Lambda$73
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass sun/nio/fs/BasicFileAttributesHolder
instanceKlass sun/nio/fs/WindowsDirectoryStream$WindowsDirectoryIterator
instanceKlass sun/nio/fs/WindowsDirectoryStream
instanceKlass java/nio/file/DirectoryStream
instanceKlass java/nio/file/Files$AcceptAllFilter
instanceKlass java/nio/file/DirectoryStream$Filter
instanceKlass java/net/NetworkInterface$2
instanceKlass java/net/DefaultInterface
instanceKlass java/net/Inet6Address$Inet6AddressHolder
instanceKlass java/net/InetAddress$PlatformNameService
instanceKlass java/net/InetAddress$NameService
instanceKlass java/net/Inet6AddressImpl
instanceKlass java/net/InetAddressImpl
instanceKlass java/net/InetAddressImplFactory
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Node
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Index
instanceKlass java/util/concurrent/ConcurrentNavigableMap
instanceKlass java/net/InetAddress$InetAddressHolder
instanceKlass java/net/InetAddress$2
instanceKlass jdk/internal/misc/JavaNetInetAddressAccess
instanceKlass java/net/InetAddress$1
instanceKlass java/net/InetAddress
instanceKlass java/net/InterfaceAddress
instanceKlass java/net/NetworkInterface$1
instanceKlass java/net/NetworkInterface
instanceKlass sun/security/provider/ByteArrayAccess
instanceKlass sun/security/provider/SeedGenerator$1
instanceKlass sun/security/util/MessageDigestSpi2
instanceKlass sun/security/jca/GetInstance$Instance
instanceKlass sun/security/jca/GetInstance
instanceKlass java/security/MessageDigestSpi
instanceKlass sun/security/provider/SeedGenerator
instanceKlass sun/security/provider/AbstractDrbg$SeederHolder
instanceKlass java/security/DrbgParameters$NextBytes
instanceKlass sun/security/provider/AbstractDrbg$$Lambda$72
instanceKlass sun/security/provider/EntropySource
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass sun/security/provider/AbstractDrbg
instanceKlass java/security/DrbgParameters$Instantiation
instanceKlass java/security/DrbgParameters
instanceKlass sun/security/provider/MoreDrbgParameters
instanceKlass sun/security/provider/DRBG$$Lambda$71
instanceKlass java/security/SecureRandomSpi
instanceKlass java/security/SecureRandomParameters
instanceKlass java/security/Provider$UString
instanceKlass java/security/Provider$Service
instanceKlass sun/security/provider/NativePRNG$NonBlocking
instanceKlass sun/security/provider/NativePRNG$Blocking
instanceKlass sun/security/provider/NativePRNG
instanceKlass sun/security/provider/SunEntries$1
instanceKlass sun/security/provider/SunEntries
instanceKlass sun/security/jca/ProviderList$2
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$PreparedASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryConverter
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ExceptionalBinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIConverter
instanceKlass jdk/internal/math/FloatingDecimal
instanceKlass java/security/Provider$EngineDescription
instanceKlass java/security/Provider$ServiceKey
instanceKlass sun/security/jca/ProviderConfig
instanceKlass sun/security/jca/ProviderList
instanceKlass sun/security/jca/Providers
instanceKlass com/google/common/base/Joiner
instanceKlass org/gradle/launcher/daemon/server/exec/DaemonCommandExecuter
instanceKlass org/gradle/internal/remote/internal/inet/MultiChoiceAddressSerializer
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistryContent$Serializer
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistryContent
instanceKlass org/gradle/cache/LockOptions
instanceKlass org/gradle/cache/internal/AbstractFileAccess
instanceKlass org/gradle/internal/serialize/Encoder
instanceKlass org/gradle/cache/internal/SimpleStateCache
instanceKlass org/gradle/cache/internal/FileIntegrityViolationSuppressingPersistentStateCacheDecorator
instanceKlass org/gradle/cache/PersistentStateCache$UpdateAction
instanceKlass org/gradle/cache/PersistentStateCache
instanceKlass org/gradle/launcher/daemon/registry/PersistentDaemonRegistry
instanceKlass org/gradle/cache/internal/CacheAccessSerializer$$Lambda$70
instanceKlass org/gradle/cache/Cache$$Lambda$69
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistryServices$$Lambda$68
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/FallbackStat
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/EmptyChmod
instanceKlass org/gradle/internal/nativeintegration/filesystem/jdk7/Jdk7Symlink
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass net/rubygrapefruit/platform/file/PosixFileInfo
instanceKlass org/gradle/internal/nativeintegration/services/NativeServices$BrokenService
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/UnavailablePosixFiles
instanceKlass net/rubygrapefruit/platform/terminal/Terminals
instanceKlass org/gradle/api/internal/file/temp/GradleUserHomeTemporaryFileProvider$1
instanceKlass org/gradle/internal/nativeintegration/services/NativeServices$2
instanceKlass net/rubygrapefruit/platform/file/WindowsFileInfo
instanceKlass net/rubygrapefruit/platform/file/FileInfo
instanceKlass net/rubygrapefruit/platform/internal/DirList
instanceKlass net/rubygrapefruit/platform/internal/AbstractFiles
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/NativePlatformBackedFileMetadataAccessor
instanceKlass java/util/Random
instanceKlass org/gradle/internal/id/RandomLongIdGenerator
instanceKlass org/gradle/cache/internal/DefaultProcessMetaDataProvider
instanceKlass org/gradle/internal/io/ExponentialBackoff$Signal
instanceKlass org/gradle/cache/FileLock
instanceKlass org/gradle/cache/FileAccess
instanceKlass org/gradle/cache/internal/DefaultFileLockManager
instanceKlass org/gradle/internal/remote/ConnectionAcceptor
instanceKlass org/gradle/internal/remote/Address
instanceKlass java/net/SocketAddress
instanceKlass org/gradle/internal/remote/internal/inet/TcpIncomingConnector
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$OutputMessageSerializer
instanceKlass org/gradle/internal/logging/serializer/LogLevelChangeEventSerializer
instanceKlass org/gradle/internal/logging/serializer/ProgressEventSerializer
instanceKlass org/gradle/internal/logging/serializer/ProgressCompleteEventSerializer
instanceKlass org/gradle/internal/operations/BuildOperationMetadata
instanceKlass org/gradle/internal/logging/serializer/ProgressStartEventSerializer
instanceKlass org/gradle/internal/logging/serializer/SpanSerializer
instanceKlass org/gradle/internal/logging/serializer/StyledTextOutputEventSerializer
instanceKlass org/gradle/internal/logging/serializer/UserInputResumeEventSerializer
instanceKlass org/gradle/internal/logging/serializer/PromptOutputEventSerializer
instanceKlass org/gradle/internal/logging/serializer/UserInputRequestEventSerializer
instanceKlass org/gradle/internal/logging/serializer/LogEventSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$CloseInputSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$ForwardInputSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$BuildEventSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$FinishedSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$SuccessSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$FailureSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$BuildStartedSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$DaemonUnavailableSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$CancelSerializer
instanceKlass org/gradle/launcher/exec/BuildActionParameters
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$BuildActionParametersSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$BuildSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer
instanceKlass org/gradle/launcher/daemon/server/DaemonTcpServerConnector
instanceKlass org/gradle/launcher/daemon/server/IncomingConnectionHandler
instanceKlass org/gradle/launcher/daemon/server/api/DaemonStateControl
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$TypeInfo
instanceKlass java/util/TreeMap$PrivateEntryIterator
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$TestExecutionRequestActionSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$ClientProvidedPhasedActionSerializer
instanceKlass org/gradle/tooling/internal/provider/serialization/SerializedPayloadSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$ClientProvidedBuildActionSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$BuildEventSubscriptionsSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$BuildModelActionSerializer
instanceKlass org/gradle/tooling/internal/provider/action/SubscribableBuildAction
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$ValueSerializer
instanceKlass org/gradle/internal/serialize/AbstractSerializer
instanceKlass org/gradle/internal/serialize/BaseSerializerFactory
instanceKlass org/gradle/internal/serialize/AbstractCollectionSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$NullableFileSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$StartParameterSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$ExecuteBuildActionSerializer
instanceKlass org/gradle/tooling/internal/provider/action/ExecuteBuildAction
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$HierarchySerializerMatcher
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$StrictSerializerMatcher
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$SerializerClassMatcherStrategy
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$1
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry
instanceKlass org/gradle/internal/serialize/SerializerRegistry
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer
instanceKlass org/gradle/initialization/BuildRequestContext
instanceKlass org/gradle/launcher/daemon/server/exec/WatchForDisconnection
instanceKlass org/gradle/launcher/daemon/server/exec/ResetDeprecationLogger
instanceKlass org/gradle/launcher/daemon/server/exec/RequestStopIfSingleUsedDaemon
instanceKlass org/gradle/launcher/daemon/server/api/StdinHandler
instanceKlass org/gradle/launcher/daemon/server/exec/ForwardClientInput
instanceKlass org/gradle/launcher/daemon/server/health/HealthLogger
instanceKlass org/gradle/launcher/daemon/server/exec/LogAndCheckHealth
instanceKlass org/gradle/launcher/daemon/server/exec/BuildCommandOnly
instanceKlass org/gradle/launcher/daemon/server/exec/ReturnResult
instanceKlass org/gradle/launcher/daemon/server/api/HandleReportStatus
instanceKlass org/gradle/launcher/daemon/server/exec/HandleCancel
instanceKlass org/gradle/launcher/daemon/server/api/HandleInvalidateVirtualFileSystem
instanceKlass org/gradle/launcher/daemon/protocol/Message
instanceKlass org/gradle/launcher/daemon/server/api/HandleStop
instanceKlass org/gradle/launcher/daemon/diagnostics/DaemonDiagnostics
instanceKlass org/gradle/tooling/internal/provider/BuildSessionLifecycleBuildActionExecuter
instanceKlass org/gradle/tooling/internal/provider/GradleThreadBuildActionExecuter
instanceKlass org/gradle/tooling/internal/provider/StartParamsValidatingActionExecuter
instanceKlass org/gradle/initialization/BuildRequestMetaData
instanceKlass org/gradle/initialization/exception/ExceptionAnalyser
instanceKlass org/gradle/initialization/exception/ExceptionCollector
instanceKlass org/gradle/tooling/internal/provider/SessionFailureReportingActionExecuter
instanceKlass org/gradle/StartParameter
instanceKlass org/gradle/concurrent/ParallelismConfiguration
instanceKlass org/gradle/tooling/internal/provider/SetupLoggingActionExecuter
instanceKlass org/gradle/initialization/ClassLoaderScopeRegistry
instanceKlass org/gradle/internal/classpath/CachedClasspathTransformer
instanceKlass org/gradle/cache/internal/FileContentCacheFactory
instanceKlass org/gradle/process/internal/worker/WorkerProcessFactory
instanceKlass org/gradle/groovy/scripts/internal/ScriptSourceHasher
instanceKlass org/gradle/internal/classloader/HashingClassLoaderFactory
instanceKlass org/gradle/cache/GlobalCacheLocations
instanceKlass org/gradle/internal/file/FileAccessTimeJournal
instanceKlass org/gradle/internal/execution/timeout/TimeoutHandler
instanceKlass org/gradle/internal/classpath/ClasspathTransformerCacheFactory
instanceKlass org/gradle/internal/hash/ClassLoaderHierarchyHasher
instanceKlass org/gradle/api/internal/initialization/loadercache/ClassLoaderCache
instanceKlass org/gradle/internal/service/scopes/WorkerSharedUserHomeScopeServices
instanceKlass org/gradle/internal/service/scopes/DefaultGradleUserHomeScopeServiceRegistry
instanceKlass org/gradle/internal/logging/text/AbstractStyledTextOutputFactory
instanceKlass org/gradle/launcher/daemon/server/expiry/DaemonExpirationResult
instanceKlass org/gradle/internal/event/DefaultListenerManager$EventBroadcast
instanceKlass org/gradle/internal/service/scopes/EventScope
instanceKlass org/gradle/launcher/daemon/server/expiry/DaemonExpirationListener
instanceKlass com/google/common/collect/ObjectArrays
instanceKlass org/gradle/launcher/daemon/server/health/LowNonHeapDaemonExpirationStrategy
instanceKlass org/gradle/launcher/daemon/server/health/LowHeapSpaceDaemonExpirationStrategy
instanceKlass org/gradle/launcher/daemon/server/health/GcThrashingDaemonExpirationStrategy
instanceKlass org/gradle/launcher/daemon/server/expiry/AnyDaemonExpirationStrategy
instanceKlass java/lang/FunctionalInterface
instanceKlass jdk/internal/reflect/ClassDefiner$1
instanceKlass jdk/internal/reflect/ClassDefiner
instanceKlass jdk/internal/reflect/MethodAccessorGenerator$1
instanceKlass jdk/internal/reflect/Label$PatchInfo
instanceKlass jdk/internal/reflect/Label
instanceKlass jdk/internal/reflect/UTF8
instanceKlass jdk/internal/reflect/ClassFileAssembler
instanceKlass jdk/internal/reflect/ByteVectorImpl
instanceKlass jdk/internal/reflect/ByteVector
instanceKlass jdk/internal/reflect/ByteVectorFactory
instanceKlass jdk/internal/reflect/AccessorGenerator
instanceKlass jdk/internal/reflect/ClassFileConstants
instanceKlass java/lang/annotation/Documented
instanceKlass org/gradle/api/Incubating
instanceKlass kotlin/annotation/Target
instanceKlass kotlin/annotation/Retention
instanceKlass kotlin/Metadata
instanceKlass org/gradle/internal/service/scopes/Scopes$BuildTree
instanceKlass org/gradle/internal/service/scopes/Scopes$BuildSession
instanceKlass org/gradle/internal/service/scopes/Scopes$UserHome
instanceKlass java/lang/annotation/Target
instanceKlass sun/reflect/annotation/AnnotationInvocationHandler
instanceKlass sun/reflect/annotation/AnnotationParser$1
instanceKlass sun/reflect/annotation/ExceptionProxy
instanceKlass java/lang/annotation/Inherited
instanceKlass java/lang/annotation/Retention
instanceKlass sun/reflect/annotation/AnnotationType$1
instanceKlass sun/reflect/annotation/AnnotationType
instanceKlass java/lang/reflect/GenericArrayType
instanceKlass org/gradle/internal/service/scopes/ServiceScope
instanceKlass java/time/Duration
instanceKlass java/time/temporal/TemporalAmount
instanceKlass java/time/temporal/TemporalUnit
instanceKlass java/util/concurrent/TimeUnit$1
instanceKlass java/lang/Class$AnnotationData
instanceKlass org/gradle/internal/service/scopes/StatefulListener
instanceKlass org/gradle/internal/concurrent/GradleThread
instanceKlass org/gradle/internal/service/scopes/Scope$Global
instanceKlass org/gradle/internal/service/scopes/Scope
instanceKlass org/gradle/internal/concurrent/ThreadFactoryImpl$ManagedThreadRunnable
instanceKlass java/util/concurrent/Executors$RunnableAdapter
instanceKlass java/util/concurrent/Executors
instanceKlass java/util/concurrent/FutureTask$WaitNode
instanceKlass java/util/concurrent/FutureTask
instanceKlass org/gradle/internal/concurrent/ManagedExecutorImpl$1
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectionCheck
instanceKlass org/gradle/launcher/daemon/server/health/gc/DefaultGarbageCollectionMonitor$1
instanceKlass java/util/concurrent/BlockingDeque
instanceKlass org/gradle/launcher/daemon/server/health/gc/DefaultSlidingWindow
instanceKlass org/gradle/launcher/daemon/server/health/gc/SlidingWindow
instanceKlass org/gradle/launcher/daemon/server/health/gc/DefaultGarbageCollectionMonitor
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectionInfo
instanceKlass org/gradle/internal/concurrent/ExecutorPolicy$CatchAndRecordFailures
instanceKlass java/util/concurrent/RunnableScheduledFuture
instanceKlass java/util/concurrent/ScheduledFuture
instanceKlass java/util/concurrent/Delayed
instanceKlass java/util/concurrent/RunnableFuture
instanceKlass java/util/concurrent/Future
instanceKlass org/gradle/internal/concurrent/ThreadFactoryImpl
instanceKlass java/util/concurrent/ThreadPoolExecutor$AbortPolicy
instanceKlass java/util/concurrent/RejectedExecutionHandler
instanceKlass java/util/concurrent/AbstractExecutorService
instanceKlass org/gradle/internal/concurrent/ManagedScheduledExecutor
instanceKlass java/util/concurrent/ScheduledExecutorService
instanceKlass org/gradle/internal/concurrent/ManagedExecutor
instanceKlass java/util/concurrent/ExecutorService
instanceKlass java/util/concurrent/Executor
instanceKlass org/gradle/internal/concurrent/AsyncStoppable
instanceKlass org/gradle/internal/concurrent/ExecutorPolicy
instanceKlass org/gradle/internal/concurrent/DefaultExecutorFactory
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectorMonitoringStrategy$3
instanceKlass sun/management/Sensor
instanceKlass sun/management/MemoryPoolImpl
instanceKlass java/lang/management/MemoryPoolMXBean
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectorMonitoringStrategy$2
instanceKlass org/gradle/util/internal/CollectionUtils
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectorMonitoringStrategy$1
instanceKlass sun/management/spi/PlatformMBeanProvider$PlatformComponent$$Lambda$67
instanceKlass sun/management/spi/PlatformMBeanProvider$PlatformComponent$$Lambda$66
instanceKlass com/sun/jmx/mbeanserver/Util
instanceKlass javax/management/ObjectName$Property
instanceKlass com/sun/jmx/mbeanserver/GetPropertyAction
instanceKlass javax/management/ObjectName
instanceKlass javax/management/QueryExp
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass sun/management/Util
instanceKlass com/sun/management/GarbageCollectorMXBean
instanceKlass java/lang/management/MemoryMXBean
instanceKlass java/util/stream/Collectors$$Lambda$65
instanceKlass java/util/stream/Collectors$$Lambda$64
instanceKlass java/util/stream/Collectors$$Lambda$63
instanceKlass java/lang/management/ManagementFactory$$Lambda$62
instanceKlass java/lang/management/ManagementFactory$PlatformMBeanFinder$$Lambda$61
instanceKlass java/util/Collections$2
instanceKlass jdk/management/jfr/internal/FlightRecorderMXBeanProvider$SingleMBeanComponent
instanceKlass jdk/management/jfr/FlightRecorderMXBean
instanceKlass java/util/HashMap$HashMapSpliterator
instanceKlass java/lang/management/ManagementFactory$PlatformMBeanFinder$$Lambda$60
instanceKlass java/lang/management/ManagementFactory$PlatformMBeanFinder$$Lambda$59
instanceKlass java/util/stream/Collectors$$Lambda$58
instanceKlass java/util/stream/Collectors$$Lambda$57
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/util/stream/Collectors$$Lambda$56
instanceKlass java/lang/management/ManagementFactory$PlatformMBeanFinder$$Lambda$55
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/util/function/Function$$Lambda$54
instanceKlass java/lang/management/ManagementFactory$PlatformMBeanFinder$$Lambda$53
instanceKlass java/lang/management/ManagementFactory$PlatformMBeanFinder$$Lambda$52
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$11
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$10
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$9
instanceKlass sun/management/ManagementFactoryHelper$LoggingMXBeanAccess$1
instanceKlass sun/management/ManagementFactoryHelper$LoggingMXBeanAccess
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$8
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$7
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$6
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$5
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$4
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$3
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$2
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$1
instanceKlass java/util/concurrent/Callable
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$5
instanceKlass sun/management/VMManagementImpl
instanceKlass sun/management/VMManagement
instanceKlass sun/management/ManagementFactoryHelper
instanceKlass sun/management/NotificationEmitterSupport
instanceKlass javax/management/NotificationEmitter
instanceKlass javax/management/NotificationBroadcaster
instanceKlass com/sun/management/DiagnosticCommandMBean
instanceKlass javax/management/DynamicMBean
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$4
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$3
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$2
instanceKlass java/util/stream/ReduceOps$Box
instanceKlass java/util/stream/ReduceOps$AccumulatingSink
instanceKlass java/util/stream/ReduceOps$ReduceOp
instanceKlass java/util/stream/ReduceOps
instanceKlass java/util/stream/Collectors$$Lambda$51
instanceKlass java/util/stream/Collectors$$Lambda$50
instanceKlass java/util/function/BinaryOperator
instanceKlass java/util/stream/Collectors$$Lambda$49
instanceKlass java/util/function/BiConsumer
instanceKlass java/util/stream/Collectors$$Lambda$48
instanceKlass java/util/stream/Collectors$CollectorImpl
instanceKlass java/util/stream/Collector
instanceKlass java/util/stream/Collectors
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$1
instanceKlass sun/management/spi/PlatformMBeanProvider$PlatformComponent
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$$Lambda$47
instanceKlass java/lang/management/ManagementFactory$PlatformMBeanFinder$$Lambda$46
instanceKlass sun/management/spi/PlatformMBeanProvider
instanceKlass java/lang/management/ManagementFactory$PlatformMBeanFinder$$Lambda$45
instanceKlass java/lang/management/ManagementFactory$PlatformMBeanFinder
instanceKlass java/lang/management/GarbageCollectorMXBean
instanceKlass java/lang/management/MemoryManagerMXBean
instanceKlass java/lang/management/PlatformManagedObject
instanceKlass java/lang/management/ManagementFactory$$Lambda$44
instanceKlass java/lang/management/ManagementFactory
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectionMonitor
instanceKlass org/gradle/internal/time/DefaultTimer
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$StateContext
instanceKlass java/text/DontCareFieldPosition$1
instanceKlass java/text/Format$FieldDelegate
instanceKlass java/util/Date
instanceKlass java/text/DigitList
instanceKlass java/text/FieldPosition
instanceKlass sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda$43
instanceKlass sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda$42
instanceKlass java/text/DateFormatSymbols
instanceKlass sun/util/calendar/CalendarUtils
instanceKlass sun/util/calendar/CalendarDate
instanceKlass sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda$41
instanceKlass sun/util/locale/provider/CalendarDataUtility$CalendarWeekParameterGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool$LocalizedObjectGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool
instanceKlass sun/util/calendar/CalendarSystem
instanceKlass java/util/Calendar$Builder
instanceKlass sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda$40
instanceKlass sun/util/calendar/ZoneInfoFile$ZoneOffsetTransitionRule
instanceKlass sun/util/calendar/ZoneInfoFile$1
instanceKlass sun/util/calendar/ZoneInfoFile
instanceKlass java/util/TimeZone
instanceKlass java/util/Calendar
instanceKlass java/text/AttributedCharacterIterator$Attribute
instanceKlass java/text/Format
instanceKlass org/gradle/internal/logging/sink/LogEventDispatcher
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$4
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$3
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$2
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$1
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$State
instanceKlass org/gradle/internal/logging/text/StreamBackedStandardOutputListener
instanceKlass org/gradle/internal/logging/text/AbstractStyledTextOutput
instanceKlass org/gradle/internal/logging/console/StyledTextOutputBackedRenderer
instanceKlass org/slf4j/helpers/FormattingTuple
instanceKlass org/slf4j/helpers/MessageFormatter
instanceKlass net/rubygrapefruit/platform/internal/FunctionResult
instanceKlass org/gradle/internal/logging/source/PrintStreamLoggingSystem$PrintStreamDestination
instanceKlass java/util/logging/ErrorManager
instanceKlass org/gradle/internal/logging/source/JavaUtilLoggingSystem$SnapshotImpl
instanceKlass org/gradle/internal/logging/config/LoggingSystemAdapter$SnapshotImpl
instanceKlass org/gradle/internal/dispatch/MethodInvocation
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer$SnapshotImpl
instanceKlass org/gradle/process/internal/shutdown/ShutdownHooks
instanceKlass org/gradle/launcher/daemon/bootstrap/DaemonMain$1
instanceKlass com/google/common/io/Files$2
instanceKlass com/google/common/io/ByteSink
instanceKlass com/google/common/io/ByteSource
instanceKlass com/google/common/io/LineProcessor
instanceKlass com/google/common/base/Predicate
instanceKlass com/google/common/graph/SuccessorsFunction
instanceKlass com/google/common/io/Files
instanceKlass org/gradle/util/internal/GFileUtils
instanceKlass java/util/regex/CharPredicates$$Lambda$39
instanceKlass org/gradle/util/GradleVersion
instanceKlass org/gradle/launcher/daemon/context/DefaultDaemonContext$Serializer
instanceKlass org/gradle/launcher/daemon/context/DefaultDaemonContext
instanceKlass net/rubygrapefruit/platform/internal/jni/PosixProcessFunctions
instanceKlass org/gradle/internal/FileUtils$1
instanceKlass org/gradle/internal/FileUtils
instanceKlass com/google/common/collect/Lists
instanceKlass org/gradle/internal/nativeintegration/ReflectiveEnvironment
instanceKlass org/gradle/internal/nativeintegration/processenvironment/AbstractProcessEnvironment
instanceKlass net/rubygrapefruit/platform/internal/DefaultProcess
instanceKlass net/rubygrapefruit/platform/internal/WrapperProcess
instanceKlass net/rubygrapefruit/platform/file/WindowsFiles
instanceKlass org/gradle/launcher/daemon/context/DaemonContextBuilder
instanceKlass org/gradle/internal/id/UUIDGenerator
instanceKlass org/gradle/internal/remote/internal/OutgoingConnector
instanceKlass org/gradle/internal/remote/internal/IncomingConnector
instanceKlass org/gradle/internal/remote/MessagingServer
instanceKlass org/gradle/internal/remote/MessagingClient
instanceKlass org/gradle/internal/id/IdGenerator
instanceKlass org/gradle/internal/remote/services/MessagingServices
instanceKlass org/gradle/api/internal/file/DefaultFileLookup
instanceKlass org/gradle/internal/build/event/OperationResultPostProcessor
instanceKlass org/gradle/tooling/internal/provider/runner/OperationDependencyLookup
instanceKlass org/gradle/tooling/internal/provider/runner/ToolingApiBuildEventListenerFactory
instanceKlass org/gradle/configurationcache/serialization/beans/BeanConstructors
instanceKlass org/gradle/nativeplatform/NativeBinarySpec
instanceKlass org/gradle/platform/base/BinarySpec
instanceKlass org/gradle/platform/base/Binary
instanceKlass org/gradle/api/CheckableComponentSpec
instanceKlass org/gradle/api/BuildableComponentSpec
instanceKlass org/gradle/platform/base/ComponentSpec
instanceKlass org/gradle/model/ModelElement
instanceKlass org/gradle/api/Buildable
instanceKlass org/gradle/internal/resource/transport/sftp/SftpClientFactory
instanceKlass org/gradle/internal/resource/transport/sftp/SftpResourcesPluginServiceRegistry$GlobalScopeServices
instanceKlass org/gradle/internal/resource/transport/aws/s3/S3ResourcesPluginServiceRegistry$GlobalScopeServices
instanceKlass org/gradle/internal/resource/transport/gcp/gcs/GcsResourcesPluginServiceRegistry$GlobalScopeServices
instanceKlass org/gradle/nativeplatform/TargetMachineBuilder
instanceKlass org/gradle/nativeplatform/TargetMachine
instanceKlass org/gradle/nativeplatform/internal/DefaultTargetMachineFactory
instanceKlass org/gradle/nativeplatform/TargetMachineFactory
instanceKlass org/gradle/nativeplatform/internal/NativePlatformResolver
instanceKlass org/gradle/platform/base/internal/PlatformResolver
instanceKlass org/gradle/nativeplatform/platform/internal/NativePlatformInternal
instanceKlass org/gradle/nativeplatform/platform/NativePlatform
instanceKlass org/gradle/platform/base/Platform
instanceKlass org/gradle/nativeplatform/platform/internal/OperatingSystemInternal
instanceKlass org/gradle/nativeplatform/platform/OperatingSystem
instanceKlass org/gradle/api/Named
instanceKlass org/gradle/nativeplatform/platform/internal/NativePlatforms
instanceKlass org/gradle/internal/logging/text/DiagnosticsVisitor
instanceKlass org/gradle/api/component/SoftwareComponentFactory
instanceKlass org/gradle/api/plugins/internal/PluginAuthorServices$GlobalScopeServices
instanceKlass org/gradle/internal/build/event/BuildEventSubscriptions
instanceKlass org/gradle/internal/build/event/OperationResultPostProcessorFactory
instanceKlass org/gradle/language/java/internal/JavaLanguagePluginServiceRegistry$JavaGlobalScopeServices
instanceKlass org/gradle/platform/base/internal/registry/ComponentModelBaseServiceRegistry$GlobalScopeServices
instanceKlass org/gradle/reporting/ReportRenderer
instanceKlass org/gradle/api/reporting/components/internal/DiagnosticsServices$1
instanceKlass org/gradle/internal/resource/transport/http/HttpClientHelper$Factory
instanceKlass org/gradle/internal/resource/transport/http/SslContextFactory
instanceKlass org/gradle/internal/resource/transport/http/HttpResourcesPluginServiceRegistry$GlobalScopeServices
instanceKlass org/gradle/api/artifacts/component/ComponentSelector
instanceKlass org/gradle/internal/resource/ExternalResourceName
instanceKlass org/gradle/api/Describable
instanceKlass org/gradle/api/internal/artifacts/transform/ArtifactTransformParameterScheme
instanceKlass org/gradle/api/internal/artifacts/transform/ArtifactTransformActionScheme
instanceKlass org/gradle/api/internal/tasks/properties/annotations/AbstractInputFilePropertyAnnotationHandler
instanceKlass org/gradle/internal/typeconversion/NotationParser
instanceKlass org/gradle/api/internal/attributes/EmptySchema
instanceKlass org/gradle/api/internal/attributes/AttributesSchemaInternal
instanceKlass org/gradle/api/internal/attributes/DescribableAttributesSchema
instanceKlass org/gradle/api/attributes/AttributesSchema
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/PlatformSupport
instanceKlass org/gradle/cache/internal/ProducerGuard
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/AbstractIvyDependencyDescriptorFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/IvyDependencyDescriptorFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/LocalComponentMetadataBuilder
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/LocalConfigurationMetadataBuilder
instanceKlass org/gradle/internal/typeconversion/NotationConverter
instanceKlass org/gradle/internal/resource/connector/ResourceConnectorFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/ExcludeRuleConverter
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/DependencyDescriptorFactory
instanceKlass org/gradle/internal/resource/local/FileResourceRepository
instanceKlass org/gradle/internal/resource/ExternalResourceRepository
instanceKlass org/gradle/api/internal/artifacts/ivyservice/IvyContextManager
instanceKlass org/gradle/api/internal/artifacts/ImmutableModuleIdentifierFactory
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementGlobalScopeServices
instanceKlass org/gradle/internal/operations/BuildOperationAncestryTracker
instanceKlass org/gradle/internal/build/event/BuildEventServices$1
instanceKlass org/gradle/internal/build/event/BuildEventListenerFactory
instanceKlass org/gradle/internal/operations/BuildOperationListener
instanceKlass org/gradle/initialization/BuildEventConsumer
instanceKlass org/gradle/internal/build/event/DefaultBuildEventsListenerRegistry
instanceKlass org/gradle/internal/build/event/BuildEventListenerRegistryInternal
instanceKlass org/gradle/build/event/BuildEventsListenerRegistry
instanceKlass org/gradle/kotlin/dsl/support/ImplicitImports
instanceKlass org/gradle/kotlin/dsl/support/GlobalServices
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotterStatistics$Collector
instanceKlass org/gradle/api/internal/changedetection/state/FileHasherStatistics$Collector
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$GlobalScopeServices
instanceKlass org/gradle/internal/service/scopes/ExecutionGlobalServices$AnnotationHandlerRegistration
instanceKlass org/gradle/api/internal/tasks/properties/PropertyWalker
instanceKlass org/gradle/api/internal/tasks/properties/TaskScheme
instanceKlass org/gradle/api/internal/tasks/properties/TypeScheme
instanceKlass org/gradle/internal/service/scopes/ExecutionGlobalServices$AnnotationHandlerRegistar
instanceKlass org/gradle/api/internal/tasks/properties/InspectionSchemeFactory
instanceKlass org/gradle/api/model/ReplacedBy
instanceKlass org/gradle/api/tasks/Internal
instanceKlass org/gradle/api/tasks/OutputFiles
instanceKlass org/gradle/api/tasks/OutputFile
instanceKlass org/gradle/api/tasks/OutputDirectory
instanceKlass org/gradle/api/tasks/OutputDirectories
instanceKlass org/gradle/api/tasks/options/OptionValues
instanceKlass org/gradle/api/tasks/Nested
instanceKlass org/gradle/api/tasks/LocalState
instanceKlass org/gradle/api/tasks/InputFiles
instanceKlass org/gradle/api/tasks/InputFile
instanceKlass org/gradle/api/tasks/InputDirectory
instanceKlass org/gradle/api/artifacts/transform/InputArtifactDependencies
instanceKlass org/gradle/api/artifacts/transform/InputArtifact
instanceKlass org/gradle/api/tasks/Input
instanceKlass org/gradle/api/tasks/Destroys
instanceKlass org/gradle/api/tasks/Console
instanceKlass org/gradle/api/internal/project/taskfactory/TaskClassInfoStore
instanceKlass org/gradle/internal/reflect/annotations/TypeAnnotationMetadataStore
instanceKlass org/gradle/api/internal/tasks/properties/annotations/TypeAnnotationHandler
instanceKlass org/gradle/internal/service/scopes/ExecutionGlobalServices
instanceKlass org/gradle/tooling/internal/provider/ExecuteBuildActionRunner
instanceKlass org/gradle/internal/buildtree/BuildActionRunner
instanceKlass org/gradle/tooling/internal/provider/serialization/ClassLoaderCache
instanceKlass org/gradle/internal/filewatch/FileSystemChangeWaiterFactory
instanceKlass org/gradle/tooling/internal/provider/LauncherServices$ToolingGlobalScopeServices
instanceKlass org/gradle/internal/service/DefaultServiceLocator$ServiceFactory
instanceKlass org/gradle/internal/service/scopes/AbstractPluginServiceRegistry
instanceKlass org/gradle/internal/service/scopes/PluginServiceRegistry
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry$DefaultModule
instanceKlass org/gradle/internal/IoActions
instanceKlass org/gradle/api/Transformer
instanceKlass org/gradle/util/internal/GUtil
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry$$Lambda$38
instanceKlass java/util/NavigableSet
instanceKlass java/util/SortedSet
instanceKlass com/google/common/collect/Sets
instanceKlass groovy/lang/MetaClass
instanceKlass groovy/lang/MetaObjectProtocol
instanceKlass groovy/lang/GroovySystem
instanceKlass groovy/lang/MetaClassRegistry
instanceKlass groovy/lang/GroovyObject
instanceKlass org/objectweb/asm/ClassVisitor
instanceKlass java/util/ComparableTimSort
instanceKlass org/gradle/internal/util/Trie$Builder
instanceKlass org/gradle/internal/util/Trie
instanceKlass org/gradle/internal/classloader/FilteringClassLoader$TrieSet
instanceKlass java/lang/ClassLoader$$Lambda$37
instanceKlass jdk/internal/loader/BootLoader$PackageHelper$$Lambda$36
instanceKlass jdk/internal/loader/BootLoader$PackageHelper
instanceKlass java/util/stream/StreamSpliterators$WrappingSpliterator$$Lambda$35
instanceKlass java/util/stream/AbstractSpinedBuffer
instanceKlass java/util/stream/Node$Builder
instanceKlass java/util/stream/Node$OfDouble
instanceKlass java/util/stream/Node$OfLong
instanceKlass java/util/stream/Node$OfInt
instanceKlass java/util/stream/Node$OfPrimitive
instanceKlass java/util/stream/Nodes$EmptyNode
instanceKlass java/util/stream/Node
instanceKlass java/util/stream/Nodes
instanceKlass java/lang/ClassLoader$$Lambda$34
instanceKlass java/util/function/IntFunction
instanceKlass jdk/internal/loader/BootLoader$$Lambda$33
instanceKlass java/util/stream/Streams$2
instanceKlass java/util/stream/StreamSpliterators$AbstractWrappingSpliterator
instanceKlass java/util/stream/AbstractPipeline$$Lambda$32
instanceKlass java/util/stream/Streams$ConcatSpliterator
instanceKlass java/lang/ClassLoader$$Lambda$31
instanceKlass org/gradle/internal/classloader/ClassLoaderUtils$AbstractClassLoaderLookuper
instanceKlass org/gradle/internal/classloader/ClassLoaderUtils$ClassLoaderPackagesFetcher
instanceKlass org/gradle/internal/classloader/ClassLoaderUtils$ClassDefiner
instanceKlass org/gradle/internal/classloader/ClassLoaderUtils
instanceKlass org/gradle/initialization/GradleApiSpecAggregator$DefaultSpec
instanceKlass kotlin/jvm/internal/Intrinsics
instanceKlass kotlin/collections/SetsKt__SetsJVMKt
instanceKlass com/google/common/collect/PeekingIterator
instanceKlass com/google/common/collect/UnmodifiableIterator
instanceKlass com/google/common/collect/Iterators
instanceKlass com/google/common/collect/Hashing
instanceKlass com/google/common/base/Java8Usage$$Lambda$30
instanceKlass com/google/common/base/Java8Usage
instanceKlass com/google/common/base/Preconditions
instanceKlass org/apache/groovy/json/DefaultFastStringServiceFactory
instanceKlass org/apache/groovy/json/FastStringServiceFactory
instanceKlass org/gradle/internal/reflect/ReflectionCache$CacheEntry
instanceKlass com/google/common/math/IntMath$1
instanceKlass com/google/common/math/MathPreconditions
instanceKlass com/google/common/math/IntMath
instanceKlass com/google/common/collect/ImmutableCollection$Builder
instanceKlass com/google/common/collect/ImmutableSet$SetBuilderImpl
instanceKlass org/gradle/kotlin/dsl/provider/KotlinGradleApiSpecProvider
instanceKlass org/gradle/initialization/GradleApiSpecProvider$SpecAdapter
instanceKlass org/gradle/initialization/GradleApiSpecProvider
instanceKlass org/gradle/internal/service/DefaultServiceLocator
instanceKlass org/gradle/initialization/GradleApiSpecProvider$Spec
instanceKlass org/gradle/initialization/GradleApiSpecAggregator
instanceKlass com/google/common/base/Function
instanceKlass org/gradle/internal/reflect/CachedInvokable
instanceKlass org/gradle/internal/reflect/ReflectionCache
instanceKlass org/gradle/internal/reflect/DirectInstantiator
instanceKlass org/gradle/initialization/DefaultClassLoaderRegistry
instanceKlass org/gradle/internal/installation/GradleRuntimeShadedJarDetector
instanceKlass sun/net/www/protocol/jar/URLJarFileCallBack
instanceKlass sun/net/www/protocol/jar/JarFileFactory
instanceKlass sun/net/www/protocol/jar/URLJarFile$URLJarFileCloseController
instanceKlass java/net/URLClassLoader$2
instanceKlass org/objectweb/asm/Type
instanceKlass org/gradle/initialization/DefaultLegacyTypesSupport
instanceKlass org/gradle/api/internal/DynamicModulesClassPathProvider
instanceKlass org/gradle/api/internal/DefaultClassPathProvider
instanceKlass org/gradle/api/internal/ClassPathProvider
instanceKlass org/gradle/api/internal/DefaultClassPathRegistry
instanceKlass org/gradle/api/internal/classpath/DefaultPluginModuleRegistry
instanceKlass org/gradle/api/internal/classpath/ManifestUtil
instanceKlass org/gradle/internal/classloader/ClassLoaderSpec
instanceKlass org/gradle/internal/classloader/ClassLoaderHierarchy
instanceKlass org/gradle/internal/classloader/ClassLoaderVisitor
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry$$Lambda$29
instanceKlass org/gradle/api/internal/classpath/Module
instanceKlass org/gradle/internal/installation/GradleInstallation$1
instanceKlass org/gradle/internal/installation/GradleInstallation
instanceKlass org/gradle/internal/classloader/ClasspathUtil
instanceKlass org/gradle/internal/installation/CurrentGradleInstallationLocator
instanceKlass org/gradle/model/internal/manage/schema/extract/ModelSchemaExtractionStrategy
instanceKlass org/gradle/api/tasks/util/PatternSet
instanceKlass org/gradle/api/tasks/util/PatternFilterable
instanceKlass org/gradle/api/tasks/AntBuilderAware
instanceKlass org/gradle/model/internal/inspect/MethodModelRuleExtractor
instanceKlass org/gradle/api/internal/tasks/properties/annotations/AbstractOutputPropertyAnnotationHandler
instanceKlass org/gradle/api/internal/tasks/properties/annotations/PropertyAnnotationHandler
instanceKlass org/gradle/internal/instantiation/InjectAnnotationHandler
instanceKlass org/gradle/model/internal/manage/schema/extract/ModelSchemaAspectExtractionStrategy
instanceKlass org/gradle/api/internal/file/FileLookup
instanceKlass org/gradle/cache/internal/locklistener/DefaultFileLockContentionHandler
instanceKlass org/gradle/internal/remote/internal/inet/InetAddressFactory
instanceKlass org/gradle/internal/event/DefaultListenerManager
instanceKlass org/gradle/api/internal/DocumentationRegistry
instanceKlass org/gradle/cache/internal/locklistener/FileLockContentionHandler
instanceKlass org/gradle/api/internal/file/DefaultFilePropertyFactory
instanceKlass org/gradle/api/internal/file/FileResolver
instanceKlass org/gradle/internal/file/PathToFileResolver
instanceKlass org/gradle/internal/file/RelativeFilePathResolver
instanceKlass org/gradle/internal/state/ManagedFactoryRegistry
instanceKlass org/gradle/api/internal/file/FileFactory
instanceKlass org/gradle/api/internal/tasks/TaskDependencyFactory
instanceKlass org/gradle/api/internal/provider/PropertyHost
instanceKlass org/gradle/internal/instantiation/InstanceGenerator
instanceKlass org/gradle/internal/operations/BuildOperationProgressEventEmitter
instanceKlass org/gradle/model/internal/inspect/ModelRuleSourceDetector
instanceKlass org/gradle/api/internal/cache/StringInterner
instanceKlass com/google/common/collect/Interner
instanceKlass org/gradle/api/internal/model/NamedObjectInstantiator
instanceKlass org/gradle/internal/state/ManagedFactory
instanceKlass org/gradle/api/internal/file/FilePropertyFactory
instanceKlass org/gradle/model/internal/inspect/ModelRuleExtractor
instanceKlass org/gradle/model/internal/manage/instance/ManagedProxyFactory
instanceKlass org/gradle/model/internal/manage/schema/extract/ModelSchemaAspectExtractor
instanceKlass org/gradle/internal/operations/CurrentBuildOperationRef
instanceKlass org/gradle/internal/service/CachingServiceLocator
instanceKlass org/gradle/initialization/layout/BuildLayoutFactory
instanceKlass org/gradle/execution/DefaultWorkValidationWarningRecorder
instanceKlass org/gradle/execution/WorkValidationWarningReporter
instanceKlass org/gradle/internal/execution/steps/ValidateStep$ValidationWarningRecorder
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry
instanceKlass org/gradle/cache/GlobalCache
instanceKlass org/gradle/internal/installation/CurrentGradleInstallation
instanceKlass org/gradle/api/internal/classpath/ModuleRegistry
instanceKlass org/gradle/internal/service/scopes/GlobalScopeServices$$Lambda$28
instanceKlass org/gradle/internal/environment/GradleBuildEnvironment
instanceKlass org/gradle/process/internal/health/memory/OsMemoryInfo
instanceKlass org/gradle/internal/service/scopes/GradleUserHomeScopeServiceRegistry
instanceKlass org/gradle/internal/operations/BuildOperationListenerManager
instanceKlass org/gradle/internal/resources/ResourceLockCoordinationService
instanceKlass org/gradle/api/internal/collections/DomainObjectCollectionFactory
instanceKlass org/gradle/model/internal/manage/binding/StructBindingsStore
instanceKlass org/gradle/model/internal/manage/schema/extract/ModelSchemaExtractor
instanceKlass org/gradle/configuration/ImportsReader
instanceKlass org/gradle/model/internal/manage/schema/ModelSchemaStore
instanceKlass org/gradle/api/model/ObjectFactory
instanceKlass org/gradle/internal/reflect/Instantiator
instanceKlass org/gradle/process/internal/health/memory/JvmMemoryInfo
instanceKlass org/gradle/internal/instantiation/InstantiatorFactory
instanceKlass org/gradle/internal/instantiation/PropertyRoleAnnotationHandler
instanceKlass org/gradle/internal/filewatch/FileWatcherFactory
instanceKlass org/gradle/cache/internal/InMemoryCacheDecoratorFactory
instanceKlass org/gradle/initialization/JdkToolsInitializer
instanceKlass org/gradle/internal/classloader/ClassLoaderFactory
instanceKlass org/gradle/internal/service/ServiceLocator
instanceKlass org/gradle/api/tasks/util/internal/PatternSpecFactory
instanceKlass org/gradle/api/execution/internal/TaskInputsListeners
instanceKlass org/gradle/api/internal/ClassPathRegistry
instanceKlass org/gradle/internal/execution/history/OverlappingOutputDetector
instanceKlass org/gradle/api/internal/classpath/PluginModuleRegistry
instanceKlass org/gradle/initialization/ClassLoaderRegistry
instanceKlass org/gradle/internal/execution/history/changes/ExecutionStateChangeDetector
instanceKlass org/gradle/process/internal/health/memory/MemoryManager
instanceKlass org/gradle/cache/internal/CacheFactory
instanceKlass org/gradle/internal/hash/StreamHasher
instanceKlass org/gradle/internal/file/Deleter
instanceKlass org/gradle/cache/internal/CrossBuildInMemoryCacheFactory
instanceKlass org/gradle/initialization/LegacyTypesSupport
instanceKlass org/gradle/internal/operations/BuildOperationIdFactory
instanceKlass org/gradle/api/internal/provider/PropertyFactory
instanceKlass org/gradle/internal/logging/progress/ProgressLoggerFactory
instanceKlass org/gradle/internal/logging/progress/ProgressListener
instanceKlass org/gradle/process/internal/ExecFactory
instanceKlass org/gradle/api/internal/ProcessOperations
instanceKlass org/gradle/process/internal/JavaForkOptionsFactory
instanceKlass org/gradle/process/internal/JavaExecHandleFactory
instanceKlass org/gradle/process/internal/ExecHandleFactory
instanceKlass org/gradle/process/internal/ExecActionFactory
instanceKlass org/gradle/internal/jvm/inspection/JvmVersionDetector
instanceKlass org/gradle/api/internal/file/FileCollectionFactory
instanceKlass org/gradle/api/internal/file/collections/DirectoryFileTreeFactory
instanceKlass org/gradle/cache/internal/ProcessMetaDataProvider
instanceKlass org/gradle/internal/jvm/inspection/JvmMetadataDetector
instanceKlass org/gradle/internal/service/scopes/BasicGlobalScopeServices
instanceKlass org/gradle/cache/FileLockManager
instanceKlass org/gradle/launcher/daemon/registry/DaemonDir
instanceKlass org/gradle/internal/concurrent/Synchronizer
instanceKlass org/gradle/cache/internal/CacheSupport
instanceKlass org/gradle/cache/internal/CacheAccessSerializer
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistry
instanceKlass org/gradle/cache/Cache
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistryServices
instanceKlass org/gradle/internal/invocation/BuildAction
instanceKlass sun/reflect/generics/tree/TypeVariableSignature
instanceKlass org/gradle/launcher/daemon/server/api/DaemonCommandAction
instanceKlass sun/reflect/generics/tree/MethodTypeSignature
instanceKlass org/gradle/internal/serialize/Serializer
instanceKlass org/gradle/launcher/daemon/server/MasterExpirationStrategy
instanceKlass org/gradle/internal/event/ListenerManager
instanceKlass org/gradle/launcher/daemon/server/Daemon
instanceKlass org/gradle/launcher/daemon/server/health/HealthExpirationStrategy
instanceKlass org/gradle/launcher/exec/BuildExecuter
instanceKlass org/gradle/launcher/daemon/server/health/DaemonHealthCheck
instanceKlass org/gradle/launcher/daemon/context/DaemonContext
instanceKlass org/gradle/launcher/daemon/server/health/DaemonMemoryStatus
instanceKlass org/gradle/launcher/daemon/server/health/DaemonHealthStats
instanceKlass org/gradle/internal/concurrent/ExecutorFactory
instanceKlass org/gradle/launcher/daemon/server/stats/DaemonRunningStats
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$CompositeServiceProvider
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$ParentServices
instanceKlass org/gradle/api/specs/Spec
instanceKlass org/gradle/internal/classpath/DefaultClassPath
instanceKlass org/gradle/internal/classpath/ClassPath
instanceKlass org/gradle/launcher/daemon/server/DaemonServerConnector
instanceKlass org/gradle/launcher/daemon/server/scaninfo/DaemonScanInfo
instanceKlass org/gradle/launcher/daemon/server/expiry/DaemonExpirationStrategy
instanceKlass org/gradle/launcher/exec/BuildActionExecuter
instanceKlass java/lang/reflect/WildcardType
instanceKlass sun/reflect/generics/reflectiveObjects/LazyReflectiveObjectGenerator
instanceKlass java/lang/reflect/TypeVariable
instanceKlass sun/reflect/generics/reflectiveObjects/ParameterizedTypeImpl
instanceKlass sun/reflect/generics/visitor/Reifier
instanceKlass sun/reflect/generics/visitor/TypeTreeVisitor
instanceKlass sun/reflect/generics/tree/ClassSignature
instanceKlass sun/reflect/generics/tree/Signature
instanceKlass sun/reflect/generics/tree/ClassTypeSignature
instanceKlass sun/reflect/generics/tree/SimpleClassTypeSignature
instanceKlass sun/reflect/generics/tree/FieldTypeSignature
instanceKlass sun/reflect/generics/tree/BaseType
instanceKlass sun/reflect/generics/tree/TypeSignature
instanceKlass sun/reflect/generics/tree/ReturnType
instanceKlass sun/reflect/generics/tree/TypeArgument
instanceKlass sun/reflect/generics/tree/FormalTypeParameter
instanceKlass sun/reflect/generics/tree/TypeTree
instanceKlass sun/reflect/generics/tree/Tree
instanceKlass sun/reflect/generics/parser/SignatureParser
instanceKlass sun/reflect/generics/repository/AbstractRepository
instanceKlass sun/reflect/generics/factory/CoreReflectionFactory
instanceKlass sun/reflect/generics/factory/GenericsFactory
instanceKlass sun/reflect/generics/scope/AbstractScope
instanceKlass sun/reflect/generics/scope/Scope
instanceKlass org/gradle/internal/logging/services/DefaultLoggingManager$StartableLoggingSystem
instanceKlass org/gradle/internal/logging/services/DefaultLoggingManager$StartableLoggingRouter
instanceKlass org/gradle/internal/logging/services/DefaultLoggingManager
instanceKlass org/gradle/internal/logging/source/JavaUtilLoggingSystem
instanceKlass org/gradle/internal/logging/slf4j/Slf4jLoggingConfigurer
instanceKlass org/gradle/internal/logging/config/LoggingSystemAdapter
instanceKlass org/gradle/internal/logging/LoggingManagerInternal
instanceKlass org/gradle/internal/logging/StandardOutputCapture
instanceKlass org/gradle/api/logging/LoggingManager
instanceKlass org/gradle/internal/logging/source/StdErrLoggingSystem
instanceKlass org/gradle/internal/logging/source/PrintStreamLoggingSystem$SnapshotImpl
instanceKlass org/gradle/internal/logging/source/PrintStreamLoggingSystem$OutputEventDestination
instanceKlass org/gradle/internal/SystemProperties
instanceKlass org/gradle/internal/logging/source/PrintStreamLoggingSystem$1
instanceKlass org/gradle/internal/service/AnnotatedServiceLifecycleHandler
instanceKlass org/gradle/internal/logging/events/operations/StyledTextBuildOperationProgressDetails
instanceKlass org/gradle/internal/io/TextStream
instanceKlass org/gradle/internal/logging/source/PrintStreamLoggingSystem
instanceKlass org/gradle/internal/logging/source/StdOutLoggingSystem
instanceKlass java/lang/reflect/ParameterizedType
instanceKlass org/gradle/internal/logging/sink/OutputEventListenerManager$1
instanceKlass org/gradle/internal/logging/sink/OutputEventListenerManager
instanceKlass org/gradle/internal/logging/services/DefaultLoggingManagerFactory
instanceKlass org/gradle/internal/logging/services/TextStreamOutputEventListener
instanceKlass org/gradle/internal/logging/services/LoggingServiceRegistry$1
instanceKlass org/gradle/internal/logging/config/LoggingConfigurer
instanceKlass org/gradle/internal/logging/config/LoggingSourceSystem
instanceKlass org/gradle/launcher/daemon/configuration/DefaultDaemonServerConfiguration
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiStorage
instanceKlass org/fusesource/jansi/Ansi
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiLibrary
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiLibraryFactory$1
instanceKlass net/rubygrapefruit/platform/internal/jni/AbstractFileEventFunctions
instanceKlass jdk/internal/logger/DefaultLoggerFinder$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper
instanceKlass java/util/logging/LogManager$4
instanceKlass jdk/internal/logger/BootstrapLogger$BootstrapExecutors
instanceKlass jdk/internal/logger/BootstrapLogger$RedirectedLoggers
instanceKlass java/lang/ModuleLayer$$Lambda$27
instanceKlass java/util/Spliterators$1Adapter
instanceKlass java/util/Spliterators$ArraySpliterator
instanceKlass java/util/Spliterator$OfDouble
instanceKlass java/util/Spliterator$OfLong
instanceKlass java/util/Spliterator$OfInt
instanceKlass java/util/Spliterator$OfPrimitive
instanceKlass java/util/Spliterators$EmptySpliterator
instanceKlass java/util/Spliterators
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend$1
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend
instanceKlass jdk/internal/logger/BootstrapLogger
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge
instanceKlass sun/util/logging/PlatformLogger$Bridge
instanceKlass java/lang/System$Logger
instanceKlass java/util/stream/Streams
instanceKlass java/util/stream/Streams$AbstractStreamBuilderImpl
instanceKlass java/util/stream/Stream$Builder
instanceKlass java/util/stream/Sink$ChainedReference
instanceKlass java/util/stream/FindOps$FindSink$OfRef$$Lambda$26
instanceKlass java/util/stream/FindOps$FindSink$OfRef$$Lambda$25
instanceKlass java/util/stream/FindOps$FindSink$OfRef$$Lambda$24
instanceKlass java/util/stream/FindOps$FindSink$OfRef$$Lambda$23
instanceKlass java/util/stream/FindOps$FindOp
instanceKlass java/util/stream/TerminalOp
instanceKlass java/util/stream/FindOps$FindSink
instanceKlass java/util/stream/TerminalSink
instanceKlass java/util/stream/Sink
instanceKlass java/util/function/Consumer
instanceKlass java/util/stream/FindOps
instanceKlass java/util/logging/Level$KnownLevel$$Lambda$22
instanceKlass sun/reflect/annotation/AnnotationParser
instanceKlass java/util/EnumMap$1
instanceKlass java/util/stream/StreamOpFlag$MaskBuilder
instanceKlass java/util/stream/PipelineHelper
instanceKlass java/util/stream/Stream
instanceKlass java/util/stream/BaseStream
instanceKlass java/util/stream/StreamSupport
instanceKlass java/util/ArrayList$ArrayListSpliterator
instanceKlass java/util/Spliterator
instanceKlass java/util/logging/Level$$Lambda$21
instanceKlass java/util/Hashtable$Enumerator
instanceKlass java/util/Collections$3
instanceKlass java/util/logging/LogManager$LoggerContext$1
instanceKlass java/util/logging/LogManager$VisitedLoggers
instanceKlass java/util/function/Predicate
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass sun/invoke/util/ValueConversions$WrapperCache
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/MethodHandles$1
instanceKlass java/lang/Byte$ByteCache
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/StringConcatFactory$Stringifiers
instanceKlass java/lang/StringConcatHelper
instanceKlass java/lang/invoke/StringConcatFactory$MethodHandleInlineCopyStrategy$3
instanceKlass java/lang/invoke/StringConcatFactory$MethodHandleInlineCopyStrategy$2
instanceKlass java/lang/invoke/StringConcatFactory$MethodHandleInlineCopyStrategy$1
instanceKlass java/lang/invoke/StringConcatFactory$MethodHandleInlineCopyStrategy
instanceKlass java/lang/invoke/StringConcatFactory$RecipeElement
instanceKlass java/lang/invoke/StringConcatFactory$Recipe
instanceKlass java/lang/invoke/StringConcatFactory$1
instanceKlass java/lang/invoke/StringConcatFactory
instanceKlass java/util/logging/LogManager$2
instanceKlass java/lang/System$LoggerFinder
instanceKlass sun/security/util/SecurityConstants
instanceKlass java/security/AccessController$1
instanceKlass java/util/logging/LogManager$LoggingProviderAccess
instanceKlass sun/util/logging/internal/LoggingProviderImpl$LogManagerAccess
instanceKlass java/lang/Shutdown$Lock
instanceKlass java/lang/Shutdown
instanceKlass java/lang/ApplicationShutdownHooks$1
instanceKlass java/lang/ApplicationShutdownHooks
instanceKlass java/util/Collections$SynchronizedMap
instanceKlass java/util/logging/LogManager$LogNode
instanceKlass java/util/logging/LogManager$LoggerContext
instanceKlass java/util/logging/LogManager$1
instanceKlass java/util/logging/LogManager
instanceKlass java/util/logging/Logger$ConfigurationData
instanceKlass java/util/logging/Logger$LoggerBundle
instanceKlass java/util/logging/Level
instanceKlass java/util/logging/Handler
instanceKlass java/util/logging/Logger
instanceKlass net/rubygrapefruit/platform/internal/jni/NativeLogger
instanceKlass net/rubygrapefruit/platform/file/FileEvents
instanceKlass java/util/RegularEnumSet$EnumSetIterator
instanceKlass net/rubygrapefruit/platform/internal/jni/NativeLibraryFunctions
instanceKlass java/lang/ClassLoader$NativeLibrary$Unloader
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel$1
instanceKlass sun/nio/ch/Interruptible
instanceKlass sun/nio/ch/FileKey
instanceKlass sun/nio/ch/FileLockTable
instanceKlass sun/nio/ch/NativeThread
instanceKlass java/nio/channels/FileLock
instanceKlass sun/nio/ch/NativeDispatcher
instanceKlass sun/nio/ch/NativeThreadSet
instanceKlass sun/nio/ch/IOUtil$1
instanceKlass sun/nio/ch/IOUtil
instanceKlass java/nio/file/attribute/FileAttribute
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel
instanceKlass java/nio/channels/InterruptibleChannel
instanceKlass java/nio/channels/ScatteringByteChannel
instanceKlass java/nio/channels/GatheringByteChannel
instanceKlass java/nio/channels/SeekableByteChannel
instanceKlass java/nio/channels/ByteChannel
instanceKlass java/nio/channels/WritableByteChannel
instanceKlass java/nio/channels/ReadableByteChannel
instanceKlass java/nio/channels/Channel
instanceKlass java/util/Formattable
instanceKlass java/util/Formatter$Conversion
instanceKlass java/util/Formatter$Flags
instanceKlass java/util/Formatter$FormatSpecifier
instanceKlass java/util/Formatter$FixedString
instanceKlass java/util/Formatter$FormatString
instanceKlass sun/util/resources/Bundles$2
instanceKlass sun/util/resources/Bundles$CacheKeyReference
instanceKlass java/util/ResourceBundle$ResourceBundleProviderHelper$$Lambda$20
instanceKlass java/util/ResourceBundle$ResourceBundleProviderHelper
instanceKlass sun/util/resources/LocaleData$LocaleDataResourceBundleProvider
instanceKlass java/util/spi/ResourceBundleProvider
instanceKlass sun/util/resources/Bundles$CacheKey
instanceKlass java/util/ResourceBundle$1
instanceKlass jdk/internal/misc/JavaUtilResourceBundleAccess
instanceKlass sun/util/resources/Bundles
instanceKlass sun/util/resources/LocaleData$LocaleDataStrategy
instanceKlass sun/util/resources/Bundles$Strategy
instanceKlass sun/util/resources/LocaleData$1
instanceKlass sun/util/locale/provider/CalendarDataUtility
instanceKlass sun/util/locale/provider/AvailableLanguageTags
instanceKlass sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda$19
instanceKlass java/util/ServiceLoader$ProviderImpl
instanceKlass java/util/ServiceLoader$Provider
instanceKlass java/util/ServiceLoader$1
instanceKlass sun/util/resources/cldr/provider/CLDRLocaleDataMetaInfo
instanceKlass jdk/internal/jimage/decompressor/ZipDecompressor
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressor
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressorFactory
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressorRepository
instanceKlass jdk/internal/jimage/decompressor/CompressedResourceHeader
instanceKlass jdk/internal/jimage/BasicImageReader$$Lambda$18
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressor$StringsProvider
instanceKlass java/util/TimSort
instanceKlass java/util/Arrays$LegacyMergeSort
instanceKlass jdk/internal/jimage/ImageBufferCache$2
instanceKlass jdk/internal/jimage/ImageBufferCache
instanceKlass jdk/internal/module/ModulePatcher$PatchedModuleReader
instanceKlass java/util/ServiceLoader$3
instanceKlass java/util/ServiceLoader$2
instanceKlass java/util/ServiceLoader$LazyClassPathLookupIterator
instanceKlass java/util/concurrent/CopyOnWriteArrayList$COWIterator
instanceKlass java/util/ServiceLoader$ModuleServicesLookupIterator
instanceKlass java/util/ServiceLoader
instanceKlass sun/util/cldr/CLDRLocaleProviderAdapter$1
instanceKlass sun/util/locale/StringTokenIterator
instanceKlass sun/util/locale/ParseStatus
instanceKlass sun/util/cldr/CLDRBaseLocaleDataMetaInfo
instanceKlass sun/util/locale/provider/LocaleDataMetaInfo
instanceKlass sun/util/locale/provider/LocaleProviderAdapter$1
instanceKlass java/util/ResourceBundle
instanceKlass java/util/ResourceBundle$Control
instanceKlass sun/util/resources/LocaleData
instanceKlass sun/util/locale/provider/LocaleResources
instanceKlass sun/util/locale/LanguageTag
instanceKlass sun/util/locale/provider/ResourceBundleBasedAdapter
instanceKlass sun/util/locale/provider/LocaleProviderAdapter
instanceKlass java/util/spi/LocaleServiceProvider
instanceKlass java/text/DecimalFormatSymbols
instanceKlass java/util/Locale$1
instanceKlass java/util/regex/Pattern$$Lambda$17
instanceKlass java/util/Formatter
instanceKlass net/rubygrapefruit/platform/internal/LibraryDef
instanceKlass net/rubygrapefruit/platform/internal/NativeLibraryLocator
instanceKlass net/rubygrapefruit/platform/internal/NativeLibraryLoader
instanceKlass net/rubygrapefruit/platform/Process
instanceKlass net/rubygrapefruit/platform/internal/Platform
instanceKlass net/rubygrapefruit/platform/Native
instanceKlass java/util/TreeMap$Entry
instanceKlass java/lang/ProcessEnvironment$CheckedEntry
instanceKlass java/lang/ProcessEnvironment$CheckedEntrySet$1
instanceKlass java/util/NavigableMap
instanceKlass java/util/SortedMap
instanceKlass java/lang/ProcessEnvironment$EntryComparator
instanceKlass java/lang/ProcessEnvironment$NameComparator
instanceKlass org/gradle/api/internal/file/temp/DefaultTemporaryFileProvider
instanceKlass org/gradle/internal/nativeintegration/services/NativeServices$1
instanceKlass org/gradle/internal/file/StatStatistics
instanceKlass org/gradle/internal/file/StatStatistics$Collector
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/GenericFileSystem
instanceKlass org/gradle/internal/service/InjectUtil
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/ClassValue$Version
instanceKlass java/lang/ClassValue$Identity
instanceKlass java/lang/ClassValue
instanceKlass java/lang/invoke/MethodHandleImpl$ArrayAccessor
instanceKlass java/lang/invoke/MethodHandleImpl$2
instanceKlass java/lang/invoke/MethodHandleImpl$LoopClauses
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$1
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/nativeintegration/filesystem/FileCanonicalizer
instanceKlass org/gradle/api/internal/file/temp/TemporaryFileProvider
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/GenericFileSystem$Factory
instanceKlass org/gradle/internal/nativeintegration/filesystem/Symlink
instanceKlass org/gradle/internal/nativeintegration/filesystem/FileSystem
instanceKlass org/gradle/internal/file/Stat
instanceKlass org/gradle/internal/file/Chmod
instanceKlass org/gradle/internal/nativeintegration/filesystem/FileModeMutator
instanceKlass org/gradle/internal/nativeintegration/filesystem/FileModeAccessor
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/FileSystemServices
instanceKlass org/gradle/internal/nativeintegration/jansi/DefaultJansiRuntimeResolver
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiRuntimeResolver
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiLibraryFactory
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiStorageLocator
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiBootPathConfigurer
instanceKlass java/lang/Class$3
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$ClassInspector$ClassDetails
instanceKlass java/util/concurrent/ConcurrentLinkedQueue$Node
instanceKlass org/gradle/internal/reflect/JavaMethod
instanceKlass org/gradle/internal/service/AbstractServiceMethod
instanceKlass java/util/LinkedList$ListItr
instanceKlass net/rubygrapefruit/platform/file/PosixFiles
instanceKlass net/rubygrapefruit/platform/file/Files
instanceKlass net/rubygrapefruit/platform/memory/Memory
instanceKlass org/gradle/internal/jvm/Jvm
instanceKlass org/gradle/internal/jvm/JavaInfo
instanceKlass net/rubygrapefruit/platform/SystemInfo
instanceKlass net/rubygrapefruit/platform/file/FileSystems
instanceKlass net/rubygrapefruit/platform/WindowsRegistry
instanceKlass org/gradle/internal/os/OperatingSystem
instanceKlass org/gradle/internal/service/RelevantMethodsBuilder
instanceKlass org/gradle/internal/Cast
instanceKlass org/gradle/internal/service/ServiceMethod
instanceKlass org/gradle/internal/service/MethodHandleBasedServiceMethodFactory
instanceKlass org/gradle/internal/service/DefaultServiceMethodFactory
instanceKlass org/gradle/internal/service/ServiceMethodFactory
instanceKlass org/gradle/internal/service/RelevantMethods
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$ClassInspector
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$ThisAsService
instanceKlass org/gradle/internal/concurrent/CompositeStoppable$1
instanceKlass org/gradle/internal/concurrent/CompositeStoppable
instanceKlass org/gradle/internal/service/AnnotatedServiceLifecycleHandler$Registration
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$OwnServices
instanceKlass org/gradle/internal/nativeintegration/ProcessEnvironment
instanceKlass org/gradle/initialization/GradleUserHomeDirProvider
instanceKlass org/gradle/internal/nativeintegration/NativeCapabilities
instanceKlass org/gradle/internal/nativeintegration/network/HostnameLookup
instanceKlass org/gradle/internal/nativeintegration/console/ConsoleDetector
instanceKlass net/rubygrapefruit/platform/ProcessLauncher
instanceKlass net/rubygrapefruit/platform/NativeIntegration
instanceKlass org/gradle/internal/nativeintegration/filesystem/FileMetadataAccessor
instanceKlass org/gradle/internal/service/ServiceRegistration
instanceKlass org/gradle/internal/service/ServiceProvider$Visitor
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$ManagedObjectServiceProvider
instanceKlass org/gradle/internal/service/Service
instanceKlass org/gradle/internal/service/ServiceProvider
instanceKlass org/gradle/internal/concurrent/Stoppable
instanceKlass org/gradle/internal/service/DefaultServiceRegistry
instanceKlass org/gradle/internal/service/ContainsServices
instanceKlass org/gradle/internal/serialize/AbstractDecoder
instanceKlass org/gradle/internal/serialize/Decoder
instanceKlass org/gradle/launcher/bootstrap/EntryPoint$RecordingExecutionListener
instanceKlass org/gradle/internal/logging/events/operations/LogEventBuildOperationProgressDetails
instanceKlass org/gradle/internal/logging/slf4j/BuildOperationAwareLogger
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer$2
instanceKlass org/gradle/internal/dispatch/ReflectionDispatch
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer$1
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer$LazyListener
instanceKlass org/gradle/internal/logging/events/OutputEventListener$1
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$1
instanceKlass java/lang/reflect/ProxyGenerator$ExceptionTableEntry
instanceKlass java/lang/reflect/ProxyGenerator$PrimitiveTypeInfo
instanceKlass java/lang/reflect/ProxyGenerator$FieldInfo
instanceKlass java/lang/reflect/ProxyGenerator$ConstantPool$Entry
instanceKlass java/lang/reflect/ProxyGenerator$MethodInfo
instanceKlass java/lang/reflect/ProxyGenerator$ProxyMethod
instanceKlass java/lang/reflect/ProxyGenerator$ConstantPool
instanceKlass java/lang/reflect/ProxyGenerator
instanceKlass java/lang/reflect/Proxy$$Lambda$16
instanceKlass java/lang/PublicMethods
instanceKlass java/lang/reflect/Proxy$ProxyBuilder
instanceKlass jdk/internal/loader/AbstractClassLoaderValue$Memoizer
instanceKlass java/lang/reflect/Proxy$$Lambda$15
instanceKlass java/lang/reflect/Proxy
instanceKlass org/gradle/internal/dispatch/ProxyDispatchAdapter$DispatchingInvocationHandler
instanceKlass java/lang/reflect/InvocationHandler
instanceKlass org/gradle/internal/dispatch/ProxyDispatchAdapter
instanceKlass org/gradle/internal/logging/events/operations/ProgressStartBuildOperationProgressDetails
instanceKlass org/gradle/internal/logging/sink/OutputEventTransformer
instanceKlass org/gradle/internal/exceptions/MultiCauseException
instanceKlass org/gradle/internal/event/AbstractBroadcastDispatch
instanceKlass org/gradle/internal/event/ListenerBroadcast
instanceKlass org/gradle/internal/dispatch/Dispatch
instanceKlass org/gradle/internal/logging/console/ColorMap
instanceKlass org/gradle/internal/nativeintegration/console/ConsoleMetaData
instanceKlass org/gradle/internal/logging/format/LogHeaderFormatter
instanceKlass org/gradle/api/logging/StandardOutputListener
instanceKlass org/gradle/internal/logging/text/StyledTextOutput
instanceKlass org/gradle/internal/Factory
instanceKlass org/gradle/internal/logging/config/LoggingSystem$Snapshot
instanceKlass org/gradle/internal/logging/events/OutputEvent
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer
instanceKlass org/gradle/internal/logging/config/LoggingRouter
instanceKlass org/gradle/internal/logging/LoggingOutputInternal
instanceKlass org/gradle/api/logging/LoggingOutput
instanceKlass org/gradle/internal/logging/config/LoggingSystem
instanceKlass org/gradle/internal/logging/slf4j/OutputEventListenerBackedLoggerContext$NoOpLogger
instanceKlass org/gradle/api/logging/Logger
instanceKlass java/util/concurrent/atomic/AtomicReference
instanceKlass org/gradle/internal/time/TimeSource$1
instanceKlass org/gradle/internal/time/TimeSource
instanceKlass org/gradle/internal/time/MonotonicClock
instanceKlass org/gradle/internal/time/CountdownTimer
instanceKlass org/gradle/internal/time/Timer
instanceKlass org/gradle/internal/time/Clock
instanceKlass org/gradle/internal/time/Time
instanceKlass org/gradle/internal/logging/events/OutputEventListener
instanceKlass org/gradle/internal/logging/slf4j/OutputEventListenerBackedLoggerContext
instanceKlass org/slf4j/impl/StaticLoggerBinder
instanceKlass org/slf4j/spi/LoggerFactoryBinder
instanceKlass java/net/URLClassLoader$3$1
instanceKlass java/net/URLClassLoader$3
instanceKlass jdk/internal/loader/URLClassPath$1
instanceKlass java/lang/CompoundEnumeration
instanceKlass jdk/internal/loader/BuiltinClassLoader$1
instanceKlass java/util/Collections$EmptyEnumeration
instanceKlass org/slf4j/helpers/Util
instanceKlass org/slf4j/helpers/NOPLoggerFactory
instanceKlass java/util/concurrent/LinkedBlockingQueue$Node
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject
instanceKlass java/util/concurrent/locks/Condition
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$Node
instanceKlass java/util/concurrent/BlockingQueue
instanceKlass org/slf4j/Logger
instanceKlass org/slf4j/helpers/SubstituteLoggerFactory
instanceKlass org/slf4j/event/LoggingEvent
instanceKlass org/slf4j/ILoggerFactory
instanceKlass org/slf4j/LoggerFactory
instanceKlass org/slf4j/helpers/BasicMarker
instanceKlass org/slf4j/Marker
instanceKlass org/slf4j/helpers/BasicMarkerFactory
instanceKlass org/slf4j/IMarkerFactory
instanceKlass org/slf4j/MarkerFactory
instanceKlass org/gradle/api/logging/Logging
instanceKlass org/gradle/launcher/daemon/configuration/DaemonServerConfiguration
instanceKlass org/gradle/internal/service/ServiceRegistry
instanceKlass org/gradle/internal/service/ServiceLookup
instanceKlass org/gradle/launcher/bootstrap/ExecutionCompleter
instanceKlass org/gradle/api/Action
instanceKlass org/gradle/internal/logging/text/StyledTextOutputFactory
instanceKlass org/gradle/api/logging/configuration/LoggingConfiguration
instanceKlass org/gradle/initialization/BuildClientMetaData
instanceKlass org/gradle/launcher/bootstrap/ExecutionListener
instanceKlass org/gradle/launcher/bootstrap/EntryPoint
instanceKlass java/io/FilePermissionCollection$1
instanceKlass java/io/FileInputStream$1
instanceKlass java/security/Security$1
instanceKlass java/security/Security
instanceKlass sun/security/util/SecurityProperties
instanceKlass sun/security/util/FilePermCompat
instanceKlass java/io/FilePermission$1
instanceKlass jdk/internal/misc/JavaIOFilePermissionAccess
instanceKlass sun/net/www/MessageHeader
instanceKlass java/net/URLConnection
instanceKlass java/util/LinkedList$Node
instanceKlass java/net/URLClassLoader$1
instanceKlass java/net/URLClassLoader$7
instanceKlass jdk/internal/misc/JavaNetURLClassLoaderAccess
instanceKlass jdk/internal/jimage/ImageLocation
instanceKlass jdk/internal/jimage/decompressor/Decompressor
instanceKlass jdk/internal/jimage/ImageStringsReader
instanceKlass jdk/internal/jimage/ImageStrings
instanceKlass jdk/internal/jimage/ImageHeader
instanceKlass jdk/internal/jimage/NativeImageBuffer$1
instanceKlass jdk/internal/jimage/NativeImageBuffer
instanceKlass jdk/internal/jimage/BasicImageReader$1
instanceKlass jdk/internal/jimage/BasicImageReader
instanceKlass jdk/internal/jimage/ImageReader
instanceKlass jdk/internal/jimage/ImageReaderFactory$1
instanceKlass java/nio/file/Paths
instanceKlass jdk/internal/jimage/ImageReaderFactory
instanceKlass jdk/internal/module/SystemModuleFinders$SystemImage
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleReader
instanceKlass java/lang/module/ModuleReader
instanceKlass jdk/internal/loader/BuiltinClassLoader$5
instanceKlass jdk/internal/loader/BuiltinClassLoader$2
instanceKlass jdk/internal/module/Resources
instanceKlass java/io/RandomAccessFile$1
instanceKlass java/lang/invoke/VarHandle$AccessDescriptor
instanceKlass org/gradle/api/Action
instanceKlass org/gradle/internal/IoActions
instanceKlass java/util/Properties$LineReader
instanceKlass java/util/regex/Pattern$BmpCharPredicate$$Lambda$14
instanceKlass java/util/regex/Pattern$$Lambda$13
instanceKlass java/util/regex/Pattern$$Lambda$12
instanceKlass java/util/regex/Pattern$BitClass$$Lambda$11
instanceKlass java/util/regex/Pattern$TreeInfo
instanceKlass java/util/regex/Pattern$CharPredicate$$Lambda$10
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/util/regex/CharPredicates$$Lambda$9
instanceKlass org/gradle/api/Transformer
instanceKlass org/gradle/util/internal/GUtil
instanceKlass java/util/zip/ZipFile$$Lambda$8
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry$$Lambda$7
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/util/LinkedHashMap$LinkedHashIterator
instanceKlass java/util/Collections$1
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry$DefaultModule
instanceKlass java/util/regex/IntHashSet
instanceKlass java/util/regex/Matcher
instanceKlass java/util/regex/MatchResult
instanceKlass java/util/regex/Pattern$$Lambda$6
instanceKlass java/util/regex/CharPredicates$$Lambda$5
instanceKlass java/util/regex/Pattern$BmpCharPredicate
instanceKlass java/util/regex/Pattern$CharPredicate
instanceKlass java/util/regex/CharPredicates
instanceKlass java/util/regex/ASCII
instanceKlass java/util/regex/Pattern$Node
instanceKlass java/util/regex/Pattern
instanceKlass org/gradle/internal/service/CachingServiceLocator
instanceKlass java/io/Reader
instanceKlass org/gradle/internal/service/DefaultServiceLocator
instanceKlass org/gradle/internal/service/ServiceLocator
instanceKlass org/gradle/internal/classloader/DefaultClassLoaderFactory
instanceKlass org/gradle/api/internal/classpath/ManifestUtil
instanceKlass org/gradle/internal/Cast
instanceKlass java/util/AbstractList$Itr
instanceKlass org/gradle/internal/classloader/ClassLoaderSpec
instanceKlass org/gradle/internal/classloader/ClassLoaderVisitor
instanceKlass java/util/Collections$EmptyIterator
instanceKlass org/gradle/internal/classpath/DefaultClassPath
instanceKlass org/gradle/internal/classpath/ClassPath
instanceKlass org/gradle/internal/installation/GradleInstallation$1
instanceKlass java/io/FileFilter
instanceKlass org/gradle/internal/installation/GradleInstallation
instanceKlass org/gradle/internal/classloader/ClasspathUtil
instanceKlass org/gradle/internal/installation/CurrentGradleInstallationLocator
instanceKlass org/gradle/internal/installation/CurrentGradleInstallation
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry$$Lambda$4
instanceKlass org/gradle/api/specs/Spec
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass sun/reflect/misc/ReflectUtil
instanceKlass org/gradle/api/internal/classpath/Module
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry
instanceKlass org/gradle/cache/GlobalCache
instanceKlass org/gradle/api/internal/DefaultClassPathProvider
instanceKlass org/gradle/api/internal/ClassPathProvider
instanceKlass org/gradle/api/internal/DefaultClassPathRegistry
instanceKlass org/gradle/internal/classloader/ClassLoaderHierarchy
instanceKlass org/gradle/internal/classloader/ClassLoaderFactory
instanceKlass org/gradle/api/internal/ClassPathRegistry
instanceKlass org/gradle/api/internal/classpath/ModuleRegistry
instanceKlass org/gradle/launcher/bootstrap/ProcessBootstrap
instanceKlass java/lang/PublicMethods$Key
instanceKlass java/lang/PublicMethods$MethodList
instanceKlass org/gradle/launcher/daemon/bootstrap/GradleDaemon
instanceKlass java/security/SecureClassLoader$DebugHolder
instanceKlass java/security/Permissions$1
instanceKlass java/security/PermissionCollection
instanceKlass java/security/SecureClassLoader$1
instanceKlass java/security/SecureClassLoader$CodeSourceKey
instanceKlass java/util/zip/Checksum$1
instanceKlass java/util/zip/CRC32
instanceKlass java/util/zip/Checksum
instanceKlass sun/nio/ByteBuffered
instanceKlass java/lang/Package$VersionInfo
instanceKlass java/lang/NamedPackage
instanceKlass sun/security/util/ManifestEntryVerifier
instanceKlass jdk/internal/loader/Resource
instanceKlass java/util/StringTokenizer
instanceKlass java/util/jar/Attributes$Name
instanceKlass java/util/jar/Attributes
instanceKlass java/security/CodeSigner
instanceKlass sun/security/util/Debug
instanceKlass java/util/jar/JarVerifier
instanceKlass java/util/zip/ZipFile$InflaterCleanupAction
instanceKlass java/util/zip/Inflater$InflaterZStreamRef
instanceKlass java/util/zip/Inflater
instanceKlass java/util/zip/ZipEntry
instanceKlass java/util/jar/JarFile$1
instanceKlass jdk/internal/util/jar/JarIndex
instanceKlass java/nio/Bits$1
instanceKlass jdk/internal/misc/JavaNioAccess$BufferPool
instanceKlass java/nio/Bits
instanceKlass sun/nio/ch/DirectBuffer
instanceKlass jdk/internal/perf/PerfCounter$CoreCounters
instanceKlass jdk/internal/perf/Perf
instanceKlass jdk/internal/perf/Perf$GetPerfAction
instanceKlass jdk/internal/perf/PerfCounter
instanceKlass java/util/zip/ZipUtils
instanceKlass java/util/zip/ZipFile$Source$End
instanceKlass java/lang/invoke/VarForm
instanceKlass java/lang/invoke/VarHandleGuards
instanceKlass jdk/internal/util/Preconditions$1
instanceKlass java/lang/invoke/VarHandle$1
instanceKlass java/lang/invoke/VarHandles
instanceKlass java/util/concurrent/atomic/AtomicBoolean
instanceKlass java/io/RandomAccessFile$2
instanceKlass jdk/internal/misc/JavaIORandomAccessFileAccess
instanceKlass java/io/RandomAccessFile
instanceKlass java/io/DataInput
instanceKlass java/io/DataOutput
instanceKlass java/nio/file/attribute/FileTime
instanceKlass sun/nio/fs/WindowsNativeDispatcher$CompletionStatus
instanceKlass sun/nio/fs/WindowsNativeDispatcher$AclInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$Account
instanceKlass sun/nio/fs/WindowsNativeDispatcher$DiskFreeSpace
instanceKlass sun/nio/fs/WindowsNativeDispatcher$VolumeInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$QueryDirectoryInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstStream
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstFile
instanceKlass java/util/Enumeration
instanceKlass java/util/ArrayDeque$DeqIterator
instanceKlass java/lang/ClassLoader$NativeLibrary
instanceKlass java/lang/ClassLoader$2
instanceKlass java/nio/charset/CoderResult
instanceKlass java/lang/Readable
instanceKlass sun/nio/cs/ArrayEncoder
instanceKlass java/lang/StringCoding$StringEncoder
instanceKlass sun/nio/fs/WindowsNativeDispatcher$1
instanceKlass sun/nio/fs/WindowsNativeDispatcher
instanceKlass sun/nio/fs/NativeBuffer$Deallocator
instanceKlass sun/nio/fs/NativeBuffer
instanceKlass sun/nio/fs/NativeBuffers
instanceKlass sun/nio/fs/WindowsFileAttributes
instanceKlass java/nio/file/attribute/DosFileAttributes
instanceKlass sun/nio/fs/AbstractBasicFileAttributeView
instanceKlass sun/nio/fs/DynamicFileAttributeView
instanceKlass sun/nio/fs/WindowsFileAttributeViews
instanceKlass sun/nio/fs/Util
instanceKlass java/nio/file/attribute/BasicFileAttributeView
instanceKlass java/nio/file/attribute/FileAttributeView
instanceKlass java/nio/file/attribute/AttributeView
instanceKlass java/nio/file/Files
instanceKlass java/nio/file/CopyOption
instanceKlass java/nio/file/attribute/BasicFileAttributes
instanceKlass sun/nio/fs/WindowsPath
instanceKlass java/nio/file/Path
instanceKlass java/nio/file/Watchable
instanceKlass java/net/URI$Parser
instanceKlass sun/nio/fs/WindowsPathParser$Result
instanceKlass sun/nio/fs/WindowsPathParser
instanceKlass java/util/Arrays$ArrayItr
instanceKlass java/nio/file/FileSystem
instanceKlass java/nio/file/OpenOption
instanceKlass java/nio/file/spi/FileSystemProvider
instanceKlass sun/nio/fs/DefaultFileSystemProvider
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder$1
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder
instanceKlass java/nio/file/FileSystems
instanceKlass java/util/zip/ZipFile$Source$Key
instanceKlass java/util/zip/ZipFile$Source
instanceKlass java/util/zip/ZipFile$CleanableResource
instanceKlass java/util/zip/ZipCoder
instanceKlass java/lang/Runtime$Version
instanceKlass java/util/jar/JavaUtilJarAccessImpl
instanceKlass jdk/internal/misc/JavaUtilJarAccess
instanceKlass jdk/internal/loader/FileURLMapper
instanceKlass jdk/internal/loader/URLClassPath$JarLoader$1
instanceKlass java/util/zip/ZipFile$1
instanceKlass jdk/internal/misc/JavaUtilZipFileAccess
instanceKlass java/util/zip/ZipFile
instanceKlass java/util/zip/ZipConstants
instanceKlass jdk/internal/loader/URLClassPath$Loader
instanceKlass jdk/internal/loader/URLClassPath$3
instanceKlass java/security/PrivilegedExceptionAction
instanceKlass sun/util/locale/InternalLocaleBuilder$CaseInsensitiveChar
instanceKlass sun/util/locale/InternalLocaleBuilder
instanceKlass sun/util/locale/LocaleUtils
instanceKlass sun/util/locale/BaseLocale$Key
instanceKlass sun/util/locale/BaseLocale
instanceKlass sun/util/locale/LocaleObjectCache
instanceKlass java/util/Locale
instanceKlass sun/net/util/URLUtil
instanceKlass sun/launcher/LauncherHelper
instanceKlass jdk/internal/module/ModuleBootstrap$2
instanceKlass jdk/internal/module/IllegalAccessLogger$Builder
instanceKlass jdk/internal/module/IllegalAccessLogger
instanceKlass java/lang/WeakPairMap$$Lambda$3
instanceKlass jdk/internal/org/objectweb/asm/FieldVisitor
instanceKlass java/lang/Module$$Lambda$2
instanceKlass java/util/function/BiFunction
instanceKlass java/lang/WeakPairMap$Pair$Lookup
instanceKlass java/lang/WeakPairMap$Pair
instanceKlass java/lang/WeakPairMap
instanceKlass java/lang/Module$ReflectionData
instanceKlass jdk/internal/misc/InnocuousThread$2
instanceKlass jdk/internal/misc/InnocuousThread$3
instanceKlass jdk/internal/ref/CleanerFactory$1$1
instanceKlass java/lang/ref/Cleaner$Cleanable
instanceKlass jdk/internal/ref/CleanerImpl
instanceKlass java/lang/ref/Cleaner$1
instanceKlass java/lang/ref/Cleaner
instanceKlass jdk/internal/ref/CleanerFactory$1
instanceKlass java/util/concurrent/ThreadFactory
instanceKlass jdk/internal/ref/CleanerFactory
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$CpPatch
instanceKlass java/lang/invoke/LambdaFormBuffer
instanceKlass java/lang/invoke/LambdaFormEditor
instanceKlass java/lang/invoke/DelegatingMethodHandle$Holder
instanceKlass sun/invoke/util/Wrapper$1
instanceKlass java/lang/invoke/DirectMethodHandle$1
instanceKlass sun/invoke/empty/Empty
instanceKlass sun/invoke/util/VerifyType
instanceKlass java/lang/invoke/ClassSpecializer$Factory
instanceKlass java/lang/invoke/ClassSpecializer$SpeciesData
instanceKlass java/lang/invoke/ClassSpecializer$1
instanceKlass java/lang/invoke/ClassSpecializer
instanceKlass java/lang/invoke/InnerClassLambdaMetafactory$1
instanceKlass jdk/internal/module/ModuleBootstrap$$Lambda$1
instanceKlass jdk/internal/org/objectweb/asm/AnnotationVisitor
instanceKlass jdk/internal/org/objectweb/asm/Frame
instanceKlass jdk/internal/org/objectweb/asm/Label
instanceKlass jdk/internal/org/objectweb/asm/MethodVisitor
instanceKlass jdk/internal/org/objectweb/asm/Item
instanceKlass jdk/internal/org/objectweb/asm/ByteVector
instanceKlass jdk/internal/org/objectweb/asm/ClassVisitor
instanceKlass sun/invoke/util/BytecodeDescriptor
instanceKlass java/lang/invoke/InfoFromMemberName
instanceKlass java/lang/invoke/MethodHandleInfo
instanceKlass sun/security/action/GetBooleanAction
instanceKlass jdk/internal/org/objectweb/asm/Type
instanceKlass java/lang/invoke/AbstractValidatingLambdaMetafactory
instanceKlass java/lang/invoke/MethodHandleImpl$1
instanceKlass jdk/internal/misc/JavaLangInvokeAccess
instanceKlass java/lang/invoke/Invokers$Holder
instanceKlass java/lang/invoke/BootstrapMethodInvoker
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$2
instanceKlass java/lang/invoke/InvokerBytecodeGenerator
instanceKlass java/lang/invoke/LambdaForm$Holder
instanceKlass java/lang/invoke/LambdaForm$Name
instanceKlass java/lang/invoke/Invokers
instanceKlass java/lang/invoke/MethodHandleImpl
instanceKlass sun/invoke/util/ValueConversions
instanceKlass java/lang/invoke/DirectMethodHandle$Holder
instanceKlass java/lang/invoke/LambdaForm$NamedFunction
instanceKlass sun/invoke/util/Wrapper$Format
instanceKlass sun/invoke/util/VerifyAccess
instanceKlass java/security/Permission
instanceKlass java/security/Guard
instanceKlass java/lang/invoke/MemberName$Factory
instanceKlass java/lang/invoke/MethodHandles
instanceKlass java/lang/invoke/MethodTypeForm
instanceKlass java/lang/Void
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet
instanceKlass java/lang/invoke/MethodHandles$Lookup
instanceKlass java/lang/invoke/LambdaMetafactory
instanceKlass java/lang/ModuleLayer$Controller
instanceKlass java/util/HashMap$HashIterator
instanceKlass jdk/internal/module/ServicesCatalog$ServiceProvider
instanceKlass java/util/concurrent/CopyOnWriteArrayList
instanceKlass java/util/Collections$UnmodifiableCollection$1
instanceKlass java/util/Collections$UnmodifiableCollection
instanceKlass java/util/ArrayList$Itr
instanceKlass java/util/ImmutableCollections$ListItr
instanceKlass java/util/ListIterator
instanceKlass java/lang/ModuleLayer
instanceKlass jdk/internal/module/ModuleLoaderMap$Mapper
instanceKlass java/util/function/Function
instanceKlass jdk/internal/module/ModuleLoaderMap
instanceKlass java/util/ImmutableCollections$Set12$1
instanceKlass java/util/ImmutableCollections$MapN$MapNIterator
instanceKlass java/util/AbstractMap$1$1
instanceKlass java/lang/module/ResolvedModule
instanceKlass java/lang/module/Configuration
instanceKlass java/util/ImmutableCollections$SetN$SetNIterator
instanceKlass jdk/internal/loader/BuiltinClassLoader$LoadedModule
instanceKlass jdk/internal/util/Preconditions
instanceKlass java/util/Deque
instanceKlass java/util/Queue
instanceKlass sun/net/util/IPAddressUtil
instanceKlass java/net/URLStreamHandler
instanceKlass sun/net/www/ParseUtil
instanceKlass java/io/ExpiringCache$Entry
instanceKlass java/net/URL$3
instanceKlass jdk/internal/misc/JavaNetURLAccess
instanceKlass java/net/URL$DefaultFactory
instanceKlass java/net/URLStreamHandlerFactory
instanceKlass jdk/internal/loader/URLClassPath
instanceKlass java/security/Principal
instanceKlass java/security/ProtectionDomain$Key
instanceKlass java/security/ProtectionDomain$JavaSecurityAccessImpl
instanceKlass jdk/internal/misc/JavaSecurityAccess
instanceKlass java/lang/ClassLoader$ParallelLoaders
instanceKlass jdk/internal/loader/AbstractClassLoaderValue
instanceKlass jdk/internal/module/ServicesCatalog
instanceKlass jdk/internal/loader/BootLoader
instanceKlass java/util/Optional
instanceKlass jdk/internal/module/ModuleBootstrap$Counters
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleFinder
instanceKlass java/lang/module/ModuleFinder
instanceKlass jdk/internal/module/SystemModuleFinders$3
instanceKlass jdk/internal/module/ModuleHashes$HashSupplier
instanceKlass java/util/KeyValueHolder
instanceKlass jdk/internal/module/SystemModuleFinders$2
instanceKlass java/util/function/Supplier
instanceKlass java/lang/module/ModuleReference
instanceKlass jdk/internal/module/ModuleResolution
instanceKlass java/util/Collections$UnmodifiableMap
instanceKlass jdk/internal/module/ModuleHashes$Builder
instanceKlass jdk/internal/module/ModuleHashes
instanceKlass jdk/internal/module/ModuleTarget
instanceKlass java/lang/Enum
instanceKlass java/lang/module/ModuleDescriptor$Version
instanceKlass java/lang/module/ModuleDescriptor$Provides
instanceKlass java/lang/module/ModuleDescriptor$Opens
instanceKlass java/lang/module/ModuleDescriptor$Exports
instanceKlass java/lang/module/ModuleDescriptor$Requires
instanceKlass jdk/internal/module/Builder
instanceKlass jdk/internal/module/SystemModules$default
instanceKlass jdk/internal/module/SystemModules
instanceKlass jdk/internal/module/SystemModulesMap
instanceKlass java/net/URI$1
instanceKlass jdk/internal/misc/JavaNetUriAccess
instanceKlass java/net/URI
instanceKlass jdk/internal/module/SystemModuleFinders
instanceKlass jdk/internal/module/ArchivedModuleGraph
instanceKlass jdk/internal/module/ModulePatcher
instanceKlass java/util/ImmutableCollections
instanceKlass java/lang/module/ModuleDescriptor$1
instanceKlass jdk/internal/misc/JavaLangModuleAccess
instanceKlass java/lang/module/ModuleDescriptor
instanceKlass jdk/internal/module/ModuleBootstrap
instanceKlass java/lang/CharacterData
instanceKlass java/lang/invoke/MethodHandleStatics
instanceKlass java/io/ExpiringCache
instanceKlass java/io/FileSystem
instanceKlass java/io/DefaultFileSystem
instanceKlass java/io/File
instanceKlass java/lang/System$2
instanceKlass jdk/internal/misc/JavaLangAccess
instanceKlass sun/io/Win32ErrorMode
instanceKlass jdk/internal/misc/OSEnvironment
instanceKlass java/lang/Integer$IntegerCache
instanceKlass jdk/internal/misc/Signal$NativeHandler
instanceKlass java/util/Hashtable$Entry
instanceKlass jdk/internal/misc/Signal
instanceKlass java/lang/Terminator$1
instanceKlass jdk/internal/misc/Signal$Handler
instanceKlass java/lang/Terminator
instanceKlass java/nio/ByteOrder
instanceKlass java/nio/Buffer$1
instanceKlass jdk/internal/misc/JavaNioAccess
instanceKlass java/nio/charset/CharsetEncoder
instanceKlass sun/security/action/GetPropertyAction
instanceKlass java/io/Writer
instanceKlass java/io/OutputStream
instanceKlass java/io/Flushable
instanceKlass java/io/FileDescriptor$1
instanceKlass jdk/internal/misc/JavaIOFileDescriptorAccess
instanceKlass java/io/FileDescriptor
instanceKlass java/lang/VersionProps
instanceKlass jdk/internal/util/StaticProperty
instanceKlass java/util/concurrent/ConcurrentHashMap$MapEntry
instanceKlass java/util/concurrent/ConcurrentHashMap$Traverser
instanceKlass java/lang/reflect/Array
instanceKlass java/util/Collections$SynchronizedCollection
instanceKlass java/util/List
instanceKlass java/util/RandomAccess
instanceKlass java/util/Collections
instanceKlass java/util/concurrent/ConcurrentHashMap$CollectionView
instanceKlass java/util/Properties$EntrySet
instanceKlass java/lang/StringCoding$Result
instanceKlass java/nio/charset/CodingErrorAction
instanceKlass java/nio/charset/CharsetDecoder
instanceKlass sun/nio/cs/ArrayDecoder
instanceKlass sun/nio/cs/DelegatableDecoder
instanceKlass java/lang/StringUTF16
instanceKlass sun/nio/cs/DoubleByte
instanceKlass java/lang/StringCoding$StringDecoder
instanceKlass java/lang/ThreadLocal$ThreadLocalMap
instanceKlass java/lang/StringCoding
instanceKlass java/util/HashMap$Node
instanceKlass jdk/internal/reflect/Reflection
instanceKlass java/lang/Class$1
instanceKlass java/lang/reflect/ReflectAccess
instanceKlass jdk/internal/reflect/LangReflectAccess
instanceKlass java/lang/reflect/Modifier
instanceKlass java/lang/Class$Atomic
instanceKlass java/lang/Class$ReflectionData
instanceKlass java/nio/charset/StandardCharsets
instanceKlass sun/nio/cs/HistoricallyNamedCharset
instanceKlass java/lang/Math
instanceKlass java/util/Arrays
instanceKlass java/lang/ThreadLocal
instanceKlass java/nio/charset/spi/CharsetProvider
instanceKlass java/nio/charset/Charset
instanceKlass java/lang/StringLatin1
instanceKlass jdk/internal/misc/VM
instanceKlass jdk/internal/misc/SharedSecrets
instanceKlass java/lang/ref/Reference$1
instanceKlass jdk/internal/misc/JavaLangRefAccess
instanceKlass java/lang/ref/ReferenceQueue$Lock
instanceKlass java/lang/ref/ReferenceQueue
instanceKlass jdk/internal/reflect/ReflectionFactory
instanceKlass java/util/concurrent/locks/LockSupport
instanceKlass java/util/concurrent/ConcurrentHashMap$Node
instanceKlass java/util/Map$Entry
instanceKlass java/util/concurrent/ConcurrentHashMap$CounterCell
instanceKlass java/util/concurrent/locks/ReentrantLock
instanceKlass java/util/concurrent/locks/Lock
instanceKlass java/lang/Runtime
instanceKlass java/util/concurrent/ConcurrentMap
instanceKlass java/util/AbstractMap
instanceKlass java/security/cert/Certificate
instanceKlass jdk/internal/reflect/ReflectionFactory$GetReflectionFactoryAction
instanceKlass java/security/PrivilegedAction
instanceKlass java/util/Objects
instanceKlass java/util/AbstractCollection
instanceKlass java/util/Set
instanceKlass java/util/Collection
instanceKlass java/lang/Iterable
instanceKlass java/security/AccessController
instanceKlass java/lang/String$CaseInsensitiveComparator
instanceKlass java/util/Comparator
instanceKlass java/io/ObjectStreamField
instanceKlass java/util/Iterator
instanceKlass java/lang/Number
instanceKlass java/lang/Character
instanceKlass java/lang/Boolean
instanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer
instanceKlass java/lang/LiveStackFrame
instanceKlass java/lang/StackFrameInfo
instanceKlass java/lang/StackWalker$StackFrame
instanceKlass java/lang/StackStreamFactory$AbstractStackWalker
instanceKlass java/lang/StackWalker
instanceKlass java/nio/Buffer
instanceKlass java/lang/StackTraceElement
instanceKlass java/security/CodeSource
instanceKlass jdk/internal/loader/ClassLoaders
instanceKlass java/util/jar/Manifest
instanceKlass java/net/URL
instanceKlass java/io/InputStream
instanceKlass java/io/Closeable
instanceKlass java/lang/AutoCloseable
instanceKlass jdk/internal/module/Modules
instanceKlass jdk/internal/misc/Unsafe
instanceKlass java/lang/AbstractStringBuilder
instanceKlass java/lang/Appendable
instanceKlass java/lang/AssertionStatusDirectives
instanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext
instanceKlass java/lang/invoke/CallSite
instanceKlass java/lang/invoke/MethodType
instanceKlass java/lang/invoke/LambdaForm
instanceKlass java/lang/invoke/MethodHandleNatives
instanceKlass java/lang/invoke/ResolvedMethodName
instanceKlass java/lang/invoke/MemberName
instanceKlass java/lang/invoke/VarHandle
instanceKlass java/lang/invoke/MethodHandle
instanceKlass jdk/internal/reflect/CallerSensitive
instanceKlass java/lang/annotation/Annotation
instanceKlass jdk/internal/reflect/FieldAccessor
instanceKlass jdk/internal/reflect/ConstantPool
instanceKlass jdk/internal/reflect/ConstructorAccessor
instanceKlass jdk/internal/reflect/MethodAccessor
instanceKlass jdk/internal/reflect/MagicAccessorImpl
instanceKlass java/lang/reflect/Parameter
instanceKlass java/lang/reflect/Member
instanceKlass java/lang/reflect/AccessibleObject
instanceKlass java/lang/Module
instanceKlass java/util/Dictionary
instanceKlass java/util/Map
instanceKlass java/lang/ThreadGroup
instanceKlass java/lang/Thread$UncaughtExceptionHandler
instanceKlass java/lang/Thread
instanceKlass java/lang/Runnable
instanceKlass java/lang/ref/Reference
instanceKlass java/security/AccessControlContext
instanceKlass java/security/ProtectionDomain
instanceKlass java/lang/SecurityManager
instanceKlass java/lang/Throwable
instanceKlass java/lang/System
instanceKlass java/lang/ClassLoader
instanceKlass java/lang/Cloneable
instanceKlass java/lang/Class
instanceKlass java/lang/reflect/Type
instanceKlass java/lang/reflect/GenericDeclaration
instanceKlass java/lang/reflect/AnnotatedElement
instanceKlass java/lang/String
instanceKlass java/lang/CharSequence
instanceKlass java/lang/Comparable
instanceKlass java/io/Serializable
ciInstanceKlass java/lang/Object 1 1 92 7 10 10 10 10 8 10 10 10 10 100 8 10 3 8 10 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 7 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 12 12 7 12 12 1 12 7 12 12 12 1 1 12 1 12 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/Serializable 1 0 7 100 100 1 1 1 1
ciInstanceKlass java/lang/String 1 1 878 10 8 9 9 9 10 10 10 9 10 7 10 10 10 10 10 100 8 10 10 9 9 8 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 100 10 10 100 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 100 100 10 10 11 11 10 10 10 10 10 10 9 11 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 10 10 10 10 10 10 100 10 7 10 10 10 10 10 8 10 10 100 3 3 7 10 10 10 10 10 11 7 10 10 7 10 10 10 11 11 11 7 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 100 100 10 100 10 10 100 10 10 10 7 10 10 10 10 8 10 10 10 8 8 10 10 10 10 10 10 10 100 10 8 10 10 10 7 3 8 8 8 10 10 10 10 10 10 8 8 10 8 8 8 8 8 10 10 10 8 7 10 10 10 7 9 7 10 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 7 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 1 12 12 12 12 12 12 12 7 12 1 7 7 12 12 12 12 1 12 7 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 12 12 1 12 12 12 12 7 12 12 12 12 12 12 12 12 12 12 1 1 12 12 12 12 12 12 12 12 12 7 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 7 12 12 12 12 7 12 12 12 12 7 12 12 7 12 1 1 12 12 12 1 12 1 1 12 12 12 12 7 12 12 12 1 12 12 100 12 12 12 1 12 7 12 12 12 12 12 12 12 12 12 12 12 1 1 1 12 1 100 12 1 1 12 1 12 12 1 12 12 1 1 12 12 12 100 12 100 12 100 12 1 1 12 12 1 1 1 1 12 12 12 12 1 1 1 1 1 1 1 12 12 12 1 1 12 12 12 1 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/String COMPACT_STRINGS Z 1
staticfield java/lang/String serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/String CASE_INSENSITIVE_ORDER Ljava/util/Comparator; java/lang/String$CaseInsensitiveComparator
ciInstanceKlass java/lang/Class 1 1 1435 10 9 9 7 10 10 8 10 8 8 10 10 10 10 10 10 10 10 10 10 10 8 10 8 8 10 7 8 8 8 10 11 10 10 8 10 10 10 10 9 10 10 10 18 10 7 10 10 10 100 10 9 7 100 8 10 10 10 10 7 10 7 100 10 10 9 10 10 7 10 100 10 10 10 9 10 10 10 9 10 10 100 10 10 10 10 9 8 10 10 10 10 10 10 9 10 7 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 100 8 10 10 7 10 7 11 10 10 10 10 10 10 8 10 10 8 9 10 10 10 10 100 9 10 9 10 10 10 8 10 10 10 10 100 10 10 10 10 10 10 10 100 10 10 10 10 10 10 10 10 10 7 10 10 11 10 10 10 10 10 10 100 10 10 10 100 100 10 10 10 10 10 10 10 10 11 10 10 9 10 9 7 10 9 10 7 10 9 10 10 10 10 10 10 10 8 10 10 9 9 10 7 9 10 10 7 10 10 10 10 9 10 9 10 10 9 9 10 10 9 100 10 10 7 10 100 11 9 9 7 10 9 9 10 10 9 7 10 10 10 10 10 10 10 9 10 10 10 10 8 7 10 7 8 8 8 8 10 9 9 10 7 9 7 10 7 10 10 9 8 10 7 10 7 10 9 100 8 10 7 4 10 10 11 10 100 10 10 8 8 10 9 11 100 11 9 10 10 10 9 9 10 10 10 10 10 11 11 11 11 7 11 10 10 7 11 10 10 10 11 11 7 10 10 9 9 10 10 10 10 100 10 10 7 9 100 100 100 100 1 1 1 7 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 1 12 1 12 1 1 12 12 12 12 12 12 7 12 12 12 12 1 12 1 1 12 1 1 1 1 12 12 12 1 7 12 12 12 7 12 7 12 12 7 12 12 1 15 16 15 16 12 7 12 1 12 12 7 1 12 12 1 1 1 12 12 12 12 1 12 1 1 12 12 12 12 1 100 12 12 12 12 12 12 12 12 12 1 12 12 12 12 1 12 12 12 12 12 12 12 7 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 1 12 1 12 1 12 100 12 12 12 12 12 1 12 12 1 12 12 12 12 12 1 12 12 12 12 12 1 12 12 12 12 1 12 12 12 12 12 12 12 1 12 12 12 12 12 12 12 1 7 12 12 12 12 100 12 12 12 1 12 12 1 1 12 12 12 12 12 12 100 12 7 12 12 12 12 12 12 1 12 12 1 12 12 100 12 12 12 100 12 12 12 12 1 12 12 12 12 12 1 12 12 12 1 12 12 7 12 7 12 12 12 12 12 12 12 12 12 12 12 1 12 1 12 100 12 12 1 12 12 12 12 12 12 1 12 12 12 12 12 12 12 12 12 1 7 1 1 1 1 12 12 12 12 1 12 1 1 1 12 7 12 12 1 12 1 12 12 1 1 1 12 12 12 1 12 1 1 12 12 12 1 12 12 100 12 7 12 12 12 12 12 12 12 12 12 12 12 12 1 12 7 12 12 1 12 100 12 12 12 12 1 12 12 12 100 12 12 100 12 12 12 1 12 12 1 12 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 12 1 1 100 1 1 100 1 1
staticfield java/lang/Class EMPTY_CLASS_ARRAY [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/Class serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/Cloneable 1 0 7 100 100 1 1 1 1
instanceKlass org/gradle/internal/classloader/CachingClassLoader
instanceKlass org/gradle/internal/classloader/MultiParentClassLoader
instanceKlass org/gradle/internal/classloader/FilteringClassLoader$RetrieveSystemPackagesClassLoader
instanceKlass org/gradle/internal/classloader/FilteringClassLoader
instanceKlass org/gradle/internal/classloader/FilteringClassLoader
instanceKlass jdk/internal/reflect/DelegatingClassLoader
instanceKlass java/security/SecureClassLoader
ciInstanceKlass java/lang/ClassLoader 1 1 1194 9 10 9 10 7 10 10 9 10 100 10 10 10 100 8 10 10 10 10 7 10 7 7 7 10 10 9 7 10 9 9 9 9 9 7 10 9 10 10 9 9 7 9 7 10 10 9 10 7 10 8 10 10 10 7 10 10 8 10 10 10 10 10 10 10 10 7 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 100 10 100 10 10 10 10 10 100 8 10 8 10 10 100 8 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 8 11 9 11 10 8 8 10 100 10 10 10 10 10 10 10 10 10 7 10 10 10 7 10 100 18 10 10 10 7 10 10 10 7 10 10 10 10 8 100 10 10 9 10 10 100 8 10 10 8 8 10 10 7 10 10 100 100 10 100 100 10 10 8 10 9 8 9 10 10 10 10 9 10 10 10 10 10 8 10 7 18 10 10 10 10 8 10 10 18 11 7 10 10 10 11 10 18 10 11 18 11 10 10 9 7 10 10 8 10 8 10 7 10 10 100 8 10 10 10 8 8 10 10 10 8 8 10 10 10 7 10 10 10 10 10 11 11 11 11 11 7 10 9 9 9 10 10 100 10 100 10 10 10 9 9 9 9 9 9 8 10 10 10 10 10 11 10 100 10 10 10 7 7 10 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 12 12 12 12 1 12 12 12 12 1 12 12 12 1 1 12 7 12 12 1 1 1 12 12 12 1 12 12 12 12 12 1 12 12 12 12 12 12 1 12 1 12 12 12 1 1 12 12 1 12 7 12 1 12 12 12 12 12 12 12 12 1 12 7 12 12 12 12 12 12 12 12 12 100 12 12 12 12 1 12 1 12 7 12 12 12 1 1 1 12 12 1 1 12 12 12 12 12 12 12 12 12 12 12 12 7 12 12 12 12 12 12 12 12 12 12 1 12 12 12 1 1 12 1 12 12 12 12 12 7 12 12 7 1 12 12 1 12 1 1 15 16 15 16 12 100 12 100 12 7 12 1 12 12 12 1 12 12 7 12 12 1 1 12 12 7 12 12 1 1 12 1 1 12 12 1 12 100 12 1 1 12 1 1 12 12 1 12 12 1 12 12 12 12 12 100 12 12 12 12 12 12 1 12 1 16 15 16 12 12 12 12 12 1 12 12 16 15 16 12 12 1 12 12 12 15 12 7 12 16 15 16 12 12 12 12 12 1 12 12 1 12 1 12 1 12 1 1 12 12 1 1 12 12 100 12 1 1 100 12 12 1 12 12 12 12 12 12 12 12 1 12 12 12 12 12 12 1 1 12 12 12 12 12 12 12 12 12 12 7 12 12 12 12 12 100 12 1 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 1 1 1 1 1 1 1 1 10 1 1 1 1 1 10 1 1 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 12 12 12 12 12 12 1 1 100 1 1 100 1 1
staticfield java/lang/ClassLoader nocerts [Ljava/security/cert/Certificate; 0 [Ljava/security/cert/Certificate;
staticfield java/lang/ClassLoader loadedLibraryNames Ljava/util/Set; java/util/HashSet
staticfield java/lang/ClassLoader $assertionsDisabled Z 1
ciInstanceKlass java/lang/System 1 1 631 10 10 10 10 10 9 7 10 11 10 10 10 100 8 10 10 9 100 8 10 10 8 10 100 10 8 10 10 100 10 10 10 9 9 7 10 10 10 10 10 10 10 100 100 8 10 10 7 10 100 8 10 8 10 100 8 10 100 10 8 10 10 10 8 10 10 10 100 8 10 10 10 100 18 100 9 10 100 10 10 10 10 10 10 10 10 7 7 10 10 100 10 10 100 8 10 9 9 10 10 10 10 8 10 10 8 10 10 8 10 7 9 10 7 9 10 9 7 10 8 10 8 10 10 10 10 10 10 10 10 10 9 100 8 10 8 10 10 8 100 10 10 10 10 100 10 10 10 10 10 8 10 10 10 10 8 10 10 10 7 10 10 10 9 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 12 12 12 12 12 12 1 7 12 100 12 100 12 12 12 1 1 12 12 12 1 1 12 100 12 1 12 1 12 12 12 1 12 100 12 12 12 12 1 12 7 12 12 12 12 12 1 1 1 12 12 1 12 1 1 1 12 1 1 1 1 12 12 7 12 1 12 100 12 7 12 1 1 12 100 12 1 1 15 16 15 16 12 1 12 12 1 12 12 7 12 12 12 12 12 12 1 1 12 12 1 12 7 12 1 1 12 12 12 12 12 12 1 12 12 1 12 12 1 7 12 1 7 12 12 1 12 12 1 12 1 12 1 7 12 12 7 12 12 7 12 12 7 12 12 7 12 12 1 1 12 1 12 1 1 12 12 12 1 12 12 12 100 12 1 12 12 1 12 12 12 1 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 12 12 1 1 100 1 1 100 1 1
staticfield java/lang/System in Ljava/io/InputStream; java/io/PipedInputStream
staticfield java/lang/System out Ljava/io/PrintStream; org/gradle/internal/io/LinePerThreadBufferingOutputStream
staticfield java/lang/System err Ljava/io/PrintStream; org/gradle/internal/io/LinePerThreadBufferingOutputStream
instanceKlass java/lang/Exception
instanceKlass java/lang/Error
ciInstanceKlass java/lang/Throwable 1 1 395 10 9 9 9 9 9 10 9 10 10 100 100 10 8 10 8 10 10 10 100 8 10 10 10 10 8 9 10 100 10 10 100 10 10 11 10 10 10 8 10 10 7 8 8 10 10 8 8 9 10 100 10 11 8 8 10 8 10 8 100 10 9 10 10 100 9 10 10 100 8 10 10 10 10 100 10 10 11 11 11 8 8 10 10 10 9 8 7 10 10 100 8 10 11 8 9 10 11 9 11 100 10 7 10 100 1 1 1 100 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 1 1 1 12 1 100 12 12 1 1 12 7 12 12 1 100 12 12 1 12 12 1 7 12 12 12 12 12 1 12 12 1 1 1 12 12 1 1 12 100 12 1 12 1 1 12 1 12 1 1 12 12 12 100 12 12 12 100 1 1 12 12 100 12 1 100 12 12 12 12 12 1 1 100 12 1 1 12 1 1 12 1 12 100 12 12 12 12 1 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Throwable UNASSIGNED_STACK [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
staticfield java/lang/Throwable SUPPRESSED_SENTINEL Ljava/util/List; java/util/Collections$EmptyList
staticfield java/lang/Throwable EMPTY_THROWABLE_ARRAY [Ljava/lang/Throwable; 0 [Ljava/lang/Throwable;
staticfield java/lang/Throwable $assertionsDisabled Z 1
instanceKlass kotlin/jvm/KotlinReflectionNotSupportedError
instanceKlass java/util/ServiceConfigurationError
instanceKlass com/google/common/util/concurrent/ExecutionError
instanceKlass java/lang/AssertionError
instanceKlass java/lang/VirtualMachineError
instanceKlass java/lang/LinkageError
instanceKlass java/lang/ThreadDeath
ciInstanceKlass java/lang/Error 1 1 40 10 10 10 10 10 100 7 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 1 1
ciInstanceKlass java/lang/ThreadDeath 0 0 21 10 100 100 1 1 1 5 0 1 1 1 1 1 1 1 1 1 12 1 1
instanceKlass java/beans/PropertyVetoException
instanceKlass java/awt/AWTException
instanceKlass java/sql/SQLException
instanceKlass org/apache/maven/settings/building/SettingsBuildingException
instanceKlass com/jcraft/jsch/JSchException
instanceKlass org/gradle/api/internal/attributes/AttributeMergingException
instanceKlass java/lang/CloneNotSupportedException
instanceKlass java/security/GeneralSecurityException
instanceKlass java/util/concurrent/TimeoutException
instanceKlass java/util/concurrent/ExecutionException
instanceKlass java/text/ParseException
instanceKlass java/security/PrivilegedActionException
instanceKlass java/lang/InterruptedException
instanceKlass java/net/URISyntaxException
instanceKlass java/io/IOException
instanceKlass java/lang/ReflectiveOperationException
instanceKlass java/lang/RuntimeException
ciInstanceKlass java/lang/Exception 1 1 40 10 10 10 10 10 100 7 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 1 1
instanceKlass org/gradle/cache/internal/btree/CorruptedCacheException
instanceKlass java/time/DateTimeException
instanceKlass java/nio/file/FileSystemNotFoundException
instanceKlass java/nio/file/FileSystemAlreadyExistsException
instanceKlass org/codehaus/groovy/vmplugin/v9/ClassFindFailedException
instanceKlass org/codehaus/groovy/control/ConfigurationException
instanceKlass org/w3c/dom/DOMException
instanceKlass groovy/lang/StringWriterIOException
instanceKlass java/lang/IllegalCallerException
instanceKlass java/lang/reflect/MalformedParameterizedTypeException
instanceKlass org/gradle/internal/locking/MissingLockStateException
instanceKlass org/gradle/internal/locking/InvalidLockFileException
instanceKlass org/gradle/api/internal/attributes/AttributeMatchException
instanceKlass org/gradle/api/internal/provider/AbstractProperty$PropertyQueryException
instanceKlass org/gradle/cli/CommandLineArgumentException
instanceKlass groovy/lang/GroovyRuntimeException
instanceKlass java/util/ConcurrentModificationException
instanceKlass org/gradle/internal/reflect/NoSuchPropertyException
instanceKlass kotlin/KotlinNothingValueException
instanceKlass org/gradle/internal/snapshot/impl/IsolationException
instanceKlass org/gradle/internal/snapshot/ValueSnapshottingException
instanceKlass org/apache/tools/ant/BuildException
instanceKlass java/io/UncheckedIOException
instanceKlass org/gradle/tooling/internal/protocol/InternalBuildActionFailureException
instanceKlass org/gradle/tooling/internal/protocol/test/InternalTestExecutionException
instanceKlass kotlin/NoWhenBranchMatchedException
instanceKlass org/gradle/internal/typeconversion/TypeConversionException
instanceKlass com/google/common/util/concurrent/UncheckedExecutionException
instanceKlass com/google/common/cache/CacheLoader$InvalidCacheLoadException
instanceKlass org/gradle/internal/work/NoAvailableWorkerLeaseException
instanceKlass org/gradle/launcher/daemon/server/BadlyFormedRequestException
instanceKlass org/gradle/internal/remote/internal/MessageIOException
instanceKlass org/gradle/cache/InsufficientLockModeException
instanceKlass org/gradle/cache/LockTimeoutException
instanceKlass org/gradle/cache/internal/locklistener/GracefullyStoppedException
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistry$EmptyRegistryException
instanceKlass org/gradle/cache/FileIntegrityViolationException
instanceKlass org/gradle/internal/file/FileException
instanceKlass org/gradle/launcher/daemon/server/api/DaemonStoppedException
instanceKlass org/gradle/launcher/daemon/server/api/DaemonUnavailableException
instanceKlass java/lang/TypeNotPresentException
instanceKlass java/util/MissingResourceException
instanceKlass org/gradle/util/internal/GFileUtils$TailReadingException
instanceKlass org/gradle/internal/jvm/JavaHomeException
instanceKlass kotlin/UninitializedPropertyAccessException
instanceKlass java/util/NoSuchElementException
instanceKlass org/gradle/api/reflect/ObjectInstantiationException
instanceKlass org/gradle/api/internal/classpath/UnknownModuleException
instanceKlass org/gradle/internal/nativeintegration/NativeIntegrationException
instanceKlass org/gradle/internal/reflect/NoSuchMethodException
instanceKlass net/rubygrapefruit/platform/NativeException
instanceKlass org/gradle/internal/service/ServiceLookupException
instanceKlass com/esotericsoftware/kryo/KryoException
instanceKlass java/lang/reflect/UndeclaredThrowableException
instanceKlass org/gradle/internal/operations/BuildOperationInvocationException
instanceKlass org/gradle/internal/UncheckedException
instanceKlass org/gradle/api/GradleException
instanceKlass java/lang/UnsupportedOperationException
instanceKlass java/lang/SecurityException
instanceKlass org/gradle/api/UncheckedIOException
instanceKlass org/gradle/internal/service/ServiceLookupException
instanceKlass java/lang/IndexOutOfBoundsException
instanceKlass org/gradle/api/GradleException
instanceKlass org/gradle/api/UncheckedIOException
instanceKlass java/lang/IllegalStateException
instanceKlass org/gradle/api/internal/classpath/UnknownModuleException
instanceKlass java/lang/IllegalArgumentException
instanceKlass java/lang/ArithmeticException
instanceKlass java/lang/NullPointerException
instanceKlass java/lang/IllegalMonitorStateException
instanceKlass java/lang/ArrayStoreException
instanceKlass java/lang/ClassCastException
ciInstanceKlass java/lang/RuntimeException 1 1 40 10 10 10 10 10 100 7 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 1 1
instanceKlass org/codehaus/groovy/reflection/ReflectionUtils$ClassContextHelper
ciInstanceKlass java/lang/SecurityManager 1 1 572 10 9 7 10 100 8 10 10 10 10 100 10 100 10 9 10 10 10 100 8 10 9 9 8 9 100 10 8 10 10 10 100 10 10 100 100 8 10 8 8 8 8 8 8 10 8 8 8 8 8 10 10 8 100 8 10 8 8 8 8 8 10 8 100 8 8 10 8 8 10 100 8 10 10 100 10 10 10 10 10 10 11 18 11 18 11 18 18 11 18 11 9 9 9 9 7 10 10 10 18 18 10 18 10 18 18 8 10 9 11 8 100 10 10 10 9 10 10 8 100 10 9 8 8 100 10 10 10 9 11 10 11 10 7 7 10 10 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 12 12 1 7 12 1 1 12 12 100 12 1 1 12 7 12 12 12 1 1 12 12 1 12 1 1 12 12 12 1 12 1 1 1 12 1 1 1 1 1 1 12 1 1 1 1 1 12 12 1 1 1 1 1 1 1 1 100 12 1 1 1 1 1 1 12 1 1 12 1 12 12 12 7 12 12 7 12 7 12 1 15 16 15 16 12 7 12 16 15 16 12 12 15 16 15 16 12 16 15 16 12 12 12 12 12 12 1 7 12 12 12 15 16 12 15 16 7 12 15 12 12 15 16 15 16 1 12 12 7 12 1 1 12 12 12 12 12 12 1 1 12 1 1 1 12 7 12 12 12 12 12 1 1 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 1 10 1 1 1 1 1 1 10 1 1 1 1 1 10 11 1 1 1 10 1 1 1 1 1 1 10 1 10 1 1 1 11 1 1 10 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 12 7 12 12 12 7 12 12 12 12 12 1 1 100 1 1 1 1 1 1 1 1 100 1 1
staticfield java/lang/SecurityManager packageAccessLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/SecurityManager packageDefinitionLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/SecurityManager nonExportedPkgs Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
ciInstanceKlass java/security/ProtectionDomain 1 1 331 10 9 7 10 9 9 9 10 7 9 9 7 9 9 10 100 10 10 10 10 9 9 10 7 10 100 10 9 8 100 8 10 10 10 10 8 11 8 10 8 8 10 10 10 10 8 10 8 8 10 9 10 9 10 100 100 10 10 7 10 100 10 10 11 11 100 11 10 10 11 11 10 10 10 11 10 8 8 10 7 10 10 7 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 7 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 100 1 1 1 1 1 100 12 12 1 12 12 12 12 12 1 12 12 1 12 12 100 12 100 12 12 12 12 100 12 12 1 1 12 100 12 1 1 1 12 12 12 1 1 12 1 1 12 12 12 12 1 12 1 1 100 12 12 12 12 12 1 1 100 12 1 1 12 12 12 12 1 12 12 12 12 12 12 100 12 12 12 1 1 7 12 1 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/security/ProtectionDomain filePermCompatInPD Z 0
ciInstanceKlass java/security/AccessControlContext 1 1 367 9 9 10 8 10 10 9 9 9 10 7 100 10 11 11 11 11 7 11 10 10 9 10 11 10 7 100 8 10 10 7 10 9 9 9 9 9 9 9 10 9 10 10 8 10 10 10 100 10 10 10 10 8 10 8 10 8 8 10 8 10 8 10 10 10 8 8 100 10 10 100 10 8 10 10 10 8 10 10 10 7 10 10 10 10 10 10 10 10 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 100 12 1 100 12 12 12 12 12 7 12 1 12 12 12 12 12 1 12 12 7 12 100 12 12 12 1 1 1 12 12 1 7 12 12 12 12 12 12 12 12 7 12 12 12 12 1 12 12 100 12 1 12 100 12 1 12 1 100 12 1 1 12 1 12 1 12 12 12 1 1 1 12 12 1 12 1 12 12 1 12 12 12 1 12 12 12 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/net/URLClassLoader
instanceKlass jdk/internal/loader/BuiltinClassLoader
ciInstanceKlass java/security/SecureClassLoader 1 1 127 10 7 10 9 10 10 9 10 10 10 10 10 10 7 10 7 10 7 10 11 7 100 8 10 10 7 7 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 1 12 12 7 12 12 12 12 12 12 12 12 12 1 1 12 1 12 7 12 1 1 1 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/InstantiationException
instanceKlass java/lang/IllegalAccessException
instanceKlass java/lang/reflect/InvocationTargetException
instanceKlass java/lang/NoSuchFieldException
instanceKlass java/lang/NoSuchMethodException
instanceKlass java/lang/ClassNotFoundException
ciInstanceKlass java/lang/ReflectiveOperationException 1 1 34 10 10 10 10 100 7 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 1 1
ciInstanceKlass java/lang/ClassNotFoundException 1 1 37 100 10 10 9 100 7 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 1 1 1
instanceKlass java/lang/ClassFormatError
instanceKlass java/lang/UnsatisfiedLinkError
instanceKlass java/lang/IncompatibleClassChangeError
instanceKlass java/lang/BootstrapMethodError
instanceKlass java/lang/NoClassDefFoundError
ciInstanceKlass java/lang/LinkageError 1 1 31 10 10 10 100 7 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 1 1
ciInstanceKlass java/lang/NoClassDefFoundError 1 1 26 10 10 100 7 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 12 12 1 1
instanceKlass org/codehaus/groovy/runtime/typehandling/GroovyCastException
ciInstanceKlass java/lang/ClassCastException 1 1 26 10 10 100 100 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 12 12 1 1
ciInstanceKlass java/lang/ArrayStoreException 1 1 26 10 10 100 100 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 12 12 1 1
instanceKlass java/lang/InternalError
instanceKlass java/lang/StackOverflowError
instanceKlass java/lang/OutOfMemoryError
ciInstanceKlass java/lang/VirtualMachineError 1 1 34 10 10 10 10 100 100 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 1 1
ciInstanceKlass java/lang/OutOfMemoryError 1 1 26 10 10 100 100 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 12 12 1 1
ciInstanceKlass java/lang/StackOverflowError 1 1 26 10 10 100 100 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 12 12 1 1
ciInstanceKlass java/lang/IllegalMonitorStateException 1 1 26 10 10 100 100 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 12 12 1 1
instanceKlass java/lang/ref/PhantomReference
instanceKlass java/lang/ref/FinalReference
instanceKlass java/lang/ref/WeakReference
instanceKlass java/lang/ref/SoftReference
ciInstanceKlass java/lang/ref/Reference 1 1 159 10 9 10 9 9 7 10 10 9 9 10 10 10 9 9 100 10 10 10 7 10 10 10 7 8 10 7 10 10 10 7 10 10 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 7 1 1 1 1 12 12 12 12 12 1 12 12 12 12 12 12 12 12 12 1 12 12 1 12 12 12 1 1 12 1 12 12 12 1 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Reference processPendingLock Ljava/lang/Object; java/lang/Object
instanceKlass com/sun/beans/util/Cache$Kind$Soft
instanceKlass org/codehaus/groovy/util/ReferenceType$SoftRef
instanceKlass sun/util/locale/provider/LocaleResources$ResourceReference
instanceKlass sun/util/resources/Bundles$BundleReference
instanceKlass sun/util/locale/LocaleObjectCache$CacheEntry
instanceKlass jdk/internal/ref/SoftCleanable
instanceKlass java/lang/invoke/LambdaFormEditor$Transform
ciInstanceKlass java/lang/ref/SoftReference 1 1 47 10 9 9 10 10 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 12 12 12 12 12 1 1 1
instanceKlass com/google/common/collect/MapMakerInternalMap$WeakValueReferenceImpl
instanceKlass com/google/common/collect/MapMakerInternalMap$AbstractWeakKeyEntry
instanceKlass java/beans/WeakIdentityMap$Entry
instanceKlass org/codehaus/groovy/util/ReferenceType$WeakRef
instanceKlass com/google/common/cache/LocalCache$WeakValueReference
instanceKlass java/util/logging/LogManager$LoggerWeakRef
instanceKlass java/util/logging/Level$KnownLevel
instanceKlass sun/nio/ch/FileLockTable$FileLockReference
instanceKlass jdk/internal/jimage/ImageBufferCache$BufferReference
instanceKlass java/lang/ClassValue$Entry
instanceKlass java/lang/WeakPairMap$WeakRefPeer
instanceKlass jdk/internal/ref/WeakCleanable
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet$WeakEntry
instanceKlass java/util/WeakHashMap$Entry
instanceKlass java/lang/ThreadLocal$ThreadLocalMap$Entry
ciInstanceKlass java/lang/ref/WeakReference 1 1 31 10 10 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 1 1
instanceKlass java/lang/ref/Finalizer
ciInstanceKlass java/lang/ref/FinalReference 1 1 36 10 100 8 10 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 1 1 12 1 1 1
instanceKlass jdk/internal/ref/PhantomCleanable
instanceKlass jdk/internal/ref/Cleaner
ciInstanceKlass java/lang/ref/PhantomReference 1 1 30 10 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 1 1
ciInstanceKlass java/lang/ref/Finalizer 1 1 139 9 10 9 9 9 9 7 10 10 7 11 100 10 100 10 10 10 100 10 10 7 10 7 10 10 10 10 7 10 7 10 10 10 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 12 12 12 12 12 12 1 12 12 1 7 12 1 12 1 12 100 12 100 12 1 12 12 1 1 12 12 12 1 12 1 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Finalizer lock Ljava/lang/Object; java/lang/Object
instanceKlass net/rubygrapefruit/platform/internal/jni/AbstractFileEventFunctions$NativeFileWatcher$1
instanceKlass org/gradle/launcher/daemon/server/exec/DaemonConnectionBackedEventConsumer$ForwardEvents
instanceKlass org/gradle/launcher/daemon/server/exec/LogToClient$AsynchronousLogDispatcher
instanceKlass java/util/logging/LogManager$Cleaner
instanceKlass jdk/internal/misc/InnocuousThread
instanceKlass java/lang/ref/Finalizer$FinalizerThread
instanceKlass java/lang/ref/Reference$ReferenceHandler
ciInstanceKlass java/lang/Thread 1 1 591 9 9 10 9 9 100 8 10 3 8 3 10 10 9 9 9 9 7 100 8 10 9 10 10 10 10 10 10 9 10 10 9 10 10 9 10 9 10 9 9 10 10 9 10 9 100 10 7 10 8 10 10 10 10 10 10 9 100 10 10 10 10 100 11 9 10 10 10 9 10 9 10 100 10 10 10 11 10 10 10 7 10 10 10 10 10 10 10 10 10 10 100 8 10 10 10 8 10 8 10 8 8 10 10 100 8 10 9 9 10 10 10 9 10 100 10 11 9 9 10 100 10 11 100 10 10 11 10 100 10 10 10 8 9 10 11 10 11 10 7 7 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 7 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 1 1 12 1 12 12 12 12 12 12 1 1 1 12 7 12 12 12 12 12 100 12 12 12 12 12 12 12 12 12 7 12 12 12 12 100 12 12 12 12 1 1 1 12 12 12 12 12 12 12 1 12 12 12 1 12 100 12 12 12 12 12 12 12 1 12 12 12 12 12 12 1 12 12 12 12 12 12 12 12 12 1 1 12 12 1 12 1 1 1 100 12 100 12 1 12 12 12 12 12 12 1 12 12 12 12 12 1 12 100 12 1 12 12 12 12 1 12 12 7 12 12 12 12 100 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Thread EMPTY_STACK_TRACE [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
ciInstanceKlass java/lang/ThreadGroup 1 1 289 10 9 8 9 7 9 9 10 10 10 10 10 9 10 10 9 10 9 9 10 100 10 10 10 9 10 10 9 10 10 10 10 10 10 10 10 10 10 10 100 10 10 10 7 10 7 10 9 10 8 10 10 10 10 11 100 9 100 10 8 10 10 8 10 10 10 10 8 10 8 10 8 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 1 12 1 12 12 12 12 12 12 12 12 12 12 12 100 12 12 12 7 12 12 7 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 12 12 1 12 12 12 12 1 12 12 12 12 1 12 1 1 12 12 1 12 12 12 100 1 1 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/Hashtable
ciInstanceKlass java/util/Dictionary 1 1 36 10 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 1 1
instanceKlass java/util/Properties
ciInstanceKlass java/util/Hashtable 1 1 488 100 10 9 100 100 10 8 10 10 10 10 10 8 10 9 7 9 7 4 10 9 4 10 11 10 10 9 10 100 10 9 10 9 10 10 3 9 9 3 10 10 10 11 11 11 11 7 11 11 10 10 10 9 9 9 10 100 100 10 10 8 10 10 8 10 8 10 7 10 10 100 10 10 7 10 100 10 10 7 11 11 100 10 10 10 11 100 10 11 11 10 10 10 10 10 10 10 100 10 10 8 10 10 100 11 10 10 10 7 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 100 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 12 12 1 1 1 12 12 12 12 7 12 1 12 12 1 12 1 7 12 12 12 12 12 12 12 12 1 12 12 12 12 12 12 12 12 12 12 12 7 12 12 12 1 12 12 12 12 12 12 12 12 1 1 12 1 12 1 1 7 12 1 12 12 1 12 12 1 1 12 1 12 12 1 100 12 12 1 12 12 12 12 12 12 12 12 100 12 1 12 1 12 100 12 1 100 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/gradle/internal/classpath/Instrumented$DecoratingProperties
instanceKlass java/security/Provider
ciInstanceKlass java/util/Properties 1 1 645 10 100 10 7 10 9 9 9 10 10 8 10 7 10 10 8 10 7 10 10 9 10 7 10 10 10 100 8 10 10 10 10 100 3 10 10 10 8 10 10 10 10 10 100 100 10 10 100 9 10 10 10 100 10 10 10 11 11 11 7 11 11 10 8 10 10 100 10 10 10 9 10 10 100 100 100 10 8 8 10 10 10 7 10 10 10 7 10 10 11 10 8 10 11 8 10 11 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 7 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 7 10 100 10 11 4 11 10 10 11 10 10 10 100 8 10 10 10 100 6 0 10 11 10 10 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 7 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 100 1 1 1 1 1 12 1 12 1 12 12 12 12 7 12 12 1 7 12 1 12 12 1 12 1 12 12 12 12 1 12 12 12 1 1 12 12 12 12 1 12 12 1 12 12 12 12 12 1 1 12 12 1 100 12 12 12 12 1 12 7 12 12 12 1 12 12 12 1 12 12 1 12 100 12 100 12 12 100 12 1 1 1 1 1 12 12 12 1 12 12 1 12 12 7 12 1 12 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 12 1 12 12 12 12 12 100 12 12 1 1 12 100 12 1 12 100 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/Properties UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/Properties hexDigit [C 16
ciInstanceKlass java/lang/Module 1 1 907 10 9 10 9 9 9 10 10 10 10 7 10 10 7 11 7 10 10 9 10 10 8 10 10 10 9 11 9 10 9 10 10 100 100 10 10 8 10 10 10 10 10 10 9 10 10 9 10 10 9 11 7 10 9 9 10 7 7 10 100 8 10 10 10 8 10 10 10 10 8 8 10 10 10 18 10 11 9 11 10 100 8 10 7 10 10 11 11 9 11 10 10 9 10 10 10 10 18 11 10 11 10 11 4 10 7 10 11 7 10 11 7 10 7 8 10 10 7 10 10 7 7 10 9 100 10 11 10 10 10 11 7 10 11 10 11 10 10 10 10 10 10 10 10 18 11 11 18 10 10 10 7 10 10 10 9 7 10 10 10 10 10 10 10 10 10 9 18 10 7 100 8 10 10 10 100 10 100 8 100 10 100 100 3 10 100 10 10 10 100 10 10 100 100 10 8 10 10 10 10 10 10 10 100 10 10 10 100 8 10 10 8 10 8 10 10 10 8 10 7 10 10 10 11 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 7 7 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 100 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 1 7 12 12 1 12 12 7 12 100 12 12 12 1 12 12 12 12 12 12 7 12 12 100 12 12 1 1 12 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 12 12 12 12 1 1 12 1 1 12 12 1 12 12 12 12 1 1 12 12 12 1 15 16 15 16 12 12 12 12 12 7 12 1 1 1 12 12 12 12 12 12 12 12 12 12 7 12 16 15 16 12 12 100 12 12 12 12 12 1 12 1 12 1 7 12 100 1 1 1 12 12 1 12 12 1 1 12 12 1 12 12 12 12 1 12 12 12 12 12 12 12 12 12 12 12 16 15 16 12 12 12 15 16 12 12 12 12 1 12 12 12 12 1 12 12 12 12 12 12 12 16 15 16 12 100 12 1 1 1 12 12 12 1 12 1 1 1 1 1 12 1 12 12 12 1 12 12 1 1 12 1 12 12 100 12 12 12 12 12 1 12 12 1 1 12 100 12 1 12 1 12 12 12 1 1 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 1 1 1 1 1 1 10 1 1 1 1 1 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 12 12 100 12 12 12 1 1 100 1 1 1 100 1 1
staticfield java/lang/Module ALL_UNNAMED_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module ALL_UNNAMED_MODULE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module EVERYONE_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module EVERYONE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module $assertionsDisabled Z 1
instanceKlass com/google/common/reflect/Element
instanceKlass java/lang/reflect/Executable
instanceKlass java/lang/reflect/Field
ciInstanceKlass java/lang/reflect/AccessibleObject 1 1 405 10 9 10 10 10 10 100 10 10 9 100 10 11 7 100 10 7 100 10 10 7 10 10 100 10 100 10 10 10 10 10 10 10 10 10 10 8 100 10 10 8 10 10 8 8 8 8 8 8 100 10 10 9 10 10 10 18 10 10 10 11 100 100 8 10 10 10 8 10 8 10 10 100 8 10 11 10 10 10 10 10 9 100 10 10 9 10 8 10 8 10 9 100 10 7 10 10 7 9 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 12 100 12 12 12 7 12 100 12 12 12 12 1 12 12 1 1 12 1 1 12 12 1 12 12 1 12 1 7 12 12 12 12 12 12 12 12 1 1 12 1 12 12 1 1 1 1 1 1 1 12 12 12 12 12 7 12 1 15 16 15 16 12 12 12 1 1 1 12 12 1 12 1 12 1 1 12 12 12 12 12 12 12 12 12 12 100 12 1 100 12 1 12 12 1 1 1 1 7 12 1 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 12 12 1 1 100 1 1 100 1 1
staticfield java/lang/reflect/AccessibleObject reflectionFactory Ljdk/internal/reflect/ReflectionFactory; jdk/internal/reflect/ReflectionFactory
ciInstanceKlass java/lang/reflect/Field 1 1 433 9 10 10 10 9 10 10 10 10 9 9 9 9 9 9 9 100 8 10 7 10 9 9 10 10 10 10 10 10 100 10 10 10 10 10 10 10 100 10 8 10 10 8 10 10 8 8 10 11 9 10 10 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 10 10 10 10 10 9 10 10 10 10 11 10 100 10 10 9 10 11 10 10 9 10 10 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 100 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 100 1 1 1 1 1 1 1 1 12 12 100 12 100 12 12 12 12 100 12 12 12 12 12 12 12 12 12 1 1 12 1 12 12 12 12 7 12 12 12 12 12 1 12 12 12 12 12 12 1 1 12 12 1 12 12 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 7 12 12 7 12 12 12 1 100 12 7 12 12 7 12 7 12 12 12 100 12 100 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/Parameter 0 0 226 10 9 9 9 9 100 10 10 10 100 10 10 11 10 10 10 10 10 8 8 10 10 10 8 10 8 10 9 10 9 10 10 10 10 10 10 10 10 11 10 100 10 10 10 10 10 9 100 10 11 11 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 12 12 12 12 12 1 12 12 100 12 1 12 12 12 100 12 12 12 12 1 1 12 12 12 1 1 12 12 12 12 12 12 12 12 12 100 12 12 100 12 12 1 100 12 12 12 12 12 12 1 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/reflect/Constructor
instanceKlass java/lang/reflect/Method
ciInstanceKlass java/lang/reflect/Executable 1 1 458 10 10 10 11 10 10 10 10 10 7 8 7 10 10 10 7 8 10 10 10 10 8 8 10 10 100 8 10 8 10 8 11 10 10 11 10 8 8 10 10 100 10 10 10 10 10 10 7 10 10 10 10 10 100 10 100 8 10 10 3 100 8 10 10 10 10 10 8 8 8 9 10 100 8 9 10 10 10 10 10 10 100 10 10 100 10 7 10 10 11 10 10 10 9 10 7 10 10 9 10 10 9 10 9 10 9 7 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 100 100 1 1 1 1 1 1 1 1 1 1 1 1 12 7 12 12 7 12 7 12 12 12 12 12 1 1 1 12 12 1 1 12 7 12 12 12 1 1 12 1 1 12 1 12 1 7 12 12 12 1 1 12 12 1 12 12 7 12 12 12 1 12 12 12 12 100 12 12 1 1 12 12 1 1 12 12 12 12 1 1 1 12 12 1 1 12 12 12 12 12 12 12 12 12 1 100 12 1 7 12 12 12 12 100 12 12 12 12 1 12 12 100 12 100 12 12 12 12 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/Method 1 1 441 9 10 10 9 10 10 10 10 9 9 9 9 9 9 9 9 9 9 9 100 8 10 7 10 9 8 10 10 10 10 10 10 10 7 10 10 10 7 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 7 8 10 100 8 10 10 10 10 10 10 10 11 9 10 10 10 10 11 10 7 10 10 10 10 9 10 10 10 10 10 11 10 7 100 100 10 10 10 100 10 8 10 10 10 10 10 8 10 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 100 7 7 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 7 12 7 12 12 12 12 7 12 12 12 12 12 12 12 12 12 12 12 12 12 1 1 12 1 12 12 1 7 12 7 12 12 12 12 12 12 1 12 12 7 12 12 7 12 12 12 12 12 7 12 12 12 12 12 12 12 1 1 1 1 12 12 12 12 12 12 12 7 12 12 12 12 12 12 12 1 12 12 12 12 12 7 12 12 7 12 7 12 7 12 7 12 7 12 1 1 1 12 12 12 1 1 12 12 12 12 1 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/Constructor 1 1 415 10 10 9 10 10 10 9 10 9 9 9 9 9 9 9 9 100 8 10 7 10 9 10 10 10 10 10 7 100 8 10 10 10 10 10 7 10 7 10 10 10 10 10 10 10 10 10 100 8 10 10 100 8 10 10 10 10 10 10 10 9 10 10 100 8 10 11 10 10 10 9 10 10 10 10 10 10 10 10 10 100 8 10 10 10 10 10 10 10 11 9 10 10 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 12 7 12 12 12 12 7 12 12 12 12 12 12 12 12 12 12 12 1 1 12 1 12 12 7 12 7 12 12 12 12 1 1 1 12 12 12 12 1 7 12 12 7 12 12 100 12 12 12 12 12 1 1 12 1 1 12 12 12 12 12 12 12 12 12 12 1 1 12 12 12 12 12 12 7 12 12 12 12 12 12 12 12 12 1 1 12 12 12 12 100 12 100 12 100 12 100 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/FieldAccessorImpl
instanceKlass jdk/internal/reflect/ConstructorAccessorImpl
instanceKlass jdk/internal/reflect/MethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MagicAccessorImpl 1 1 16 10 100 7 1 1 1 1 1 1 1 1 1 12 1 1
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor1
instanceKlass jdk/internal/reflect/DelegatingMethodAccessorImpl
instanceKlass jdk/internal/reflect/NativeMethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MethodAccessorImpl 1 1 25 10 100 7 100 1 1 1 1 1 1 1 1 1 1 100 100 1 1 12 1 1 1 1 1
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor6
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor5
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor4
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor3
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor2
instanceKlass jdk/internal/reflect/BootstrapConstructorAccessorImpl
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor1
instanceKlass jdk/internal/reflect/DelegatingConstructorAccessorImpl
instanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl
ciInstanceKlass jdk/internal/reflect/ConstructorAccessorImpl 1 1 27 10 100 7 100 1 1 1 1 1 1 1 1 1 1 100 100 100 1 1 12 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/DelegatingClassLoader 1 1 18 10 100 7 1 1 1 1 1 1 1 1 1 1 1 12 1 1
ciInstanceKlass jdk/internal/reflect/ConstantPool 1 1 138 10 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 7 7 8 10 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 1 7 12 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/FieldAccessorImpl 1 1 59 10 100 7 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeQualifiedFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl 1 1 254 10 9 10 10 9 10 9 10 10 9 10 10 10 10 100 10 10 10 8 10 10 100 8 10 8 10 8 10 100 10 10 8 10 8 10 8 10 8 10 8 10 8 10 8 10 8 10 8 10 10 8 8 8 8 8 8 10 8 8 8 10 10 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 7 12 12 7 12 12 12 12 12 12 12 7 12 12 1 12 12 1 12 1 1 12 1 12 1 12 1 12 1 12 1 100 12 1 100 12 1 100 12 1 100 12 1 100 12 1 100 12 1 100 12 1 100 12 12 1 1 1 1 1 1 12 1 1 1 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/reflect/UnsafeFieldAccessorImpl unsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass jdk/internal/reflect/UnsafeQualifiedStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl 1 1 43 10 9 10 9 7 7 8 10 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 7 12 12 1 1 7 12 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/CallerSensitive 0 0 17 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/DelegatingMethodHandle
instanceKlass java/lang/invoke/BoundMethodHandle
instanceKlass java/lang/invoke/DirectMethodHandle
ciInstanceKlass java/lang/invoke/MethodHandle 1 1 489 9 10 10 7 7 10 9 10 10 10 10 10 10 11 10 10 10 9 10 100 100 10 8 10 10 8 10 10 10 10 10 10 10 10 10 10 7 10 10 10 8 10 8 10 10 10 10 8 10 8 10 8 10 9 100 10 9 9 8 10 10 10 10 10 10 10 10 10 10 10 8 10 8 10 10 10 10 10 9 8 10 10 8 10 10 10 10 10 100 8 10 10 9 10 7 10 10 9 10 10 8 9 9 9 10 10 10 10 7 10 8 10 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 7 12 1 1 12 12 12 12 12 7 12 12 12 100 12 12 12 12 12 12 1 1 1 12 12 1 12 12 7 12 12 12 12 12 12 12 7 12 1 12 12 12 1 7 12 1 12 12 12 12 1 12 1 12 1 100 12 12 1 100 12 100 1 12 12 12 12 12 12 12 12 12 12 12 1 12 1 12 12 12 12 12 12 1 12 12 1 12 12 7 12 12 1 1 12 12 12 1 100 12 12 12 12 12 1 12 12 12 7 12 12 12 12 1 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MethodHandle FORM_OFFSET J 20
staticfield java/lang/invoke/MethodHandle $assertionsDisabled Z 1
instanceKlass java/lang/invoke/DirectMethodHandle$Interface
instanceKlass java/lang/invoke/DirectMethodHandle$Constructor
instanceKlass java/lang/invoke/DirectMethodHandle$Special
instanceKlass java/lang/invoke/DirectMethodHandle$Accessor
ciInstanceKlass java/lang/invoke/DirectMethodHandle 1 1 922 7 7 100 7 7 10 10 100 10 10 10 10 10 10 7 7 10 10 10 10 10 10 9 100 10 9 10 10 10 10 10 10 7 10 10 10 8 10 7 10 7 10 10 10 10 10 10 100 10 10 7 10 10 10 10 8 10 10 10 10 10 9 7 10 10 10 100 10 8 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 8 9 8 9 9 8 9 9 8 9 9 8 10 10 7 9 7 10 100 10 10 10 10 7 10 10 10 7 10 10 10 10 10 9 10 9 9 10 10 7 7 7 9 10 10 10 10 9 10 100 10 100 10 10 8 9 9 10 9 10 9 9 10 10 10 10 10 10 10 9 10 10 10 10 10 9 10 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 8 9 9 10 100 10 9 10 7 9 10 10 10 10 10 8 8 8 8 10 9 10 7 10 8 9 10 8 8 8 9 8 8 8 8 8 8 7 8 10 10 8 8 10 10 10 10 7 7 1 1 1 1 1 1 1 100 1 1 1 1 7 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 3 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 12 12 1 12 12 12 12 12 12 1 1 12 12 12 12 12 12 12 1 12 12 12 12 12 12 12 1 12 12 12 1 12 1 12 1 12 12 12 12 12 1 12 12 1 12 12 12 12 12 12 12 12 12 7 12 1 12 7 12 12 1 1 12 12 12 12 12 12 12 12 12 12 12 12 7 12 12 12 12 12 1 12 1 12 12 1 12 12 1 12 12 1 12 12 1 12 1 12 1 12 12 12 12 1 12 12 12 12 7 12 12 12 12 12 12 12 7 12 12 1 1 1 12 12 12 12 12 12 12 1 12 1 12 12 1 12 12 12 12 100 12 12 12 12 12 12 12 12 12 7 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 12 12 12 1 12 7 12 1 12 12 12 12 12 1 1 1 1 12 12 12 1 100 12 12 12 12 1 1 12 12 1 12 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/DirectMethodHandle IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/DirectMethodHandle FT_UNCHECKED_REF I 8
staticfield java/lang/invoke/DirectMethodHandle ACCESSOR_FORMS [Ljava/lang/invoke/LambdaForm; 132 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/DirectMethodHandle ALL_WRAPPERS [Lsun/invoke/util/Wrapper; 10 [Lsun/invoke/util/Wrapper;
staticfield java/lang/invoke/DirectMethodHandle NFS [Ljava/lang/invoke/LambdaForm$NamedFunction; 12 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/DirectMethodHandle OBJ_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle LONG_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle $assertionsDisabled Z 1
instanceKlass java/lang/invoke/VarHandleLongs$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleObjects$Array
instanceKlass java/lang/invoke/VarHandleObjects$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleInts$FieldInstanceReadOnly
ciInstanceKlass java/lang/invoke/VarHandle 1 1 298 10 9 100 10 9 10 10 10 9 10 10 9 9 10 10 10 10 10 10 10 9 100 10 9 10 10 7 7 10 10 10 9 10 9 10 10 10 100 10 9 9 10 10 10 10 10 10 10 7 10 10 9 8 10 7 10 7 100 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 12 12 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 100 12 12 1 12 12 12 1 1 12 100 12 12 12 12 12 12 12 12 1 7 12 12 7 12 12 12 12 12 12 12 1 7 12 12 12 1 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/VarHandle AIOOBE_SUPPLIER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$1
staticfield java/lang/invoke/VarHandle VFORM_OFFSET J 12
staticfield java/lang/invoke/VarHandle $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MemberName 1 1 747 7 7 100 9 10 9 10 10 10 10 10 10 10 9 10 100 100 10 8 10 10 10 10 9 8 10 7 7 10 10 7 7 7 10 9 100 8 10 10 10 10 10 10 10 10 10 10 8 8 8 10 10 9 3 10 10 10 10 10 10 10 10 10 7 8 10 10 8 9 8 9 10 8 10 10 10 10 10 100 10 10 8 10 10 8 10 10 7 10 10 8 8 100 10 10 100 10 10 10 10 10 10 10 10 10 3 10 3 10 3 3 3 3 3 3 100 10 10 10 3 9 10 3 10 10 10 10 10 10 10 10 10 10 10 10 10 10 100 10 10 10 100 10 10 10 10 10 10 8 10 10 10 10 10 10 10 10 10 10 10 10 10 100 10 100 8 10 7 10 10 10 10 10 8 8 8 8 10 10 10 8 8 10 8 10 10 10 8 8 10 10 8 10 8 10 10 10 8 8 8 100 10 8 8 8 8 10 100 100 100 10 100 10 100 10 9 10 100 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 7 1 1 12 12 12 12 12 12 12 12 12 12 100 12 12 1 1 12 1 12 12 12 12 12 1 100 12 1 1 12 1 12 12 1 1 12 12 12 12 12 12 12 12 12 12 1 1 1 100 12 12 12 12 12 12 12 12 12 12 12 1 12 12 100 100 12 1 12 12 12 12 12 1 12 12 1 12 12 1 12 12 1 12 12 1 1 1 12 100 12 1 12 12 12 12 12 12 12 12 12 12 100 1 12 7 12 12 12 12 12 7 12 12 12 12 12 12 12 12 12 1 12 1 12 12 12 12 12 12 12 12 12 12 12 12 12 1 12 1 1 1 12 12 12 12 12 1 1 1 1 12 12 12 1 1 12 1 12 12 1 1 12 1 12 1 12 12 12 1 1 1 1 1 1 1 1 12 1 1 1 1 1 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MemberName $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/ResolvedMethodName 1 1 16 10 100 100 1 1 1 1 1 1 1 1 1 12 1 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives 1 1 660 100 10 9 10 100 10 10 10 10 8 8 8 8 8 8 8 8 8 8 7 10 7 10 10 7 10 10 8 10 8 10 8 10 9 8 10 100 10 100 100 8 7 7 10 10 7 9 10 10 10 7 10 10 10 10 10 9 8 10 8 10 8 8 8 100 10 8 10 10 10 100 8 10 7 8 10 8 8 8 8 8 10 10 10 10 10 7 10 100 100 10 10 8 8 10 10 10 8 10 8 8 10 10 100 10 7 9 10 10 10 9 10 9 9 10 10 10 7 7 10 10 10 10 10 8 10 10 10 10 10 10 100 8 10 9 10 10 100 10 10 100 100 10 10 100 100 10 100 10 10 10 10 10 10 10 10 10 10 10 10 8 100 10 10 10 10 10 10 7 10 10 10 10 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 1 12 12 12 1 1 1 1 1 1 1 1 1 1 1 12 1 12 12 1 12 1 12 1 12 1 12 100 12 1 100 12 1 12 1 1 1 1 1 12 1 7 12 12 12 12 1 12 7 12 12 12 12 12 1 12 1 12 1 1 1 1 12 1 12 12 100 12 1 100 12 1 1 12 1 1 1 1 1 12 12 12 12 12 1 12 1 1 12 12 1 1 12 12 1 100 12 1 1 12 12 1 12 1 12 7 12 12 12 12 12 12 12 12 12 1 1 12 12 12 7 12 12 1 12 12 12 12 7 12 12 1 1 12 12 12 1 12 12 1 1 1 1 1 12 12 12 12 12 12 12 12 12 1 1 12 12 12 12 12 12 1 12 12 12 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MethodHandleNatives $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/LambdaForm 1 1 1075 100 9 10 10 9 9 10 100 10 9 10 9 10 7 9 9 9 9 10 7 10 7 10 10 10 10 10 10 9 100 10 9 10 10 10 10 10 7 10 10 8 10 10 10 7 10 10 7 10 10 9 9 9 10 9 10 10 100 10 9 10 10 100 10 10 10 10 10 10 10 8 10 10 8 8 9 9 9 10 10 10 9 10 10 10 10 10 10 9 10 8 8 8 8 8 8 8 8 10 9 7 10 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 100 8 10 10 10 10 8 10 8 8 10 9 10 10 100 10 10 10 10 9 8 10 10 10 10 10 9 8 10 100 10 10 9 9 8 10 10 100 100 10 10 8 8 100 8 10 10 10 8 8 9 10 10 8 8 8 100 8 100 8 100 8 10 8 9 10 10 9 10 10 10 10 10 10 10 10 10 10 8 100 10 10 9 10 8 8 100 8 8 8 8 8 8 8 8 10 10 10 10 8 8 8 10 8 10 8 8 8 8 8 10 10 10 10 10 10 10 10 10 10 10 9 8 10 9 10 9 9 9 9 7 10 9 10 10 7 8 10 9 7 10 8 100 10 9 9 10 7 10 10 10 9 10 10 10 9 10 10 10 9 10 9 7 9 10 9 10 100 10 7 9 100 1 1 100 1 100 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 1 3 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 12 12 12 12 12 12 1 12 12 12 7 12 12 12 12 12 12 1 12 1 12 12 100 12 100 12 12 12 12 1 12 12 12 12 12 12 12 1 12 1 12 12 12 1 12 12 1 12 12 12 12 12 12 12 12 12 1 12 12 12 12 1 12 12 12 12 12 12 12 1 12 12 1 1 12 12 12 12 7 12 12 12 7 12 12 12 12 12 12 12 1 1 1 1 1 1 1 1 12 12 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 1 12 12 12 1 12 1 1 12 12 12 12 1 12 12 7 12 12 12 1 100 12 12 12 12 12 12 1 12 12 7 12 12 1 12 12 1 1 12 1 1 1 1 12 12 12 1 1 12 12 12 1 1 1 1 1 1 1 1 1 12 1 12 12 12 12 12 12 12 12 12 12 12 12 1 12 12 12 12 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 1 1 1 12 1 12 1 1 1 1 1 12 12 12 7 12 12 12 12 12 12 12 12 12 1 12 12 12 12 12 12 12 1 7 12 12 12 12 1 1 12 12 1 12 1 1 12 12 12 12 1 12 7 12 12 7 12 12 12 12 12 12 12 12 12 12 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/LambdaForm COMPILE_THRESHOLD I 0
staticfield java/lang/invoke/LambdaForm INTERNED_ARGUMENTS [[Ljava/lang/invoke/LambdaForm$Name; 5 [[Ljava/lang/invoke/LambdaForm$Name;
staticfield java/lang/invoke/LambdaForm IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/LambdaForm LF_identity [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm LF_zero [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm NF_identity [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm NF_zero [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm createFormsLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/invoke/LambdaForm DEBUG_NAME_COUNTERS Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm DEBUG_NAMES Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm TRACE_INTERPRETER Z 0
staticfield java/lang/invoke/LambdaForm $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MethodType 1 1 674 7 10 9 9 9 10 9 8 10 10 9 9 10 100 10 8 10 10 10 100 8 10 100 10 10 10 10 11 9 11 7 7 10 10 9 10 10 10 10 10 10 9 7 10 7 10 10 10 10 10 10 10 10 10 8 8 10 9 100 10 10 10 10 10 10 10 10 10 8 10 10 10 10 10 10 10 10 10 9 10 10 10 10 9 7 10 10 10 10 10 10 10 10 100 8 8 8 10 10 10 10 11 11 10 9 10 10 10 10 10 10 10 10 10 10 10 10 9 7 10 10 10 10 10 10 10 8 10 11 9 10 10 10 10 10 9 9 10 9 10 10 100 10 7 10 7 7 9 100 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 100 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 100 1 12 12 12 12 7 12 7 12 1 100 12 12 7 7 12 1 1 12 12 12 1 1 12 1 12 12 12 12 12 12 1 7 12 12 12 12 12 12 7 12 12 12 12 1 12 1 12 12 7 12 12 12 12 12 12 12 1 1 12 12 1 12 12 12 12 100 12 12 12 1 12 7 12 12 12 12 12 12 12 12 12 12 12 12 12 1 12 7 12 12 7 12 12 12 1 1 1 1 12 12 12 12 12 100 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 12 12 12 12 12 12 12 1 7 12 12 12 12 12 100 12 12 12 12 12 100 12 12 100 12 12 12 1 1 1 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MethodType internTable Ljava/lang/invoke/MethodType$ConcurrentWeakInternSet; java/lang/invoke/MethodType$ConcurrentWeakInternSet
staticfield java/lang/invoke/MethodType NO_PTYPES [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType objectOnlyTypes [Ljava/lang/invoke/MethodType; 20 [Ljava/lang/invoke/MethodType;
staticfield java/lang/invoke/MethodType METHOD_HANDLE_ARRAY [Ljava/lang/Class; 1 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/invoke/MethodType $assertionsDisabled Z 1
ciInstanceKlass java/lang/BootstrapMethodError 0 0 45 10 10 10 10 10 100 100 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 12 12 12 12 12 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/VolatileCallSite
instanceKlass java/lang/invoke/MutableCallSite
instanceKlass java/lang/invoke/ConstantCallSite
ciInstanceKlass java/lang/invoke/CallSite 1 1 299 10 10 9 10 9 10 10 100 7 10 7 10 10 10 100 100 10 10 10 8 10 10 10 10 10 10 10 10 9 9 7 8 10 10 100 10 9 8 100 10 10 100 8 10 10 10 100 10 10 10 10 10 9 9 8 10 9 100 10 10 10 10 10 10 100 8 10 10 100 100 100 8 10 10 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 7 1 1 1 1 1 1 12 12 12 12 12 12 12 1 1 12 1 12 12 12 1 1 12 12 1 12 12 12 12 12 100 12 12 12 100 12 1 12 12 1 100 12 12 12 12 1 1 12 12 1 12 12 12 12 12 12 12 100 12 12 1 100 12 12 12 12 7 12 1 1 12 1 1 1 1 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/CallSite $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext 1 1 49 10 7 10 10 10 10 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 12 1 7 12 7 12 12 1 1 1 1 1 1 1 1 100 1 1 1 1 1
ciInstanceKlass java/lang/invoke/ConstantCallSite 1 1 49 10 9 10 100 10 9 100 10 10 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 12 12 12 1 12 12 1 12 1 1 1 1
ciInstanceKlass java/lang/invoke/MutableCallSite 0 0 67 10 10 9 10 10 10 9 10 10 100 10 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 12 12 12 12 12 12 12 12 100 12 1 12 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/VolatileCallSite 0 0 41 10 10 10 10 10 10 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 1 1 1 1 1 1 1
ciInstanceKlass java/lang/AssertionStatusDirectives 0 0 24 10 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 1 1
instanceKlass java/lang/StringBuilder
instanceKlass java/lang/StringBuffer
ciInstanceKlass java/lang/AbstractStringBuilder 1 1 522 7 7 10 9 9 9 9 10 9 10 10 10 10 10 10 10 10 7 3 10 3 100 10 10 100 10 10 10 10 10 10 10 100 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 10 10 8 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 18 100 10 18 10 10 10 11 10 10 10 100 10 8 10 10 8 8 10 10 10 10 100 10 100 10 100 10 7 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 7 1 1 1 100 1 1 1 1 1 1 1 1 1 12 12 12 12 12 7 12 12 12 7 12 12 12 12 12 7 12 1 12 1 12 1 12 12 12 12 12 12 1 12 100 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 12 12 12 12 12 12 7 12 12 12 100 12 12 12 12 12 12 12 12 12 7 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 15 16 15 16 12 1 100 12 15 12 12 12 12 12 12 1 1 12 12 1 1 12 12 12 1 1 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 1 10 1 1 1 1 1 10 1 1 1 1 1 1 1 1 1 1 100 12 12 12 1 1 100 1 1 100 1 1
staticfield java/lang/AbstractStringBuilder EMPTYVALUE [B 0
ciInstanceKlass java/lang/StringBuffer 1 1 466 10 10 10 11 10 10 10 9 10 10 10 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 9 10 10 100 10 10 10 10 10 8 10 8 10 8 10 10 10 10 7 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 7 10 7 10 9 9 9 7 100 100 100 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 7 12 100 1 12 100 12 12 1 12 1 12 1 12 12 100 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 12 1 12 7 12 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/StringBuffer serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/StringBuilder 1 1 403 10 10 10 11 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 9 9 10 10 10 10 10 10 10 10 10 10 10 100 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 7 10 7 100 100 100 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 7 12 7 100 12 12 12 12 12 100 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/misc/Unsafe 1 1 1165 10 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 100 10 10 10 5 0 10 10 10 10 10 10 10 10 10 100 10 10 10 10 10 10 10 10 10 10 10 10 10 10 5 0 5 0 5 0 10 10 10 100 10 10 10 10 10 10 10 10 10 100 10 10 10 10 8 10 8 8 10 9 9 9 9 9 9 9 9 10 10 10 10 5 0 5 0 9 10 10 10 10 10 8 3 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 100 10 100 10 9 5 0 10 5 0 10 5 0 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 5 0 5 0 5 0 10 10 10 10 10 7 10 7 10 9 7 9 7 9 7 9 7 9 7 9 7 9 7 9 7 9 10 9 9 9 9 9 9 9 9 9 10 10 10 7 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 7 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 12 12 12 12 12 12 12 12 12 12 12 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 12 12 12 12 12 12 12 12 1 12 12 12 1 12 1 1 12 7 12 100 7 100 100 100 100 12 12 12 12 12 12 12 12 12 12 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 1 12 12 12 1 12 1 12 1 12 1 12 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/Unsafe theUnsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_INT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_INT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ADDRESS_SIZE I 8
staticfield jdk/internal/misc/Unsafe BE Z 0
staticfield jdk/internal/misc/Unsafe unalignedAccess Z 1
ciInstanceKlass jdk/internal/module/Modules 1 1 483 10 9 11 11 11 11 11 11 11 11 10 10 18 10 100 10 10 10 10 11 10 10 10 10 9 10 10 10 100 100 11 10 11 10 10 10 10 10 11 18 11 10 11 100 11 11 11 10 10 18 11 18 11 10 18 18 10 9 11 100 10 11 11 7 11 10 100 10 10 10 11 10 100 10 18 10 100 8 10 10 18 11 11 10 10 10 18 10 10 7 10 10 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 100 100 100 100 100 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 12 12 7 12 12 12 12 12 12 12 12 12 100 12 1 15 16 15 16 12 100 12 1 100 12 100 12 12 12 12 12 12 12 12 12 100 12 1 1 12 100 12 12 12 12 100 12 12 12 12 16 15 16 12 12 100 12 100 12 1 12 12 12 12 100 12 16 15 16 12 12 16 15 16 12 12 12 15 16 15 16 12 12 12 1 12 12 1 12 1 12 12 12 12 12 1 15 16 12 12 1 1 12 12 15 16 12 12 12 15 12 1 12 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 1 1 1 1 1 1 10 1 1 1 1 1 1 10 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 10 1 1 1 1 1 10 1 1 10 1 1 1 1 1 100 12 12 12 12 12 12 12 12 12 1 1 100 1 1 1 1 1 100 1 1
staticfield jdk/internal/module/Modules JLA Ljdk/internal/misc/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/module/Modules $assertionsDisabled Z 1
instanceKlass org/gradle/internal/io/StreamByteBuffer$StreamByteBufferInputStream
instanceKlass jdk/nio/zipfs/ZipFileSystem$EntryInputStream
instanceKlass com/google/common/io/BaseEncoding$StandardBaseEncoding$2
instanceKlass java/io/ObjectInputStream
instanceKlass org/apache/tools/ant/util/FileUtils$1
instanceKlass org/gradle/util/internal/BulkReadInputStream
instanceKlass java/io/PipedInputStream
instanceKlass org/gradle/internal/remote/internal/inet/SocketConnection$SocketInputStream
instanceKlass org/gradle/internal/io/RandomAccessFileInputStream
instanceKlass com/esotericsoftware/kryo/io/Input
instanceKlass org/gradle/internal/serialize/kryo/KryoBackedDecoder$1
instanceKlass org/gradle/internal/serialize/AbstractDecoder$DecoderStream
instanceKlass org/gradle/internal/stream/EncodedStream$EncodedInput
instanceKlass java/util/zip/ZipFile$ZipFileInputStream
instanceKlass java/io/FilterInputStream
instanceKlass java/io/FileInputStream
instanceKlass java/io/ByteArrayInputStream
ciInstanceKlass java/io/InputStream 1 1 170 100 10 100 10 10 10 10 7 3 10 100 8 10 7 10 3 100 8 10 7 10 11 10 11 11 11 7 10 5 0 10 8 10 8 10 10 7 100 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 1 12 7 12 12 1 12 1 1 12 1 7 12 1 1 1 12 100 12 12 12 12 7 12 12 1 12 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/ByteArrayInputStream 1 1 96 10 9 9 9 9 10 10 10 10 10 10 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 7 12 7 12 7 12 100 12 12 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/net/URL 1 1 744 10 10 10 9 9 10 10 10 9 10 8 10 100 10 10 8 10 9 100 8 10 10 8 9 10 9 10 10 9 9 8 9 10 8 9 10 10 8 10 7 10 10 10 10 10 8 10 10 8 9 8 10 10 100 10 10 10 100 8 10 10 8 10 10 10 10 10 10 8 10 7 10 10 10 9 10 9 10 10 100 100 10 10 10 10 10 10 7 10 10 10 100 10 10 8 9 10 10 9 10 100 10 10 10 10 10 10 10 10 10 10 10 9 9 100 8 10 10 9 10 8 10 8 10 10 8 8 10 100 10 10 10 7 100 10 9 10 8 10 100 10 10 8 9 10 10 10 10 10 11 10 10 9 10 10 10 8 10 7 100 10 8 8 10 8 8 8 100 10 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 100 8 10 10 10 10 10 8 7 10 7 10 7 10 7 7 10 9 9 7 10 10 100 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 100 12 12 12 12 12 1 12 1 12 1 12 12 1 1 12 12 1 12 12 12 12 12 12 12 1 12 12 1 12 12 7 12 1 12 1 12 12 12 12 12 1 12 12 1 12 1 12 12 1 12 12 12 1 1 12 1 12 12 12 12 12 12 1 12 1 7 12 12 100 12 12 12 12 100 12 1 1 12 12 12 12 12 12 1 12 1 12 12 1 12 100 12 12 100 12 12 1 12 12 12 12 12 12 12 7 12 12 12 12 12 1 1 12 12 12 1 7 12 1 12 12 1 1 12 1 100 12 12 12 1 1 12 12 1 12 1 100 12 100 12 12 12 12 12 7 12 12 12 12 12 12 100 12 12 12 1 1 12 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 1 12 12 12 1 1 1 1 1 1 12 7 12 12 1 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/net/URL defaultFactory Ljava/net/URLStreamHandlerFactory; java/net/URL$DefaultFactory
staticfield java/net/URL streamHandlerLock Ljava/lang/Object; java/lang/Object
staticfield java/net/URL serialPersistentFields [Ljava/io/ObjectStreamField; 7 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/util/jar/Manifest 1 1 301 10 7 10 9 7 10 9 9 10 10 10 10 10 11 11 10 10 100 100 10 8 10 10 10 10 11 100 10 10 11 11 11 11 100 100 8 10 11 7 9 10 10 10 10 8 10 10 11 10 10 10 8 10 7 10 10 10 100 8 10 10 8 10 10 10 10 11 10 10 10 7 10 11 10 11 10 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 1 12 1 12 12 12 12 12 12 12 7 12 12 100 12 1 1 1 12 12 12 12 1 12 12 12 100 12 12 12 1 1 1 12 1 7 12 12 12 12 12 1 12 12 12 12 12 1 12 1 12 12 12 1 1 12 1 12 100 12 12 12 12 12 7 12 12 1 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders 1 1 143 10 9 9 9 7 11 100 11 11 10 10 100 100 10 9 8 10 7 10 7 10 10 7 10 8 10 8 8 7 10 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 1 100 12 1 12 12 100 12 100 12 1 1 7 12 12 1 7 12 1 12 1 12 12 1 12 1 7 12 1 1 1 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/loader/ClassLoaders JLA Ljdk/internal/misc/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/loader/ClassLoaders BOOT_LOADER Ljdk/internal/loader/ClassLoaders$BootClassLoader; jdk/internal/loader/ClassLoaders$BootClassLoader
staticfield jdk/internal/loader/ClassLoaders PLATFORM_LOADER Ljdk/internal/loader/ClassLoaders$PlatformClassLoader; jdk/internal/loader/ClassLoaders$PlatformClassLoader
staticfield jdk/internal/loader/ClassLoaders APP_LOADER Ljdk/internal/loader/ClassLoaders$AppClassLoader; jdk/internal/loader/ClassLoaders$AppClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$BootClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/loader/BuiltinClassLoader 1 1 717 10 10 9 9 7 10 9 9 10 10 11 100 100 10 10 8 10 10 7 10 10 11 11 11 7 9 8 8 10 10 9 11 7 10 10 10 10 10 10 10 11 10 100 10 10 10 100 8 10 10 8 10 10 11 11 7 7 10 11 11 10 7 10 10 7 7 10 7 7 10 10 100 10 11 100 100 10 10 100 100 10 10 18 10 10 18 100 10 7 10 10 10 10 10 9 100 10 10 10 10 10 10 10 10 10 10 18 7 10 10 10 10 100 10 7 10 10 10 11 7 10 7 100 10 10 11 10 10 10 10 10 10 10 10 8 10 10 10 100 8 8 10 10 8 8 10 11 9 10 9 9 9 9 9 9 10 8 10 7 10 10 7 7 10 11 10 10 10 100 10 10 10 7 10 10 8 10 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 7 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 7 12 12 12 12 1 12 12 12 12 12 12 1 1 12 1 12 12 1 12 12 7 12 12 12 1 12 1 1 12 7 12 12 12 1 12 12 12 7 12 12 12 12 12 7 12 1 7 12 12 1 1 12 1 12 12 12 12 1 1 12 12 1 12 12 1 1 12 1 1 12 7 12 1 12 12 1 1 12 12 1 1 12 12 1 15 16 15 16 12 12 12 15 16 1 7 12 1 12 12 12 12 12 1 12 12 12 12 12 12 12 12 12 15 16 12 1 12 12 12 12 1 12 100 1 1 12 12 12 12 1 12 1 12 12 12 12 12 12 12 12 12 12 12 1 12 12 12 1 1 1 12 1 1 7 12 7 12 7 12 12 12 12 12 12 12 12 12 1 12 1 12 12 1 1 12 12 12 12 1 12 12 1 12 12 1 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 10 1 1 1 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 12 12 12 12 1 1 100 1 1 100 1 1
staticfield jdk/internal/loader/BuiltinClassLoader packageToModule Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
staticfield jdk/internal/loader/BuiltinClassLoader $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader 1 1 134 8 10 9 10 10 10 10 10 10 7 8 10 10 10 9 11 10 10 100 10 7 7 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 12 12 7 12 100 12 12 12 12 12 1 1 12 7 12 100 12 12 100 12 12 7 12 1 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader 1 1 57 8 10 9 11 10 100 10 100 7 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 12 12 100 12 7 12 1 12 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/security/CodeSource 1 1 402 10 9 9 9 9 10 9 10 100 10 100 10 7 10 10 10 100 10 10 10 10 10 100 10 10 10 10 10 10 10 10 10 10 10 10 10 8 10 10 10 10 8 10 10 100 10 10 8 10 10 10 8 8 9 100 8 10 10 8 10 8 8 8 10 10 10 10 10 10 100 100 10 10 10 10 10 100 10 10 8 10 10 10 100 10 100 100 8 8 10 10 10 100 10 10 11 10 10 11 10 10 8 100 10 10 100 10 11 11 7 100 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 1 1 1 1 1 1 1 1 1 12 12 12 12 12 7 12 12 100 12 100 12 1 12 12 100 1 12 100 12 12 12 1 12 100 100 12 100 12 12 12 12 12 12 1 12 12 12 12 1 12 1 12 1 12 12 12 1 1 12 1 1 12 12 1 12 1 1 1 12 12 12 12 12 12 1 1 12 12 12 12 12 1 12 1 12 12 12 1 12 1 1 1 1 12 100 12 1 12 12 12 12 12 100 1 1 12 12 1 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackTraceElement 0 0 224 10 10 9 9 9 9 8 10 100 9 8 9 9 9 8 10 10 100 10 10 8 10 10 8 8 8 10 8 8 10 8 8 100 10 10 10 10 9 10 10 100 10 10 10 10 10 10 10 10 10 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 1 100 12 1 12 1 12 12 12 1 12 12 1 12 1 12 12 1 1 1 12 1 1 12 1 1 1 12 12 12 12 12 12 12 1 12 100 12 100 12 12 12 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/nio/IntBuffer
instanceKlass java/nio/LongBuffer
instanceKlass java/nio/CharBuffer
instanceKlass java/nio/ByteBuffer
ciInstanceKlass java/nio/Buffer 1 1 195 100 10 9 9 10 9 10 10 100 100 10 8 10 10 8 8 10 10 8 9 100 8 10 8 8 9 10 8 8 8 10 8 8 8 100 10 100 10 100 10 100 10 7 10 10 9 7 10 10 7 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 1 1 1 12 12 1 1 12 12 1 12 1 1 12 1 1 12 12 1 1 1 12 1 1 1 1 1 1 1 1 7 12 7 12 12 1 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/nio/Buffer UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/nio/Buffer $assertionsDisabled Z 1
ciInstanceKlass java/lang/StackWalker 0 0 235 9 10 100 10 10 11 10 10 100 10 100 8 10 10 10 10 9 9 9 9 10 9 10 11 100 8 10 10 9 10 10 10 18 100 8 10 10 10 9 11 10 100 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 100 12 1 100 12 12 100 12 12 12 1 12 1 1 12 12 12 12 12 12 12 12 12 12 100 12 12 1 1 12 12 12 100 12 100 12 1 15 16 15 16 12 1 1 12 100 12 12 100 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 10 1 1 1 1 1 1 1 1 1 1 1 1 100 12 12 1 1 100 1 1 100 1 1
ciInstanceKlass java/lang/StackStreamFactory$AbstractStackWalker 1 0 306 100 100 3 10 10 10 9 10 9 9 9 9 9 10 10 9 10 10 9 9 100 10 8 10 10 8 10 10 100 8 10 8 10 9 10 9 8 5 0 8 8 9 10 10 10 9 10 10 10 10 10 10 8 10 10 10 10 8 100 10 10 10 10 10 10 9 8 10 10 10 10 10 10 10 10 10 10 8 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 100 12 12 12 12 12 12 12 100 12 100 12 12 12 12 12 12 100 12 1 1 12 12 1 12 100 12 1 1 12 1 12 100 12 12 12 1 1 1 12 12 12 12 12 12 12 12 12 12 12 1 12 12 12 12 1 1 12 12 12 12 12 100 12 12 1 12 12 12 12 12 12 12 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/LiveStackFrameInfo
ciInstanceKlass java/lang/StackFrameInfo 0 0 132 10 9 9 9 11 9 11 10 10 10 11 11 11 10 9 10 10 10 11 10 9 10 100 8 10 10 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 12 100 12 12 100 12 12 12 12 100 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 1 12 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/LiveStackFrameInfo 0 0 97 10 9 9 9 9 9 100 10 10 8 10 100 8 8 8 10 100 10 100 10 100 100 100 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 1 12 12 1 12 1 1 1 1 1 12 1 12 1 1 1 1 1 1 1 1 1
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer
ciInstanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer 1 1 32 10 9 7 7 100 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 1 1 1
ciInstanceKlass java/lang/Boolean 1 1 128 10 9 10 10 8 10 9 9 8 10 7 10 10 100 100 10 10 8 10 9 7 100 100 1 1 1 1 1 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 1 12 12 12 1 12 1 12 7 12 1 1 12 12 1 7 12 12 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Boolean TRUE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean FALSE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Character 1 1 550 7 100 10 9 9 10 10 10 10 10 3 3 3 3 3 10 10 3 11 11 10 10 100 10 10 3 10 10 10 100 8 7 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 5 0 10 10 10 10 10 10 10 10 10 10 9 100 10 10 10 3 10 10 10 100 10 10 10 10 8 10 9 10 10 10 10 10 10 10 10 10 100 8 10 10 8 10 9 100 100 7 1 1 100 1 100 1 100 1 1 1 1 3 1 3 1 1 3 1 3 1 1 1 1 1 1 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 3 1 1 3 1 1 1 1 1 3 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 7 12 12 12 12 1 12 12 12 12 1 1 1 100 12 12 12 12 12 12 12 12 7 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 12 12 100 12 12 12 1 12 12 12 1 12 100 12 12 12 12 12 12 12 1 1 12 7 12 1 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Character TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Character $assertionsDisabled Z 1
instanceKlass com/google/common/cache/Striped64
instanceKlass java/math/BigDecimal
instanceKlass java/util/concurrent/atomic/Striped64
instanceKlass java/math/BigInteger
instanceKlass java/util/concurrent/atomic/AtomicLong
instanceKlass java/util/concurrent/atomic/AtomicInteger
instanceKlass java/lang/Long
instanceKlass java/lang/Integer
instanceKlass java/lang/Short
instanceKlass java/lang/Byte
instanceKlass java/lang/Double
instanceKlass java/lang/Float
ciInstanceKlass java/lang/Number 1 1 37 10 10 100 7 100 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 1 1 1
ciInstanceKlass java/lang/Float 1 1 192 7 100 10 10 4 100 10 10 8 8 10 10 10 10 4 4 4 10 9 10 10 10 10 10 10 3 10 10 10 10 8 10 9 7 100 1 1 1 1 1 4 1 1 1 4 1 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 12 100 12 1 12 12 1 1 100 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 7 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Float TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Double 1 1 254 7 100 10 10 10 100 10 10 6 0 8 10 8 10 8 6 0 10 100 5 0 5 0 8 8 10 10 8 10 8 8 8 10 10 10 10 10 10 10 10 6 0 6 0 6 0 10 9 10 10 10 10 5 0 10 10 10 10 8 10 9 7 100 1 1 1 1 1 6 0 1 1 1 6 0 1 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 12 12 12 1 12 100 12 1 12 1 12 1 12 1 1 1 100 12 12 1 12 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 7 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Double TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Byte 1 1 178 7 10 9 10 100 100 10 8 10 8 10 10 10 10 10 10 10 10 8 8 10 9 10 10 10 10 10 5 0 10 8 10 9 7 100 7 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 12 12 12 1 1 12 1 12 1 12 12 12 12 12 12 12 12 1 1 12 12 12 12 12 12 12 1 7 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Byte TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Short 1 1 186 7 100 10 10 100 100 10 8 10 8 10 10 10 10 10 10 9 10 10 10 8 8 10 9 10 10 10 10 10 3 3 5 0 10 8 10 9 100 100 100 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 12 12 1 1 12 1 12 1 12 12 12 12 12 12 12 12 12 12 1 1 12 12 12 12 12 12 12 1 7 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Short TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Integer 1 1 417 7 100 7 7 10 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 9 9 100 8 10 100 10 8 10 10 8 10 8 10 3 10 3 10 10 10 7 11 100 10 11 10 8 10 8 100 10 10 5 0 8 10 10 10 10 7 9 9 10 10 9 10 10 10 10 100 100 10 10 8 8 10 8 8 8 8 8 8 10 10 10 5 0 3 3 3 3 10 3 10 10 8 10 9 3 3 3 3 3 3 9 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 100 12 12 100 12 12 100 12 12 12 7 12 12 12 12 12 12 12 12 12 1 1 12 1 12 1 12 12 1 12 1 12 12 12 12 7 12 1 1 12 1 12 1 1 12 12 1 12 12 12 12 1 12 12 12 12 12 12 12 7 12 1 1 12 12 1 1 12 1 1 1 1 1 1 12 12 12 12 12 12 1 7 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Integer TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Integer digits [C 36
staticfield java/lang/Integer DigitTens [B 100
staticfield java/lang/Integer DigitOnes [B 100
staticfield java/lang/Integer sizeTable [I 10
ciInstanceKlass java/lang/Long 1 1 482 7 100 7 7 10 9 9 10 10 10 10 10 10 10 10 5 0 5 0 7 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 7 5 0 5 0 9 9 5 0 100 8 10 8 10 8 8 10 5 0 10 5 0 10 10 10 100 11 100 10 11 10 8 10 8 100 10 10 8 8 11 10 10 10 5 0 5 0 9 10 10 8 8 10 8 8 8 8 8 8 10 10 10 10 9 10 10 10 100 100 10 10 10 10 10 10 10 5 0 5 0 5 0 10 5 0 5 0 10 10 10 8 10 9 7 100 7 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 3 1 3 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 7 12 12 100 12 12 12 12 12 1 12 12 12 12 12 12 100 12 12 12 12 12 12 100 12 12 12 12 12 12 12 1 12 12 1 1 12 1 12 1 1 12 12 12 12 100 12 1 1 12 1 12 1 1 12 12 1 1 12 12 12 12 12 12 12 1 1 12 1 1 1 1 1 1 12 12 12 12 12 12 7 12 1 1 12 12 12 12 12 12 12 12 12 12 1 7 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Long TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/util/Iterator 1 1 53 100 8 10 10 11 11 11 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 7 12 12 12 7 12 1 1 1 1 1 1 1 1 1 1
instanceKlass kotlin/KotlinNullPointerException
ciInstanceKlass java/lang/NullPointerException 1 1 26 10 10 100 100 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 12 12 1 1
ciInstanceKlass java/lang/ArithmeticException 1 1 26 10 10 100 100 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 12 12 1 1
ciInstanceKlass java/lang/StringLatin1 1 1 314 7 10 100 10 10 10 10 10 10 10 10 10 10 10 10 10 9 10 10 10 10 10 10 10 100 10 10 10 8 8 8 10 10 10 7 10 10 10 10 10 10 10 10 10 10 10 8 10 100 10 10 10 10 10 7 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 1 12 12 7 12 12 7 12 12 7 12 12 12 12 12 7 12 12 12 12 12 12 12 12 1 12 12 1 1 1 12 100 12 12 1 12 12 12 12 12 12 12 12 12 12 1 12 1 12 100 12 7 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Math 1 1 389 10 10 10 10 10 10 10 7 6 0 6 0 10 10 10 10 10 10 10 10 10 10 10 10 100 3 3 3 10 100 5 0 5 0 5 0 5 0 5 0 9 10 100 8 10 8 10 10 100 5 0 5 0 100 3 5 0 3 5 0 10 10 10 9 9 10 7 6 0 10 9 100 10 10 100 10 10 10 10 10 10 10 10 10 6 0 10 10 10 10 7 4 10 10 10 10 10 10 10 10 10 10 10 5 0 6 0 4 6 0 4 6 0 4 10 9 10 9 10 4 6 0 100 100 1 1 1 1 1 6 0 1 6 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 100 12 12 12 12 12 12 1 12 12 12 12 12 12 12 12 12 12 12 12 1 12 1 12 100 12 1 1 12 1 12 12 1 1 12 12 12 12 12 12 1 12 12 1 12 1 12 12 12 12 12 12 12 12 12 12 12 12 12 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Math negativeZeroFloatBits J -2147483648
staticfield java/lang/Math negativeZeroDoubleBits J -9223372036854775808
staticfield java/lang/Math $assertionsDisabled Z 1
ciInstanceKlass java/lang/StringUTF16 1 1 532 100 7 10 100 10 7 3 100 100 10 8 10 10 8 10 10 9 10 100 8 10 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 100 10 10 10 10 10 10 10 10 10 10 10 10 100 3 10 10 10 10 10 10 10 9 10 10 10 10 100 10 10 10 10 10 8 8 8 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 8 10 100 10 10 10 10 11 10 10 10 9 9 5 0 5 0 10 10 10 10 10 100 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 1 1 1 1 1 12 12 1 12 12 12 12 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 12 12 12 12 12 7 12 100 12 12 12 12 12 1 12 12 12 12 12 12 12 12 12 12 12 12 1 12 12 100 12 12 1 1 1 12 12 12 12 100 12 12 12 12 12 12 12 12 7 12 12 12 12 1 12 1 12 100 12 12 12 100 12 12 12 12 12 12 12 12 12 7 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/StringUTF16 HI_BYTE_SHIFT I 0
staticfield java/lang/StringUTF16 LO_BYTE_SHIFT I 8
staticfield java/lang/StringUTF16 $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/org/objectweb/asm/Type 1 1 334 10 9 9 9 9 10 10 7 10 10 10 10 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 8 8 8 8 8 8 8 8 8 7 10 10 10 8 10 10 7 10 10 10 10 10 10 10 10 10 3 10 10 10 10 8 10 10 10 100 3 3 3 3 3 3 3 3 3 3 3 7 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 1 12 12 12 12 100 12 12 100 12 100 12 100 12 100 12 100 12 100 12 100 12 12 12 12 12 100 12 12 12 12 12 1 1 1 1 1 1 1 1 1 1 12 12 12 1 12 12 1 12 12 12 12 12 12 12 12 12 12 12 100 1 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/org/objectweb/asm/Type VOID_TYPE Ljdk/internal/org/objectweb/asm/Type; jdk/internal/org/objectweb/asm/Type
staticfield jdk/internal/org/objectweb/asm/Type BOOLEAN_TYPE Ljdk/internal/org/objectweb/asm/Type; jdk/internal/org/objectweb/asm/Type
staticfield jdk/internal/org/objectweb/asm/Type CHAR_TYPE Ljdk/internal/org/objectweb/asm/Type; jdk/internal/org/objectweb/asm/Type
staticfield jdk/internal/org/objectweb/asm/Type BYTE_TYPE Ljdk/internal/org/objectweb/asm/Type; jdk/internal/org/objectweb/asm/Type
staticfield jdk/internal/org/objectweb/asm/Type SHORT_TYPE Ljdk/internal/org/objectweb/asm/Type; jdk/internal/org/objectweb/asm/Type
staticfield jdk/internal/org/objectweb/asm/Type INT_TYPE Ljdk/internal/org/objectweb/asm/Type; jdk/internal/org/objectweb/asm/Type
staticfield jdk/internal/org/objectweb/asm/Type FLOAT_TYPE Ljdk/internal/org/objectweb/asm/Type; jdk/internal/org/objectweb/asm/Type
staticfield jdk/internal/org/objectweb/asm/Type LONG_TYPE Ljdk/internal/org/objectweb/asm/Type; jdk/internal/org/objectweb/asm/Type
staticfield jdk/internal/org/objectweb/asm/Type DOUBLE_TYPE Ljdk/internal/org/objectweb/asm/Type; jdk/internal/org/objectweb/asm/Type
instanceKlass jdk/internal/org/objectweb/asm/ClassWriter
ciInstanceKlass jdk/internal/org/objectweb/asm/ClassVisitor 1 1 106 10 10 100 3 3 100 10 9 9 10 10 100 10 10 10 10 3 10 10 10 10 10 10 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 1 1 12 12 12 12 1 12 12 12 12 12 12 12 12 12 1 1
ciInstanceKlass jdk/internal/org/objectweb/asm/ClassWriter 1 1 812 7 100 3 10 9 7 10 9 7 9 6 0 9 10 9 9 9 9 7 9 10 10 9 9 9 10 9 9 10 9 9 9 9 9 7 3 10 9 100 10 10 9 9 10 9 10 100 10 9 9 9 10 9 9 9 9 9 9 10 9 9 9 7 10 10 3 100 8 10 9 10 9 9 10 9 9 8 8 8 8 8 3 8 3 8 8 10 8 8 8 8 9 9 9 8 10 10 10 3 10 9 10 10 10 9 10 10 10 10 9 9 9 9 100 10 10 10 10 10 7 10 7 10 7 10 7 10 7 10 10 7 10 10 7 10 10 7 7 10 10 10 100 9 9 9 9 9 10 100 100 10 8 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 9 10 10 9 10 9 9 10 10 10 10 10 10 10 100 10 10 10 8 10 10 10 10 8 10 9 7 1 1 1 3 1 3 1 1 3 1 3 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 1 12 12 1 12 12 12 12 12 12 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 12 12 1 12 12 12 12 12 12 12 1 12 12 12 12 12 12 12 12 12 100 12 12 12 12 12 1 12 12 1 1 12 12 12 12 12 12 12 1 1 1 1 1 1 1 1 1 12 12 12 1 12 12 12 12 12 12 12 12 12 12 12 12 12 1 12 12 12 12 12 1 1 12 1 1 12 1 12 12 1 12 12 1 12 12 1 1 12 12 12 1 12 12 12 12 12 12 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 100 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 100 12 12 12 12 12 12 12 1 12 1 12 12 12 12 1 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/org/objectweb/asm/ClassWriter TYPE [B 221
ciInstanceKlass jdk/internal/org/objectweb/asm/Item 1 1 89 10 9 9 9 9 9 9 9 9 100 3 10 10 10 10 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 1 100 12 100 12 7 12 12 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/org/objectweb/asm/Label 1 1 187 10 9 7 100 8 10 9 9 10 10 10 9 9 10 100 100 9 9 9 10 100 10 9 9 9 9 9 10 10 100 10 8 10 10 10 10 7 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 1 1 1 12 12 100 12 12 12 12 12 12 100 12 1 1 12 100 12 12 12 1 12 12 12 12 12 12 12 1 1 12 12 12 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/org/objectweb/asm/Frame 1 1 343 7 10 9 10 3 9 9 9 9 9 100 10 7 10 10 10 3 8 7 9 10 9 9 9 3 10 10 9 9 9 10 3 3 10 10 3 3 10 10 3 10 3 3 3 3 3 10 10 3 9 3 9 3 9 3 3 3 100 100 3 3 9 8 8 8 8 10 10 10 3 100 8 10 9 10 10 9 10 3 3 3 3 3 3 3 3 100 10 10 10 10 3 10 10 3 3 10 8 10 8 9 7 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 12 12 12 12 12 12 12 12 1 12 1 7 12 12 12 1 1 12 12 12 12 12 7 12 7 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 1 1 12 1 1 1 12 12 12 1 1 12 12 12 12 12 12 1 12 12 12 12 12 12 1 12 1 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/org/objectweb/asm/Frame SIZE [I 202
ciMethod java/lang/Object <init> ()V 4097 1 577862 0 96
ciMethod java/lang/String length ()I 4097 1 404093 0 -1
ciMethod java/lang/String charAt (I)C 3329 1 1016584 0 128
ciMethod java/lang/String equals (Ljava/lang/Object;)Z 2193 1 14624 0 -1
ciMethod java/lang/String hashCode ()I 4097 1 6285 0 -1
ciMethod java/lang/String indexOf (I)I 2577 1 189898 0 -1
ciMethod java/lang/String substring (II)Ljava/lang/String; 2057 1 12680 0 -1
ciMethod java/lang/String isLatin1 ()Z 4097 1 1330493 0 64
ciMethod java/lang/System arraycopy (Ljava/lang/Object;ILjava/lang/Object;II)V 3073 1 384 0 -1
ciMethod java/lang/StringLatin1 charAt ([BI)C 3689 1 993299 0 96
ciMethod java/lang/Math max (II)I 2049 1 32851 0 -1
ciMethod java/lang/StringUTF16 getChar ([BI)C 4097 1 30308 0 -1
ciMethod java/lang/StringUTF16 charAt ([BI)C 0 0 39 0 0
ciMethod java/lang/StringUTF16 checkIndex (I[B)V 0 0 95 0 -1
ciMethodData java/lang/String isLatin1 ()Z 2 1330609 orig 320 224 139 163 245 250 127 0 0 0 163 215 119 142 2 0 0 200 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 137 93 162 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 0 0 7 0 2 0 0 0 0 0 0 0 88 0 0 0 254 255 255 255 7 0 3 0 0 0 0 0 data 18 0x30007 0x0 0x58 0x144bb0 0xa0007 0x98 0x38 0x144b18 0xe0003 0x144b18 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/Object <init> ()V 2 577862 orig 320 224 139 163 245 250 127 0 0 16 6 215 119 142 2 0 0 88 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 49 122 70 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 0 0 3 0 1 0 0 0 0 0 0 0 0 0 0 0 254 255 255 255 0 0 0 0 0 0 0 0 data 4 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String charAt (I)C 2 1017003 orig 320 224 139 163 245 250 127 0 0 184 84 215 119 142 2 0 0 0 2 0 0 56 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 17 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 157 1 0 0 233 23 124 0 1 0 0 0 10 116 2 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 0 0 16 0 2 0 0 0 0 0 0 0 120 0 0 0 254 255 255 255 5 0 1 0 0 0 0 0 data 25 0x10005 0xf82fd 0x0 0x0 0x0 0x0 0x0 0x8000000600040007 0x19 0x30 0xf82e5 0xc0002 0xf82e5 0x150002 0x19 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringLatin1 charAt ([BI)C 2 993365 orig 320 224 139 163 245 250 127 0 0 120 139 236 119 142 2 0 0 200 1 0 0 32 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 205 1 0 0 65 52 121 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 0 0 8 0 2 0 0 0 0 0 0 0 80 0 0 0 254 255 255 255 7 0 1 0 0 0 0 0 data 18 0x10007 0x0 0x40 0xf2688 0x70007 0xf2688 0x30 0x0 0xf0002 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringUTF16 charAt ([BI)C 1 39 orig 320 224 139 163 245 250 127 0 0 32 29 242 119 142 2 0 0 168 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 17 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 57 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 32 0 0 0 254 255 255 255 2 0 2 0 0 0 0 0 data 14 0x20002 0x27 0x70002 0x10 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethod jdk/internal/org/objectweb/asm/Type getArgumentsAndReturnSizes (Ljava/lang/String;)I 1001 25185 2137 0 0
ciMethod jdk/internal/org/objectweb/asm/ClassWriter addType (Ljava/lang/String;)I 1121 1 2257 0 0
ciMethod jdk/internal/org/objectweb/asm/ClassWriter addUninitializedType (Ljava/lang/String;I)I 0 0 4 0 0
ciMethod jdk/internal/org/objectweb/asm/ClassWriter addType (Ljdk/internal/org/objectweb/asm/Item;)Ljdk/internal/org/objectweb/asm/Item; 649 1 436 0 0
ciMethod jdk/internal/org/objectweb/asm/ClassWriter get (Ljdk/internal/org/objectweb/asm/Item;)Ljdk/internal/org/objectweb/asm/Item; 2065 81 6518 0 256
ciMethod jdk/internal/org/objectweb/asm/ClassWriter put (Ljdk/internal/org/objectweb/asm/Item;)V 2081 1 6451 0 672
ciMethod jdk/internal/org/objectweb/asm/Item <init> (ILjdk/internal/org/objectweb/asm/Item;)V 2081 1 5602 0 672
ciMethod jdk/internal/org/objectweb/asm/Item set (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V 2065 1 5997 0 4352
ciMethod jdk/internal/org/objectweb/asm/Item isEqualTo (Ljdk/internal/org/objectweb/asm/Item;)Z 553 1 5236 0 -1
ciMethod jdk/internal/org/objectweb/asm/Frame get (I)I 1145 1 2811 0 0
ciMethod jdk/internal/org/objectweb/asm/Frame set (II)V 537 1 938 0 0
ciMethod jdk/internal/org/objectweb/asm/Frame push (I)V 1865 1 4484 0 0
ciMethod jdk/internal/org/objectweb/asm/Frame push (Ljdk/internal/org/objectweb/asm/ClassWriter;Ljava/lang/String;)V 521 1 1196 0 0
ciMethod jdk/internal/org/objectweb/asm/Frame type (Ljdk/internal/org/objectweb/asm/ClassWriter;Ljava/lang/String;)I 1153 1 2182 0 -1
ciMethod jdk/internal/org/objectweb/asm/Frame pop ()I 937 1 1875 0 0
ciMethod jdk/internal/org/objectweb/asm/Frame pop (I)V 593 1 1368 0 0
ciMethod jdk/internal/org/objectweb/asm/Frame pop (Ljava/lang/String;)V 321 1 570 0 0
ciMethod jdk/internal/org/objectweb/asm/Frame init (I)V 0 0 8 0 0
ciMethod jdk/internal/org/objectweb/asm/Frame execute (IILjdk/internal/org/objectweb/asm/ClassWriter;Ljdk/internal/org/objectweb/asm/Item;)V 2393 1 5419 0 -1
ciMethodData jdk/internal/org/objectweb/asm/ClassWriter get (Ljdk/internal/org/objectweb/asm/Item;)Ljdk/internal/org/objectweb/asm/Item; 2 6518 orig 320 224 139 163 245 250 127 0 0 8 238 13 120 142 2 0 0 56 2 0 0 120 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 1 0 0 161 195 0 0 249 10 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 1 0 12 0 2 0 0 0 0 0 0 0 176 0 0 0 254 255 255 255 7 0 17 0 0 0 0 0 data 32 0x110007 0xf9d 0xb0 0xa36 0x1c0007 0xc7 0x78 0x96f 0x210005 0x96f 0x0 0x0 0x0 0x0 0x0 0x240007 0x8d7 0x38 0x98 0x2c0003 0x15f 0xffffffffffffff68 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0xffffffffffffffff oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Item isEqualTo (Ljdk/internal/org/objectweb/asm/Item;)Z 2 5236 orig 320 224 139 163 245 250 127 0 0 16 63 14 120 142 2 0 0 240 7 0 0 240 4 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 69 0 0 0 121 161 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 0 0 48 0 2 0 0 0 0 0 0 0 40 6 0 0 254 255 255 255 8 0 4 0 0 0 0 0 data 215 0x40008 0x42 0x0 0x508 0xa15 0x220 0x0 0x508 0x0 0x290 0x0 0x290 0x0 0x258 0x0 0x258 0x4ec 0x220 0x5 0x220 0xdb 0x508 0x57 0x508 0x0 0x508 0x1e 0x358 0x0 0x508 0x0 0x508 0x0 0x508 0x0 0x220 0x0 0x508 0x0 0x420 0x0 0x220 0x0 0x220 0x0 0x508 0x0 0x508 0x0 0x508 0x0 0x508 0x0 0x508 0x0 0x508 0x0 0x508 0x0 0x508 0x0 0x508 0x3d9 0x220 0x0 0x2c8 0x0 0x258 0x9c0005 0x12df 0x0 0x0 0x0 0x0 0x0 0xa90007 0x0 0x38 0x0 0xad0003 0x0 0x18 0xba0007 0x0 0x38 0x0 0xbe0003 0x0 0x18 0xcb0007 0x0 0x90 0x0 0xd60005 0x0 0x0 0x0 0x0 0x0 0x0 0xd90007 0x0 0x38 0x0 0xdd0003 0x0 0x18 0xea0005 0x1e 0x0 0x0 0x0 0x0 0x0 0xed0007 0x14 0x90 0xa 0xf80005 0xa 0x0 0x0 0x0 0x0 0x0 0xfb0007 0x3 0x38 0x7 0xff0003 0x7 0x18 0x10d0007 0x0 0xe8 0x0 0x1180005 0x0 0x0 0x0 0x0 0x0 0x0 0x11b0007 0x0 0x90 0x0 0x1260005 0x0 0x0 0x0 0x0 0x0 0x0 0x1290007 0x0 0x38 0x0 0x12d0003 0x0 0x18 0x13a0005 0x132 0x0 0x0 0x0 0x0 0x0 0x13d0007 0x2 0xe8 0x130 0x1480005 0x130 0x0 0x0 0x0 0x0 0x0 0x14b0007 0x26 0x90 0x10a 0x1560005 0x10a 0x0 0x0 0x0 0x0 0x0 0x1590007 0x11 0x38 0xf9 0x15d0003 0xf9 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Item set (ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V 2 5997 orig 320 224 139 163 245 250 127 0 0 16 59 14 120 142 2 0 0 240 4 0 0 112 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 1 0 0 89 179 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 0 0 9 0 2 0 0 0 0 0 0 0 80 3 0 0 254 255 255 255 8 0 22 0 0 0 0 0 data 119 0x160008 0x3e 0x0 0x2a8 0xb7f 0x200 0x0 0x2a8 0x0 0x2a8 0x0 0x2a8 0x0 0x2a8 0x0 0x2a8 0x42b 0x200 0x2e 0x200 0xfb 0x2a8 0x146 0x2a8 0x9 0x2a8 0x200 0x238 0x0 0x2a8 0x0 0x2a8 0x0 0x2a8 0x0 0x200 0x0 0x2a8 0x0 0x2a8 0x0 0x200 0x0 0x200 0x0 0x2a8 0x0 0x2a8 0x0 0x2a8 0x0 0x2a8 0x0 0x2a8 0x0 0x2a8 0x0 0x2a8 0x0 0x2a8 0x0 0x2a8 0x249 0x200 0xa60005 0x1221 0x0 0x0 0x0 0x0 0x0 0xb40005 0x200 0x0 0x0 0x0 0x0 0x0 0xb80005 0x200 0x0 0x0 0x0 0x0 0x0 0xc70005 0x24a 0x0 0x0 0x0 0x0 0x0 0xcb0005 0x24a 0x0 0x0 0x0 0x0 0x0 0xd10005 0x24a 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Item <init> (ILjdk/internal/org/objectweb/asm/Item;)V 2 5602 orig 320 224 139 163 245 250 127 0 0 16 54 14 120 142 2 0 0 168 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 4 1 0 0 241 166 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 0 0 3 0 2 0 0 0 0 0 0 0 16 0 0 0 254 255 255 255 2 0 1 0 0 0 0 0 data 14 0x10002 0x14de 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/ClassWriter put (Ljdk/internal/org/objectweb/asm/Item;)V 2 6451 orig 320 224 139 163 245 250 127 0 0 200 239 13 120 142 2 0 0 168 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 4 1 0 0 121 193 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 2 0 12 0 2 0 0 0 0 0 0 0 0 1 0 0 254 255 255 255 7 0 13 0 0 0 0 0 data 46 0xd0007 0x182f 0xc8 0x0 0x290007 0x0 0xa8 0x0 0x370007 0x0 0x70 0x0 0x5c0004 0x0 0x0 0x0 0x0 0x0 0x0 0x610003 0x0 0xffffffffffffffa8 0x670003 0x0 0xffffffffffffff70 0x960004 0x0 0x0 0x28e7dcb4300 0x182f 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 1 28 jdk/internal/org/objectweb/asm/Item methods 0
ciMethodData jdk/internal/org/objectweb/asm/Type getArgumentsAndReturnSizes (Ljava/lang/String;)I 2 100651 orig 320 224 139 163 245 250 127 0 0 240 36 13 120 142 2 0 0 160 4 0 0 152 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 76 12 0 0 41 63 0 0 249 230 11 0 70 5 0 0 190 8 1 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 3 0 91 0 2 0 0 0 0 0 0 0 32 3 0 0 254 255 255 255 5 0 9 0 0 0 0 0 data 109 0x90005 0x17ba 0x0 0x0 0x0 0x0 0x0 0x100007 0xfd6 0xe8 0x7e4 0x150005 0x7e4 0x0 0x0 0x0 0x0 0x0 0x1f0007 0x471 0x38 0x373 0x230003 0x373 0x70 0x290007 0x0 0x40 0x471 0x2f0007 0x46d 0x38 0x4 0x330003 0x4 0x18 0x3c0007 0x2d8 0xa8 0xcfe 0x440005 0x17a01 0x0 0x0 0x0 0x0 0x0 0x490007 0xcfb 0x38 0x16d06 0x4c0003 0x16d06 0xffffffffffffffa8 0x520003 0xcfb 0x158 0x8000000600580007 0x2d2 0xe8 0x7 0x5d0005 0x7 0x0 0x0 0x0 0x0 0x0 0x640007 0x7 0x38 0x0 0x6a0003 0x0 0xffffffffffffffa8 0x700007 0x0 0x40 0x7 0x760007 0x7 0x90 0x0 0x7c0003 0x0 0x70 0x820007 0x8 0x40 0x2ca 0x8000000600880007 0x2c3 0x38 0x8 0x8e0003 0x10 0x18 0x940003 0xfd5 0xfffffffffffffcf8 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Frame execute (IILjdk/internal/org/objectweb/asm/ClassWriter;Ljdk/internal/org/objectweb/asm/Item;)V 2 5419 orig 320 224 139 163 245 250 127 0 0 248 70 15 120 142 2 0 0 224 60 0 0 48 56 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 26 1 0 0 1 160 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 0 0 235 0 2 0 0 0 0 0 0 0 64 57 0 0 254 255 255 255 8 0 1 0 0 0 0 0 data 1909 0x10008 0x192 0x0 0x38d0 0x0 0xca0 0x2 0xcb8 0x0 0xd08 0xa 0xd08 0xa 0xd08 0x7 0xd08 0x6 0xd08 0x6 0xd08 0x5 0xd08 0x0 0xd58 0x0 0xd58 0x0 0xde0 0x0 0xde0 0x0 0xde0 0x0 0xe30 0x0 0xe30 0x0 0xd08 0x0 0xd08 0x1 0xeb8 0x0 0x38d0 0x0 0x38d0 0x1ae 0xd08 0x4 0xd58 0x4 0xde0 0x4 0xe30 0x7b8 0x1388 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x1410 0x0 0x1498 0x0 0x1558 0x0 0x15e0 0xc 0x16a0 0x0 0x1410 0x0 0x1410 0x0 0x1410 0xac 0x1798 0x0 0x1960 0x0 0x1798 0x0 0x1960 0x23d 0x1798 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x38d0 0x0 0x1b98 0x0 0x1be8 0x0 0x1b98 0x0 0x1be8 0x2 0x1b98 0x0 0x1b98 0x0 0x1b98 0x0 0x1b98 0x0 0x1c38 0x0 0x1c88 0x51 0x1cd8 0x0 0x1d98 0x0 0x1ec8 0x0 0x2068 0x0 0x21d0 0x0 0x23a8 0x0 0x25f0 0x0 0x26e8 0x0 0x2770 0x0 0x2830 0x0 0x28b8 0x0 0x26e8 0x0 0x2770 0x0 0x2830 0x0 0x28b8 0x0 0x26e8 0x0 0x2770 0x0 0x2830 0x0 0x28b8 0x0 0x26e8 0x0 0x2770 0x0 0x2830 0x0 0x28b8 0x0 0x26e8 0x0 0x2770 0x0 0x2830 0x0 0x28b8 0x0 0xca0 0x0 0xca0 0x0 0xca0 0x0 0xca0 0x0 0x26e8 0x0 0x2978 0x0 0x26e8 0x0 0x2978 0x0 0x26e8 0x0 0x2978 0x0 0x26e8 0x0 0x2770 0x0 0x26e8 0x0 0x2770 0x0 0x26e8 0x0 0x2770 0x0 0x2a38 0x0 0x2a88 0x0 0x2b48 0x0 0x2bd0 0x0 0x26e8 0x0 0x2830 0x0 0x15e0 0x0 0x2c90 0x0 0x2a88 0x0 0x2bd0 0x0 0x26e8 0x0 0x1498 0x0 0x2830 0x0 0xca0 0x0 0xca0 0x0 0xca0 0x0 0x2d18 0x0 0x26e8 0x0 0x26e8 0x0 0x2d18 0x0 0x2d18 0x0 0x1c38 0x0 0x1c38 0x0 0x1c38 0x0 0x1c38 0x0 0x1c38 0x0 0x1c38 0x0 0x1c88 0x0 0x1c88 0x0 0x1c88 0x0 0x1c88 0x0 0x1c88 0x0 0x1c88 0x0 0x1c88 0x0 0x1c88 0x0 0xca0 0x0 0x2da0 0x0 0x2da0 0x0 0x1c38 0x0 0x1c38 0x6 0x1c38 0x1 0x1c88 0x0 0x1c38 0x0 0x1c88 0x7e 0x1c38 0x1e 0xca0 0x1c 0x2db0 0x0 0x2e00 0x262 0x2e50 0x2a 0x2ed8 0x177 0x2f60 0x8 0x2f60 0x68 0x2f60 0x0 0x2f60 0x0 0x30f0 0x4 0x3178 0x0 0x3200 0x2 0x3548 0x0 0x2c90 0x0 0x1c38 0x1df 0x3768 0x0 0x2c90 0x0 0x1c38 0x0 0x1c38 0x0 0x38d0 0x0 0x38d0 0x0 0x1c38 0x0 0x1c38 0x3300003 0x1e 0x2ca0 0x3360005 0x2 0x0 0x0 0x0 0x0 0x0 0x3390003 0x2 0x2c50 0x33f0005 0x1da 0x0 0x0 0x0 0x0 0x0 0x3420003 0x1da 0x2c00 0x3480005 0x4 0x0 0x0 0x0 0x0 0x0 0x34e0005 0x4 0x0 0x0 0x0 0x0 0x0 0x3510003 0x4 0x2b78 0x3570005 0x4 0x0 0x0 0x0 0x0 0x0 0x35a0003 0x4 0x2b28 0x3600005 0x4 0x0 0x0 0x0 0x0 0x0 0x3660005 0x4 0x0 0x0 0x0 0x0 0x0 0x3690003 0x4 0x2aa0 0x3710008 0x1e 0x0 0x448 0x0 0x100 0x0 0x1d8 0x0 0x150 0x0 0x228 0x0 0x2b0 0x1 0x338 0x0 0x448 0x0 0x448 0x0 0x448 0x0 0x448 0x0 0x448 0x0 0x448 0x0 0x448 0x0 0x3c0 0x3bb0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3be0003 0x0 0x2950 0x3c40005 0x0 0x0 0x0 0x0 0x0 0x0 0x3ca0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3cd0003 0x0 0x28c8 0x3d30005 0x0 0x0 0x0 0x0 0x0 0x0 0x3d60003 0x0 0x2878 0x3dc0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3e20005 0x0 0x0 0x0 0x0 0x0 0x0 0x3e50003 0x0 0x27f0 0x3ee0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3f20005 0x0 0x0 0x0 0x0 0x0 0x0 0x3f50003 0x0 0x2768 0x3fe0005 0x0 0x0 0x28e7dcb4230 0x1 0x0 0x0 0x4020005 0x1 0x0 0x0 0x0 0x0 0x0 0x4050003 0x1 0x26e0 0x40e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4120005 0x0 0x0 0x0 0x0 0x0 0x0 0x4150003 0x0 0x2658 0x41e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4220005 0x0 0x0 0x0 0x0 0x0 0x0 0x4250003 0x0 0x25d0 0x42b0005 0x7b8 0x0 0x0 0x0 0x0 0x0 0x42e0005 0x7b8 0x0 0x0 0x0 0x0 0x0 0x4310003 0x7b8 0x2548 0x4360005 0x0 0x0 0x0 0x0 0x0 0x0 0x43c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x43f0003 0x0 0x24c0 0x4440005 0x0 0x0 0x0 0x0 0x0 0x0 0x44a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4500005 0x0 0x0 0x0 0x0 0x0 0x0 0x4530003 0x0 0x2400 0x4580005 0x0 0x0 0x0 0x0 0x0 0x0 0x45e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4610003 0x0 0x2378 0x4660005 0x0 0x0 0x0 0x0 0x0 0x0 0x46c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4720005 0x0 0x0 0x0 0x0 0x0 0x0 0x4750003 0x0 0x22b8 0x47a0005 0xc 0x0 0x0 0x0 0x0 0x0 0x47e0005 0xc 0x0 0x0 0x0 0x0 0x0 0x4880007 0xc 0x38 0x0 0x48d0003 0x0 0x18 0x4950005 0xc 0x0 0x0 0x0 0x0 0x0 0x4980003 0xc 0x21c0 0x49c0005 0x2e9 0x0 0x0 0x0 0x0 0x0 0x4a50005 0x2e9 0x0 0x0 0x0 0x0 0x0 0x4a90007 0x48 0x2138 0x2a1 0x4b00005 0x2a1 0x0 0x0 0x0 0x0 0x0 0x4b90007 0x0 0x40 0x2a1 0x4c00007 0x2a1 0x70 0x0 0x4c90005 0x0 0x0 0x0 0x0 0x0 0x0 0x4cc0003 0x0 0x2068 0x4d60007 0x229 0x2050 0x78 0x4e20005 0x78 0x0 0x0 0x0 0x0 0x0 0x4e50003 0x78 0x1ff8 0x4ea0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4ee0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4f70005 0x0 0x0 0x0 0x0 0x0 0x0 0x5000005 0x0 0x0 0x0 0x0 0x0 0x0 0x5040007 0x0 0x1f00 0x0 0x50b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5140007 0x0 0x40 0x0 0x51b0007 0x0 0x70 0x0 0x5240005 0x0 0x0 0x0 0x0 0x0 0x0 0x5270003 0x0 0x1e30 0x5310007 0x0 0x1e18 0x0 0x53d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5400003 0x0 0x1dc0 0x5450005 0x2 0x0 0x0 0x0 0x0 0x0 0x5480003 0x2 0x1d70 0x54d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5500003 0x0 0x1d20 0x5550005 0x84 0x0 0x0 0x0 0x0 0x0 0x5580003 0x84 0x1cd0 0x55d0005 0x1 0x0 0x0 0x0 0x0 0x0 0x5600003 0x1 0x1c80 0x5640005 0x51 0x0 0x0 0x0 0x0 0x0 0x56c0005 0x51 0x0 0x0 0x0 0x0 0x0 0x5720005 0x51 0x0 0x0 0x0 0x0 0x0 0x5750003 0x51 0x1bc0 0x5790005 0x0 0x0 0x0 0x0 0x0 0x0 0x57f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5870005 0x0 0x0 0x0 0x0 0x0 0x0 0x58d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5930005 0x0 0x0 0x0 0x0 0x0 0x0 0x5960003 0x0 0x1a90 0x59a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5a00005 0x0 0x0 0x0 0x0 0x0 0x0 0x5a60005 0x0 0x0 0x0 0x0 0x0 0x0 0x5ae0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5b40005 0x0 0x0 0x0 0x0 0x0 0x0 0x5ba0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5c00005 0x0 0x0 0x0 0x0 0x0 0x0 0x5c30003 0x0 0x18f0 0x5c70005 0x0 0x0 0x0 0x0 0x0 0x0 0x5cd0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5d50005 0x0 0x0 0x0 0x0 0x0 0x0 0x5db0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5e10005 0x0 0x0 0x0 0x0 0x0 0x0 0x5e70005 0x0 0x0 0x0 0x0 0x0 0x0 0x5ea0003 0x0 0x1788 0x5ee0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5f40005 0x0 0x0 0x0 0x0 0x0 0x0 0x5fa0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6020005 0x0 0x0 0x0 0x0 0x0 0x0 0x6080005 0x0 0x0 0x0 0x0 0x0 0x0 0x60e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6140005 0x0 0x0 0x0 0x0 0x0 0x0 0x61a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x61d0003 0x0 0x15b0 0x6210005 0x0 0x0 0x0 0x0 0x0 0x0 0x6270005 0x0 0x0 0x0 0x0 0x0 0x0 0x62d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6330005 0x0 0x0 0x0 0x0 0x0 0x0 0x63b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6410005 0x0 0x0 0x0 0x0 0x0 0x0 0x6470005 0x0 0x0 0x0 0x0 0x0 0x0 0x64d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6530005 0x0 0x0 0x0 0x0 0x0 0x0 0x6590005 0x0 0x0 0x0 0x0 0x0 0x0 0x65c0003 0x0 0x1368 0x6600005 0x0 0x0 0x0 0x0 0x0 0x0 0x6660005 0x0 0x0 0x0 0x0 0x0 0x0 0x66e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6740005 0x0 0x0 0x0 0x0 0x0 0x0 0x6770003 0x0 0x1270 0x67c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6820005 0x0 0x0 0x0 0x0 0x0 0x0 0x6850003 0x0 0x11e8 0x68a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6900005 0x0 0x0 0x0 0x0 0x0 0x0 0x6960005 0x0 0x0 0x0 0x0 0x0 0x0 0x6990003 0x0 0x1128 0x69e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6a40005 0x0 0x0 0x0 0x0 0x0 0x0 0x6a70003 0x0 0x10a0 0x6ac0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6b20005 0x0 0x0 0x0 0x0 0x0 0x0 0x6b80005 0x0 0x0 0x0 0x0 0x0 0x0 0x6bb0003 0x0 0xfe0 0x6c00005 0x0 0x0 0x0 0x0 0x0 0x0 0x6c60005 0x0 0x0 0x0 0x0 0x0 0x0 0x6cc0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6cf0003 0x0 0xf20 0x6d60005 0x0 0x0 0x0 0x0 0x0 0x0 0x6d90003 0x0 0xed0 0x6de0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6e40005 0x0 0x0 0x0 0x0 0x0 0x0 0x6ea0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6ed0003 0x0 0xe10 0x6f20005 0x0 0x0 0x0 0x0 0x0 0x0 0x6f80005 0x0 0x0 0x0 0x0 0x0 0x0 0x6fb0003 0x0 0xd88 0x7000005 0x0 0x0 0x0 0x0 0x0 0x0 0x7060005 0x0 0x0 0x0 0x0 0x0 0x0 0x70c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x70f0003 0x0 0xcc8 0x7140005 0x0 0x0 0x0 0x0 0x0 0x0 0x71a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x71d0003 0x0 0xc40 0x7220005 0x0 0x0 0x0 0x0 0x0 0x0 0x7280005 0x0 0x0 0x0 0x0 0x0 0x0 0x72b0003 0x0 0xbb8 0x7340002 0x0 0x73f0005 0x1c 0x0 0x0 0x0 0x0 0x0 0x7420003 0x1c 0xb58 0x74b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x74e0003 0x0 0xb08 0x7530005 0x262 0x0 0x0 0x0 0x0 0x0 0x75d0005 0x262 0x0 0x0 0x0 0x0 0x0 0x7600003 0x262 0xa80 0x7690005 0x2a 0x0 0x0 0x0 0x0 0x0 0x76d0005 0x2a 0x0 0x0 0x0 0x0 0x0 0x7710003 0x2a 0x9f8 0x77a0005 0x1e7 0x0 0x0 0x0 0x0 0x0 0x7810007 0x68 0x108 0x17f 0x7850005 0x17f 0x0 0x0 0x0 0x0 0x0 0x78e0007 0x177 0xb0 0x8 0x7970005 0x8 0x0 0x0 0x0 0x0 0x0 0x79c0007 0x0 0x58 0x8 0x7a20005 0x8 0x0 0x0 0x0 0x0 0x0 0x7ac0005 0x1e7 0x0 0x0 0x0 0x0 0x0 0x7af0003 0x1e7 0x868 0x7b80005 0x0 0x0 0x0 0x0 0x0 0x0 0x7c20005 0x0 0x0 0x0 0x0 0x0 0x0 0x7c50003 0x0 0x7e0 0x7d20005 0x0 0x0 0x28e7dcb4230 0x4 0x0 0x0 0x7d60005 0x4 0x0 0x0 0x0 0x0 0x0 0x7d90003 0x4 0x758 0x7dd0005 0x0 0x0 0x0 0x0 0x0 0x0 0x7e20008 0x10 0x0 0x2c0 0x0 0x90 0x0 0xe0 0x0 0x220 0x0 0x270 0x0 0x130 0x0 0x180 0x0 0x1d0 0x80f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x8120003 0x0 0x640 0x8180005 0x0 0x0 0x0 0x0 0x0 0x0 0x81b0003 0x0 0x5f0 0x8210005 0x0 0x0 0x0 0x0 0x0 0x0 0x8240003 0x0 0x5a0 0x82a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x82d0003 0x0 0x550 0x8330005 0x0 0x0 0x0 0x0 0x0 0x0 0x8360003 0x0 0x500 0x83c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x83f0003 0x0 0x4b0 0x8450005 0x0 0x0 0x0 0x0 0x0 0x0 0x8480003 0x0 0x460 0x84e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x8510003 0x0 0x410 0x85c0005 0x2 0x0 0x0 0x0 0x0 0x0 0x8630005 0x2 0x0 0x0 0x0 0x0 0x0 0x8680007 0x2 0x128 0x0 0x8710002 0x0 0x8760005 0x0 0x0 0x0 0x0 0x0 0x0 0x87b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x87e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x8810005 0x0 0x0 0x0 0x0 0x0 0x0 0x8840003 0x0 0x278 0x88d0005 0x0 0x0 0x28e7dcb4230 0x2 0x0 0x0 0x8910005 0x2 0x0 0x0 0x0 0x0 0x0 0x8940003 0x2 0x1f0 0x89f0005 0x1df 0x0 0x0 0x0 0x0 0x0 0x8a60005 0x1df 0x0 0x0 0x0 0x0 0x0 0x8ab0007 0x1db 0x70 0x4 0x8b20005 0x4 0x0 0x0 0x0 0x0 0x0 0x8b50003 0x4 0x110 0x8be0005 0x0 0x0 0x28e7dcb4230 0x1db 0x0 0x0 0x8c20005 0x1db 0x0 0x0 0x0 0x0 0x0 0x8c50003 0x1db 0x88 0x8ca0005 0x0 0x0 0x0 0x0 0x0 0x0 0x8d40005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 4 577 jdk/internal/org/objectweb/asm/ClassWriter 1586 jdk/internal/org/objectweb/asm/ClassWriter 1759 jdk/internal/org/objectweb/asm/ClassWriter 1804 jdk/internal/org/objectweb/asm/ClassWriter methods 0
ciMethodData jdk/internal/org/objectweb/asm/Frame push (I)V 2 4570 orig 320 224 139 163 245 250 127 0 0 224 47 15 120 142 2 0 0 40 2 0 0 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 233 0 0 0 137 135 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 0 0 16 0 2 0 0 0 0 0 0 0 128 0 0 0 254 255 255 255 7 0 4 0 0 0 0 0 data 30 0x40007 0x104b 0x20 0xa6 0x1a0007 0x10c9 0x40 0x28 0x260002 0x28 0x340002 0x28 0x620007 0xc5a 0x20 0x497 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x36 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/ClassWriter addType (Ljava/lang/String;)I 2 2304 orig 320 224 139 163 245 250 127 0 0 200 231 13 120 142 2 0 0 80 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 140 0 0 0 161 67 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 0 0 6 0 2 0 0 0 0 0 0 0 200 0 0 0 254 255 255 255 5 0 9 0 0 0 0 0 data 35 0x90005 0x874 0x0 0x0 0x0 0x0 0x0 0x110005 0x874 0x0 0x0 0x0 0x0 0x0 0x160007 0x6fb 0x58 0x179 0x1e0005 0x179 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xffffffffffffffff 0xffffffffffffffff oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Frame get (I)I 2 3039 orig 320 224 139 163 245 250 127 0 0 112 45 15 120 142 2 0 0 232 1 0 0 32 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 143 0 0 0 129 90 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 0 0 11 0 2 0 0 0 0 0 0 0 96 0 0 0 254 255 255 255 7 0 4 0 0 0 0 0 data 22 0x40007 0x268 0x40 0x8e8 0xd0007 0x8e8 0x20 0x0 0x1d0007 0x728 0x20 0x1c0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Frame pop (I)V 2 1379 orig 320 224 139 163 245 250 127 0 0 144 53 15 120 142 2 0 0 176 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 74 0 0 0 201 40 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 0 0 6 0 2 0 0 0 0 0 0 0 56 0 0 0 254 255 255 255 7 0 5 0 0 0 0 0 data 15 0x50007 0x0 0x38 0x519 0x120003 0x519 0x18 0x0 0x0 0x0 0x0 0x9 0x2 0x6 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Frame pop ()I 2 1897 orig 320 224 139 163 245 250 127 0 0 176 52 15 120 142 2 0 0 144 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 117 0 0 0 161 55 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 0 0 5 0 2 0 0 0 0 0 0 0 32 0 0 0 254 255 255 255 7 0 4 0 0 0 0 0 data 11 0x40007 0x0 0x20 0x6f4 0x0 0x0 0x0 0x0 0x9 0x1 0x6 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Frame set (II)V 1 1040 orig 320 224 139 163 245 250 127 0 0 144 46 15 120 142 2 0 0 240 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 67 0 0 0 105 30 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 0 0 13 0 2 0 0 0 0 0 0 0 96 0 0 0 254 255 255 255 7 0 4 0 0 0 0 0 data 23 0x40007 0x34e 0x20 0x7f 0x170007 0x386 0x40 0x47 0x200002 0x47 0x300002 0x47 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Frame push (Ljdk/internal/org/objectweb/asm/ClassWriter;Ljava/lang/String;)V 2 1216 orig 320 224 139 163 245 250 127 0 0 208 48 15 120 142 2 0 0 112 2 0 0 136 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 65 0 0 0 249 35 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 224 0 0 0 254 255 255 255 2 0 2 0 0 0 0 0 data 39 0x20002 0x47f 0x70007 0x2b 0xd0 0x454 0xc0005 0x454 0x0 0x0 0x0 0x0 0x0 0x120007 0x1 0x40 0x453 0x180007 0x453 0x58 0x0 0x1e0005 0x1 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Frame type (Ljdk/internal/org/objectweb/asm/ClassWriter;Ljava/lang/String;)I 2 2273 orig 320 224 139 163 245 250 127 0 0 224 51 15 120 142 2 0 0 128 8 0 0 56 6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 144 0 0 0 137 66 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 1 0 101 0 2 0 0 0 0 0 0 0 248 6 0 0 254 255 255 255 5 0 2 0 0 0 0 0 data 233 0x20005 0x851 0x0 0x0 0x0 0x0 0x0 0x70007 0x63c 0x70 0x215 0xd0005 0x215 0x0 0x0 0x0 0x0 0x0 0x120003 0x215 0x18 0x190005 0x851 0x0 0x0 0x0 0x0 0x0 0x1c0008 0x34 0x4 0x258 0x0 0x1b0 0x0 0x1b0 0x4 0x1b0 0x0 0x258 0x4 0x1b0 0x0 0x258 0x0 0x258 0x190 0x1b0 0x5 0x1b0 0x0 0x258 0x685 0x1b0 0x0 0x258 0x0 0x258 0x0 0x258 0x0 0x258 0x0 0x258 0x0 0x258 0x0 0x1b0 0x0 0x258 0x0 0x258 0x2b 0x1b0 0x0 0x258 0x0 0x258 0x0 0x258 0x0 0x1b0 0xa30005 0x685 0x0 0x0 0x0 0x0 0x0 0xa80005 0x685 0x0 0x0 0x0 0x0 0x0 0xb00005 0x0 0x0 0x28e7dcb4230 0x685 0x0 0x0 0xbd0005 0x4 0x0 0x0 0x0 0x0 0x0 0xc20007 0x4 0x38 0x0 0xc80003 0x0 0xffffffffffffffa8 0xce0005 0x4 0x0 0x0 0x0 0x0 0x0 0xd10008 0x34 0x0 0x270 0x0 0x1e0 0x0 0x1c8 0x0 0x258 0x0 0x270 0x0 0x228 0x0 0x270 0x0 0x270 0x0 0x210 0x0 0x240 0x0 0x270 0x4 0x270 0x0 0x270 0x0 0x270 0x0 0x270 0x0 0x270 0x0 0x270 0x0 0x270 0x0 0x1f8 0x0 0x270 0x0 0x270 0x0 0x270 0x0 0x270 0x0 0x270 0x0 0x270 0x0 0x1b0 0x1480003 0x0 0x168 0x14f0003 0x0 0x150 0x1560003 0x0 0x138 0x15d0003 0x0 0x120 0x1640003 0x0 0x108 0x16b0003 0x0 0xf0 0x1720003 0x0 0xd8 0x1790003 0x0 0xc0 0x1820005 0x4 0x0 0x0 0x0 0x0 0x0 0x1870005 0x4 0x0 0x0 0x0 0x0 0x0 0x18f0005 0x0 0x0 0x28e7dcb4230 0x4 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 99 jdk/internal/org/objectweb/asm/ClassWriter 219 jdk/internal/org/objectweb/asm/ClassWriter methods 0
ciMethodData jdk/internal/org/objectweb/asm/Frame pop (Ljava/lang/String;)V 1 607 orig 320 224 139 163 245 250 127 0 0 136 54 15 120 142 2 0 0 8 3 0 0 48 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 40 0 0 0 185 17 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 0 0 23 0 2 0 0 0 0 0 0 0 128 1 0 0 254 255 255 255 5 0 2 0 0 0 0 0 data 58 0x20005 0x237 0x0 0x0 0x0 0x0 0x0 0x90007 0x2a 0x80 0x20d 0xe0002 0x20d 0x150005 0x20d 0x0 0x0 0x0 0x0 0x0 0x180003 0x20d 0xe0 0x1e0007 0x0 0x40 0x2a 0x240007 0x2a 0x70 0x0 0x290005 0x0 0x0 0x0 0x0 0x0 0x0 0x2c0003 0x0 0x50 0x310005 0x2a 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x6 0xffffffffffffffff oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Frame init (I)V 1 8 orig 320 224 139 163 245 250 127 0 0 168 55 15 120 142 2 0 0 240 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 65 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 96 0 0 0 254 255 255 255 7 0 4 0 0 0 0 0 data 23 0x40007 0x0 0x20 0x8 0x190007 0x8 0x40 0x0 0x250002 0x0 0x330002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x24 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/ClassWriter addUninitializedType (Ljava/lang/String;I)I 1 4 orig 320 224 139 163 245 250 127 0 0 224 232 13 120 142 2 0 0 88 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 33 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 200 0 0 0 254 255 255 255 5 0 34 0 0 0 0 0 data 36 0x220005 0x4 0x0 0x0 0x0 0x0 0x0 0x310005 0x4 0x0 0x0 0x0 0x0 0x0 0x360007 0x0 0x58 0x4 0x3e0005 0x4 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0xffffffffffffffff 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/ClassWriter addType (Ljdk/internal/org/objectweb/asm/Item;)Ljdk/internal/org/objectweb/asm/Item; 1 439 orig 320 224 139 163 245 250 127 0 0 24 234 13 120 142 2 0 0 120 2 0 0 136 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 81 0 0 0 49 11 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 248 3 0 0 248 31 0 0 2 0 0 0 0 0 9 0 2 0 0 0 0 0 0 0 208 0 0 0 254 255 255 255 2 0 23 0 0 0 0 0 data 40 0x170002 0x166 0x1d0005 0x166 0x0 0x0 0x0 0x0 0x0 0x240007 0xe7 0x20 0x7f 0x390007 0x166 0x30 0x0 0x530002 0x0 0x640004 0x0 0x0 0x28e7dcb4300 0x166 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xffffffffffffffff 0x0 oops 1 22 jdk/internal/org/objectweb/asm/Item methods 0
compile jdk/internal/org/objectweb/asm/Frame execute (IILjdk/internal/org/objectweb/asm/ClassWriter;Ljdk/internal/org/objectweb/asm/Item;)V -1 4 inline 39 0 -1 jdk/internal/org/objectweb/asm/Frame execute (IILjdk/internal/org/objectweb/asm/ClassWriter;Ljdk/internal/org/objectweb/asm/Item;)V 1 1365 jdk/internal/org/objectweb/asm/Frame pop (I)V 1 2207 jdk/internal/org/objectweb/asm/Frame pop ()I 1 2214 java/lang/String charAt (I)C 2 1 java/lang/String isLatin1 ()Z 2 12 java/lang/StringLatin1 charAt ([BI)C 1 2226 jdk/internal/org/objectweb/asm/Frame push (Ljdk/internal/org/objectweb/asm/ClassWriter;Ljava/lang/String;)V 2 12 jdk/internal/org/objectweb/asm/Frame push (I)V 1 2238 jdk/internal/org/objectweb/asm/ClassWriter addType (Ljava/lang/String;)I 2 17 jdk/internal/org/objectweb/asm/ClassWriter get (Ljdk/internal/org/objectweb/asm/Item;)Ljdk/internal/org/objectweb/asm/Item; 2 30 jdk/internal/org/objectweb/asm/ClassWriter addType (Ljdk/internal/org/objectweb/asm/Item;)Ljdk/internal/org/objectweb/asm/Item; 3 23 jdk/internal/org/objectweb/asm/Item <init> (ILjdk/internal/org/objectweb/asm/Item;)V 4 1 java/lang/Object <init> ()V 1 2242 jdk/internal/org/objectweb/asm/Frame push (I)V 1 2147 java/lang/String charAt (I)C 2 1 java/lang/String isLatin1 ()Z 2 12 java/lang/StringLatin1 charAt ([BI)C 1 1914 jdk/internal/org/objectweb/asm/Frame pop (Ljava/lang/String;)V 2 2 java/lang/String charAt (I)C 3 1 java/lang/String isLatin1 ()Z 3 12 java/lang/StringLatin1 charAt ([BI)C 1 1925 jdk/internal/org/objectweb/asm/Frame pop ()I 1 1943 java/lang/String charAt (I)C 2 1 java/lang/String isLatin1 ()Z 2 12 java/lang/StringLatin1 charAt ([BI)C 1 1964 jdk/internal/org/objectweb/asm/Frame push (Ljdk/internal/org/objectweb/asm/ClassWriter;Ljava/lang/String;)V 2 12 jdk/internal/org/objectweb/asm/Frame push (I)V 1 1875 jdk/internal/org/objectweb/asm/Frame pop (I)V 1 1885 jdk/internal/org/objectweb/asm/Frame push (Ljdk/internal/org/objectweb/asm/ClassWriter;Ljava/lang/String;)V 2 12 jdk/internal/org/objectweb/asm/Frame push (I)V 1 1855 jdk/internal/org/objectweb/asm/Frame push (Ljdk/internal/org/objectweb/asm/ClassWriter;Ljava/lang/String;)V 2 12 jdk/internal/org/objectweb/asm/Frame push (I)V 1 1180 jdk/internal/org/objectweb/asm/Frame pop ()I 1 1189 jdk/internal/org/objectweb/asm/Frame set (II)V 1 1200 jdk/internal/org/objectweb/asm/Frame get (I)I 1 1250 jdk/internal/org/objectweb/asm/Frame set (II)V 1 1067 jdk/internal/org/objectweb/asm/Frame get (I)I 1 1070 jdk/internal/org/objectweb/asm/Frame push (I)V 1 831 jdk/internal/org/objectweb/asm/Frame push (I)V

package io.dcloud.uniplugin.model;


public class DccyDdcVO {
    private Long id;
    private Long pjdyId;
    private String pjdybh;
    private Long dcdwId;
    private String dcdw;
    private Long dcrId;
    private String dcr;
    private Long xfrId;
    private String xfr;
    private Long dcTime;
    private Long xfsj;
    private Integer zt;
    private Long chrId;
    private String chr;
    private Long chsj;
    private Double dwjd;
    private Double dwwd;
    private Integer hasLocalData;
    private String xmmc;
    private Long userId;

    public String getXmmc() {
        return xmmc;
    }

    public void setXmmc(String xmmc) {
        this.xmmc = xmmc;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPjdyId() {
        return pjdyId;
    }

    public void setPjdyId(Long pjdyId) {
        this.pjdyId = pjdyId;
    }

    public String getpjdybh() {
        return pjdybh;
    }

    public void setpjdybh(String pjdybh) {
        this.pjdybh = pjdybh;
    }

    public Long getDcdwId() {
        return dcdwId;
    }

    public void setDcdwId(Long dcdwId) {
        this.dcdwId = dcdwId;
    }

    public String getDcdw() {
        return dcdw;
    }

    public void setDcdw(String dcdw) {
        this.dcdw = dcdw;
    }

    public String getDcr() {
        return dcr;
    }

    public void setDcr(String dcr) {
        this.dcr = dcr;
    }

    public Long getDcrId() {
        return dcrId;
    }

    public void setDcrId(Long dcrId) {
        this.dcrId = dcrId;
    }



    public Long getXfrId() {
        return xfrId;
    }

    public void setXfrId(Long xfrId) {
        this.xfrId = xfrId;
    }

    public String getXfr() {
        return xfr;
    }

    public void setXfr(String xfr) {
        this.xfr = xfr;
    }

    public Long getDcTime() {
        return dcTime;
    }

    public void setDcTime(Long dcTime) {
        this.dcTime = dcTime;
    }

    public Long getXfsj() {
        return xfsj;
    }

    public void setXfsj(Long xfsj) {
        this.xfsj = xfsj;
    }

    public Integer getZt() {
        return zt;
    }

    public void setZt(Integer zt) {
        this.zt = zt;
    }

    public Long getChrId() {
        return chrId;
    }

    public void setChrId(Long chrId) {
        this.chrId = chrId;
    }

    public String getChr() {
        return chr;
    }

    public void setChr(String chr) {
        this.chr = chr;
    }

    public Long getChsj() {
        return chsj;
    }

    public void setChsj(Long chsj) {
        this.chsj = chsj;
    }

    public Integer getHasLocalData() {
        return hasLocalData;
    }

    public void setHasLocalData(Integer hasLocalData) {
        this.hasLocalData = hasLocalData;
    }

    public Double getDwjd() {
        return dwjd;
    }

    public void setDwjd(Double dwjd) {
        this.dwjd = dwjd;
    }

    public Double getDwwd() {
        return dwwd;
    }

    public void setDwwd(Double dwwd) {
        this.dwwd = dwwd;
    }
}

package com.deltaphone.cameramodule;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.hardware.Camera;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.MotionEvent;
import android.view.Surface;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.view.Window;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.VideoView;

import androidx.core.app.ActivityCompat;

import com.deltaphone.cameramodule.camera.ApiCamera;
import com.deltaphone.cameramodule.camera.CountDownTimer;
import com.deltaphone.cameramodule.camera.FileUtils;
import com.deltaphone.cameramodule.camera.PermissionReq;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

public class CameraActivity extends Activity implements SensorEventListener, LocationListener {

    private static final String TAG = "CameraActivity";
    private SurfaceView mSurfaceView;
    private SurfaceHolder mSurfaceHolder;
    private ApiCamera apiCamera;
    private CountDownTimer countDownTimer;

    private Context mContext;

    private ImageView sv_capture,iv_show,back_uniapp;
    private LinearLayout sv_change_camera,iv_show_chose_fail,iv_show_chose_success,zoom_layout,head_layout;
    private TextView video_countdown;
    private VideoView vv_show;
    private RelativeLayout bottom_layout;

    private String picturePath,videoPath;

    private boolean isVideoMode = false;//用来判断是不是录像模式
    private boolean isRecording = false;//判断是否正在录制
    
    // 传感器相关
    private SensorManager sensorManager;
    private Sensor accelerometer;
    private Sensor magneticField;
    private float[] accelerometerReading = new float[3];
    private float[] magnetometerReading = new float[3];
    private float[] rotationMatrix = new float[9];
    private float[] orientationAngles = new float[3];
    private float currentDirection = 0f; // 方位角（方向）
    
    // 位置相关
    private LocationManager locationManager;
    private Location currentLocation;
    private boolean shouldCaptureLocation = false;
    private static final long MIN_TIME_BW_UPDATES = 100; // 0.1秒更新一次，提高更新频率
    private static final float MIN_DISTANCE_CHANGE_FOR_UPDATES = 0.1f; // 0.1米变化即更新，提高灵敏度
    
    // 拍照时捕获的位置和方位角信息
    private double capturedLatitude = 0;
    private double capturedLongitude = 0;
    private float capturedDirection = 0;
    
    // 水印信息
    private String xmmc = "默认项目"; // 项目名称
    private String pjdybh = "默认单元"; // 单元编号
    private boolean enableWatermark = true; // 是否启用水印

    // 添加新的成员变量
    private boolean hasValidLocation = false; // 是否有有效的位置数据
    private boolean isCheckingGpsBeforeCapture = false; // 是否正在检查GPS
    private static final long MAX_LOCATION_AGE = 180000; // 3分钟位置有效期
    private static final long LOCATION_TIMEOUT = 20000; // GPS等待超时时间为20秒
    private boolean isWaitingForLocation = false; // 是否正在等待位置信息
    private Handler handler = new Handler();
    private boolean hasValidAltitude = false; // 是否有有效的海拔数据

    // 添加后台定位相关变量
    private static final long BACKGROUND_LOCATION_UPDATE_INTERVAL = 10000; // 10秒更新一次位置
    private Runnable backgroundLocationUpdateTask; // 后台定位任务
    private boolean isBackgroundLocationUpdateRunning = false; // 是否正在运行后台定位

    public CameraActivity() {
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.camera_module_activity_camera);
        initView();

        mContext = this;
        
        // 检查相机权限
        boolean hasCameraPermission = ActivityCompat.checkSelfPermission(mContext, "android.permission.CAMERA") == 0;
        boolean hasLocationPermission = ActivityCompat.checkSelfPermission(mContext, android.Manifest.permission.ACCESS_FINE_LOCATION) == 0;
        
        if (!hasCameraPermission) {
            Log.d(TAG, "onCreate: 需要请求相机权限");
            // 如果没有相机权限，会在surfaceCreated中请求
        } else {
            Log.d(TAG, "onCreate: 已有相机权限");
        }
        
        if (!hasLocationPermission) {
            Log.d(TAG, "onCreate: 需要请求位置权限");
            // 如果没有位置权限，会在initLocationAndSensors中请求
        } else {
            Log.d(TAG, "onCreate: 已有位置权限");
        }

        Intent mIntent = getIntent();
        String model = mIntent.getStringExtra("model");
        Log.d("model", "onCreate: "+model);
//        规定0是拍照，1是录视频
        if (Objects.equals(model,"1")){
            isVideoMode = true;//如果是1，那就是开启录制模式
        }
        
        // 获取水印相关信息
        xmmc = mIntent.getStringExtra("xmmc");
        if (TextUtils.isEmpty(xmmc)) {
            xmmc = "默认项目";
        }
        pjdybh = mIntent.getStringExtra("pjdybh");
        if (TextUtils.isEmpty(pjdybh)) {
            pjdybh = "默认单元";
        }
        enableWatermark = mIntent.getBooleanExtra("enableWatermark", true);
        
        // 检查是否需要捕获位置信息
        shouldCaptureLocation = mIntent.getBooleanExtra("captureLocationOnShot", false) || enableWatermark;
        if (shouldCaptureLocation) {
            Log.d(TAG, "需要捕获位置信息，初始化传感器和位置服务");
            initLocationAndSensors();
            
            // 启动后台定位服务，每10秒更新一次位置
            startBackgroundLocationUpdates();
        }
        
        //切换视频录制模式
        toggleVideoMode();

        // 确保相机权限请求前SurfaceView已正确创建
        mSurfaceView.post(new Runnable() {
            @Override
            public void run() {
                // 确保SurfaceView布局已经完成
                if (mSurfaceView.getWidth() > 0 && mSurfaceView.getHeight() > 0) {
                    Log.d(TAG, "SurfaceView创建完成: " + mSurfaceView.getWidth() + "x" + mSurfaceView.getHeight());
                }
            }
        });

        // 在onCreate最后添加对拍照按钮的修改
        // 修改拍照按钮的点击监听，添加GPS检查
        sv_capture.setOnClickListener(new View.OnClickListener(){
            @Override
            public void onClick (View v){
                // 首先检查基本拍照权限
                boolean hasCameraPermission = ActivityCompat.checkSelfPermission(mContext, "android.permission.CAMERA") == 0;
                boolean hasStoragePermission = ActivityCompat.checkSelfPermission(mContext, "android.permission.WRITE_EXTERNAL_STORAGE") == 0;
                
                if (!hasCameraPermission || !hasStoragePermission) {
                    Toast.makeText(mContext, "需要相机和存储权限才能拍照", Toast.LENGTH_SHORT).show();
                    return;
                }
                
                // 视频模式需要检查录音权限
                if (isVideoMode && ActivityCompat.checkSelfPermission(mContext, "android.permission.RECORD_AUDIO") != 0) {
                    // 请求录音权限，然后退出相机
                    ActivityCompat.requestPermissions(
                        CameraActivity.this, 
                        new String[]{"android.permission.RECORD_AUDIO"}, 
                        1002); // 使用1002作为录音权限请求码
                    Toast.makeText(mContext, "请授予录音权限后重新打开相机进行录制", Toast.LENGTH_LONG).show();
                    // 延迟一秒关闭，让Toast显示出来
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            finish(); // 关闭活动
                        }
                    }, 1000);
                    return;
                }
                
                // 检查是否需要位置权限（仅用于拍照水印）
                if (shouldCaptureLocation && ActivityCompat.checkSelfPermission(mContext, android.Manifest.permission.ACCESS_FINE_LOCATION) != 0) {
                    // 请求位置权限
                    ActivityCompat.requestPermissions(
                        CameraActivity.this, 
                        new String[]{android.Manifest.permission.ACCESS_FINE_LOCATION}, 
                        1001);
                    Toast.makeText(mContext, "未获取位置权限，无法拍照", Toast.LENGTH_SHORT).show();
                    return;
                }
                
                // 如果是视频模式，直接拍摄，不需要检查GPS
                if (isVideoMode) {
                    capturePhotoOrVideo();
                } else {
                    // 拍照模式，先检查GPS
                    checkGpsBeforeTakingPhoto();
                }
            }
        });
    }
    
    /**
     * 初始化位置和传感器服务
     */
    private void initLocationAndSensors() {
        // 初始化传感器管理器
        sensorManager = (SensorManager) getSystemService(Context.SENSOR_SERVICE);
        if (sensorManager != null) {
            accelerometer = sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER);
            magneticField = sensorManager.getDefaultSensor(Sensor.TYPE_MAGNETIC_FIELD);
            
            if (accelerometer != null && magneticField != null) {
                sensorManager.registerListener(this, accelerometer, SensorManager.SENSOR_DELAY_NORMAL);
                sensorManager.registerListener(this, magneticField, SensorManager.SENSOR_DELAY_NORMAL);
                Log.d(TAG, "传感器监听器注册成功");
            } else {
                Log.e(TAG, "无法注册传感器监听器：传感器不可用");
            }
        }
        
        // 检查位置权限
        if (ActivityCompat.checkSelfPermission(this, android.Manifest.permission.ACCESS_FINE_LOCATION) != 0) {
            Log.d(TAG, "没有位置权限，请求ACCESS_FINE_LOCATION权限");
            // 单独请求位置权限，与相机权限分开处理
            ActivityCompat.requestPermissions(
                this, 
                new String[]{android.Manifest.permission.ACCESS_FINE_LOCATION}, 
                1001); // 使用1001作为位置权限请求码
            return; // 暂不初始化位置服务，等待权限授予
        }
        
        // 初始化位置管理器
        try {
            locationManager = (LocationManager) getSystemService(Context.LOCATION_SERVICE);
            if (locationManager != null) {
                // 尝试同时使用GPS和网络提供程序以提高定位速度和成功率
                boolean gpsEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
                boolean networkEnabled = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER);
                
                // 注册位置更新监听器
                if (gpsEnabled) {
                    locationManager.requestLocationUpdates(
                            LocationManager.GPS_PROVIDER,
                            MIN_TIME_BW_UPDATES,
                            MIN_DISTANCE_CHANGE_FOR_UPDATES,
                            this);
                    Log.d(TAG, "GPS位置提供程序已启动");
                    
                    // 尝试获取最后已知位置
                    Location gpsLocation = locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER);
                    if (gpsLocation != null) {
                        // 检查位置是否在有效期内（3分钟）
                        long locationAge = System.currentTimeMillis() - gpsLocation.getTime();
                        if (locationAge <= MAX_LOCATION_AGE) {
                            currentLocation = gpsLocation;
                            hasValidLocation = true;
                            Log.d(TAG, "获取到有效的GPS最后已知位置: " + gpsLocation.getLatitude() + ", " + gpsLocation.getLongitude() + ", 时间差: " + (locationAge / 1000) + "秒");
                        } else {
                            Log.d(TAG, "GPS最后已知位置已过期: " + gpsLocation.getLatitude() + ", " + gpsLocation.getLongitude() + ", 时间差: " + (locationAge / 1000) + "秒");
                        }
                    }
                }
                
                // 如果GPS不可用或没有获取到位置，尝试使用网络提供程序
                if (networkEnabled && (currentLocation == null || !gpsEnabled)) {
                    locationManager.requestLocationUpdates(
                            LocationManager.NETWORK_PROVIDER,
                            MIN_TIME_BW_UPDATES,
                            MIN_DISTANCE_CHANGE_FOR_UPDATES,
                            this);
                    Log.d(TAG, "网络位置提供程序已启动");
                    
                    // 只有在没有GPS位置时才使用网络位置
                    if (currentLocation == null) {
                        Location networkLocation = locationManager.getLastKnownLocation(LocationManager.NETWORK_PROVIDER);
                        if (networkLocation != null) {
                            // 检查位置是否在有效期内（3分钟）
                            long locationAge = System.currentTimeMillis() - networkLocation.getTime();
                            if (locationAge <= MAX_LOCATION_AGE) {
                                currentLocation = networkLocation;
                                hasValidLocation = true;
                                Log.d(TAG, "获取到有效的网络最后已知位置: " + networkLocation.getLatitude() + ", " + networkLocation.getLongitude() + ", 时间差: " + (locationAge / 1000) + "秒");
                            } else {
                                Log.d(TAG, "网络最后已知位置已过期: " + networkLocation.getLatitude() + ", " + networkLocation.getLongitude() + ", 时间差: " + (locationAge / 1000) + "秒");
                            }
                        }
                    }
                }
                
                // 如果两种方式都没有获取到位置
                if (currentLocation == null) {
                    if (!gpsEnabled && !networkEnabled) {
                        Log.w(TAG, "GPS和网络位置提供程序都未启用");
                    } else {
                        Log.w(TAG, "没有获取到最后已知位置");
                    }
                } else {
                    // 检查位置是否有海拔数据
                    // hasValidAltitude = true; // 移除此行
                    // altitude = DEFAULT_ALTITUDE; // 移除此行
                    Log.d(TAG, "位置包含有效海拔: " + currentLocation.getAltitude());
                }
            }
        } catch (SecurityException e) {
            Log.e(TAG, "位置权限错误: " + e.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "初始化位置服务错误: " + e.getMessage());
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        apiCamera.release();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        apiCamera.release();
        
        // 停止后台定位服务
        stopBackgroundLocationUpdates();
        
        // 释放传感器和位置资源
        if (sensorManager != null) {
            sensorManager.unregisterListener(this);
        }
        if (locationManager != null) {
            try {
                locationManager.removeUpdates(this);
            } catch (SecurityException e) {
                Log.e(TAG, "移除位置更新错误: " + e.getMessage());
            }
        }
    }
    /**
     * 初始化控件
     */
    private void initView() {
        iv_show = (ImageView) findViewById(R.id.iv_show_camera_activity);
        iv_show_chose_fail = findViewById(R.id.iv_show_chose_fail);
        iv_show_chose_success = findViewById(R.id.iv_show_chose_success);
        sv_capture = findViewById(R.id.sv_capture);
        sv_change_camera = findViewById(R.id.sv_change_camera);
        video_countdown = findViewById(R.id.videoCountdown);
        vv_show = findViewById(R.id.vv_show_camera_activity);
        head_layout = findViewById(R.id.head_layout);
        zoom_layout = findViewById(R.id.zoom_layout);
        back_uniapp = findViewById(R.id.back_uniapp);
        bottom_layout = findViewById(R.id.bottom_layout);

        //mSurfaceView
        mSurfaceView = (SurfaceView) findViewById(R.id.surface_view_camera2_activity);
        mSurfaceHolder = mSurfaceView.getHolder();
        // mSurfaceView 不需要自己的缓冲区
        mSurfaceHolder.setType(SurfaceHolder.SURFACE_TYPE_PUSH_BUFFERS);

        //调用照相机的相关类
        apiCamera = new ApiCamera(this);

        //定时器相关的类120000
        countDownTimer = new CountDownTimer(120000,video_countdown){
            @Override
            public void onFinish(){
                toggleRecordingStatus();
            }
        };

        // mSurfaceView添加回调
        mSurfaceHolder.addCallback(new SurfaceHolder.Callback() {
            @Override
            public void surfaceCreated(SurfaceHolder holder) { //SurfaceView创建
                // 初始化Camera
                boolean hasCameraPermission = ActivityCompat.checkSelfPermission(mContext, "android.permission.CAMERA") == 0;
                boolean hasStoragePermission = ActivityCompat.checkSelfPermission(mContext, "android.permission.WRITE_EXTERNAL_STORAGE") == 0 && 
                                              ActivityCompat.checkSelfPermission(mContext, "android.permission.READ_EXTERNAL_STORAGE") == 0;
                
                // 拍照模式不一定需要录音权限
                boolean hasAudioPermission = !isVideoMode || ActivityCompat.checkSelfPermission(mContext, "android.permission.RECORD_AUDIO") == 0;
                
                // 拍照必须的基本权限：相机+存储
                boolean hasBasicPermissions = hasCameraPermission && hasStoragePermission;
                // 如果是视频模式，还需要录音权限
                boolean hasAllRequiredPermissions = hasBasicPermissions && (isVideoMode ? hasAudioPermission : true);
                
                if (hasBasicPermissions) {
                    //有基本权限，可以初始化相机
                    Log.d(TAG, "已获取基本权限，初始化相机");
                    apiCamera.openCamera(mSurfaceHolder, Camera.CameraInfo.CAMERA_FACING_BACK);
                    changSurfaceView(mSurfaceView);
                    apiCamera.startPreview();
                    
                    // 如果是视频模式但没有录音权限，给出提示
                    if (isVideoMode && !hasAudioPermission) {
                        Toast.makeText(mContext, "没有录音权限，无法录制视频", Toast.LENGTH_SHORT).show();
                    }
                } else {
                    // 请求所需的基本权限
                    Log.d(TAG, "请求基本权限：相机=" + hasCameraPermission + ", 存储=" + hasStoragePermission);
                    
                    List<String> permissionsToRequest = new ArrayList<>();
                    if (!hasCameraPermission) {
                        permissionsToRequest.add("android.permission.CAMERA");
                    }
                    if (!hasStoragePermission) {
                        permissionsToRequest.add("android.permission.WRITE_EXTERNAL_STORAGE");
                        permissionsToRequest.add("android.permission.READ_EXTERNAL_STORAGE");
                    }
                    if (isVideoMode && !hasAudioPermission) {
                        permissionsToRequest.add("android.permission.RECORD_AUDIO");
                    }
                    
                    final String[] permissions = permissionsToRequest.toArray(new String[0]);
                    
                    // 使用自定义权限请求
                    PermissionReq.with(mContext).permissions(permissions).result(new PermissionReq.Result() {
                        public void onGranted() {
                            // 确保在UI线程中执行相机初始化
                            runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    try {
                                        // 权限已授予，初始化相机
                                        if (apiCamera != null) {
                                            apiCamera.release(); // 先释放之前的资源
                                        }
                                        apiCamera.openCamera(mSurfaceHolder, Camera.CameraInfo.CAMERA_FACING_BACK);
                                        changSurfaceView(mSurfaceView);
                                        apiCamera.startPreview();
                                        
                                        // 确保UI组件正确显示
                                        mSurfaceView.setVisibility(View.VISIBLE);
                                        beforeTask(); // 调用UI初始化方法
                                        
                                        Log.d(TAG, "权限已授予，相机已初始化");
                                    } catch (Exception e) {
                                        Log.e(TAG, "权限授予后初始化相机出错: " + e.getMessage());
                                        Toast.makeText(mContext, "相机初始化失败，请重试", Toast.LENGTH_SHORT).show();
                                    }
                                }
                            });
                        }

                        public void onDenied(int status) {
                            // 权限被拒绝
                            runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    Toast.makeText(mContext, "需要相机和存储权限才能使用拍照功能", Toast.LENGTH_LONG).show();
                                }
                            });
                        }
                    }).request();
                }
            }

            @Override
            public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {

            }

            @Override
            public void surfaceDestroyed(SurfaceHolder holder) { //SurfaceView销毁
                // 释放Camera资源
                apiCamera.release();
            }
        });

        //选择图片的按钮
        iv_show_chose_success.setOnClickListener(iv_chose_success);
        //不选择这张图片的按钮
        iv_show_chose_fail.setOnClickListener(iv_chose_fail);
        //调转摄像头的按钮
        sv_change_camera.setOnClickListener(sv_change_camera_cli);
        //返回uniapp的页面
        back_uniapp.setOnClickListener(back_uniapp_cli);
    }

    /**
     * 点击拍摄按钮的拍照后获取图片
     */
    ApiCamera.TakePictureCallback pictureCallback = new ApiCamera.TakePictureCallback() {
        @Override
        public void onPictureTaken(File pictureFile, int rotation) {
            if (pictureFile != null) {
                // 照片拍摄成功，捕获当前位置和方位角
                if (shouldCaptureLocation) {
                    captureLocationAndDirection();
                }
                
                // 如果需要添加水印，处理图片
                if (enableWatermark) {
                    try {
                        File watermarkedFile = addWatermarkToImage(pictureFile, rotation);
                        if (watermarkedFile != null) {
                            pictureFile = watermarkedFile; // 替换为带水印的图片
                            Log.d(TAG, "水印添加成功: " + watermarkedFile.getPath());
                        } else {
                            Log.e(TAG, "水印添加失败，将使用原始图片");
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "添加水印时发生错误: " + e.getMessage(), e);
                    }
                }
                
                if (iv_show != null && iv_show.getVisibility() == View.GONE) {
                    apiCamera.stopPreview();
                    afterTake();//控件显隐性控制
                    picturePath = pictureFile.toString();
                    Bitmap bitmap = BitmapFactory.decodeFile(pictureFile.toString());
                    iv_show.setImageBitmap(bitmap);
                }
            } else if(pictureFile == null&&rotation==410086){
                Toast.makeText(mContext, "快速点击了两次拍照按钮！", Toast.LENGTH_SHORT).show();
            }else{
                Toast.makeText(mContext, "照片保存失败！", Toast.LENGTH_SHORT).show();
            }
        }
    };

    /**
     * 在图像上添加水印信息
     * @param originalFile 原始图片文件
     * @param rotation 图片旋转角度
     * @return 添加水印后的图片文件
     */
    private File addWatermarkToImage(File originalFile, int rotation) {
        try {
            // 解码原始图片
            Bitmap originalBitmap = BitmapFactory.decodeFile(originalFile.getPath());
            if (originalBitmap == null) {
                Log.e(TAG, "无法解码原始图片: " + originalFile.getPath());
                return null;
            }
            
            // 创建可修改的副本
            Bitmap watermarkedBitmap = originalBitmap.copy(Bitmap.Config.ARGB_8888, true);
            
            // 如果需要旋转图片
            if (rotation != 0 && rotation != 410086) {
                Matrix matrix = new Matrix();
                matrix.postRotate(rotation);
                watermarkedBitmap = Bitmap.createBitmap(
                        watermarkedBitmap, 
                        0, 
                        0, 
                        watermarkedBitmap.getWidth(), 
                        watermarkedBitmap.getHeight(), 
                        matrix, 
                        true);
            }
            
            // 创建Canvas在bitmap上绘制
            Canvas canvas = new Canvas(watermarkedBitmap);
            
            // 创建文字画笔
            Paint paint = new Paint();
            paint.setColor(Color.WHITE); // 白色文字
            paint.setTextSize(watermarkedBitmap.getHeight() / 30); // 文字大小为图片高度的1/30
            paint.setTypeface(Typeface.DEFAULT_BOLD); // 粗体
            paint.setShadowLayer(3f, 1f, 1f, Color.BLACK); // 加强阴影效果，使文字在各种背景上都清晰可见
            paint.setAntiAlias(true); // 抗锯齿
            paint.setStyle(Paint.Style.FILL);
            
            // 获取当前时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
            String currentTime = sdf.format(new Date());
            
            // 计算可用宽度（图片宽度减去两边内边距）
            int padding = watermarkedBitmap.getHeight() / 50;
            int availableWidth = watermarkedBitmap.getWidth() - 2 * padding;
            
            // 准备水印文本，处理可能的长文本换行
            List<String> watermarkLines = new ArrayList<>();
            
            // 处理项目名称，可能需要换行
            addTextWithWrapping("项目: " + xmmc, paint, availableWidth, watermarkLines);
            
            // 处理单元编号，可能需要换行
            addTextWithWrapping("单元: " + pjdybh, paint, availableWidth, watermarkLines);
            
            // 其他信息通常不会太长，直接添加
            watermarkLines.add("经度: " + String.format("%.6f", capturedLongitude));
            watermarkLines.add("纬度: " + String.format("%.6f", capturedLatitude));
            watermarkLines.add("方向: " + String.format("%.1f°", capturedDirection));
            watermarkLines.add("时间: " + currentTime);
            
            // 计算文本位置
            int lineHeight = (int) (paint.getTextSize() * 1.3); // 行间距
            
            // 绘制水印文本
            int y = watermarkedBitmap.getHeight() - padding - lineHeight * (watermarkLines.size() - 1);
            
            // 逐行绘制水印文本
            for (String line : watermarkLines) {
                canvas.drawText(line, padding, y, paint);
                y += lineHeight;
            }
            
            // 保存带水印的图片到临时文件
            File watermarkedFile = new File(originalFile.getParent(), "watermarked_" + originalFile.getName());
            FileOutputStream outputStream = new FileOutputStream(watermarkedFile);
            watermarkedBitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream);
            outputStream.flush();
            outputStream.close();
            
            // 清理原始Bitmap
            originalBitmap.recycle();
            watermarkedBitmap.recycle();
            
            return watermarkedFile;
        } catch (IOException e) {
            Log.e(TAG, "添加水印过程中IO异常: " + e.getMessage(), e);
            return null;
        } catch (Exception e) {
            Log.e(TAG, "添加水印过程中异常: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 捕获当前位置和方位角
     */
    private void captureLocationAndDirection() {
        boolean hasValidLocationData = false;
        
        if (currentLocation != null) {
            // 检查位置信息的时间戳是否在有效期内（3分钟）
            long locationAge = System.currentTimeMillis() - currentLocation.getTime();
            if (locationAge <= MAX_LOCATION_AGE) {
                hasValidLocationData = true;
                capturedLatitude = currentLocation.getLatitude();
                capturedLongitude = currentLocation.getLongitude();
                Log.d(TAG, "使用有效位置信息，时间差: " + (locationAge / 1000) + "秒");
            } else {
                Log.d(TAG, "位置信息已过期，时间差: " + (locationAge / 1000) + "秒，使用默认值");
                hasValidLocationData = false;
            }
        }
        
        if (!hasValidLocationData) {
            // 如果没有有效位置，使用默认值
            capturedLatitude = 0;
            capturedLongitude = 0;
            Log.w(TAG, "拍照时没有有效位置信息，使用默认值");
        }
        
        capturedDirection = currentDirection;
        
        Log.d(TAG, "拍照瞬间捕获位置: 纬度=" + capturedLatitude + 
              ", 经度=" + capturedLongitude +
              ", 方位角=" + capturedDirection +
              ", 位置有效=" + hasValidLocationData);
    }

    /**
     * 选择照片的按钮监听，关闭当前页面，跳转会uniapp，并把图片的路径穿个uniapp
     */
    View.OnClickListener iv_chose_success = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            Intent intent = new Intent();
            if(isVideoMode){
                intent.putExtra("imagePath",videoPath);
            }else{
                intent.putExtra("imagePath",picturePath);
                
                // 添加位置和方位角信息到返回的Intent
                if (shouldCaptureLocation) {
                    intent.putExtra("latitude", capturedLatitude);
                    intent.putExtra("longitude", capturedLongitude);
                    intent.putExtra("direction", capturedDirection);
                    Log.d(TAG, "将位置信息添加到返回Intent: 纬度=" + capturedLatitude + 
                          ", 经度=" + capturedLongitude +
                          ", 方位角=" + capturedDirection);
                }
            }
            setResult(1000, intent);
            finish();
        }
    };

    /**
     * 不选择照片的按钮监听，回到拍摄的页面，
     */
    View.OnClickListener iv_chose_fail = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            beforeTask();
            apiCamera.changeCamera(mSurfaceHolder,Camera.CameraInfo.CAMERA_FACING_BACK);
            apiCamera.startPreview();
        }
    };

    /**
     * 翻转摄像头的按钮
     */
    View.OnClickListener sv_change_camera_cli = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            apiCamera.changeCamera(mSurfaceHolder);
            apiCamera.startPreview();
        }
    };

    /**
     * 返回按钮返回到uniapp页面
     */
    View.OnClickListener back_uniapp_cli = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            finish();
        }
    };


    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (mSurfaceView != null) {
//            viewWidth = mSurfaceView.getWidth();
//            viewHeight = mSurfaceView.getHeight();
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        TextView zoom_factor = findViewById(R.id.zoom_factor);
        apiCamera.onTouchEvent(event,zoom_factor); // 调用ApiCamera中的onTouchEvent方法处理手势事件
        return super.onTouchEvent(event);
    }

    /**
     * 拍摄好照片之后控件的显隐性控制
     */
    public void afterTake(){
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                // 在这里更新UI的操作
                if(isVideoMode){
                    vv_show.setVisibility(View.VISIBLE);
                }else{
                    iv_show.setVisibility(View.VISIBLE);
                }
                iv_show_chose_fail.setVisibility(View.VISIBLE);
                iv_show_chose_success.setVisibility(View.VISIBLE);
                mSurfaceView.setVisibility(View.GONE);
                sv_capture.setVisibility(View.GONE);
                bottom_layout.setVisibility(View.GONE);
                sv_change_camera.setVisibility(View.GONE);
                head_layout.setVisibility(View.INVISIBLE);
                zoom_layout.setVisibility(View.INVISIBLE);
            }
        });
    }

    public void beforeTask(){
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                // 在这里更新UI的操作
                iv_show.setVisibility(View.GONE);
                vv_show.setVisibility(View.GONE);
                iv_show_chose_fail.setVisibility(View.GONE);
                iv_show_chose_success.setVisibility(View.GONE);
                mSurfaceView.setVisibility(View.VISIBLE);
                sv_capture.setVisibility(View.VISIBLE);
                bottom_layout.setVisibility(View.VISIBLE);
                sv_change_camera.setVisibility(View.VISIBLE);
                head_layout.setVisibility(View.VISIBLE);
                zoom_layout.setVisibility(View.VISIBLE);
            }
        });
        //依据是录制模式还是拍照模式，控制图标展示
        toggleVideoMode();
    }

    private void toggleVideoMode() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                // 在这里更新UI的操作
                int resId = isVideoMode ? R.drawable.ic_capture_record : R.drawable.ic_capture;
                if (resId != 0) {
                    sv_capture.setImageResource(resId);
                }
                if (isVideoMode){
                    video_countdown.setVisibility(View.VISIBLE);
                    sv_change_camera.setVisibility(View.GONE);
                    
                    // 检查录音权限
                    if (ActivityCompat.checkSelfPermission(mContext, "android.permission.RECORD_AUDIO") != 0) {
                        Log.d(TAG, "视频模式需要录音权限，但未获取");
                        // 自动请求录音权限，然后退出相机
                        ActivityCompat.requestPermissions(
                            CameraActivity.this, 
                            new String[]{"android.permission.RECORD_AUDIO"}, 
                            1002);
                        Toast.makeText(mContext, "请授予录音权限后重新打开相机进行录制", Toast.LENGTH_LONG).show();
                        // 延迟一秒关闭，让Toast显示出来
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                finish(); // 关闭活动
                            }
                        }, 1000);
                    }
                } else {
                    video_countdown.setVisibility(View.GONE);
                    sv_change_camera.setVisibility(View.VISIBLE);
                }
            }
        });
    }

    private void toggleRecordingStatus() {
        if (!isVideoMode) return;

        isRecording = !isRecording;

        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                // 在这里更新UI的操作
                int resId = isRecording ? R.drawable.ic_capture_record_pressing : R.drawable.ic_capture_record;
                if (resId != 0){
                    sv_capture.setImageResource(resId);
                }

                // 切换到false的时候停止录制
                if (!isRecording) {
                    //停止录制且停止倒计时的计时器
                    apiCamera.releaseMediaRecorder();
                    countDownTimer.stop();
                    //切换到录制好的视频界面，并且直接播放
                    afterTake();
                    Uri uri = Uri.parse(videoPath);
                    vv_show.setVideoPath(String.valueOf(uri));
                    vv_show.start();
                }
            }
        });
    }

    private void changSurfaceView(SurfaceView surfaceView){
        //获取手机屏幕的宽度
        DisplayMetrics displayMetrics = new DisplayMetrics();
        getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
        int screenWidth = displayMetrics.widthPixels;

        //根据4:3的比例计算SurfaceView的高度
        int surfaceHeight = screenWidth * 4 / 3;

        //获取状态栏的高度
        Rect rectangle = new Rect();
        Window window = getWindow();
        window.getDecorView().getWindowVisibleDisplayFrame(rectangle);
        int statusBarHeight = rectangle.top;

        //获取不包括状态栏的屏幕高度
        Resources resources = this.getResources();
        int screenHeight = resources.getDisplayMetrics().heightPixels - statusBarHeight;

        //获取顶部导航栏的高度
        head_layout.measure(0, 0);
        int headLayoutHeight = head_layout.getMeasuredHeight();

        //设置SurfaceView的布局参数
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(screenWidth, surfaceHeight);
        //上边距为顶部导航栏的高度
        params.setMargins(0, headLayoutHeight, 0, 0);
        surfaceView.setLayoutParams(params);
        Log.d("size", "changSurfaceView: "+screenWidth+"*"+surfaceHeight);

        //设置底部按钮栏的高度是剩余可用高度
        bottom_layout.getLayoutParams().height = screenHeight - headLayoutHeight - surfaceHeight;
    }

    // SensorEventListener接口实现
    @Override
    public void onSensorChanged(SensorEvent event) {
        if (event.sensor.getType() == Sensor.TYPE_ACCELEROMETER) {
            System.arraycopy(event.values, 0, accelerometerReading, 0, accelerometerReading.length);
        } else if (event.sensor.getType() == Sensor.TYPE_MAGNETIC_FIELD) {
            System.arraycopy(event.values, 0, magnetometerReading, 0, magnetometerReading.length);
        }
        
        // 更新方位角
        updateOrientationAngles();
    }
    
    /**
     * 更新方位角信息
     */
    private void updateOrientationAngles() {
        // 更新旋转矩阵
        boolean rotationOk = SensorManager.getRotationMatrix(rotationMatrix, null, accelerometerReading, magnetometerReading);
        
        if (rotationOk) {
            // 获取方位角、俯仰角和滚动角
            SensorManager.getOrientation(rotationMatrix, orientationAngles);
            
            // orientationAngles[0]是方位角（方向），单位为弧度，需要转换为角度
            float azimuthRadians = orientationAngles[0];
            float azimuthDegrees = (float) Math.toDegrees(azimuthRadians);
            
            // 确保角度在0-360之间
            azimuthDegrees = (azimuthDegrees + 360) % 360;
            
            // 将磁北方位角转换为真北方位角
            // 磁偏角会根据地理位置不同而变化，国内大部分地区的磁偏角在-8°至+8°之间
            // 这里我们使用一个估计值：中国大部分地区磁偏角约为-6°(西偏)，广州地区约为-2°
            float magneticDeclination = -2.0f; // 广州地区磁偏角约为-2度(西偏)
            float trueNorthAzimuth = (azimuthDegrees + magneticDeclination + 360) % 360;
            
            // 更新当前方位角为真北方位角
            currentDirection = trueNorthAzimuth;
            
            // 记录日志
            if (Math.abs(currentDirection - trueNorthAzimuth) > 5) {
                Log.d(TAG, "当前磁北方位角: " + azimuthDegrees + "°, 真北方位角: " + trueNorthAzimuth + "°");
            }
        }
    }
    
    @Override
    public void onAccuracyChanged(Sensor sensor, int accuracy) {
        // 不需要处理传感器精度变化
    }
    
    // LocationListener接口实现
    @Override
    public void onLocationChanged(Location location) {
        if (location != null) {
            // 只有当位置真的变化时才更新
            if (currentLocation == null || 
                location.getLatitude() != currentLocation.getLatitude() || 
                location.getLongitude() != currentLocation.getLongitude()) {
                
                currentLocation = location;
                
                // 设置已有有效位置
                double lat = location.getLatitude();
                double lng = location.getLongitude();
                if (lat != 0 || lng != 0) {
                    hasValidLocation = true;
                    
                    // 如果正在等待GPS定位，显示当前位置信息
                    if (isCheckingGpsBeforeCapture) {
                        showLocationInfo();
                    }
                }
                
                Log.d(TAG, "位置已更新: 纬度=" + lat + 
                         ", 经度=" + lng + 
                         ", 有效性=" + hasValidLocation);
            }
        }
    }
    
    /**
     * 显示当前位置信息
     */
    private void showLocationInfo() {
        if (currentLocation != null) {
            String locationInfo = String.format("当前位置: %.6f, %.6f", 
                currentLocation.getLatitude(), currentLocation.getLongitude());
                
            // 如果有海拔数据，也显示海拔
            // if (currentLocation.hasAltitude()) { // 移除此行
            //     locationInfo += String.format(", 海拔: %.1fm", currentLocation.getAltitude()); // 移除此行
            // } // 移除此行
                
            Toast.makeText(mContext, locationInfo, Toast.LENGTH_SHORT).show();
            Log.d(TAG, "显示位置信息: " + locationInfo);
        }
    }
    
    @Override
    public void onStatusChanged(String provider, int status, Bundle extras) {
        // Android新版本已废弃此方法
    }
    
    @Override
    public void onProviderEnabled(String provider) {
        Log.d(TAG, "位置提供程序已启用: " + provider);
    }
    
    @Override
    public void onProviderDisabled(String provider) {
        Log.d(TAG, "位置提供程序已禁用: " + provider);
    }

    /**
     * 检查GPS状态，如果没有位置信息则等待或提供选项
     */
    private void checkGpsBeforeTakingPhoto() {
        // 检查是否有位置信息
        if (currentLocation != null && hasValidLocation) {
            // 有位置信息，直接拍照
            capturePhotoOrVideo();
            return;
        }
        
        // 没有位置信息，显示等待对话框
        isWaitingForLocation = true;
        isCheckingGpsBeforeCapture = true;
        
        final ProgressDialog progressDialog = new ProgressDialog(this);
        progressDialog.setTitle("等待GPS信号");
        progressDialog.setMessage("正在获取位置信息，请将设备放在室外开阔位置...");
        progressDialog.setCancelable(true);
        
        // 定义超时任务，防止无限等待
        final Runnable locationTimeoutRunnable = new Runnable() {
            @Override
            public void run() {
                if (isWaitingForLocation) {
                    isWaitingForLocation = false;
                    isCheckingGpsBeforeCapture = false;
                    
                    // 关闭进度对话框
                    if (progressDialog.isShowing()) {
                        progressDialog.dismiss();
                    }
                    
                    // 提示用户GPS信号弱
                    Toast.makeText(CameraActivity.this, 
                        "无法获取位置信息，请确保设备在室外开阔位置并重试", 
                        Toast.LENGTH_LONG).show();
                }
            }
        };
        
        progressDialog.setButton(DialogInterface.BUTTON_NEGATIVE, "取消", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                isWaitingForLocation = false;
                isCheckingGpsBeforeCapture = false;
                handler.removeCallbacks(locationTimeoutRunnable);
            }
        });
        progressDialog.show();
        
        // 检查位置权限
        if (ActivityCompat.checkSelfPermission(this, android.Manifest.permission.ACCESS_FINE_LOCATION) != 0) {
            progressDialog.dismiss();
            Toast.makeText(this, "没有位置权限，无法拍照", Toast.LENGTH_LONG).show();
            return;
        }
        
        // 尝试获取位置
        try {
            // 确保位置管理器已初始化
            if (locationManager == null) {
                locationManager = (LocationManager) getSystemService(Context.LOCATION_SERVICE);
            }
            
            // 检查GPS是否开启
            boolean isGpsEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
            boolean isNetworkEnabled = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER);
            
            if (!isGpsEnabled && !isNetworkEnabled) {
                // GPS和网络定位都未开启
                progressDialog.dismiss();
                Toast.makeText(this, "请开启GPS或网络定位服务后再试", Toast.LENGTH_LONG).show();
                return;
            }
            
            // 请求位置更新
            if (isGpsEnabled) {
                locationManager.requestLocationUpdates(
                    LocationManager.GPS_PROVIDER,
                    100, // 0.1秒更新一次
                    0.1f, // 0.1米变化即更新
                    new LocationListener() {
                        @Override
                        public void onLocationChanged(Location location) {
                            if (location != null && isWaitingForLocation) {
                                currentLocation = location;
                                hasValidLocation = true;
                                isWaitingForLocation = false;
                                
                                // 移除超时回调
                                handler.removeCallbacks(locationTimeoutRunnable);
                                
                                // 关闭进度对话框
                                if (progressDialog.isShowing()) {
                                    progressDialog.dismiss();
                                }
                                
                                // 拍照
                                capturePhotoOrVideo();
                                
                                // 取消位置更新
                                try {
                                    locationManager.removeUpdates(this);
                                } catch (SecurityException e) {
                                    Log.e(TAG, "移除位置更新错误: " + e.getMessage());
                                }
                            }
                        }
                        
                        @Override
                        public void onStatusChanged(String provider, int status, Bundle extras) {}
                        
                        @Override
                        public void onProviderEnabled(String provider) {}
                        
                        @Override
                        public void onProviderDisabled(String provider) {}
                    }
                );
            }
            
            if (isNetworkEnabled) {
                locationManager.requestLocationUpdates(
                    LocationManager.NETWORK_PROVIDER,
                    100, // 0.1秒更新一次
                    0.1f, // 0.1米变化即更新
                    new LocationListener() {
                        @Override
                        public void onLocationChanged(Location location) {
                            if (location != null && isWaitingForLocation) {
                                currentLocation = location;
                                hasValidLocation = true;
                                isWaitingForLocation = false;
                                
                                // 移除超时回调
                                handler.removeCallbacks(locationTimeoutRunnable);
                                
                                // 关闭进度对话框
                                if (progressDialog.isShowing()) {
                                    progressDialog.dismiss();
                                }
                                
                                // 拍照
                                capturePhotoOrVideo();
                                
                                // 取消位置更新
                                try {
                                    locationManager.removeUpdates(this);
                                } catch (SecurityException e) {
                                    Log.e(TAG, "移除位置更新错误: " + e.getMessage());
                                }
                            }
                        }
                        
                        @Override
                        public void onStatusChanged(String provider, int status, Bundle extras) {}
                        
                        @Override
                        public void onProviderEnabled(String provider) {}
                        
                        @Override
                        public void onProviderDisabled(String provider) {}
                    }
                );
            }
            
            // 20秒后超时
            handler.postDelayed(locationTimeoutRunnable, 20000);
            
        } catch (SecurityException e) {
            progressDialog.dismiss();
            Log.e(TAG, "位置权限错误: " + e.getMessage());
            Toast.makeText(this, "位置权限错误，无法拍照", Toast.LENGTH_LONG).show();
        } catch (Exception e) {
            progressDialog.dismiss();
            Log.e(TAG, "获取位置错误: " + e.getMessage());
            Toast.makeText(this, "获取位置信息失败，无法拍照", Toast.LENGTH_LONG).show();
        }
    }

    /**
     * 执行拍照或录像
     */
    private void capturePhotoOrVideo() {
        // 录像模式直接执行
        if(isVideoMode) {
            executeMediaCapture();
            return;
        }
        
        // 拍照模式使用后台定位的位置信息
        if (currentLocation != null) {
            // 已有位置信息，直接拍照
            executeMediaCapture();
        } else if (locationManager != null && ActivityCompat.checkSelfPermission(this, android.Manifest.permission.ACCESS_FINE_LOCATION) == 0) {
            // 尝试获取一次最新位置
            updateCurrentLocation();
            
            // 无论是否获取到位置，都直接拍照
            executeMediaCapture();
        } else {
            // 无位置管理器或无权限，直接拍照
            executeMediaCapture();
        }
    }
    
    /**
     * 实际执行拍照或录像操作
     */
    private void executeMediaCapture() {
        String picturePath = FileUtils.getSDPath(mContext) + "/BCGDGISData/BCGDCameraData";
        
        // 确保目录存在
        File directory = new File(picturePath);
        if (!directory.exists()) {
            directory.mkdirs();
        }
        
        if (isVideoMode && !isRecording) {
            // 再次检查录音权限
            if (ActivityCompat.checkSelfPermission(mContext, "android.permission.RECORD_AUDIO") != 0) {
                Toast.makeText(mContext, "需要录音权限才能录制视频", Toast.LENGTH_SHORT).show();
                // 请求权限并关闭
                ActivityCompat.requestPermissions(
                    CameraActivity.this, 
                    new String[]{"android.permission.RECORD_AUDIO"}, 
                    1002);
                // 延迟关闭
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        finish();
                    }
                }, 1000);
                return;
            }
            
            try {
                File file = new File(picturePath, FileUtils.getTimeStampFileName(1));
                Surface surface = mSurfaceHolder.getSurface();
                boolean success = apiCamera.startVideoRecorder(file, surface);
                if (success) {
                    videoPath = file.toString();
                    countDownTimer.start();
                    toggleRecordingStatus(); // 修改按钮的状态
                } else {
                    Toast.makeText(mContext, "无法初始化视频录制，请重试", Toast.LENGTH_SHORT).show();
                }
            } catch (Exception e) {
                Toast.makeText(mContext, "视频录制错误: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                Log.e(TAG, "视频录制错误", e);
            }
        } else if (isVideoMode && isRecording) {
            try {
                toggleRecordingStatus();
            } catch (Exception e) {
                Toast.makeText(mContext, "停止录制失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                Log.e(TAG, "停止录制失败", e);
            }
        } else {
            // 拍照
            File file = new File(picturePath, FileUtils.getTimeStampFileName(0));
            apiCamera.takePhoto(file, 2000000, pictureCallback);
        }
    }

    /**
     * 处理权限请求结果
     */
    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        Log.d(TAG, "权限请求回调，请求码: " + requestCode + "，权限数量: " + permissions.length);
        
        // 判断是位置权限还是相机权限请求
        if (requestCode == 1001) {  // 位置权限请求码
            // 检查是否授予了位置权限
            boolean locationPermissionGranted = false;
            for (int i = 0; i < permissions.length; i++) {
                if (permissions[i].equals(android.Manifest.permission.ACCESS_FINE_LOCATION) && 
                    grantResults[i] == android.content.pm.PackageManager.PERMISSION_GRANTED) {
                    locationPermissionGranted = true;
                    break;
                }
            }
            
            if (locationPermissionGranted) {
                Log.d(TAG, "位置权限已授予，初始化位置服务");
                // 权限已授予，重新初始化位置服务
                initLocationAndSensors();
                
                // 启动后台定位服务
                startBackgroundLocationUpdates();
            } else {
                Log.d(TAG, "位置权限被拒绝");
                Toast.makeText(mContext, "没有位置权限，将无法获取GPS坐标", Toast.LENGTH_LONG).show();
            }
            return;
        }
        
        // 判断请求码
        if (requestCode == 1002) {  // 录音权限请求码
            // 检查是否授予了录音权限
            boolean audioPermissionGranted = false;
            for (int i = 0; i < permissions.length; i++) {
                if (permissions[i].equals("android.permission.RECORD_AUDIO") && 
                    grantResults[i] == android.content.pm.PackageManager.PERMISSION_GRANTED) {
                    audioPermissionGranted = true;
                    break;
                }
            }
            
            if (audioPermissionGranted) {
                Log.d(TAG, "录音权限已授予，请重新打开相机进行录制");
                Toast.makeText(mContext, "录音权限已授予，请重新打开相机进行录制", Toast.LENGTH_LONG).show();
                // 延迟关闭，让Toast显示出来
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        finish(); // 直接关闭活动
                    }
                }, 1000);
            } else {
                Log.d(TAG, "录音权限被拒绝");
                Toast.makeText(mContext, "录音权限被拒绝，无法进行视频录制", Toast.LENGTH_LONG).show();
                // 延迟关闭，让Toast显示出来
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        finish(); // 直接关闭活动
                    }
                }, 1000);
            }
            return;
        }
        
        // 处理相机权限
        boolean cameraPermissionGranted = false;
        for (int i = 0; i < permissions.length; i++) {
            if (permissions[i].equals("android.permission.CAMERA") && 
                grantResults[i] == android.content.pm.PackageManager.PERMISSION_GRANTED) {
                cameraPermissionGranted = true;
                break;
            }
        }
        
        if (cameraPermissionGranted) {
            Log.d(TAG, "系统权限回调：相机权限已授予，重新初始化相机");
            
            // 权限授予后重新初始化相机
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    try {
                        // 重新创建相机
                        if (apiCamera != null) {
                            apiCamera.release(); // 先释放
                        }
                        
                        // 重新设置SurfaceView
                        if (mSurfaceHolder != null) {
                            apiCamera.openCamera(mSurfaceHolder, Camera.CameraInfo.CAMERA_FACING_BACK);
                            changSurfaceView(mSurfaceView);
                            apiCamera.startPreview();
                            
                            // 刷新UI
                            beforeTask();
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "初始化相机出错: " + e.getMessage());
                        // 出错时不要立即关闭，让用户有机会重试
                        Toast.makeText(mContext, "相机初始化失败，请重试", Toast.LENGTH_SHORT).show();
                    }
                }
            });
        } else {
            // 相机权限被拒绝
            Log.d(TAG, "系统权限回调：相机权限被拒绝");
            // 只显示提示但不关闭活动，让用户有机会再次请求权限
            Toast.makeText(mContext, "需要相机权限才能使用拍照功能", Toast.LENGTH_LONG).show();
        }
    }

    /**
     * 启动后台定位服务，每10秒更新一次位置
     */
    private void startBackgroundLocationUpdates() {
        if (isBackgroundLocationUpdateRunning) {
            return; // 已经在运行，不需要重复启动
        }
        
        // 检查位置权限
        if (ActivityCompat.checkSelfPermission(this, android.Manifest.permission.ACCESS_FINE_LOCATION) != 0) {
            Log.d(TAG, "没有位置权限，无法启动后台定位");
            return;
        }
        
        // 创建后台定位任务
        backgroundLocationUpdateTask = new Runnable() {
            @Override
            public void run() {
                try {
                    // 检查位置管理器是否可用
                    if (locationManager != null) {
                        // 尝试获取最新位置
                        updateCurrentLocation();
                        
                        // 记录定位时间
                        Log.d(TAG, "后台定位更新: " + (currentLocation != null ? 
                                currentLocation.getLatitude() + ", " + currentLocation.getLongitude() : "无位置"));
                    }
                } catch (Exception e) {
                    Log.e(TAG, "后台定位更新错误: " + e.getMessage());
                }
                
                // 安排下一次更新
                handler.postDelayed(this, BACKGROUND_LOCATION_UPDATE_INTERVAL);
            }
        };
        
        // 立即执行一次，然后每10秒执行一次
        handler.post(backgroundLocationUpdateTask);
        isBackgroundLocationUpdateRunning = true;
        Log.d(TAG, "后台定位服务已启动");
    }

    /**
     * 停止后台定位服务
     */
    private void stopBackgroundLocationUpdates() {
        if (!isBackgroundLocationUpdateRunning) {
            return; // 没有运行，不需要停止
        }
        
        // 移除后台定位任务
        if (backgroundLocationUpdateTask != null) {
            handler.removeCallbacks(backgroundLocationUpdateTask);
        }
        
        isBackgroundLocationUpdateRunning = false;
        Log.d(TAG, "后台定位服务已停止");
    }

    /**
     * 更新当前位置信息
     */
    private void updateCurrentLocation() {
        try {
            if (locationManager == null) return;
            
            // 检查权限
            if (ActivityCompat.checkSelfPermission(this, android.Manifest.permission.ACCESS_FINE_LOCATION) != 0) {
                return;
            }
            
            // 尝试获取GPS位置
            Location gpsLocation = locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER);
            // 尝试获取网络位置
            Location networkLocation = locationManager.getLastKnownLocation(LocationManager.NETWORK_PROVIDER);
            
            // 选择最新的位置
            Location bestLocation = getBestLocation(gpsLocation, networkLocation);
            
            if (bestLocation != null) {
                // 检查位置是否在有效期内（3分钟）
                long locationAge = System.currentTimeMillis() - bestLocation.getTime();
                if (locationAge <= MAX_LOCATION_AGE) {
                    // 更新当前位置
                    currentLocation = bestLocation;
                    
                    // 标记已有有效位置
                    hasValidLocation = true;
                    
                    Log.d(TAG, "更新位置成功: 纬度=" + bestLocation.getLatitude() + 
                          ", 经度=" + bestLocation.getLongitude() + 
                          ", 时间差=" + (locationAge / 1000) + "秒");
                } else {
                    Log.d(TAG, "位置信息已过期，时间差: " + (locationAge / 1000) + "秒，需要重新获取");
                    // 位置已过期，不更新
                    hasValidLocation = false;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "更新位置错误: " + e.getMessage());
        }
    }

    /**
     * 获取两个位置中较好的一个
     * @param location1 第一个位置
     * @param location2 第二个位置
     * @return 较好的位置
     */
    private Location getBestLocation(Location location1, Location location2) {
        if (location1 == null && location2 == null) return null;
        if (location1 == null) return location2;
        if (location2 == null) return location1;
        
        // 比较位置时间，选择最新的
        if (location1.getTime() > location2.getTime()) {
            return location1;
        } else {
            return location2;
        }
    }

    /**
     * 辅助方法：将文本按指定宽度换行
     * @param text 要换行的文本
     * @param paint 画笔，用于获取文本宽度
     * @param maxWidth 最大可用宽度
     * @param lines 存储换行后文本的列表
     */
    private void addTextWithWrapping(String text, Paint paint, int maxWidth, List<String> lines) {
        if (text == null || text.isEmpty()) {
            return;
        }
        
        // 如果文本长度在可用宽度内，直接添加
        if (paint.measureText(text) <= maxWidth) {
            lines.add(text);
            return;
        }
        
        // 文本超过宽度，需要换行
        StringBuilder currentLine = new StringBuilder();
        int i = 0;
        
        while (i < text.length()) {
            char currentChar = text.charAt(i);
            String testLine = currentLine.toString() + currentChar;
            
            // 检查添加当前字符后是否超过宽度
            if (paint.measureText(testLine) <= maxWidth) {
                currentLine.append(currentChar);
                i++;
            } else {
                // 当前行已满，保存当前行并开始新行
                if (currentLine.length() > 0) {
                    lines.add(currentLine.toString());
                    currentLine = new StringBuilder();
                } else {
                    // 如果单个字符就超过宽度，强制添加避免无限循环
                    currentLine.append(currentChar);
                    lines.add(currentLine.toString());
                    currentLine = new StringBuilder();
                    i++;
                }
            }
        }
        
        // 添加最后一行
        if (currentLine.length() > 0) {
            lines.add(currentLine.toString());
        }
    }
}

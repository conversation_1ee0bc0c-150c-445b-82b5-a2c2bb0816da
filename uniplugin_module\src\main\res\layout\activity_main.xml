<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="?attr/actionBarSize"
        android:visibility="gone" />

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@android:color/white"
        app:menu="@menu/bottom_navigation_menu"
        android:visibility="gone" />

    <com.esri.arcgisruntime.mapping.view.MapView
        android:id="@+id/mapView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="10dp"
        tools:context="io.dcloud.uniplugin.com.abdu.qrcode.MainActivity"/>

    <com.esri.arcgisruntime.toolkit.scalebar.Scalebar
        android:orientation="horizontal"
        android:id="@+id/scalebar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:fillColor="@android:color/holo_orange_light"
        app:lineColor="@android:color/holo_blue_bright"
        app:style="line"
        app:textColor="@android:color/black"
        app:textShadowColor="@android:color/white"
        app:unitSystem="metric"
        android:layout_gravity="start|bottom"
        tools:ignore="MissingClass"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/pointInfoL"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="70dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:background="#80FFFFFF"
        android:padding="8dp"
        android:visibility="gone">
        <TextView
            android:id="@+id/legend_label1"
            android:layout_width="wrap_content" 
            android:layout_height="wrap_content" 
            android:background="@color/white"
            android:gravity="center"
            android:text="目标点" />
        <ImageButton
            android:id="@+id/blue"
            style="@style/color_btn"
            android:contentDescription="@string/app_name"
            app:srcCompat="@drawable/blue" />

        <TextView
            android:id="@+id/legend_label2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:background="@color/white"
            android:gravity="center"
            android:text="已调查" />
        <ImageButton
            android:id="@+id/green"
            style="@style/color_btn"
            android:contentDescription="@string/app_name"
            app:srcCompat="@drawable/green" />

        <TextView
            android:id="@+id/legend_label3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:background="@color/white"
            android:gravity="center"
            android:text="未调查" />
        <ImageButton
            android:id="@+id/yellow"
            style="@style/color_btn"
            android:contentDescription="@string/app_name"
            app:srcCompat="@drawable/yellow" />


        <TextView
            android:id="@+id/legend_label4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:background="@color/white"
            android:gravity="center"
            android:text="今调查" />
        <ImageButton
            android:id="@+id/darkgreen"
            style="@style/color_btn"
            android:contentDescription="@string/app_name"
            app:srcCompat="@drawable/darkgreen" />


    </LinearLayout>

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|top"
        android:layout_marginTop="50dp"
        android:layout_marginEnd="20dp">
        <ImageButton
            android:id="@+id/compassButton2"
            style="@style/map_btn"
            android:contentDescription="@string/app_name" />

        <ImageButton
            android:id="@+id/compassButton"
            style="@style/map_btn"
            android:background="@drawable/bg_go"
            android:contentDescription="@string/app_name"
            app:srcCompat="@drawable/north" />
    </FrameLayout>

    <ImageButton
        android:id="@+id/zoomInButton"
        style="@style/map_btn"
        android:layout_gravity="start|bottom"
        android:layout_marginStart="20dp"
        android:layout_marginBottom="140dp"
        android:contentDescription="@string/app_name"
        app:srcCompat="@drawable/addbold" />

    <ImageButton
        android:id="@+id/LegendButton"
        style="@style/map_btn"
        android:layout_gravity="start|top"
        android:layout_marginStart="20dp"
        android:layout_marginTop="390dp"
        android:contentDescription="@string/app_name"
        android:visibility="gone"
        app:srcCompat="@drawable/legend" />

    <ImageView
        android:id="@+id/legend"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_gravity="start|top"
        android:layout_marginStart="20dp"
        android:layout_marginTop="450dp"
        android:visibility="gone" />

    <ImageButton
        android:id="@+id/zomOutButton"
        style="@style/map_btn"
        android:layout_gravity="start|bottom"
        android:layout_marginStart="20dp"
        android:layout_marginBottom="80dp"
        android:contentDescription="@string/app_name"
        app:srcCompat="@drawable/minus" />

    <ImageButton
        android:id="@+id/fullPicButton"
        style="@style/map_btn"
        android:layout_gravity="end|top"
        android:layout_marginTop="140dp"
        android:layout_marginEnd="20dp"
        android:contentDescription="@string/app_name"
        android:visibility="invisible"
        app:srcCompat="@drawable/fullpic" />

    <ImageButton
        android:id="@+id/layersButton"
        style="@style/map_btn"
        android:layout_gravity="end|top"
        android:layout_marginTop="185dp"
        android:layout_marginEnd="20dp"
        android:contentDescription="@string/app_name"
        android:visibility="invisible"
        app:srcCompat="@drawable/layers" />

    <ImageButton
        android:id="@+id/measurementButton"
        style="@style/map_btn"
        android:layout_gravity="end|top"
        android:layout_marginTop="230dp"
        android:layout_marginEnd="20dp"
        android:contentDescription="@string/app_name"
        android:visibility="invisible"
        app:srcCompat="@drawable/ruler" />

    <ImageButton
        android:id="@+id/currentLoButton"
        style="@style/map_btn"
        android:layout_gravity="end|top"
        android:layout_marginTop="440dp"
        android:layout_marginEnd="20dp"
        android:contentDescription="@string/app_name"
        app:srcCompat="@drawable/current_lo"
        android:visibility="gone" />

    <ImageButton
        android:id="@+id/layerChange"
        style="@style/map_btn"
        android:layout_gravity="end|top"
        android:layout_marginTop="275dp"
        android:layout_marginEnd="20dp"
        android:contentDescription="@string/app_name"
        android:visibility="invisible"
        app:srcCompat="@drawable/map" />

    <ImageButton
        android:id="@+id/identifyButton"
        style="@style/map_btn"
        android:layout_gravity="end|top"
        android:layout_marginTop="320dp"
        android:layout_marginEnd="20dp"
        android:contentDescription="@string/app_name"
        android:visibility="invisible"
        app:srcCompat="@drawable/identify" />

    <ImageButton
        android:id="@+id/navigationButton"
        style="@style/map_btn"
        android:layout_gravity="end|top"
        android:layout_marginTop="365dp"
        android:layout_marginEnd="20dp"
        android:contentDescription="@string/app_name"
        android:visibility="gone"
        app:srcCompat="@drawable/orbit" />

    <ImageButton
        android:id="@+id/toolbar_content"
        style="@style/map_btn"
        android:layout_gravity="end|top"
        android:layout_marginTop="95dp"
        android:layout_marginEnd="20dp"
        android:contentDescription="@string/app_name"
        app:srcCompat="@drawable/toolbox" />

    <include
        android:id="@+id/toolbarInclude"
        layout="@layout/measurement_toolbar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal|top"
        android:layout_marginTop="10dp"
        android:visibility="gone"/>

    <LinearLayout
        android:id="@+id/bottomSheet"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/white"
        android:orientation="vertical"
        app:behavior_hideable="true"
        app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior"
        android:visibility="invisible">

        <io.dcloud.uniplugin.BottomSheetRecyclerView
            android:id="@+id/layersRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
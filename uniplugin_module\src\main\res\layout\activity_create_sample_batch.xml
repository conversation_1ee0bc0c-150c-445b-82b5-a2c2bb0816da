<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="io.dcloud.uniplugin.sampleflow.CreateSampleBatchActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="样品信息"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp"/>

        <!-- 项目选择 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="项目选择"
            android:textSize="16sp"
            android:layout_marginBottom="8dp"/>

        <Spinner
            android:id="@+id/spinnerProject"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="48dp"
            android:layout_marginBottom="16dp"
            android:background="@android:drawable/btn_dropdown"/>

        <!-- 批次类型选择 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="批次类型"
            android:textSize="16sp"
            android:layout_marginBottom="8dp"/>

        <Spinner
            android:id="@+id/spinnerBatchType"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="48dp"
            android:layout_marginBottom="16dp"
            android:background="@android:drawable/btn_dropdown"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#E0E0E0"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="16dp"/>

        <!-- 送样信息部分（可隐藏） -->
        <LinearLayout
            android:id="@+id/layoutSenderInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="送样信息"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp"/>

            <!-- 送样单位 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:hint="送样单位">

                <EditText
                    android:id="@+id/editTextSendOrg"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- 送样人姓名 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:hint="送样人姓名">

                <EditText
                    android:id="@+id/editTextSenderName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPersonName"
                    android:maxLines="1" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- 送样人联系电话 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="送样人联系电话">

                <EditText
                    android:id="@+id/editTextSenderPhone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="phone"
                    android:maxLines="1" />
            </com.google.android.material.textfield.TextInputLayout>
        </LinearLayout>

        <!-- 送样人签名区域 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="送样人签名"
            android:textSize="16sp"
            android:layout_marginBottom="8dp"/>
        
        <!-- 签名状态提示 -->
        <TextView
            android:id="@+id/textViewSignatureStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#888888"
            android:text="请在下方区域签名"
            android:layout_marginBottom="8dp"/>

        <!-- 签名区域容器 -->
        <FrameLayout
            android:id="@+id/layoutSignatureArea"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#F5F5F5"
            android:padding="8dp"
            android:descendantFocusability="beforeDescendants"
            android:clickable="true"
            android:focusable="true"
            android:focusableInTouchMode="true">
            
            <!-- 签名视图 -->
            <io.dcloud.uniplugin.view.SignatureView
                android:id="@+id/signatureViewSender"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:background="#FFFFFF"/>
        </FrameLayout>
        
        <!-- 签名按钮区域 -->
        <LinearLayout
            android:id="@+id/layoutSignatureButtons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp">

            <Button
                android:id="@+id/buttonClearSignature"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="清除签名"
                android:textSize="14sp"
                style="@style/Widget.AppCompat.Button.Colored"
                android:backgroundTint="#9E9E9E"
                android:layout_marginEnd="4dp"/>

            <Button
                android:id="@+id/buttonConfirmSignature"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="确认签名"
                android:textSize="14sp"
                style="@style/Widget.AppCompat.Button.Colored"
                android:backgroundTint="#2196F3"
                android:layout_marginStart="4dp"/>

        </LinearLayout>
        
        <!-- 签名预览区域 -->
        <LinearLayout
            android:id="@+id/layoutSignaturePreview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="16dp"
            android:visibility="gone">
            
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="已上传签名"
                android:textSize="16sp"
                android:layout_marginBottom="8dp"/>
                
            <ImageView
                android:id="@+id/imageViewSignaturePreview"
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:scaleType="fitCenter"
                android:background="#F5F5F5"/>
                
            <!-- 重新签名按钮 -->
            <Button
                android:id="@+id/buttonRenewSignature"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="重新签名"
                android:textSize="14sp"
                style="@style/Widget.AppCompat.Button.Colored"
                android:backgroundTint="#FF9800"
                android:layout_marginTop="8dp"/>
                
            <!-- 移除URL显示 -->
            <TextView
                android:id="@+id/textViewSignatureUrl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="#666666"
                android:layout_marginTop="4dp"
                android:maxLines="2"
                android:ellipsize="end"
                android:visibility="gone"/>
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#E0E0E0"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"/>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="配送信息"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp"/>

        <!-- 接样单位（检测机构） -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="接样单位（检测机构）"
            android:textSize="16sp"
            android:layout_marginBottom="8dp"/>

        <Spinner
            android:id="@+id/spinnerReceiveOrg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="48dp"
            android:layout_marginBottom="16dp"
            android:background="@android:drawable/btn_dropdown"/>

        <!-- 配送方式 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="配送方式">

            <EditText
                android:id="@+id/editTextDeliveryType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLines="1" />
        </com.google.android.material.textfield.TextInputLayout>
        
        <!-- 配送信息 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="配送信息">

            <EditText
                android:id="@+id/editTextDeliveryMessage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLines="2" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- 按钮区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="24dp">

            <Button
                android:id="@+id/buttonCancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="取消"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:textColor="#FF5722"
                android:layout_marginEnd="8dp"/>

            <Button
                android:id="@+id/buttonSubmit"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="提交"
                style="@style/Widget.AppCompat.Button.Colored"
                android:backgroundTint="#4CAF50"
                android:layout_marginStart="8dp"/>
        </LinearLayout>

    </LinearLayout>

</ScrollView> 
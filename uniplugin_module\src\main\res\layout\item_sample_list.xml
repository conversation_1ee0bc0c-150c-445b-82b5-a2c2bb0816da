<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 样品编号和类型 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/textViewSampleNumber"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="样品编号"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#333333" />

            <TextView
                android:id="@+id/textViewSampleType"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="样品类型"
                android:textSize="14sp"
                android:textColor="#666666"
                android:background="#E3F2FD"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <!-- 分隔线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#E0E0E0"
            android:layout_marginBottom="8dp" />

        <!-- 样品详细信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textViewSampleWeight"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="重量: 0.0g"
                android:textSize="14sp"
                android:textColor="#666666"
                android:layout_marginBottom="4dp"
                android:drawableStart="@android:drawable/ic_menu_sort_by_size"
                android:drawablePadding="8dp"
                android:gravity="center_vertical" />

            <TextView
                android:id="@+id/textViewSampleCode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="标识码: "
                android:textSize="14sp"
                android:textColor="#666666"
                android:layout_marginBottom="4dp"
                android:drawableStart="@android:drawable/ic_menu_info_details"
                android:drawablePadding="8dp"
                android:gravity="center_vertical" />

            <TextView
                android:id="@+id/textViewCreateTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="创建时间: "
                android:textSize="14sp"
                android:textColor="#999999"
                android:layout_marginBottom="8dp"
                android:drawableStart="@android:drawable/ic_menu_recent_history"
                android:drawablePadding="8dp"
                android:gravity="center_vertical" />

        </LinearLayout>

        <!-- 操作按钮区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end"
            android:weightSum="3">

            <Button
                android:id="@+id/btnEditSample"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="36dp"
                android:text="修改"
                android:textSize="11sp"
                android:textColor="@android:color/white"
                android:background="#2196F3"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                android:drawableStart="@android:drawable/ic_menu_edit"
                android:drawablePadding="2dp"
                android:layout_marginEnd="4dp"
                android:gravity="center" />

            <Button
                android:id="@+id/btnDeleteSample"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="36dp"
                android:text="删除"
                android:textSize="11sp"
                android:textColor="@android:color/white"
                android:background="#F44336"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                android:drawableStart="@android:drawable/ic_menu_delete"
                android:drawablePadding="2dp"
                android:layout_marginEnd="4dp"
                android:gravity="center" />

            <Button
                android:id="@+id/btnPrintSample"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="36dp"
                android:text="打印标签"
                android:textSize="11sp"
                android:textColor="@android:color/white"
                android:background="#FF9800"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                android:drawableStart="@android:drawable/ic_menu_save"
                android:drawablePadding="2dp"
                android:gravity="center" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView> 
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="io.dcloud.uniplugin.samplingPoint.SamplingPointsActivity">

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tabGravity="fill"
        app:tabMode="fixed" />
        
    <TextView
        android:id="@+id/tv_status_counts"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="8dp"
        android:textSize="14sp"
        android:textColor="@color/black"
        android:background="#f5f5f5"
        android:gravity="center"
        android:text="待调查: 0 | 待提交: 0 | 已提交: 0 | 待整改: 0"
        app:layout_constraintTop_toBottomOf="@+id/tabLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />
        
    <LinearLayout
        android:id="@+id/searchContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        android:orientation="horizontal"
        android:background="@android:drawable/editbox_background"
        app:layout_constraintTop_toBottomOf="@+id/tv_status_counts"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">
        
        <androidx.appcompat.widget.SearchView
            android:id="@+id/searchView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@null"
            android:iconifiedByDefault="false"
            android:queryHint="请根据样点编号搜索"
            app:queryBackground="@null"
            app:submitBackground="@null"
            app:closeIcon="@android:drawable/ic_menu_close_clear_cancel"
            app:searchIcon="@android:drawable/ic_menu_search"
            app:iconifiedByDefault="false"
            app:showAsAction="always"
            app:defaultQueryHint="请根据样点编号搜索" />
    </LinearLayout>
    
    <!-- 添加同步数据按钮 -->
    <Button
        android:id="@+id/btnSyncData"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:text="同步在线数据"
        android:textColor="#FFFFFF"
        android:background="@color/colorPrimary"
        app:layout_constraintTop_toBottomOf="@+id/searchContainer"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btnSyncData">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- 移除主活动中的空状态TextView，只使用Fragment中的空状态显示 -->

    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
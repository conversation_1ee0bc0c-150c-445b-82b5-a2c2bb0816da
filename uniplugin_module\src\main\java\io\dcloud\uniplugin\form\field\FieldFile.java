package io.dcloud.uniplugin.form.field;

import android.graphics.Bitmap;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;

import java.io.File;

/**
 * 表单字段文件类，用于管理表单中的文件（照片、视频、签名等）
 */
public class FieldFile {
    private static final String TAG = "FieldFile";
    private String path;       // 文件路径
    private String name;       // 文件名称
    private String type;       // 文件类型 (image, video, signature)
    private long size;         // 文件大小
    private Uri uri;           // 文件URI
    private boolean isLocal;   // 是否是本地文件
    private Double latitude;   // 纬度
    private Double longitude;  // 经度
    private Float direction;   // 方位角
    private String locationInfo; // 位置信息文本
    //时间
    private String fileTime;
    
    // 预览相关
    private ImageView previewImageView;   // 预览图片的ImageView
    private View videoPreviewView;        // 预览视频的View

    /**
     * 默认构造函数
     */
    public FieldFile() {
    }

    /**
     * 带路径的构造函数
     * @param path 文件路径
     */
    public FieldFile(String path) {
        this.path = path;
        this.name = getNameFromPath(path);
        this.type = getTypeFromPath(path);
        this.isLocal = true;
        
        try {
            File file = new File(path);
            if (file.exists()) {
                this.size = file.length();
            }
        } catch (Exception e) {
            this.size = 0;
        }
    }

    /**
     * 带路径和类型的构造函数
     * @param path 文件路径
     * @param type 文件类型
     */
    public FieldFile(String path, String type) {
        this(path);
        if (!TextUtils.isEmpty(type)) {
            this.type = type;
        }
    }

    /**
     * 带路径、类型和位置信息的构造函数
     * @param path 文件路径
     * @param type 文件类型
     * @param latitude 纬度
     * @param longitude 经度
     * @param direction 方位角
     */
    public FieldFile(String path, String type, Double latitude, Double longitude, Float direction) {
        this(path, type);
        this.latitude = latitude;
        this.longitude = longitude;
        this.direction = direction;
        
        if (latitude != null && longitude != null) {
            this.locationInfo = "经度:" + longitude + " 纬度:" + latitude;
            if (direction != null) {
                this.locationInfo += " 方位角:" + direction;
            }
        }
    }

    /**
     * 带URI的构造函数
     * @param uri 文件URI
     * @param type 文件类型
     */
    public FieldFile(Uri uri, String type) {
        this.uri = uri;
        this.path = uri.toString();
        this.name = uri.getLastPathSegment();
        this.type = type;
        this.isLocal = false;
    }

    /**
     * 从路径获取文件名
     * @param path 文件路径
     * @return 文件名
     */
    private String getNameFromPath(String path) {
        if (TextUtils.isEmpty(path)) {
            return "";
        }
        
        try {
            return new File(path).getName();
        } catch (Exception e) {
            return path.substring(path.lastIndexOf('/') + 1);
        }
    }

    /**
     * 从路径获取文件类型
     * @param path 文件路径
     * @return 文件类型
     */
    private String getTypeFromPath(String path) {
        if (TextUtils.isEmpty(path)) {
            return "unknown";
        }
        
        String lowerCasePath = path.toLowerCase();
        
        // 先根据文件名特征判断
        if (lowerCasePath.contains("video_") || 
            lowerCasePath.contains("/video/") || 
            lowerCasePath.contains("_video_")) {
            Log.d("FieldFile", "根据文件名判断为视频: " + path);
            return "video";
        } else if (lowerCasePath.contains("signature")) {
            Log.d("FieldFile", "根据文件名判断为签名: " + path);
            return "signature";
        }
        
        // 再根据扩展名判断
        if (lowerCasePath.endsWith(".jpg") || lowerCasePath.endsWith(".jpeg") || 
                lowerCasePath.endsWith(".png") || lowerCasePath.endsWith(".gif")) {
            Log.d("FieldFile", "根据扩展名判断为图片: " + path);
            return "image";
        } else if (lowerCasePath.endsWith(".mp4") || lowerCasePath.endsWith(".3gp") || 
                lowerCasePath.endsWith(".mov") || lowerCasePath.endsWith(".avi")) {
            Log.d("FieldFile", "根据扩展名判断为视频: " + path);
            return "video";
        } else {
            Log.d("FieldFile", "无法判断文件类型: " + path + ", 默认为文件");
            return "file";
        }
    }

    /**
     * 获取文件路径
     * @return 文件路径
     */
    public String getPath() {
        return path;
    }

    /**
     * 设置文件路径
     * @param path 文件路径
     */
    public void setPath(String path) {
        this.path = path;
        
        if (TextUtils.isEmpty(this.name)) {
            this.name = getNameFromPath(path);
        }
        
        if (TextUtils.isEmpty(this.type)) {
            this.type = getTypeFromPath(path);
        }
        
        try {
            File file = new File(path);
            if (file.exists()) {
                this.size = file.length();
            }
        } catch (Exception e) {
            this.size = 0;
        }
    }

    /**
     * 获取文件名称
     * @return 文件名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置文件名称
     * @param name 文件名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取文件类型
     * @return 文件类型
     */
    public String getType() {
        return type;
    }

    /**
     * 设置文件类型
     * @param type 文件类型
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * 设置媒体类型（与setType相同，保留以兼容现有代码）
     * @param mediaType 媒体类型
     */
    public void setMediaType(String mediaType) {
        this.type = mediaType;
    }

    /**
     * 获取文件大小
     * @return 文件大小
     */
    public long getSize() {
        return size;
    }

    /**
     * 设置文件大小
     * @param size 文件大小
     */
    public void setSize(long size) {
        this.size = size;
    }

    /**
     * 获取文件URI
     * @return 文件URI
     */
    public Uri getUri() {
        return uri;
    }

    /**
     * 设置文件URI
     * @param uri 文件URI
     */
    public void setUri(Uri uri) {
        this.uri = uri;
    }

    /**
     * 是否是本地文件
     * @return 是否是本地文件
     */
    public boolean isLocal() {
        return isLocal;
    }

    /**
     * 设置是否是本地文件
     * @param local 是否是本地文件
     */
    public void setLocal(boolean local) {
        isLocal = local;
    }

    /**
     * 文件是否存在
     * @return 是否存在
     */
    public boolean exists() {
        if (TextUtils.isEmpty(path)) {
            return false;
        }
        
        try {
            return new File(path).exists();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取格式化的文件大小
     * @return 格式化的文件大小
     */
    public String getFormattedSize() {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024 * 1024));
        }
    }

    /**
     * 删除文件
     * @return 是否删除成功
     */
    public boolean delete() {
        if (TextUtils.isEmpty(path) || !isLocal) {
            return false;
        }
        
        try {
            File file = new File(path);
            return file.exists() && file.delete();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取纬度
     * @return 纬度
     */
    public Double getLatitude() {
        return latitude;
    }

    /**
     * 设置纬度
     * @param latitude 纬度
     */
    public void setLatitude(Double latitude) {
        this.latitude = latitude;
        updateLocationInfo();
    }

    /**
     * 获取经度
     * @return 经度
     */
    public Double getLongitude() {
        return longitude;
    }

    /**
     * 设置经度
     * @param longitude 经度
     */
    public void setLongitude(Double longitude) {
        this.longitude = longitude;
        updateLocationInfo();
    }

    /**
     * 获取方位角
     * @return 方位角
     */
    public Float getDirection() {
        return direction;
    }

    /**
     * 设置方位角
     * @param direction 方位角
     */
    public void setDirection(Float direction) {
        this.direction = direction;
        updateLocationInfo();
    }


    public String getFileTime() {
        return fileTime;
    }

    public void setFileTime(String fileTime) {
        this.fileTime = fileTime;
    }

    /**
     * 获取位置信息文本
     * @return 位置信息文本
     */
    public String getLocationInfo() {
        return locationInfo;
    }

    /**
     * 设置位置信息文本
     * @param locationInfo 位置信息文本
     */
    public void setLocationInfo(String locationInfo) {
        this.locationInfo = locationInfo;
    }
    
    /**
     * 更新位置信息文本
     */
    private void updateLocationInfo() {
        if (latitude != null && longitude != null) {
            this.locationInfo = "经度:" + longitude + " 纬度:" + latitude;
            if (direction != null) {
                this.locationInfo += " 方位角:" + direction;
            }
        } else {
            this.locationInfo = null;
        }
    }
    
    /**
     * 设置预览图片视图
     * @param imageView 图片视图
     */
    public void setPreviewImageView(ImageView imageView) {
        this.previewImageView = imageView;
    }
    
    /**
     * 设置预览视频视图
     * @param view 视频视图
     */
    public void setVideoPreviewView(View view) {
        this.videoPreviewView = view;
    }
    
    /**
     * 设置预览图片
     * @param bitmap 图片位图
     */
    public void setPreviewImage(Bitmap bitmap) {
        if (previewImageView != null && bitmap != null) {
            try {
                previewImageView.setImageBitmap(bitmap);
                previewImageView.setVisibility(View.VISIBLE);
                
                if (videoPreviewView != null) {
                    videoPreviewView.setVisibility(View.GONE);
                }
                
                Log.d(TAG, "设置预览图片成功");
            } catch (Exception e) {
                Log.e(TAG, "设置预览图片失败: " + e.getMessage());
            }
        } else {
            Log.w(TAG, "无法设置预览图片: imageView=" + previewImageView + ", bitmap=" + bitmap);
        }
    }
    
    /**
     * 显示视频预览
     * @param videoPath 视频路径
     */
    public void showVideoPreview(String videoPath) {
        if (videoPreviewView != null && !TextUtils.isEmpty(videoPath)) {
            try {
                // 不同的视频预览实现可能不同，这里仅设置可见性
                videoPreviewView.setVisibility(View.VISIBLE);
                
                if (previewImageView != null) {
                    previewImageView.setVisibility(View.GONE);
                }
                
                Log.d(TAG, "显示视频预览成功: " + videoPath);
            } catch (Exception e) {
                Log.e(TAG, "显示视频预览失败: " + e.getMessage());
            }
        } else {
            Log.w(TAG, "无法显示视频预览: videoView=" + videoPreviewView + ", videoPath=" + videoPath);
        }
    }
    
    /**
     * 清除预览
     */
    public void clearPreview() {
        try {
            if (previewImageView != null) {
                previewImageView.setImageBitmap(null);
                previewImageView.setVisibility(View.GONE);
            }
            
            if (videoPreviewView != null) {
                videoPreviewView.setVisibility(View.GONE);
            }
            
            Log.d(TAG, "预览已清除");
        } catch (Exception e) {
            Log.e(TAG, "清除预览失败: " + e.getMessage());
        }
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("FieldFile{");
        sb.append("path='").append(path).append('\'');
        sb.append(", name='").append(name).append('\'');
        sb.append(", type='").append(type).append('\'');
        sb.append(", size=").append(size);
        
        if (latitude != null && longitude != null) {
            sb.append(", latitude=").append(latitude);
            sb.append(", longitude=").append(longitude);
            if (direction != null) {
                sb.append(", direction=").append(direction);
            }
        }
        
        sb.append(", isLocal=").append(isLocal);
        sb.append('}');
        return sb.toString();
    }
} 
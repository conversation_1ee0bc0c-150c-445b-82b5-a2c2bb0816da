package io.dcloud.uniplugin.form.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.os.Environment;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 表单工具类，抽取DynamicFormActivity中常用的工具方法
 */
public class FormUtils {
    private static final String TAG = "FormUtils";

    /**
     * 生成唯一ID
     * @param prefix ID前缀
     * @return 生成的唯一ID
     */
    public static String generateUniqueId(String prefix) {
        return prefix + "_" + System.currentTimeMillis() + "_" + Math.random();
    }


    /**
     * 保存签名图片到文件
     * @param context 上下文
     * @param signatureBitmap 签名位图
     * @param fieldId 字段ID，用于生成唯一文件名
     * @return 保存的文件路径，保存失败返回null
     */
    public static String saveSignatureImage(Context context, Bitmap signatureBitmap, String fieldId) {
        if (signatureBitmap == null) return null;
        
        try {
            // 保存签名图片到文件
            String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
            String fileName = "SIGNATURE_" + fieldId + "_" + timeStamp + ".png";
            
            File baseDir;
            
            // 检查Android版本，适配Android 10及以上版本的存储访问
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
                // Android 10及以上，优先尝试使用应用专属外部存储
                baseDir = new File(Environment.getExternalStorageDirectory(), "BCGDGISData//BCGDCameraData");

                if (!baseDir.exists()) {
                    boolean created = baseDir.mkdirs();
                    if (!created) {
                        Log.e(TAG, "无法创建应用专属目录: " + baseDir.getAbsolutePath());
                        // 回退到应用私有的图片目录
                        baseDir = context.getExternalFilesDir(android.os.Environment.DIRECTORY_PICTURES);
                    } else {
                        Log.d(TAG, "成功创建应用专属目录: " + baseDir.getAbsolutePath());
                    }
                }
            } else {
                // Android 9及以下版本，可以使用共享外部存储
                baseDir = new File( Environment.getExternalStorageDirectory(), "BCGDGISData/BCGDCameraData");
                if (!baseDir.exists()) {
                    boolean created = baseDir.mkdirs();
                    if (!created) {
                        Log.e(TAG, "无法创建外部存储目录: " + baseDir.getAbsolutePath());
                        // 回退到应用私有目录
                        baseDir = context.getExternalFilesDir(android.os.Environment.DIRECTORY_PICTURES);
                    } else {
                        Log.d(TAG, "成功创建外部存储目录: " + baseDir.getAbsolutePath());
                    }
                }
            }
            
            File signatureFile = new File(baseDir, fileName);
            Log.d(TAG, "保存签名到文件: " + signatureFile.getAbsolutePath());

            FileOutputStream out = new FileOutputStream(signatureFile);
            signatureBitmap.compress(Bitmap.CompressFormat.PNG, 100, out);
            out.flush();
            out.close();
            
            Log.d(TAG, "签名文件保存成功，大小: " + signatureFile.length() + " 字节");
            
            // 如果使用的是公共目录，需要通知媒体扫描器
            if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.Q) {
                try {
                    android.media.MediaScannerConnection.scanFile(
                        context,
                        new String[] { signatureFile.getAbsolutePath() },
                        new String[] { "image/png" },
                        null
                    );
                } catch (Exception e) {
                    Log.e(TAG, "媒体扫描失败: " + e.getMessage());
                }
            }
            
            return signatureFile.getAbsolutePath();
        } catch (Exception e) {
            Log.e(TAG, "保存签名失败: " + e.getMessage(), e);
            return null;
        }
    }

} 
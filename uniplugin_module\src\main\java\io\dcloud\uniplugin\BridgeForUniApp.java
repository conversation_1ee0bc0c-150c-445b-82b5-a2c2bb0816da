package io.dcloud.uniplugin;
import android.content.Intent;
import org.json.JSONArray;
import io.dcloud.common.DHInterface.IWebview;
import io.dcloud.common.DHInterface.StandardFeature;

public class BridgeForUniApp extends StandardFeature{
    public void goMainActivity(IWebview pWebview, JSONArray arr){
        Intent intent = new Intent(pWebview.getContext(), MainActivity.class);
        intent.putExtra("CallBackID", arr.optString(0));
        pWebview.getContext().startActivity(intent);
    }


}

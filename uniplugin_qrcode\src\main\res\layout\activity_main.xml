<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#1D74FF"
    tools:context="com.abdu.qrcode.MainActivity"
    android:orientation="vertical"
    android:gravity="center_horizontal">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="二维码工具"
        android:gravity="center"
        android:textColor="#FFFFFF"
        android:textStyle="bold"
        android:layout_marginTop="44dp"
        android:textSize="20sp"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:gravity="center"
        android:layout_marginTop="8dp"
        android:background="#0D62EB" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingLeft="36dp"
        android:paddingRight="36dp"
        android:paddingBottom="32dp"
        android:background="#EFEFE9">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <EditText
                android:id="@+id/etQRCodeText"
                android:layout_width="match_parent"
                android:layout_height="140dp"
                android:padding="14dp"
                android:hint="请输入内容"
                android:layout_marginTop="26dp"
                android:gravity="top"
                android:textSize="12sp"
                android:textColor="#333"
                android:maxLines="5"
                android:text="@string/test"
                android:background="@drawable/bg_login_input_code"/>



            <Button
                android:id="@+id/btnCreateQRCode"
                android:layout_width="match_parent"
                android:layout_height="54dp"
                android:layout_marginTop="16dp"
                android:textColor="@color/white"
                android:background="@drawable/bg_main_btn_create_qr_code"
                android:text="生成二维码"/>

            <Button
                android:id="@+id/btnScanQRCode"
                android:layout_width="match_parent"
                android:layout_height="54dp"
                android:layout_marginTop="26dp"
                android:textColor="@color/white"
                android:background="@drawable/bg_main_btn_scan_qr_code"
                android:text="扫描二维码"/>


            <ImageView
                android:id="@+id/qrcode_image"
                android:layout_marginTop="16dp"
                android:layout_width="match_parent"
                android:scaleType="centerInside"
                android:layout_height="wrap_content"/>


        </LinearLayout>



    </ScrollView>


    




</LinearLayout>
package io.dcloud.uniplugin.sampleflow;

import android.app.ProgressDialog;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.bumptech.glide.Glide;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import io.dcloud.uniplugin.enums.SharedPreferencesEnum;
import io.dcloud.uniplugin.http.RetrofitManager;
import io.dcloud.uniplugin.http.api.FileUploadService;
import io.dcloud.uniplugin.model.ApiResponse;
import io.dcloud.uniplugin.model.BcgdSampleYplzqd;
import io.dcloud.uniplugin.model.SoilBagDetail;
import io.dcloud.uniplugin.model.YPLZ;
import io.dcloud.uniplugin.model.YplzBatch;
import io.dcloud.uniplugin.model.YplzPageResponse;
import io.dcloud.uniplugin.view.SignatureView;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import uni.dcloud.io.uniplugin_module.R;

/**
 * 样品流转详情页面
 */
public class SampleFlowDetailActivity extends AppCompatActivity {
    
    private static final String TAG = "SampleFlowDetailActivity";
    private static final int REQUEST_SCAN_QR = 1001;
    private static final int REQUEST_ADD_SAMPLE = 1002;
    
    private TextView textViewBatchName;
    private TextView textViewBatchCode;
    private TextView textViewBsm;
    private TextView textViewBatchState;
    private TextView textViewCreateTime;
    private TextView textViewSenderName;
    private TextView textViewSenderPhone;
    private TextView textViewSendOrg;
    private TextView textViewReceiverName;
    private TextView textViewReceiverPhone;
    private TextView textViewReceiveOrg;
    private TextView textViewSampleNum;
    private TextView textViewDeliveryType;
    private TextView textViewDeliveryMessage;
    private TextView textViewSampleCount;
    private RecyclerView recyclerViewSamples;
    private LinearLayout layoutEmptySamples;
    
    // 签名相关控件
    private ImageView imageViewSenderSignature;
    private LinearLayout layoutSenderSignature;
    private ImageView imageViewReceiverSignature;
    private LinearLayout layoutReceiverSignature;
    
    // 新增：确认接收按钮
    private Button buttonReceive;
    // 新增：接收操作区域
    private LinearLayout layoutReceiveAction;
    
    private SampleDetailAdapter adapter;
    private List<YPLZ> sampleList = new ArrayList<>();
    
    // 分页相关
    private int currentPage = 1;
    private int pageSize = 10;
    private boolean isLoading = false;
    private boolean hasMoreData = true;
    private View loadMoreView; // 加载更多视图
    private TextView textViewLoadingMore; // 加载更多提示文本
    private View progressBarLoading; // 加载进度条
    
    private Long batchId;
    private YplzBatch batchDetail; // 存储批次详情
    
    // 新增的样品操作区域
    private LinearLayout layoutSampleActions;
    private Button buttonScanSample;
    private Button buttonAddSample;
    
    private SwipeRefreshLayout swipeRefreshLayout;
    
    // 新增：是否为接收模式
    private boolean isReceiveMode = false;
    // 新增：是否直接进入接收确认流程
    private boolean directReceive = false;
    
    // 新增：签名相关
    private FileUploadService fileUploadService;
    private String receiverSignatureUrl;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sample_flow_detail);
        
        // 从Intent中获取批次ID和模式参数
        if (getIntent() != null) {
            batchId = getIntent().getLongExtra("batchId", 0);
            isReceiveMode = getIntent().getBooleanExtra("isReceiveMode", false);
            directReceive = getIntent().getBooleanExtra("directReceive", false);
        } else {
            Toast.makeText(this, "未提供批次ID", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }
        
        // 设置ActionBar标题和返回按钮
        setTitle(isReceiveMode ? "样品接收" : "批次详情");
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        
        // 初始化文件上传服务
        fileUploadService = new FileUploadService(this);
        
        initViews();
        setupRecyclerView();
        
        // 加载数据
        loadData();
        
        // 如果是直接进入接收确认模式，加载数据后自动显示接收确认对话框
        if (directReceive) {
            new android.os.Handler().postDelayed(() -> {
                if (batchDetail != null) {
                    showReceiveConfirmDialog();
                }
            }, 1000); // 延迟1秒等待数据加载完成
        }
    }
    
    private void initViews() {
        // 初始化刷新布局
        swipeRefreshLayout = findViewById(R.id.swipeRefreshLayout);
        swipeRefreshLayout.setOnRefreshListener(() -> {
            // 重新加载数据
            loadData();
        });
        swipeRefreshLayout.setColorSchemeResources(
            android.R.color.holo_blue_bright,
            android.R.color.holo_green_light,
            android.R.color.holo_orange_light,
            android.R.color.holo_red_light
        );
        
        // 批次基本信息
        textViewBatchName = findViewById(R.id.textViewBatchName);
        textViewBatchCode = findViewById(R.id.textViewBatchCode);
        textViewBsm = findViewById(R.id.textViewBsm);
        textViewBatchState = findViewById(R.id.textViewBatchState);
        textViewCreateTime = findViewById(R.id.textViewCreateTime);
        textViewSenderName = findViewById(R.id.textViewSenderName);
        textViewSenderPhone = findViewById(R.id.textViewSenderPhone);
        textViewSendOrg = findViewById(R.id.textViewSendOrg);
        textViewReceiverName = findViewById(R.id.textViewReceiverName);
        textViewReceiverPhone = findViewById(R.id.textViewReceiverPhone);
        textViewReceiveOrg = findViewById(R.id.textViewReceiveOrg);
        textViewSampleNum = findViewById(R.id.textViewSampleNum);
        textViewDeliveryType = findViewById(R.id.textViewDeliveryType);
        textViewDeliveryMessage = findViewById(R.id.textViewDeliveryMessage);
        textViewSampleCount = findViewById(R.id.textViewSampleCount);
        recyclerViewSamples = findViewById(R.id.recyclerViewSamples);
        layoutEmptySamples = findViewById(R.id.layoutEmptySamples);
        loadMoreView = findViewById(R.id.loadMoreView);
        textViewLoadingMore = findViewById(R.id.textViewLoadingMore);
        progressBarLoading = findViewById(R.id.progressBarLoading);
        
        // 初始化签名相关控件
        imageViewSenderSignature = findViewById(R.id.imageViewSenderSignature);
        layoutSenderSignature = findViewById(R.id.layoutSenderSignature);
        imageViewReceiverSignature = findViewById(R.id.imageViewReceiverSignature);
        layoutReceiverSignature = findViewById(R.id.layoutReceiverSignature);
        
        // 设置加载更多按钮点击事件
        if (loadMoreView != null) {
            loadMoreView.setOnClickListener(v -> {
                if (!isLoading && hasMoreData) {
                    loadMoreSamples();
                }
            });
        }
        
        // 初始化样品操作区域
        layoutSampleActions = findViewById(R.id.layoutSampleActions);
        buttonScanSample = findViewById(R.id.buttonScanSample);
        buttonAddSample = findViewById(R.id.buttonAddSample);
        
        // 新增：初始化接收操作区域
        layoutReceiveAction = findViewById(R.id.layoutReceiveAction);
        buttonReceive = findViewById(R.id.buttonReceive);
        
        // 隐藏手动添加样品按钮
        buttonAddSample.setVisibility(View.GONE);
        
        // 根据模式设置UI状态
        if (isReceiveMode) {
            // 接收模式：隐藏样品操作区域，显示接收操作区域
            layoutSampleActions.setVisibility(View.GONE);
            
            // 设置接收按钮点击事件
            if (buttonReceive != null) {
                buttonReceive.setOnClickListener(v -> {
                    showReceiveConfirmDialog();
                });
            }
        } else {
            // 发送模式：暂时隐藏样品操作区域，等待批次信息加载完成后再决定是否显示
            layoutSampleActions.setVisibility(View.GONE);
            
            // 隐藏接收操作区域
            if (layoutReceiveAction != null) {
                layoutReceiveAction.setVisibility(View.GONE);
            }
        }
        
        // 设置扫码按钮点击事件
        buttonScanSample.setOnClickListener(v -> startScanQRCode());
    }
    
    private void setupRecyclerView() {
        adapter = new SampleDetailAdapter(this, sampleList);
        recyclerViewSamples.setLayoutManager(new LinearLayoutManager(this));
        recyclerViewSamples.setAdapter(adapter);
        
        // 设置样品操作监听器
        adapter.setOnSampleOperationListener(new SampleDetailAdapter.OnSampleOperationListener() {
            @Override
            public void onEdit(YPLZ sample, int position) {
                showEditSampleDialog(sample, position);
            }
            
            @Override
            public void onDelete(YPLZ sample, int position) {
                confirmDeleteSample(sample, position);
            }
        });
        
        // 初始时，根据批次状态设置按钮显示/隐藏
        // 默认情况下，在批次信息加载完成前，先隐藏操作按钮
        adapter.setShowOperationButtons(false);
    }
    
    /**
     * 加载数据：先加载批次详情，再加载样品列表
     */
    private void loadData() {
        // 显示刷新指示器
        swipeRefreshLayout.setRefreshing(true);
        
        // 先加载批次详情
        loadBatchDetail();
    }
    
    /**
     * 加载批次详情
     */
    private void loadBatchDetail() {
        RetrofitManager.getInstance(this)
                .getSampleBatchServiceApi()
                .getSampleBatchDetail(batchId)
                .enqueue(new Callback<ApiResponse<YplzBatch>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<YplzBatch>> call, 
                                          Response<ApiResponse<YplzBatch>> response) {
                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<YplzBatch> apiResponse = response.body();
                            if (apiResponse.getCode() == 0 && apiResponse.getData() != null) {
                                batchDetail = apiResponse.getData();
                                updateBatchUI(batchDetail);
                                
                                // 批次详情加载成功后，加载样品列表
                                loadSampleList();
                            } else {
                                swipeRefreshLayout.setRefreshing(false);
                                showError("加载批次详情失败：" + apiResponse.getMsg());
                            }
                        } else {
                            swipeRefreshLayout.setRefreshing(false);
                            showError("网络请求失败");
                        }
                    }
                    
                    @Override
                    public void onFailure(Call<ApiResponse<YplzBatch>> call, Throwable t) {
                        swipeRefreshLayout.setRefreshing(false);
                        Log.e(TAG, "加载批次详情失败", t);
                        showError("加载批次详情失败：" + t.getMessage());
                    }
                });
    }
    
    /**
     * 加载样品列表（首次加载）
     */
    private void loadSampleList() {
        // 重置分页参数
        currentPage = 1;
        hasMoreData = true;
        
        // 清空现有数据
        sampleList.clear();
        adapter.notifyDataSetChanged();
        
        // 隐藏加载更多按钮，等待数据加载完成后再根据情况显示
        if (loadMoreView != null) {
            loadMoreView.setVisibility(View.GONE);
        }
        
        loadSampleListByPage(true);
    }
    
    /**
     * 加载更多样品
     */
    private void loadMoreSamples() {
        if (isLoading || !hasMoreData) return;
        
        currentPage++;
        loadSampleListByPage(false);
    }
    
    /**
     * 设置加载更多状态
     * @param isLoading 是否正在加载
     */
    private void setLoadingMoreState(boolean isLoading) {
        if (loadMoreView != null) {
            loadMoreView.setVisibility(View.VISIBLE);
            
            // 控制进度条显示
            if (progressBarLoading != null) {
                progressBarLoading.setVisibility(isLoading ? View.VISIBLE : View.GONE);
            }
            
            // 控制文本显示
            if (textViewLoadingMore != null) {
                textViewLoadingMore.setText(isLoading ? "加载中..." : "点击加载更多");
            }
            
            // 控制按钮是否可点击
            loadMoreView.setClickable(!isLoading);
        }
    }
    
    /**
     * 按页加载样品列表
     * @param isRefresh 是否是刷新操作
     */
    private void loadSampleListByPage(final boolean isRefresh) {
        isLoading = true;
        
        // 如果不是刷新，显示加载更多
        if (!isRefresh) {
            setLoadingMoreState(true);
        }
        
        RetrofitManager.getInstance(this)
                .getSampleFlowService()
                .getSampleListByBatchId(currentPage, pageSize, batchId, null, null, null, null, null)
                .enqueue(new Callback<ApiResponse<YplzPageResponse<YPLZ>>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<YplzPageResponse<YPLZ>>> call, 
                                          Response<ApiResponse<YplzPageResponse<YPLZ>>> response) {
                        isLoading = false;
                        setLoadingMoreState(false);
                        swipeRefreshLayout.setRefreshing(false);  // 隐藏刷新指示器
                        
                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<YplzPageResponse<YPLZ>> apiResponse = response.body();
                            if (apiResponse.getCode() == 0 && apiResponse.getData() != null) {
                                YplzPageResponse<YPLZ> pageResponse = apiResponse.getData();
                                
                                // 记录总数和总页数
                                long total = pageResponse.getTotal() != null ? pageResponse.getTotal() : 0;
                                
                                // 计算总页数，确保至少为1页
                                long totalPages;
                                if (pageResponse.getPages() != null && pageResponse.getPages() > 0) {
                                    totalPages = pageResponse.getPages();
                                } else if (total > 0) {
                                    // 如果没有提供总页数但有总记录数，则根据页大小计算总页数
                                    totalPages = (total + pageSize - 1) / pageSize;
                                } else {
                                    // 如果既没有总页数也没有总记录数，则至少为1页
                                    totalPages = Math.max(1, currentPage);
                                }
                                
                                if (pageResponse.getList() != null && !pageResponse.getList().isEmpty()) {
                                    // 添加日志输出，查看返回的样品状态
                                    for (YPLZ sample : pageResponse.getList()) {
                                        Log.d(TAG, "返回的样品 ID: " + sample.getId() + ", 状态值: " + sample.getStatus());
                                    }
                                    
                                    // 如果是刷新，先清空列表
                                    if (isRefresh) {
                                        sampleList.clear();
                                    }
                                    
                                    // 添加新数据
                                    sampleList.addAll(pageResponse.getList());
                                    
                                    // 检查是否还有更多数据
                                    hasMoreData = currentPage < totalPages;
                                    
                                    // 更新UI，显示页码信息
                                    updateSampleListUI(total, totalPages);
                                    
                                    // 确保样品编辑和删除按钮的显示状态与批次状态一致
                                    if (batchDetail != null && batchDetail.getBatchState() != null) {
                                        boolean isEditableState = batchDetail.getBatchState() == 0 || Long.valueOf(0).equals(batchDetail.getBatchState());
                                        adapter.setShowOperationButtons(isEditableState);
                                        Log.d(TAG, "加载样品后设置按钮显示状态: " + isEditableState);
                                    }
                                } else {
                                    // 如果返回的列表为空
                                    if (isRefresh) {
                                        // 如果是刷新操作，说明没有任何数据
                                        sampleList.clear();
                                        updateSampleListUI(0, 0);
                                    } else if (currentPage > 1) {
                                        // 如果是加载更多操作，且当前页大于1，说明已经到达最后一页
                                        currentPage--; // 恢复到上一页
                                        updateSampleListUI(total, totalPages);
                                    }
                                    
                                    // 没有更多数据了
                                    hasMoreData = false;
                                }
                            } else {
                                showError("加载样品列表失败：" + apiResponse.getMsg());
                            }
                        } else {
                            showError("网络请求失败");
                        }
                    }
                    
                    @Override
                    public void onFailure(Call<ApiResponse<YplzPageResponse<YPLZ>>> call, Throwable t) {
                        isLoading = false;
                        setLoadingMoreState(false);
                        swipeRefreshLayout.setRefreshing(false);  // 隐藏刷新指示器
                        Log.e(TAG, "加载样品列表失败", t);
                        showError("加载样品列表失败：" + t.getMessage());
                    }
                });
    }
    
    /**
     * 更新批次信息UI
     */
    private void updateBatchUI(YplzBatch batch) {
        if (batch == null) return;
        
        // 添加日志输出，检查批次状态
        Log.d(TAG, "批次状态: " + (batch.getBatchState() != null ? batch.getBatchState() : "null"));
        
        // 设置批次编号
        textViewBatchCode.setText(batch.getBatchCode() != null ? batch.getBatchCode() : "未设置");
        
        // 设置批次类型
        textViewBsm.setText(getBatchTypeName(batch.getBatchType()));
        
        // 设置项目名称
        textViewBatchName.setText(batch.getBatchName() != null ? batch.getBatchName() : "未设置");
        
        // 设置批次状态
        String batchStateText = "未知";
        boolean isEditableState = false; // 是否为可编辑状态
        boolean canReceive = false; // 是否可以进行接收确认
        
        if (batch.getBatchState() != null) {
            // 使用更严格的比较方式
            if (batch.getBatchState() == 0 || Long.valueOf(0).equals(batch.getBatchState())) {
                batchStateText = "待寄送";
                isEditableState = true; // 只有待寄送状态才可编辑
                canReceive = false;
            } else if (batch.getBatchState() == 1 || Long.valueOf(1).equals(batch.getBatchState())) {
                batchStateText = "寄送中";
                isEditableState = false;
                canReceive = true; // 寄送中状态可以接收
            } else if (batch.getBatchState() == 100 || Long.valueOf(100).equals(batch.getBatchState())) {
                batchStateText = "寄送完成";
                isEditableState = false;
                canReceive = false; // 寄送完成状态不可接收
            } else if (batch.getBatchState() == -1 || Long.valueOf(-1).equals(batch.getBatchState())) {
                batchStateText = "已退回";
                isEditableState = false;
                canReceive = false;
            } else {
                batchStateText = "未知状态(" + batch.getBatchState() + ")";
            }
        }
        textViewBatchState.setText(batchStateText);
        
        // 根据模式和批次状态控制UI元素
        if (isReceiveMode) {
            // 接收模式：显示/隐藏接收按钮
            if (layoutReceiveAction != null) {
                layoutReceiveAction.setVisibility(canReceive ? View.VISIBLE : View.GONE);
            }
            // 隐藏样品操作区域
            if (layoutSampleActions != null) {
                layoutSampleActions.setVisibility(View.GONE);
            }
        } else {
            // 发送模式：根据批次状态控制样品操作区域
            if (layoutSampleActions != null) {
                layoutSampleActions.setVisibility(isEditableState ? View.VISIBLE : View.GONE);
            }
            // 隐藏接收操作区域
            if (layoutReceiveAction != null) {
                layoutReceiveAction.setVisibility(View.GONE);
            }
        }
        
        // 同时设置适配器中样品的编辑和删除按钮的显示状态
        if (adapter != null) {
            adapter.setShowOperationButtons(isEditableState && !isReceiveMode);
        }
        
        // 格式化时间
        if (batch.getCreateTime() != null) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
                String formattedTime = sdf.format(new Date(batch.getCreateTime()));
                textViewCreateTime.setText(formattedTime);
            } catch (Exception e) {
                textViewCreateTime.setText("解析时间错误");
            }
        } else {
            textViewCreateTime.setText("未提供创建时间");
        }
        
        // 设置送样人信息
        textViewSenderName.setText(batch.getSenderName() != null ? batch.getSenderName() : "");
        textViewSenderPhone.setText(batch.getSenderPhone() != null ? batch.getSenderPhone() : "");
        textViewSendOrg.setText(batch.getSendOrganization() != null ? batch.getSendOrganization() : "");
        
        // 渲染送样人签名
        if (batch.getSenderSignature() != null && !TextUtils.isEmpty(batch.getSenderSignature())) {
            layoutSenderSignature.setVisibility(View.VISIBLE);
            // 使用Glide加载签名图片
            Glide.with(this)
                .load(batch.getSenderSignature())
                .placeholder(R.drawable.signature_placeholder)
                .error(R.drawable.ic_signature_error)
                .centerInside()
                .into(imageViewSenderSignature);
        } else {
            // 当没有签名时，显示占位图
            layoutSenderSignature.setVisibility(View.VISIBLE);
            imageViewSenderSignature.setImageResource(R.drawable.signature_placeholder);
        }
        
        // 设置接样人信息
        textViewReceiverName.setText(batch.getReceiverName() != null ? batch.getReceiverName() : "");
        textViewReceiverPhone.setText(batch.getReceiverPhone() != null ? batch.getReceiverPhone() : "");
        textViewReceiveOrg.setText(batch.getReceiveOrganization() != null ? batch.getReceiveOrganization() : "");
        
        // 渲染接样人签名
        if (batch.getReceiverSignature() != null && !TextUtils.isEmpty(batch.getReceiverSignature())) {
            layoutReceiverSignature.setVisibility(View.VISIBLE);
            // 使用Glide加载签名图片
            Glide.with(this)
                .load(batch.getReceiverSignature())
                .placeholder(R.drawable.signature_placeholder)
                .error(R.drawable.ic_signature_error)
                .centerInside()
                .into(imageViewReceiverSignature);
        } else {
            // 如果批次状态为待寄送或寄送中且接样人签名为空，则隐藏接样人签名区域
            if (batch.getBatchState() != null && (batch.getBatchState() == 0 || batch.getBatchState() == 1)) {
                layoutReceiverSignature.setVisibility(View.GONE);
            } else {
                // 对于其他状态，显示占位图
                layoutReceiverSignature.setVisibility(View.VISIBLE);
                imageViewReceiverSignature.setImageResource(R.drawable.signature_placeholder);
            }
        }
        
        // 设置样品数量
        textViewSampleNum.setText(batch.getSampleNumber() != null ? batch.getSampleNumber().toString() : "0");
        
        // 设置运送信息
        textViewDeliveryType.setText(batch.getDeliveryType() != null ? batch.getDeliveryType() : "");
        textViewDeliveryMessage.setText(batch.getDeliveryMessage() != null ? batch.getDeliveryMessage() : "");
    }
    
    /**
     * 更新样品列表UI
     * @param total 总记录数
     * @param totalPages 总页数
     */
    private void updateSampleListUI(long total, long totalPages) {
        // 确保总页数至少为1
        if (totalPages <= 0) {
            if (total > 0) {
                totalPages = (total + pageSize - 1) / pageSize;
            } else {
                totalPages = Math.max(1, currentPage);
            }
        }
        
        // 确保总记录数至少等于当前加载的样品数量
        if (total < sampleList.size()) {
            total = sampleList.size();
        }
        
        // 更新样品数量信息
        if (total > 0) {
            textViewSampleCount.setText(String.format("共%d个样品 | 第%d/%d页", total, currentPage, totalPages));
        } else {
            textViewSampleCount.setText("共" + sampleList.size() + "个样品");
        }
        
        adapter.notifyDataSetChanged();
        
        // 显示或隐藏空数据提示
        if (sampleList.isEmpty()) {
            layoutEmptySamples.setVisibility(View.VISIBLE);
            recyclerViewSamples.setVisibility(View.GONE);
            loadMoreView.setVisibility(View.GONE); // 确保没有样品时不显示加载更多按钮
        } else {
            layoutEmptySamples.setVisibility(View.GONE);
            recyclerViewSamples.setVisibility(View.VISIBLE);
            
            // 显示加载更多按钮（如果还有更多数据）
            loadMoreView.setVisibility(hasMoreData ? View.VISIBLE : View.GONE);
            if (textViewLoadingMore != null) {
                if (hasMoreData) {
                    textViewLoadingMore.setText(String.format("点击加载下一页 (%d/%d)", currentPage, totalPages));
                } else {
                    textViewLoadingMore.setText("已加载全部数据");
                }
            }
            
            // 控制进度条
            if (progressBarLoading != null) {
                progressBarLoading.setVisibility(View.GONE);
            }
        }
    }
    
    /**
     * 更新样品列表UI
     */
    private void updateSampleListUI() {
        updateSampleListUI(0, 0);
    }
    
    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public boolean onOptionsItemSelected(android.view.MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        } else if (item.getItemId() == R.id.action_refresh) {
            // 刷新数据
            loadData();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
    
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // 添加刷新按钮到菜单
        menu.add(Menu.NONE, R.id.action_refresh, Menu.NONE, "刷新")
            .setIcon(android.R.drawable.ic_popup_sync)
            .setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS);
        return true;
    }
    
    /**
     * 打开扫描二维码页面
     */
    private void startScanQRCode() {
        Intent intent = new Intent(this, QRCodeScanActivity.class);
        startActivityForResult(intent, REQUEST_SCAN_QR);
    }
    
    /**
     * 打开添加样品页面
     */
    private void startAddSample() {
        // 显示样品添加对话框
        showAddSampleDialog(null);
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == REQUEST_SCAN_QR && resultCode == RESULT_OK) {
            // 处理扫码结果
            if (data != null && data.hasExtra("scan_result")) {
                String sampleCode = data.getStringExtra("scan_result");
                Log.i(TAG, "onActivityResult: "+sampleCode);
                
                // 查询采土袋详情并显示添加对话框
                querySoilBagDetail(sampleCode);
            }
        }
    }
    
    /**
     * 查询采土袋详情
     * @param sampleCode 样品编号
     */
    private void querySoilBagDetail(String sampleCode) {
        // 显示加载对话框
        AlertDialog loadingDialog = new AlertDialog.Builder(this)
                .setMessage("正在查询样品信息...")
                .setCancelable(false)
                .create();
        loadingDialog.show();
        
        // 调用采土袋API获取详情
        RetrofitManager.getInstance(this)
                .getDccyService()
                .getSoilBagDetail(sampleCode)
                .enqueue(new Callback<ApiResponse<SoilBagDetail>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<SoilBagDetail>> call, Response<ApiResponse<SoilBagDetail>> response) {
                        loadingDialog.dismiss();
                        
                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<SoilBagDetail> apiResponse = response.body();
                            if (apiResponse.getCode() == 0 && apiResponse.getData() != null) {
                                // 获取采土袋详情成功，显示添加样品对话框并填充数据
                                SoilBagDetail soilBag = apiResponse.getData();
                                
                                // 添加日志输出整个响应体，便于调试
                                Log.d(TAG, "采土袋API响应: " + apiResponse.toString());
                                Log.d(TAG, "采土袋详情数据: " + soilBag.toString());
                                
                                showAddSampleDialog(soilBag);
                            } else {
                                // API返回错误
                                String errorMsg = apiResponse.getMsg();
                                if (TextUtils.isEmpty(errorMsg)) {
                                    errorMsg = "查询样品信息失败";
                                }
                                Toast.makeText(SampleFlowDetailActivity.this, errorMsg, Toast.LENGTH_SHORT).show();
                            }
                        } else {
                            // 网络请求失败
                            Toast.makeText(SampleFlowDetailActivity.this, "网络请求失败", Toast.LENGTH_SHORT).show();
                        }
                    }
                    
                    @Override
                    public void onFailure(Call<ApiResponse<SoilBagDetail>> call, Throwable t) {
                        loadingDialog.dismiss();
                        Toast.makeText(SampleFlowDetailActivity.this, "网络请求失败: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                        Log.e(TAG, "查询采土袋详情失败", t);
                    }
                });
    }
    
    /**
     * 显示添加样品对话框
     * @param soilBag 采土袋详情，如果为空则显示空表单
     */
    private void showAddSampleDialog(SoilBagDetail soilBag) {
        // 加载对话框布局
        View dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_add_sample, null);
        
        // 获取输入控件
        Spinner spinnerSampleType = dialogView.findViewById(R.id.spinnerSampleType);
        EditText editTextSampleWeight = dialogView.findViewById(R.id.editTextSampleWeight);
        EditText editTextSampleCode = dialogView.findViewById(R.id.editTextSampleCode);
        EditText editTextBsm = dialogView.findViewById(R.id.editTextBsm);
        Button btnCancel = dialogView.findViewById(R.id.btnCancel);
        Button btnConfirm = dialogView.findViewById(R.id.btnConfirm);
        
        // 设置样品类型下拉列表 - 注意：这里保持与Sample类中的SampleType枚举一致
        String[] sampleTypeNames = new String[]{"表层样品", "土壤容重样品", "水稳性大团聚体样品"};
        int[] sampleTypeCodes = new int[]{1, 2, 3}; // 修改为整型数组，与枚举中的code对应
        
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, 
                android.R.layout.simple_spinner_dropdown_item, sampleTypeNames);
        spinnerSampleType.setAdapter(adapter);
        
        // 禁用样品编号、标识码和样品类型的编辑
        editTextSampleCode.setEnabled(false);
        editTextBsm.setEnabled(false);
        spinnerSampleType.setEnabled(false);
        
        // 如果有扫码获取的数据，则填充表单
        if (soilBag != null) {
            Log.d(TAG, "采土袋详情: " + soilBag.toString());
            
            // 设置样品编号，禁止编辑
            editTextSampleCode.setText(soilBag.getCtdbh());
            
            // 设置标识码，禁止编辑
            editTextBsm.setText(soilBag.getpjdybh());
            
            // 根据yplx设置样品类型 - 直接使用整数值对应
            Integer yplx = soilBag.getYplx();
            if (yplx != null) {
                Log.d(TAG, "样品类型yplx值: " + yplx);
                // 查找匹配的类型位置
                int typePosition = 0; // 默认选中第一个类型
                for (int i = 0; i < sampleTypeCodes.length; i++) {
                    if (sampleTypeCodes[i] == yplx) {
                        typePosition = i;
                        Log.d(TAG, "找到匹配的样品类型: " + sampleTypeNames[i] + " 位置: " + i);
                        break;
                    }
                }
                spinnerSampleType.setSelection(typePosition);
            } else {
                Log.w(TAG, "样品类型yplx为空");
            }
            
            // 设置样品重量（唯一可编辑的字段）
            if (soilBag.getYpzl() != null) {
                editTextSampleWeight.setText(String.valueOf(soilBag.getYpzl()));
            }
            
            // 确保用户关注于样品重量输入框
            editTextSampleWeight.requestFocus();
        } else {
            // 手动添加模式下也禁止编辑所有字段，并显示错误提示
            Toast.makeText(this, "请使用扫码方式添加样品", Toast.LENGTH_SHORT).show();
            btnConfirm.setEnabled(false);
            return;
        }
        
        // 创建对话框
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setView(dialogView);
        AlertDialog dialog = builder.create();
        
        // 设置取消按钮点击事件
        btnCancel.setOnClickListener(v -> dialog.dismiss());
        
        // 设置确定按钮点击事件
        btnConfirm.setOnClickListener(v -> {
            // 获取样品编号（不可编辑，从预填充中获取）
            String sampleCode = editTextSampleCode.getText().toString().trim();
            if (TextUtils.isEmpty(sampleCode)) {
                Toast.makeText(SampleFlowDetailActivity.this, "样品编号不能为空", Toast.LENGTH_SHORT).show();
                return;
            }
            
            // 获取标识码（不可编辑，从预填充中获取）
            String bsm = editTextBsm.getText().toString().trim();
            if (TextUtils.isEmpty(bsm)) {
                Toast.makeText(SampleFlowDetailActivity.this, "标识码不能为空", Toast.LENGTH_SHORT).show();
                return;
            }
            
            // 获取样品类型（不可编辑，从预填充中获取）
            int position = spinnerSampleType.getSelectedItemPosition();
            String sampleType = sampleTypeNames[position];
            
            // 获取样品重量（唯一可编辑的字段）
            String sampleWeightStr = editTextSampleWeight.getText().toString().trim();
            if (TextUtils.isEmpty(sampleWeightStr)) {
                Toast.makeText(SampleFlowDetailActivity.this, "样品重量不能为空", Toast.LENGTH_SHORT).show();
                return;
            }
            
            Double sampleWeight = Double.parseDouble(sampleWeightStr);
            
            // 创建样品对象
            YPLZ sample = new YPLZ();
            sample.setSampleCode(sampleCode);
            sample.setSampleName(sampleType);
            sample.setSampleType(sampleType);
            sample.setSampleWeight(sampleWeight);
            sample.setWeightUnit("g");
            sample.setBatchId(batchId);
            sample.setBsm(bsm);
            
            // 关闭对话框
            dialog.dismiss();
            
            // 添加样品
            addSampleToBatch(sample);
        });
        
        // 显示对话框
        dialog.show();
    }
    
    /**
     * 添加样品到批次
     * @param sample 样品对象
     */
    private void addSampleToBatch(YPLZ sample) {
        // 显示加载对话框
        AlertDialog loadingDialog = new AlertDialog.Builder(this)
                .setMessage("正在添加样品...")
                .setCancelable(false)
                .create();
        loadingDialog.show();
        
        // 创建BcgdSampleYplzqd对象并转换字段
        BcgdSampleYplzqd bcgdSample = new BcgdSampleYplzqd();
        bcgdSample.setBsm(sample.getBsm());
        bcgdSample.setSampleCode(sample.getSampleCode());
        bcgdSample.setSampleName(sample.getSampleName());
        bcgdSample.setSampleType(sample.getSampleType());
        bcgdSample.setSampleWeight(sample.getSampleWeight());
        bcgdSample.setWeightUnit(sample.getWeightUnit());
        bcgdSample.setSampleBatchId(sample.getBatchId());
        
        // 调用创建样品API
        RetrofitManager.getInstance(this)
                .getDccyService()
                .createSample(bcgdSample)
                .enqueue(new Callback<ApiResponse<Long>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<Long>> call, Response<ApiResponse<Long>> response) {
                        loadingDialog.dismiss();
                        
                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<Long> apiResponse = response.body();
                            if (apiResponse.getCode() == 0 && apiResponse.getData() != null) {
                                // 添加成功，刷新样品列表
                                Toast.makeText(SampleFlowDetailActivity.this, "添加样品成功", Toast.LENGTH_SHORT).show();
                                loadSampleList();
                            } else {
                                // API返回错误
                                String errorMsg = apiResponse.getMsg();
                                if (TextUtils.isEmpty(errorMsg)) {
                                    errorMsg = "添加样品失败";
                                }
                                Toast.makeText(SampleFlowDetailActivity.this, errorMsg, Toast.LENGTH_SHORT).show();
                            }
                        } else {
                            // 网络请求失败
                            Toast.makeText(SampleFlowDetailActivity.this, "网络请求失败", Toast.LENGTH_SHORT).show();
                        }
                    }
                    
                    @Override
                    public void onFailure(Call<ApiResponse<Long>> call, Throwable t) {
                        loadingDialog.dismiss();
                        Toast.makeText(SampleFlowDetailActivity.this, "网络请求失败: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                        Log.e(TAG, "添加样品失败", t);
                    }
                });
    }
    
    /**
     * 获取样品类型名称
     * @param yplx 样品类型ID
     * @return 样品类型名称
     */
    private String getYplxName(Integer yplx) {
        if (yplx == null) return "未知";
        
        switch (yplx) {
            case 1: return "土壤";
            case 2: return "水样";
            case 3: return "气体";
            case 4: return "生物";
            default: return "其他";
        }
    }

    /**
     * 获取批次类型名称
     * @param batchType 批次类型值（0=县级, 1=市级, 2=省级）
     * @return 批次类型名称
     */
    private String getBatchTypeName(Integer batchType) {
        if (batchType == null) {
            return "未设置";
        }
        switch (batchType) {
            case 0:
                return "县级";
            case 1:
                return "市级";
            case 2:
                return "省级";
            default:
                return "未知";
        }
    }
    
    /**
     * 显示编辑样品对话框
     * @param sample 要编辑的样品
     * @param position 样品在列表中的位置
     */
    private void showEditSampleDialog(YPLZ sample, int position) {
        // 加载对话框布局
        View dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_add_sample, null);
        
        // 获取输入控件
        Spinner spinnerSampleType = dialogView.findViewById(R.id.spinnerSampleType);
        EditText editTextSampleWeight = dialogView.findViewById(R.id.editTextSampleWeight);
        EditText editTextSampleCode = dialogView.findViewById(R.id.editTextSampleCode);
        EditText editTextBsm = dialogView.findViewById(R.id.editTextBsm);
        Button btnCancel = dialogView.findViewById(R.id.btnCancel);
        Button btnConfirm = dialogView.findViewById(R.id.btnConfirm);
        
        // 设置样品类型下拉列表 - 保持与Sample类中的SampleType枚举一致
        String[] sampleTypeNames = new String[]{"表层样品", "土壤容重样品", "水稳性大团聚体样品"};
        
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, 
                android.R.layout.simple_spinner_dropdown_item, sampleTypeNames);
        spinnerSampleType.setAdapter(adapter);
        
        // 禁用样品编号、标识码和样品类型的编辑
        editTextSampleCode.setEnabled(false);
        editTextBsm.setEnabled(false);
        spinnerSampleType.setEnabled(false);
        
        // 填充样品数据
        editTextSampleCode.setText(sample.getSampleCode());
        editTextBsm.setText(sample.getBsm());
        
        if (sample.getSampleWeight() != null) {
            editTextSampleWeight.setText(String.valueOf(sample.getSampleWeight()));
        }
        
        // 选择对应的样品类型
        for (int i = 0; i < sampleTypeNames.length; i++) {
            if (sampleTypeNames[i].equals(sample.getSampleType())) {
                spinnerSampleType.setSelection(i);
                Log.d(TAG, "编辑样品 - 设置样品类型: " + sample.getSampleType() + " 位置: " + i);
                break;
            }
        }
        
        // 确保用户关注于样品重量输入框
        editTextSampleWeight.requestFocus();
        
        // 创建对话框
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("修改样品");
        builder.setView(dialogView);
        AlertDialog dialog = builder.create();
        
        // 设置取消按钮点击事件
        btnCancel.setOnClickListener(v -> dialog.dismiss());
        
        // 设置确定按钮点击事件
        btnConfirm.setOnClickListener(v -> {
            // 获取样品类型（不可编辑，从预填充数据获取）
            int typePosition = spinnerSampleType.getSelectedItemPosition();
            String sampleType = sampleTypeNames[typePosition];
            
            // 获取样品重量（唯一可编辑的字段）
            String sampleWeightStr = editTextSampleWeight.getText().toString().trim();
            if (TextUtils.isEmpty(sampleWeightStr)) {
                Toast.makeText(this, "样品重量不能为空", Toast.LENGTH_SHORT).show();
                return;
            }
            
            Double sampleWeight;
            try {
                sampleWeight = Double.parseDouble(sampleWeightStr);
            } catch (NumberFormatException e) {
                Toast.makeText(this, "请输入有效的样品重量", Toast.LENGTH_SHORT).show();
                return;
            }
            
            // 更新样品数据（仅更新重量，其他保持不变）
            sample.setSampleWeight(sampleWeight);
            
            // 关闭对话框
            dialog.dismiss();
            
            // 更新样品
            updateSample(sample, position);
        });
        
        // 显示对话框
        dialog.show();
    }
    
    /**
     * 确认删除样品
     * @param sample 要删除的样品
     * @param position 样品在列表中的位置
     */
    private void confirmDeleteSample(YPLZ sample, int position) {
        new AlertDialog.Builder(this)
                .setTitle("删除样品")
                .setMessage("确定要删除样品 " + sample.getSampleCode() + " 吗？")
                .setPositiveButton("删除", (dialog, which) -> {
                    deleteSample(sample, position);
                })
                .setNegativeButton("取消", null)
                .show();
    }
    
    /**
     * 更新样品信息
     * @param sample 要更新的样品
     * @param position 样品在列表中的位置
     */
    private void updateSample(YPLZ sample, int position) {
        // 显示加载对话框
        AlertDialog loadingDialog = new AlertDialog.Builder(this)
                .setMessage("正在更新样品信息...")
                .setCancelable(false)
                .create();
        loadingDialog.show();
        
        try {
            // 确保样品对象中的ID字段已正确设置
            YPLZ originalSample = sampleList.get(position);
            Long sampleId = originalSample.getId();
            
            // 将ID设置到要更新的样品对象中
            sample.setId(sampleId);
            
            // 调用API更新样品
            RetrofitManager.getInstance(this)
                    .getSampleFlowService()
                    .updateSample(sample)
                    .enqueue(new Callback<ApiResponse<Boolean>>() {
                        @Override
                        public void onResponse(Call<ApiResponse<Boolean>> call, Response<ApiResponse<Boolean>> response) {
                            loadingDialog.dismiss();
                            
                            if (response.isSuccessful() && response.body() != null) {
                                ApiResponse<Boolean> apiResponse = response.body();
                                if (apiResponse.getCode() == 0 && Boolean.TRUE.equals(apiResponse.getData())) {
                                    // 更新成功
                                    Toast.makeText(SampleFlowDetailActivity.this, "样品更新成功", Toast.LENGTH_SHORT).show();
                                    
                                    // 更新内存中的样品对象
                                    sampleList.set(position, sample);
                                    
                                    // 更新列表项
                                    adapter.notifyItemChanged(position);
                                } else {
                                    // 更新失败
                                    String errorMsg = apiResponse.getMsg();
                                    if (TextUtils.isEmpty(errorMsg)) {
                                        errorMsg = "更新样品失败";
                                    }
                                    Toast.makeText(SampleFlowDetailActivity.this, errorMsg, Toast.LENGTH_SHORT).show();
                                }
                            } else {
                                // 网络请求失败
                                Toast.makeText(SampleFlowDetailActivity.this, "网络请求失败", Toast.LENGTH_SHORT).show();
                            }
                        }
                        
                        @Override
                        public void onFailure(Call<ApiResponse<Boolean>> call, Throwable t) {
                            loadingDialog.dismiss();
                            Toast.makeText(SampleFlowDetailActivity.this, "网络请求失败: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                            Log.e(TAG, "更新样品失败", t);
                        }
                    });
        } catch (Exception e) {
            loadingDialog.dismiss();
            Log.e(TAG, "设置样品ID失败", e);
            Toast.makeText(this, "更新样品失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * 删除样品
     * @param sample 要删除的样品
     * @param position 样品在列表中的位置
     */
    private void deleteSample(YPLZ sample, int position) {
        // 显示加载对话框
        AlertDialog loadingDialog = new AlertDialog.Builder(this)
                .setMessage("正在删除样品...")
                .setCancelable(false)
                .create();
        loadingDialog.show();
        
        // 获取样品ID
        Long sampleId = sample.getId();
        if (sampleId == null) {
            loadingDialog.dismiss();
            Toast.makeText(this, "样品ID为空，无法删除", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 调用API删除样品
        RetrofitManager.getInstance(this)
                .getSampleFlowService()
                .deleteSample(sampleId)
                .enqueue(new Callback<ApiResponse<Boolean>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<Boolean>> call, Response<ApiResponse<Boolean>> response) {
                        loadingDialog.dismiss();
                        
                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<Boolean> apiResponse = response.body();
                            if (apiResponse.getCode() == 0 && Boolean.TRUE.equals(apiResponse.getData())) {
                                // 删除成功
                                Toast.makeText(SampleFlowDetailActivity.this, "样品删除成功", Toast.LENGTH_SHORT).show();
                                
                                // 从列表中移除
                                sampleList.remove(position);
                                adapter.notifyItemRemoved(position);
                                adapter.notifyItemRangeChanged(position, sampleList.size());
                                
                                // 更新UI
                                updateSampleListUI();
                            } else {
                                // 删除失败
                                String errorMsg = apiResponse.getMsg();
                                if (TextUtils.isEmpty(errorMsg)) {
                                    errorMsg = "删除样品失败";
                                }
                                Toast.makeText(SampleFlowDetailActivity.this, errorMsg, Toast.LENGTH_SHORT).show();
                            }
                        } else {
                            // 网络请求失败
                            Toast.makeText(SampleFlowDetailActivity.this, "网络请求失败", Toast.LENGTH_SHORT).show();
                        }
                    }
                    
                    @Override
                    public void onFailure(Call<ApiResponse<Boolean>> call, Throwable t) {
                        loadingDialog.dismiss();
                        Toast.makeText(SampleFlowDetailActivity.this, "网络请求失败: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                        Log.e(TAG, "删除样品失败", t);
                    }
                });
    }
    
    /**
     * 显示确认接收对话框
     * 提供接收人填写手机号和进行签名的界面
     */
    private void showReceiveConfirmDialog() {
        if (batchDetail == null) {
            Toast.makeText(this, "批次信息未加载或已被删除", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 检查批次状态是否为寄送中(状态值为1)
        if (batchDetail.getBatchState() == null || batchDetail.getBatchState() != 1) {
            Toast.makeText(this, "只有寄送中状态的批次才能确认接收", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 加载对话框布局
        View dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_receive_sample, null);
        
        // 获取输入控件
        EditText editTextReceiverPhone = dialogView.findViewById(R.id.editTextReceiverPhone);
        SignatureView signatureView = dialogView.findViewById(R.id.signatureView);
        Button buttonClearSignature = dialogView.findViewById(R.id.buttonClearSignature);
        Button buttonConfirmSignature = dialogView.findViewById(R.id.buttonConfirmSignature); // 新增：确认签名按钮
        Button buttonCancel = dialogView.findViewById(R.id.buttonCancel);
        Button buttonConfirm = dialogView.findViewById(R.id.buttonConfirm);
        TextView textViewSignatureStatus = dialogView.findViewById(R.id.textViewSignatureStatus);
        
        // 如果已有电话号码，预填充
        if (batchDetail.getReceiverPhone() != null) {
            editTextReceiverPhone.setText(batchDetail.getReceiverPhone());
        } else {
            // 从SharedPreferences获取当前用户手机号
            SharedPreferences sp = getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, MODE_PRIVATE);
            String userPhone = sp.getString("userPhone", "");
            if (!TextUtils.isEmpty(userPhone)) {
                editTextReceiverPhone.setText(userPhone);
            }
        }
        
        // 创建对话框
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("确认接收样品");
        builder.setView(dialogView);
        AlertDialog dialog = builder.create();
        
        // 设置清除签名按钮点击事件
        buttonClearSignature.setOnClickListener(v -> {
            signatureView.clear();
            textViewSignatureStatus.setText("请在上方区域签名");
            // 重置签名URL
            receiverSignatureUrl = null;
            // 重新启用签名确认按钮
            buttonConfirmSignature.setEnabled(true);
            buttonConfirm.setEnabled(false);
        });
        
        // 新增：设置确认签名按钮点击事件
        buttonConfirmSignature.setOnClickListener(v -> {
            // 检查签名是否为空
            if (signatureView.isEmpty()) {
                Toast.makeText(this, "请先完成签名", Toast.LENGTH_SHORT).show();
                return;
            }
            
            // 更新状态提示
            textViewSignatureStatus.setText("正在上传签名...");
            buttonConfirmSignature.setEnabled(false);
            
            // 上传签名
            uploadReceiverSignature(signatureView, new FileUploadService.FileUploadCallback() {
                @Override
                public void onSuccess(String fileUrl) {
                    runOnUiThread(() -> {
                        // 上传成功
                        receiverSignatureUrl = fileUrl;
                        textViewSignatureStatus.setText("签名已确认并上传");
                        buttonConfirmSignature.setEnabled(false); // 禁用确认签名按钮
                        buttonConfirm.setEnabled(true); // 启用确认接收按钮
                        Toast.makeText(SampleFlowDetailActivity.this, "签名上传成功", Toast.LENGTH_SHORT).show();
                    });
                }
                
                @Override
                public void onFailure(String errorMsg) {
                    runOnUiThread(() -> {
                        textViewSignatureStatus.setText("签名上传失败：" + errorMsg);
                        buttonConfirmSignature.setEnabled(true); // 重新启用签名确认按钮
                        Toast.makeText(SampleFlowDetailActivity.this, "签名上传失败：" + errorMsg, Toast.LENGTH_SHORT).show();
                    });
                }
            });
        });
        
        // 设置取消按钮点击事件
        buttonCancel.setOnClickListener(v -> dialog.dismiss());
        
        // 设置确定按钮点击事件
        buttonConfirm.setOnClickListener(v -> {
            // 获取接收人手机号
            String receiverPhone = editTextReceiverPhone.getText().toString().trim();
            if (TextUtils.isEmpty(receiverPhone)) {
                Toast.makeText(this, "请填写接收人手机号", Toast.LENGTH_SHORT).show();
                return;
            }
            
            // 检查签名是否已经上传
            if (TextUtils.isEmpty(receiverSignatureUrl)) {
                Toast.makeText(this, "请先确认签名", Toast.LENGTH_SHORT).show();
                return;
            }
            
            // 关闭对话框
            dialog.dismiss();
            
            // 确认接收
            confirmReceive(receiverPhone, receiverSignatureUrl);
        });
        
        // 初始状态下，禁用确认接收按钮，等待签名上传成功后再启用
        buttonConfirm.setEnabled(false);
        
        // 显示对话框
        dialog.show();
    }
    
    /**
     * 上传接收人签名
     */
    private void uploadReceiverSignature(SignatureView signatureView, FileUploadService.FileUploadCallback callback) {
        // 获取签名图片
        Bitmap signatureBitmap = signatureView.getSignatureBitmap();
        if (signatureBitmap == null) {
            callback.onFailure("获取签名图片失败");
            return;
        }
        
        // 上传签名图片
        fileUploadService.uploadSignature(signatureBitmap, callback);
    }
    
    /**
     * 确认接收样品
     */
    private void confirmReceive(String receiverPhone, String receiverSignature) {
        // 显示进度对话框
        ProgressDialog progressDialog = new ProgressDialog(this);
        progressDialog.setMessage("正在确认接收...");
        progressDialog.setCancelable(false);
        progressDialog.show();
        
        // 构建确认接收的请求对象
        YplzBatch receiveBatch = new YplzBatch();
        receiveBatch.setId(batchId); // 设置批次ID
        receiveBatch.setReceiverPhone(receiverPhone); // 设置接收人电话
        receiveBatch.setReceiverSignature(receiverSignature); // 设置接收人签名
        receiveBatch.setBatchState(100L); // 设置批次状态为"寄送完成"
        receiveBatch.setIsSure(1L); // 设置已确认
        
        // 日志输出确认状态设置
        Log.d(TAG, "确认接收样品 - 批次ID: " + batchId + ", 设置状态为: 100 (寄送完成)");

        // 从SharedPreferences获取当前用户信息，设置为接收人
        SharedPreferences sp = getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, MODE_PRIVATE);
        String userName = sp.getString("userName", "");
        receiveBatch.setReceiverName(userName);
        
        // 发起网络请求
        RetrofitManager.getInstance(this)
                .getSampleBatchServiceApi()
                .receiveSampleBatch(receiveBatch)
                .enqueue(new Callback<ApiResponse<Integer>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<Integer>> call, Response<ApiResponse<Integer>> response) {
                        progressDialog.dismiss();
                        
                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<Integer> apiResponse = response.body();
                            if (apiResponse.getCode() == 0) {
                                // 确认接收成功
                                Toast.makeText(SampleFlowDetailActivity.this, "样品接收成功", Toast.LENGTH_SHORT).show();
                                
                                // 重新加载数据
                                loadData();
                                
                                // 设置结果并返回
                                setResult(RESULT_OK);
                                
                                // 添加返回逻辑，确保回到列表页面
                                new Handler().postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        finish(); // 延迟关闭当前页面，返回到列表页
                                    }
                                }, 1000); // 延迟1秒，让用户看到成功提示
                            } else {
                                // 确认接收失败
                                String errorMsg = "确认接收失败: " + apiResponse.getMsg();
                                Toast.makeText(SampleFlowDetailActivity.this, errorMsg, Toast.LENGTH_SHORT).show();
                            }
                        } else {
                            // 网络请求失败
                            Toast.makeText(SampleFlowDetailActivity.this, "网络请求失败", Toast.LENGTH_SHORT).show();
                        }
                    }
                    
                    @Override
                    public void onFailure(Call<ApiResponse<Integer>> call, Throwable t) {
                        progressDialog.dismiss();
                        Toast.makeText(SampleFlowDetailActivity.this, "网络请求失败: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                        Log.e(TAG, "确认接收样品失败", t);
                    }
                });
    }
} 
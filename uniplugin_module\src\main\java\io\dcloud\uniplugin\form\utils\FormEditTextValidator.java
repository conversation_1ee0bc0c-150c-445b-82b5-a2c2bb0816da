package io.dcloud.uniplugin.form.utils;

import android.content.Context;
import android.text.InputFilter;
import android.text.TextUtils;
import android.util.Log;
import android.widget.EditText;

import io.dcloud.uniplugin.model.FormFieldConfig;

/**
 * 表单输入框验证器，负责实现失去焦点时的输入验证
 */
public class FormEditTextValidator {
    private static final String TAG = "FormEditTextValidator";

    /**
     * 设置输入框的最大长度限制和失焦验证
     * 
     * @param context 上下文
     * @param editText 输入框控件
     * @param field 字段配置
     */
    public static void setupEditTextValidation(Context context, EditText editText, FormFieldConfig field) {
        if (editText == null || field == null) {
            return;
        }
        
        String fieldId = field.getFieldId();
        String fieldName = field.getFieldName();
        
        // 设置最大长度限制
        setupMaxLengthLimit(editText, field);
        
        // 设置失焦验证监听
        setupFocusChangeListener(context, editText, field);
        
        Log.d(TAG, "已设置字段的输入验证: " + fieldId + " (" + fieldName + ")");
    }
    
    /**
     * 设置最大长度限制
     */
    private static void setupMaxLengthLimit(EditText editText, FormFieldConfig field) {
        // 只使用maxLength属性
        Integer maxLength = field.getMaxLength();
        
        // 如果有长度限制，应用到输入框
        if (maxLength != null && maxLength > 0) {
            InputFilter[] filters = new InputFilter[1];
            filters[0] = new InputFilter.LengthFilter(maxLength);
            editText.setFilters(filters);
            Log.d(TAG, "为字段 " + field.getFieldId() + " 设置最大长度限制: " + maxLength);
        }
    }
    
    /**
     * 设置失焦验证监听器
     */
    private static void setupFocusChangeListener(Context context, EditText editText, FormFieldConfig field) {
        editText.setOnFocusChangeListener((v, hasFocus) -> {
            if (!hasFocus) {
                // 失去焦点时进行验证
                String value = editText.getText().toString();
                String errorMessage = null;
                
                // 1. 检查必填项
                if (Boolean.TRUE.equals(field.isRequired())) {
                    if (TextUtils.isEmpty(value)) {
                        errorMessage = field.getLabel() + "为必填项";
                        Log.d(TAG, "字段 " + field.getFieldId() + " 验证失败: 必填项为空");
                    }
                }
                
                // 2. 如果有值，检查长度限制
                if (errorMessage == null && !TextUtils.isEmpty(value)) {
                    Integer maxLength = field.getMaxLength();
                    
                    if (maxLength != null && maxLength > 0 && value.length() > maxLength) {
                        errorMessage = field.getLabel() + "长度不能超过" + maxLength + "个字符";
                        Log.d(TAG, "字段 " + field.getFieldId() + " 验证失败: 超出最大长度 " + maxLength);
                    }
                }
                
                // 3. 如果通过前两项，执行详细的格式验证
                if (errorMessage == null && !TextUtils.isEmpty(value)) {
                    errorMessage = FormValidationUtils.validateField(field, value);
                    if (errorMessage != null) {
                        Log.d(TAG, "字段 " + field.getFieldId() + " 验证失败: " + errorMessage);
                    }
                }
                
                // 设置或清除错误消息
                if (errorMessage != null) {
                    editText.setError(errorMessage);
                    highlightError(editText);
                } else {
                    editText.setError(null);
                }
            }
        });
    }
    
    /**
     * 突出显示错误
     */
    private static void highlightError(EditText editText) {
        try {
            // 使用背景色过渡动画凸显错误
            android.animation.ValueAnimator colorAnim = android.animation.ValueAnimator.ofArgb(
                    android.graphics.Color.parseColor("#FFEBEE"),  // 淡红色开始
                    android.graphics.Color.WHITE                   // 白色结束
            );
            
            colorAnim.addUpdateListener(valueAnimator -> {
                editText.setBackgroundTintList(android.content.res.ColorStateList.valueOf(
                        (Integer) valueAnimator.getAnimatedValue()));
            });
            
            colorAnim.setDuration(1000);
            colorAnim.start();
        } catch (Exception e) {
            Log.e(TAG, "高亮错误动画失败: " + e.getMessage());
        }
    }
} 
package io.dcloud.uniplugin.form;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * 模拟数据提供者，用于在后端API未完成时提供测试数据
 */
public class MockDataProvider {

    /**
     * 获取模拟的表单配置数据
     * @param formId 表单ID
     * @return 表单配置的JSON字符串
     */
    public static String getMockFormConfig(String formId) {
        try {
            JSONObject config = new JSONObject();
            config.put("code", 0);
            config.put("msg", "success");

            // 创建data对象
            JSONObject data = new JSONObject();
            data.put("id", formId);
            data.put("name", "样点调查表");
            data.put("description", "用于记录样点的基本信息和调查数据");
            data.put("version", "1.0");
            data.put("status", 1);
            data.put("createTime", "2024-03-20 10:00:00");
            data.put("updateTime", "2024-03-20 10:00:00");

            // 创建fields数组
            JSONArray fields = new JSONArray();

            // 基本信息组
            fields.put(createBasicInfoGroup());

            // 位置信息组
            fields.put(createLocationInfoGroup());

            // 土地信息组
            fields.put(createLandInfoGroup());

            // 调查数据组
            fields.put(createSurveyDataGroup());

            // 现场照片组
            fields.put(createPhotosGroup());

            // 签名确认组
            fields.put(createSignatureGroup());

            // 将fields添加到data中
            data.put("fields", fields);

            // 添加验证规则
            data.put("rules", createValidationRules());

            // 将data添加到config中
            config.put("data", data);

            return config.toString();
        } catch (JSONException e) {
            e.printStackTrace();
            return "{}";
        }
    }

    private static JSONObject createBasicInfoGroup() throws JSONException {
        JSONObject group = new JSONObject();
        group.put("id", "basic_info");
        group.put("name", "基本信息");
        group.put("type", "group");
        group.put("required", true);

        JSONArray fields = new JSONArray();

        // 项目名称
        JSONObject projectName = new JSONObject();
        projectName.put("id", "project_name");
        projectName.put("name", "项目名称");
        projectName.put("type", "text");
        projectName.put("required", true);
        projectName.put("defaultValue", "");
        projectName.put("placeholder", "请输入项目名称");
        projectName.put("maxLength", 100);
        fields.put(projectName);

        // 调查日期
        JSONObject surveyDate = new JSONObject();
        surveyDate.put("id", "survey_date");
        surveyDate.put("name", "调查日期");
        surveyDate.put("type", "date");
        surveyDate.put("required", true);
        surveyDate.put("defaultValue", "");
        surveyDate.put("format", "yyyy-MM-dd");
        fields.put(surveyDate); 

        // 天气状况
        JSONObject weather = new JSONObject();
        weather.put("id", "weather");
        weather.put("name", "天气状况");
        weather.put("type", "select");
        weather.put("required", true);
        weather.put("defaultValue", "sunny");

        JSONArray weatherOptions = new JSONArray();
        weatherOptions.put(new JSONObject().put("label", "晴").put("value", "sunny"));
        weatherOptions.put(new JSONObject().put("label", "多云").put("value", "cloudy"));
        weatherOptions.put(new JSONObject().put("label", "阴").put("value", "overcast"));
        weatherOptions.put(new JSONObject().put("label", "雨").put("value", "rainy"));
        weather.put("options", weatherOptions);

        fields.put(weather);

        group.put("fields", fields);
        return group;
    }

    private static JSONObject createLocationInfoGroup() throws JSONException {
        JSONObject group = new JSONObject();
        group.put("id", "location_info");
        group.put("name", "位置信息");
        group.put("type", "group");
        group.put("required", true);

        JSONArray fields = new JSONArray();

        // 省份
        JSONObject province = new JSONObject();
        province.put("id", "province");
        province.put("name", "省份");
        province.put("type", "select");
        province.put("required", true);
        province.put("defaultValue", "440000");

        JSONArray provinceOptions = new JSONArray();
        provinceOptions.put(new JSONObject().put("label", "广东省").put("value", "440000"));
        province.put("options", provinceOptions);
        fields.put(province);

        // 城市
        JSONObject city = new JSONObject();
        city.put("id", "city");
        city.put("name", "城市");
        city.put("type", "select");
        city.put("required", true);

        JSONArray cityOptions = new JSONArray();
        cityOptions.put(new JSONObject().put("label", "广州市").put("value", "440100"));
        cityOptions.put(new JSONObject().put("label", "深圳市").put("value", "440300"));
        cityOptions.put(new JSONObject().put("label", "珠海市").put("value", "440400"));
        city.put("options", cityOptions);
        fields.put(city);

        // 区县
        JSONObject district = new JSONObject();
        district.put("id", "district");
        district.put("name", "区县");
        district.put("type", "select");
        district.put("required", true);
        district.put("options", new JSONArray());

        JSONArray districtOptions = new JSONArray();
        districtOptions.put(new JSONObject().put("label", "天河区").put("value", "440101"));
        districtOptions.put(new JSONObject().put("label", "黄埔区").put("value", "440102"));
        districtOptions.put(new JSONObject().put("label", "越秀区").put("value", "440103"));
        district.put("options", districtOptions);
        fields.put(district);

        // 详细地址
        JSONObject address = new JSONObject();
        address.put("id", "address");
        address.put("name", "详细地址");
        address.put("type", "text");
        address.put("required", true);
        address.put("maxLength", 200);
        fields.put(address);

        // GPS坐标
        JSONObject coordinates = new JSONObject();
        coordinates.put("id", "coordinates");
        coordinates.put("name", "GPS坐标");
        coordinates.put("type", "location");
        coordinates.put("required", true);
        coordinates.put("format", "WGS84");
        fields.put(coordinates);

        group.put("fields", fields);
        return group;
    }

    private static JSONObject createLandInfoGroup() throws JSONException {
        JSONObject group = new JSONObject();
        group.put("id", "land_info");
        group.put("name", "土地信息");
        group.put("type", "group");
        group.put("required", true);

        JSONArray fields = new JSONArray();

        // 土地类型
        JSONObject landType = new JSONObject();
        landType.put("id", "land_type");
        landType.put("name", "土地类型");
        landType.put("type", "select");
        landType.put("required", true);

        JSONArray landTypeOptions = new JSONArray();
        landTypeOptions.put(new JSONObject().put("label", "耕地").put("value", "farmland"));
        landTypeOptions.put(new JSONObject().put("label", "林地").put("value", "woodland"));
        landTypeOptions.put(new JSONObject().put("label", "草地").put("value", "grassland"));
        landTypeOptions.put(new JSONObject().put("label", "其他").put("value", "other"));
        landType.put("options", landTypeOptions);
        fields.put(landType);

        // 面积
        JSONObject landArea = new JSONObject();
        landArea.put("id", "land_area");
        landArea.put("name", "面积（亩）");
        landArea.put("type", "number");
        landArea.put("required", true);
        landArea.put("min", 0);
        landArea.put("max", 10000);
        landArea.put("precision", 2);
        fields.put(landArea);

        // 土壤类型
        JSONObject soilType = new JSONObject();
        soilType.put("id", "soil_type");
        soilType.put("name", "土壤类型");
        soilType.put("type", "select");
        soilType.put("required", true);

        JSONArray soilTypeOptions = new JSONArray();
        soilTypeOptions.put(new JSONObject().put("label", "砂土").put("value", "sandy"));
        soilTypeOptions.put(new JSONObject().put("label", "粘土").put("value", "clay"));
        soilTypeOptions.put(new JSONObject().put("label", "壤土").put("value", "loam"));
        soilType.put("options", soilTypeOptions);
        fields.put(soilType);

        group.put("fields", fields);
        return group;
    }

    private static JSONObject createSurveyDataGroup() throws JSONException {
        JSONObject group = new JSONObject();
        group.put("id", "survey_data");
        group.put("name", "调查数据");
        group.put("type", "group");
        group.put("required", true);

        JSONArray fields = new JSONArray();

        // 土壤pH值
        JSONObject soilPh = new JSONObject();
        soilPh.put("id", "soil_ph");
        soilPh.put("name", "土壤pH值");
        soilPh.put("type", "number");
        soilPh.put("required", true);
        soilPh.put("min", 0);
        soilPh.put("max", 14);
        soilPh.put("precision", 1);
        fields.put(soilPh);

        // 新增字段
        JSONObject newadd = new JSONObject();
        newadd.put("id", "asdasd");
        newadd.put("name", "新增阿斯顿");
        newadd.put("type", "text");
        newadd.put("required", true);
        newadd.put("maxLength", 500);
        newadd.put("precision", 1);
        fields.put(newadd);

        // 有机质含量
        JSONObject organicMatter = new JSONObject();
        organicMatter.put("id", "organic_matter");
        organicMatter.put("name", "有机质含量（%）");
        organicMatter.put("type", "number");
        organicMatter.put("required", true);
        organicMatter.put("min", 0);
        organicMatter.put("max", 100);
        organicMatter.put("precision", 2);
        fields.put(organicMatter);

        // 备注
        JSONObject notes = new JSONObject();
        notes.put("id", "notes");
        notes.put("name", "备注");
        notes.put("type", "textarea");
        notes.put("required", false);
        notes.put("maxLength", 500);
        notes.put("rows", 3);
        fields.put(notes);

        group.put("fields", fields);
        return group;
    }

    private static JSONObject createPhotosGroup() throws JSONException {
        JSONObject group = new JSONObject();
        group.put("id", "photos");
        group.put("name", "现场照片和视频");
        group.put("type", "group");
        group.put("required", true);

        JSONArray fields = new JSONArray();

        // 概貌照片
        JSONObject overviewPhotos = new JSONObject();
        overviewPhotos.put("id", "overview_photos");
        overviewPhotos.put("name", "概貌照片");
        overviewPhotos.put("type", "photo");
        overviewPhotos.put("required", true);
        overviewPhotos.put("maxCount", 3);
        overviewPhotos.put("description", "请拍摄地块整体概貌照片");
        fields.put(overviewPhotos);

        // 细节照片
        JSONObject detailPhotos = new JSONObject();
        detailPhotos.put("id", "detail_photos");
        detailPhotos.put("name", "细节照片");
        detailPhotos.put("type", "photo");
        detailPhotos.put("required", true);
        detailPhotos.put("maxCount", 5);
        detailPhotos.put("description", "请拍摄地块特征细节照片");
        fields.put(detailPhotos);

        // 更多照片
        JSONObject morePhotos = new JSONObject();
        morePhotos.put("id", "more_photos");
        morePhotos.put("name", "更多照片");
        morePhotos.put("type", "photo");
        morePhotos.put("required", true);
        morePhotos.put("maxCount", 5);
        morePhotos.put("description", "请拍摄更多照片");
        fields.put(morePhotos);

        // 现场视频
        JSONObject siteVideo = new JSONObject();
        siteVideo.put("id", "site_video");
        siteVideo.put("name", "现场视频");
        siteVideo.put("type", "video");  // 使用photo类型，但通过mediaType指定为视频
//        siteVideo.put("mediaType", "video");  // 指定媒体类型为视频
        siteVideo.put("required", true);
        siteVideo.put("maxCount", 2);
        siteVideo.put("description", "请录制现场情况视频");
        siteVideo.put("maxDuration", 30);  // 最大录制时长（秒）
        siteVideo.put("quality", "high");  // 视频质量
        fields.put(siteVideo);

        group.put("fields", fields);
        return group;
    }

    private static JSONObject createSignatureGroup() throws JSONException {
        JSONObject group = new JSONObject();
        group.put("id", "signature");
        group.put("name", "签名确认");
        group.put("type", "group");
        group.put("required", true);

        JSONArray fields = new JSONArray();

        // 调查人
        JSONObject surveyor = new JSONObject();
        surveyor.put("id", "surveyor");
        surveyor.put("name", "调查人");
        surveyor.put("type", "text");
        surveyor.put("required", true);
        fields.put(surveyor);

        // 调查人签名
        JSONObject surveySignature = new JSONObject();
        surveySignature.put("id", "survey_signature");
        surveySignature.put("name", "调查人签名");
        surveySignature.put("type", "signature");
        surveySignature.put("required", true);
        fields.put(surveySignature);

        // 见证人
        JSONObject witness = new JSONObject();
        witness.put("id", "witness");
        witness.put("name", "见证人");
        witness.put("type", "text");
        witness.put("required", false);
        fields.put(witness);

        // 见证人签名
        JSONObject witnessSignature = new JSONObject();
        witnessSignature.put("id", "witness_signature");
        witnessSignature.put("name", "见证人签名");
        witnessSignature.put("type", "signature");
        witnessSignature.put("required", false);
        fields.put(witnessSignature);

        group.put("fields", fields);
        return group;
    }

    private static JSONArray createValidationRules() throws JSONException {
        JSONArray rules = new JSONArray();

        // 面积验证规则
        JSONObject landAreaRule = new JSONObject();
        landAreaRule.put("field", "land_area");
        landAreaRule.put("type", "range");
        JSONObject landAreaParams = new JSONObject();
        landAreaParams.put("min", 0);
        landAreaParams.put("max", 10000);
        landAreaRule.put("params", landAreaParams);
        landAreaRule.put("message", "面积必须在0-10000亩之间");
        rules.put(landAreaRule);

        // pH值验证规则
        JSONObject phRule = new JSONObject();
        phRule.put("field", "soil_ph");
        phRule.put("type", "range");
        JSONObject phParams = new JSONObject();
        phParams.put("min", 0);
        phParams.put("max", 14);
        phRule.put("params", phParams);
        phRule.put("message", "pH值必须在0-14之间");
        rules.put(phRule);

        // 有机质含量验证规则
        JSONObject organicMatterRule = new JSONObject();
        organicMatterRule.put("field", "organic_matter");
        organicMatterRule.put("type", "range");
        JSONObject organicMatterParams = new JSONObject();
        organicMatterParams.put("min", 0);
        organicMatterParams.put("max", 100);
        organicMatterRule.put("params", organicMatterParams);
        organicMatterRule.put("message", "有机质含量必须在0-100%之间");
        rules.put(organicMatterRule);

        return rules;
    }
} 
package com.chy.map;

import static androidx.constraintlayout.helper.widget.MotionEffect.TAG;

import android.app.Activity;
import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageButton;
import android.widget.TextView;

import com.esri.arcgisruntime.geometry.AreaUnit;
import com.esri.arcgisruntime.geometry.AreaUnitId;
import com.esri.arcgisruntime.geometry.GeodeticCurveType;
import com.esri.arcgisruntime.geometry.Geometry;
import com.esri.arcgisruntime.geometry.GeometryEngine;
import com.esri.arcgisruntime.geometry.GeometryType;
import com.esri.arcgisruntime.geometry.LinearUnit;
import com.esri.arcgisruntime.geometry.LinearUnitId;
import com.esri.arcgisruntime.geometry.Polygon;
import com.esri.arcgisruntime.geometry.Polyline;
import com.esri.arcgisruntime.geometry.SpatialReference;
import com.esri.arcgisruntime.mapping.view.Graphic;
import com.esri.arcgisruntime.mapping.view.GraphicsOverlay;
import com.esri.arcgisruntime.mapping.view.MapView;
import com.esri.arcgisruntime.mapping.view.SketchCreationMode;
import com.esri.arcgisruntime.mapping.view.SketchEditor;
import com.esri.arcgisruntime.mapping.view.SketchGeometryChangedEvent;
import com.esri.arcgisruntime.mapping.view.SketchGeometryChangedListener;
import com.esri.arcgisruntime.symbology.SimpleFillSymbol;
import com.esri.arcgisruntime.symbology.SimpleLineSymbol;
import com.esri.arcgisruntime.symbology.SimpleMarkerSymbol;
import com.google.android.material.snackbar.Snackbar;

import java.math.BigDecimal;

public class MeasurementTool {
    private Context context;
    private Activity activity;
    private MapView mapView;

    public SketchEditor mSketchEditor;
    private ImageButton mPolylineButton;
    private ImageButton mPolygonButton;

    private SimpleMarkerSymbol mPointSymbol;
    private SimpleFillSymbol mFillSymbol;
    private SimpleLineSymbol mLineSymbol;

    private GraphicsOverlay mGraphicsOverlay;

    private final LinearUnit mUnitOfMeasurement = new LinearUnit(LinearUnitId.METERS);
    private final AreaUnit mAreaOfMeasurement = new AreaUnit(AreaUnitId.SQUARE_METERS);


    public MeasurementTool(Context context, Activity activity,MapView mapView) {
        this.context = context;
        this.activity = activity;
        this.mapView=mapView;
        mPointSymbol = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.SQUARE, 0xFFFF0000, 20);
        mLineSymbol = new SimpleLineSymbol(SimpleLineSymbol.Style.SOLID, 0xFFFF8800, 4);
        mFillSymbol = new SimpleFillSymbol(SimpleFillSymbol.Style.CROSS, 0x40FFA9A9, mLineSymbol);
        mSketchEditor=new SketchEditor();
        mapView.setSketchEditor(mSketchEditor);
        mPolylineButton = activity.findViewById(R.id.polylineButton);
        mPolygonButton = activity.findViewById(R.id.polygonButton);
        ImageButton undo = activity.findViewById(R.id.undo);
        ImageButton redo =  activity.findViewById(R.id.redo);
        ImageButton cancle_btn = activity.findViewById(R.id.cancle_btn);
        // add click listeners
        mPolylineButton.setOnClickListener(view -> createModePolyline());
        mPolygonButton.setOnClickListener(view -> createModePolygon());
        undo.setOnClickListener(view -> undo());
        redo.setOnClickListener(view -> redo());
        cancle_btn.setOnClickListener(view -> cancle_btnClick());
        mSketchEditor.addGeometryChangedListener(new SketchGeometryChangedListener() {
            @Override
            public void geometryChanged(SketchGeometryChangedEvent sketchGeometryChangedEvent) {
                Geometry geometry = mSketchEditor.getGeometry();
                if (geometry == null) {
                    return;
                }
                Log.d("草图模式", mSketchEditor.getSketchCreationMode().toString());
                switch (mSketchEditor.getSketchCreationMode()) {
                    case POLYLINE:
                        if (mSketchEditor.isSketchValid()) {
                            double length = GeometryEngine.lengthGeodetic((Polyline) geometry, mUnitOfMeasurement, GeodeticCurveType.GEODESIC);
                            activity.runOnUiThread(() -> {
                                TextView result_text = (TextView) activity.findViewById(R.id.result_text);
                                if (length >= 1000) {
                                    result_text.setText(String.format("%.2f", length / 1000) + "千米");
                                } else {
                                    result_text.setText(String.format("%.2f", length) + "米");
                                }
                                String span = "请开始绘制！";
                            });
                        }
                        break;
                    case POLYGON:
                        if (mSketchEditor.isSketchValid()) {
                            double area = GeometryEngine.areaGeodetic((Polygon) geometry, mAreaOfMeasurement, GeodeticCurveType.GEODESIC);
                            activity.runOnUiThread(() -> {
                                TextView result_text = (TextView) activity.findViewById(R.id.result_text);
                                if (area >= 666.7) {
                                    result_text.setText(String.format("%.2f", area / 666.7) + "亩");
                                } else {
                                    result_text.setText(String.format("%.2f", area) + "平方米");
                                }
                                String span = "请开始绘制！";
                            });
                        }
                        break;
                }
            }
        });
    }
    //量测相关函数开始

    /**
     * When the polyline button is clicked, reset other buttons, show the polyline button as selected, and start
     * polyline drawing mode.
     */
    private void createModePolyline() {
        //选择了一个画线，清空绘制图层的图案
//        if (mGraphicsOverlay.getGraphics() != null) {
//            mGraphicsOverlay.getGraphics().removeAll(mGraphicsOverlay.getGraphics());
//        }
        resetButtons();
        mPolylineButton.setSelected(true);
        mSketchEditor.start(SketchCreationMode.POLYLINE);


    }

    /**
     * When the polygon button is clicked, reset other buttons, show the polygon button as selected, and start polygon
     * drawing mode.
     */
    private void createModePolygon() {
        //选择了一个画面，清空绘制图层的图案
//        if (mGraphicsOverlay.getGraphics() != null) {
//            mGraphicsOverlay.getGraphics().removeAll(mGraphicsOverlay.getGraphics());
//        }
        resetButtons();
        mPolygonButton.setSelected(true);
        mSketchEditor.start(SketchCreationMode.POLYGON);
    }

    /**
     * When the undo button is clicked, undo the last event on the SketchEditor.
     */
    private void undo() {
        if (mSketchEditor.canUndo()) {
            mSketchEditor.undo();
        }
    }

    /**
     * When the redo button is clicked, redo the last undone event on the SketchEditor.
     */
    private void redo() {
        if (mSketchEditor.canRedo()) {
            mSketchEditor.redo();
        }
    }

    /**
     * When the stop button is clicked, check that sketch is valid. If so, get the geometry from the sketch, set its
     * symbol and add it to the graphics overlay.
     */
    private void stop() {
        if (!mSketchEditor.isSketchValid()) {
            reportNotValid();
            mSketchEditor.stop();
            resetButtons();
            return;
        }
        //获取显示测量结果的文本框
        TextView result_text = (TextView) activity.findViewById(R.id.result_text);
        String span = "请开始绘制！";
        // get the geometry from sketch editor
        Geometry sketchGeometry = mSketchEditor.getGeometry();
        //mSketchEditor.stop();
        resetButtons();

        if (sketchGeometry != null) {

            // create a graphic from the sketch editor geometry
            Graphic graphic = new Graphic(sketchGeometry);

            // assign a symbol based on geometry type
            if (graphic.getGeometry().getGeometryType() == GeometryType.POLYGON) {
                graphic.setSymbol(mFillSymbol);
                //如果绘制的是面，则计算面积
                Polygon polygon = (Polygon) GeometryEngine.project(sketchGeometry, SpatialReference.create(3857));
                BigDecimal areaB = BigDecimal.valueOf(Math.abs(GeometryEngine.area(polygon)));
                double area = areaB.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
                //TODO:保留两位小数
                span = "面积: " + 0.0015 * area + "亩";
                result_text.setText(String.format("%.6f", span));
            } else if (graphic.getGeometry().getGeometryType() == GeometryType.POLYLINE) {
                graphic.setSymbol(mLineSymbol);
                //如果绘制的是线，则计算长度
                Polyline polyline = (Polyline) GeometryEngine.project(sketchGeometry, SpatialReference.create(3857));
                BigDecimal polylineB = BigDecimal.valueOf(GeometryEngine.length(polyline));
                span = "长度: " + polylineB.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue() + "m";
                result_text.setText(String.format("%.6f", span));
            } else if (graphic.getGeometry().getGeometryType() == GeometryType.POINT ||
                    graphic.getGeometry().getGeometryType() == GeometryType.MULTIPOINT) {
                graphic.setSymbol(mPointSymbol);
            }
            // add the graphic to the graphics overlay
            mGraphicsOverlay.getGraphics().add(graphic);
        }
    }

    /**
     * Called if sketch is invalid. Reports to user why the sketch was invalid.
     */
    private void reportNotValid() {
        String validIf;
        if (mSketchEditor.getSketchCreationMode() == SketchCreationMode.POLYLINE) {
            validIf = "绘制线至少点击两个点！";
        } else if (mSketchEditor.getSketchCreationMode() == SketchCreationMode.POLYGON) {
            validIf = "多边形至少包含三个点！";
        } else {
            validIf = "请选择创建的类型！";
        }
        String report = "警告:\n" + validIf;
        Snackbar reportSnackbar = Snackbar.make(activity.findViewById(R.id.toolbarInclude), report, Snackbar.LENGTH_INDEFINITE);
        reportSnackbar.setAction("确定", view -> reportSnackbar.dismiss());
        TextView snackbarTextView = reportSnackbar.getView().findViewById(com.google.android.material.R.id.snackbar_text);
        snackbarTextView.setSingleLine(false);
        reportSnackbar.show();
        Log.e(TAG, report);
    }

    /**
     * De-selects all buttons.
     */
    public void resetButtons() {
        mPolylineButton.setSelected(false);
        mPolygonButton.setSelected(false);
    }

    private void cancle_btnClick() {
        resetButtons();
        mSketchEditor.stop();
        TextView textView = (TextView) activity.findViewById(R.id.result_text);
        textView.setText("请开始绘制！");
    }
}

package io.dcloud.uniplugin.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 角色枚举类
 * 用于将角色代码转换为中文名称
 */
public enum RoleEnum {
    
    JCSYS("sysry", "检测实验室"),
    CYD("cyd", "采样队"),
    SJ_ADMIN("sjAdmin", "省级管理员");
    
    private final String code;
    private final String chineseName;
    
    // 缓存映射关系以提高查找性能
    private static final Map<String, String> CODE_TO_NAME_MAP = new HashMap<>();
    
    static {
        for (RoleEnum role : RoleEnum.values()) {
            CODE_TO_NAME_MAP.put(role.getCode(), role.getChineseName());
        }
    }
    
    RoleEnum(String code, String chineseName) {
        this.code = code;
        this.chineseName = chineseName;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getChineseName() {
        return chineseName;
    }
    
    /**
     * 根据角色代码获取中文名称
     * @param code 角色代码
     * @return 对应的中文名称，如果没有对应的角色则返回原代码
     */
    public static String getChineseNameByCode(String code) {
        return CODE_TO_NAME_MAP.getOrDefault(code, code);
    }
    
    /**
     * 将角色代码集合转换为中文名称字符串
     * @param roleCodes 角色代码集合
     * @return 以逗号分隔的中文角色名称字符串
     */
    public static String convertRolesToChineseNames(Set<String> roleCodes) {
        if (roleCodes == null || roleCodes.isEmpty()) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        for (String code : roleCodes) {
            sb.append(getChineseNameByCode(code)).append(", ");
        }
        
        // 删除最后的逗号和空格
        if (sb.length() > 0) {
            sb.delete(sb.length() - 2, sb.length());
        }
        
        return sb.toString();
    }
} 
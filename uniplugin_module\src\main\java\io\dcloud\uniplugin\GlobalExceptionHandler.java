package io.dcloud.uniplugin;

import android.content.Context;
import android.os.Environment;
import android.util.Log;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 全局异常处理器
 * 用于捕获应用中的所有未捕获异常，并将错误信息保存到日志文件
 */
public class GlobalExceptionHandler implements Thread.UncaughtExceptionHandler {
    
    private static final String TAG = "GlobalExceptionHandler";
    private static final String LOG_DIR = "BCGDGISData/ERROR";
    
    private Context context;
    private Thread.UncaughtExceptionHandler defaultHandler;
    
    public GlobalExceptionHandler(Context context) {
        this.context = context;
        this.defaultHandler = Thread.getDefaultUncaughtExceptionHandler();
    }
    
    /**
     * 初始化全局异常处理器
     * @param context 应用上下文
     */
    public static void init(Context context) {
        GlobalExceptionHandler handler = new GlobalExceptionHandler(context);
        Thread.setDefaultUncaughtExceptionHandler(handler);
        Log.i(TAG, "全局异常处理器初始化完成");
    }
    
    @Override
    public void uncaughtException(Thread thread, Throwable ex) {
        // 记录异常到日志文件
        saveExceptionToFile(ex);
        
        // 调用默认的异常处理器
        if (defaultHandler != null) {
            defaultHandler.uncaughtException(thread, ex);
        }
    }
    
    /**
     * 保存异常信息到文件
     * @param throwable 异常对象
     */
    private void saveExceptionToFile(Throwable throwable) {
        try {
            // 创建错误日志目录
            File errorDir = createErrorDirectory();
            if (errorDir == null) {
                Log.e(TAG, "无法创建错误日志目录");
                return;
            }
            
            // 生成日志文件名（使用当前日期）
            String fileName = generateLogFileName();
            File logFile = new File(errorDir, fileName);
            
            // 写入错误信息
            writeExceptionToFile(logFile, throwable);
            
            Log.i(TAG, "异常信息已保存到: " + logFile.getAbsolutePath());
            
        } catch (Exception e) {
            Log.e(TAG, "保存异常信息失败", e);
        }
    }
    
    /**
     * 创建错误日志目录
     * @return 创建的目录文件对象，失败返回null
     */
    private File createErrorDirectory() {
        try {
            // 获取外部存储根目录
            File externalStorage = Environment.getExternalStorageDirectory();
            File errorDir = new File(externalStorage, LOG_DIR);
            
            if (!errorDir.exists()) {
                boolean created = errorDir.mkdirs();
                if (!created) {
                    Log.e(TAG, "创建错误日志目录失败: " + errorDir.getAbsolutePath());
                    return null;
                }
            }
            
            return errorDir;
        } catch (Exception e) {
            Log.e(TAG, "创建错误日志目录时发生异常", e);
            return null;
        }
    }
    
    /**
     * 生成日志文件名（格式：error_yyyyMMdd.log）
     * @return 文件名
     */
    private String generateLogFileName() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd", Locale.getDefault());
        String dateStr = sdf.format(new Date());
        return "error_" + dateStr + ".log";
    }
    
    /**
     * 将异常信息写入文件
     * @param logFile 日志文件
     * @param throwable 异常对象
     */
    private void writeExceptionToFile(File logFile, Throwable throwable) {
        FileWriter fileWriter = null;
        PrintWriter printWriter = null;
        
        try {
            // 追加模式打开文件
            fileWriter = new FileWriter(logFile, true);
            printWriter = new PrintWriter(fileWriter);
            
            // 写入时间戳
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
            String timestamp = sdf.format(new Date());
            printWriter.println("========================================");
            printWriter.println("异常时间: " + timestamp);
            printWriter.println("线程名称: " + Thread.currentThread().getName());
            printWriter.println("========================================");
            
            // 写入异常堆栈信息
            StringWriter stringWriter = new StringWriter();
            PrintWriter stackTraceWriter = new PrintWriter(stringWriter);
            throwable.printStackTrace(stackTraceWriter);
            printWriter.println(stringWriter);
            
            printWriter.println(); // 空行分隔
            printWriter.flush();
            
        } catch (IOException e) {
            Log.e(TAG, "写入异常信息到文件失败", e);
        } finally {
            // 关闭资源
            if (printWriter != null) {
                printWriter.close();
            }
            if (fileWriter != null) {
                try {
                    fileWriter.close();
                } catch (IOException e) {
                    Log.e(TAG, "关闭文件写入流失败", e);
                }
            }
        }
    }
    
//    /**
//     * 手动记录异常（用于try-catch块中主动记录异常）
//     * @param context 上下文
//     * @param throwable 异常对象
//     * @param extraInfo 额外信息
//     */
//    public static void logException(Context context, Throwable throwable, String extraInfo) {
//        GlobalExceptionHandler handler = new GlobalExceptionHandler(context);
//        handler.saveExceptionWithExtraInfo(throwable, extraInfo);
//    }
//
//    /**
//     * 保存异常信息到文件（带额外信息）
//     * @param throwable 异常对象
//     * @param extraInfo 额外信息
//     */
//    private void saveExceptionWithExtraInfo(Throwable throwable, String extraInfo) {
//        try {
//            File errorDir = createErrorDirectory();
//            if (errorDir == null) {
//                return;
//            }
//
//            String fileName = generateLogFileName();
//            File logFile = new File(errorDir, fileName);
//
//            FileWriter fileWriter = new FileWriter(logFile, true);
//            PrintWriter printWriter = new PrintWriter(fileWriter);
//
//            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
//            String timestamp = sdf.format(new Date());
//
//            printWriter.println("========================================");
//            printWriter.println("异常时间: " + timestamp);
//            printWriter.println("异常类型: 手动记录");
//            if (extraInfo != null && !extraInfo.isEmpty()) {
//                printWriter.println("额外信息: " + extraInfo);
//            }
//            printWriter.println("========================================");
//
//            StringWriter stringWriter = new StringWriter();
//            PrintWriter stackTraceWriter = new PrintWriter(stringWriter);
//            throwable.printStackTrace(stackTraceWriter);
//            printWriter.println(stringWriter.toString());
//
//            printWriter.println();
//            printWriter.flush();
//            printWriter.close();
//            fileWriter.close();
//
//            Log.i(TAG, "手动记录的异常信息已保存到: " + logFile.getAbsolutePath());
//
//        } catch (Exception e) {
//            Log.e(TAG, "手动记录异常信息失败", e);
//        }
//    }
} 
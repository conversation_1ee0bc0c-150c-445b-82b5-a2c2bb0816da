<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context="io.dcloud.uniplugin.ProfileActivity">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="基本信息"
                android:textColor="#333333"
                android:textSize="18sp"
                android:textStyle="bold" />

<!--            <LinearLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginTop="16dp"-->
<!--                android:orientation="horizontal">-->

<!--                <TextView-->
<!--                    android:layout_width="80dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="用户名："-->
<!--                    android:textColor="#666666" />-->

<!--                <TextView-->
<!--                    android:id="@+id/textViewUsername"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="未知用户"-->
<!--                    android:textColor="#333333" />-->
<!--            </LinearLayout>-->

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="姓名："
                    android:textColor="#666666" />

                <TextView
                    android:id="@+id/textViewName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="未知"
                    android:textColor="#333333" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="角色："
                    android:textColor="#666666" />

                <TextView
                    android:id="@+id/textViewRole"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="未知角色"
                    android:textColor="#333333" />
            </LinearLayout>

<!--            <LinearLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginTop="8dp"-->
<!--                android:orientation="horizontal">-->

<!--                <TextView-->
<!--                    android:layout_width="80dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="手机号："-->
<!--                    android:textColor="#666666" />-->

<!--                <TextView-->
<!--                    android:id="@+id/textViewMobile"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="未知"-->
<!--                    android:textColor="#333333" />-->
<!--            </LinearLayout>-->
        </LinearLayout>
    </androidx.cardview.widget.CardView>


    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="组织信息"
                android:textColor="#333333"
                android:textSize="18sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="组织名称："
                    android:textColor="#666666" />

                <TextView
                    android:id="@+id/textViewOrgName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="未知"
                    android:textColor="#333333" />
            </LinearLayout>

<!--            <LinearLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginTop="8dp"-->
<!--                android:orientation="horizontal">-->

<!--                <TextView-->
<!--                    android:layout_width="80dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="组织ID："-->
<!--                    android:textColor="#666666" />-->

<!--                <TextView-->
<!--                    android:id="@+id/textViewOrgId"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="未知"-->
<!--                    android:textColor="#333333" />-->
<!--            </LinearLayout>-->

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="85dp"
                    android:layout_height="wrap_content"
                    android:text="归宿地代码："
                    android:textColor="#666666" />

                <TextView
                    android:id="@+id/textViewGsddm"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="未知"
                    android:textColor="#333333" />
            </LinearLayout>

<!--            <LinearLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginTop="8dp"-->
<!--                android:orientation="horizontal">-->

<!--                <TextView-->
<!--                    android:layout_width="80dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="所属地区："-->
<!--                    android:textColor="#666666" />-->

<!--                <TextView-->
<!--                    android:id="@+id/textViewGsdmc"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="未知"-->
<!--                    android:textColor="#333333" />-->
<!--            </LinearLayout>-->
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <Button
        android:id="@+id/buttonLogout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@android:color/holo_red_light"
        android:text="退出登录"
        android:textColor="@android:color/white" />

</LinearLayout>
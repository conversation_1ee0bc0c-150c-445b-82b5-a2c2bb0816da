<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout 
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="添加样品"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp"/>

        <!-- 样品类型选择 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="样品类型 *"
            android:textSize="14sp"
            android:layout_marginBottom="8dp" />

        <Spinner
            android:id="@+id/spinnerSampleType"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:padding="12dp"
            android:background="@android:drawable/editbox_background" />

        <!-- 样品重量输入 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="样品重量(g) *"
            android:textSize="14sp"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/editTextSampleWeight"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="请输入样品重量"
            android:inputType="numberDecimal"
            android:padding="12dp"
            android:background="@android:drawable/editbox_background" />

        <!-- 标识码输入 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="标识码"
            android:textSize="14sp"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/editTextBsm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="请输入标识码"
            android:padding="12dp"
            android:background="@android:drawable/editbox_background" />

        <!-- 样品编号输入 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="样品编号 *"
            android:textSize="14sp"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/editTextSampleCode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="请输入样品编号"
            android:padding="12dp"
            android:background="@android:drawable/editbox_background" />

        <!-- 按钮区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <Button
                android:id="@+id/btnCancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="取消"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/btnConfirm"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="确定" />
        </LinearLayout>
    </LinearLayout>
</ScrollView> 
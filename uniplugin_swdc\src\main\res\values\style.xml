<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!--    地图上的按钮-->
    <style name="map_btn">
        <item name="layout_constraintLeft_toLeftOf">parent</item>
        <item name="layout_constraintTop_toTopOf">parent</item>
        <item name="android:layout_width">40dp</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:padding">5dp</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:radius">10dp</item>
        <item name="android:background">@color/white</item>
    </style>
    <!--    量测计算中的按钮-->
    <style name="measurement_btn">
        <item name="android:background">@drawable/button_bar_background</item>
        <item name="android:layout_alignParentTop">true</item>
        <item name="android:layout_height">30dp</item>
        <item name="android:layout_width">30dp</item>
        <item name="android:padding">5dp</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="layout_constraintBottom_toBottomOf">parent</item>
        <item name="layout_constraintHorizontal_bias">0.5</item>
    </style>
    <style name="color_btn">
        <item name="layout_constraintLeft_toLeftOf">parent</item>
        <item name="layout_constraintTop_toTopOf">parent</item>
        <item name="android:layout_width">40dp</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:padding">5dp</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:radius">10dp</item>
        <item name="android:background">@color/white</item>
    </style>
</resources>
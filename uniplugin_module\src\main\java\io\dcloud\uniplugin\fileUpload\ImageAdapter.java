package io.dcloud.uniplugin.fileUpload;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.provider.MediaStore;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import uni.dcloud.io.uniplugin_module.R;

public class ImageAdapter extends RecyclerView.Adapter<ImageAdapter.ImageViewHolder> {

    private List<FileItem> fileItems;
    private Context context;
    private OnItemDeleteListener onItemDeleteListener;
    private OnItemClickListener onItemClickListener;
    private boolean showDeleteButton = true;

    public ImageAdapter(Context context) {
        this.context = context;
        this.fileItems = new ArrayList<>();
    }

    public void setOnItemDeleteListener(OnItemDeleteListener listener) {
        this.onItemDeleteListener = listener;
    }
    
    public void setOnItemClickListener(OnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    public void setShowDeleteButton(boolean show) {
        this.showDeleteButton = show;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ImageViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_image, parent, false);
        return new ImageViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ImageViewHolder holder, int position) {
        FileItem fileItem = fileItems.get(position);
        
        // 设置文件名
        holder.textFileName.setText(fileItem.getFileName());
        
        // 如果是图片，显示预览
        if (fileItem.isImage()) {
            try {
                if (fileItem.getUri() != null) {
                    Bitmap bitmap = MediaStore.Images.Media.getBitmap(context.getContentResolver(), fileItem.getUri());
                    holder.imagePreview.setImageBitmap(bitmap);
                    holder.imagePreview.setVisibility(View.VISIBLE);
                } else if (fileItem.getFilePath() != null) {
                    File imgFile = new File(fileItem.getFilePath());
                    if (imgFile.exists()) {
                        // 获取图片的原始尺寸
                        BitmapFactory.Options options = new BitmapFactory.Options();
                        options.inJustDecodeBounds = true;
                        BitmapFactory.decodeFile(fileItem.getFilePath(), options);
                        
                        // 计算压缩比例
                        int targetWidth = 300; // 目标宽度
                        int scale = 1;
                        if (options.outWidth > targetWidth) {
                            scale = Math.round((float) options.outWidth / targetWidth);
                        }
                        
                        // 加载压缩后的图片
                        options.inJustDecodeBounds = false;
                        options.inSampleSize = scale;
                        
                        Bitmap bitmap = BitmapFactory.decodeFile(fileItem.getFilePath(), options);
                        if (bitmap != null) {
                            holder.imagePreview.setImageBitmap(bitmap);
                            holder.imagePreview.setVisibility(View.VISIBLE);
                            Log.d("ImageAdapter", "成功加载图片: " + fileItem.getFilePath() + 
                                " 原始尺寸: " + options.outWidth + "x" + options.outHeight + 
                                " 压缩比例: " + scale);
                        } else {
                            Log.e("ImageAdapter", "无法解码图片: " + fileItem.getFilePath());
                            holder.imagePreview.setVisibility(View.GONE);
                            Toast.makeText(context, "无法加载图片: " + fileItem.getFileName(), Toast.LENGTH_SHORT).show();
                        }
                    } else {
                        Log.e("ImageAdapter", "图片文件不存在: " + fileItem.getFilePath());
                        holder.imagePreview.setVisibility(View.GONE);
                        Toast.makeText(context, "图片文件不存在: " + fileItem.getFileName(), Toast.LENGTH_SHORT).show();
                    }
                }
            } catch (Exception e) {
                Log.e("ImageAdapter", "加载图片出错: " + e.getMessage());
                e.printStackTrace();
                holder.imagePreview.setVisibility(View.GONE);
                Toast.makeText(context, "加载图片出错: " + fileItem.getFileName(), Toast.LENGTH_SHORT).show();
            }
            
            // 设置图片点击事件，点击查看大图
            holder.imagePreview.setOnClickListener(v -> {
                int adapterPosition = holder.getAdapterPosition();
                if (adapterPosition != RecyclerView.NO_POSITION) {
                    FileItem item = fileItems.get(adapterPosition);
                    if (item.isImage()) {
                        // 打开图片预览Activity
                        Intent intent = new Intent(context, ImagePreviewActivity.class);
                        if (item.getFilePath() != null) {
                            intent.putExtra("imagePath", item.getFilePath());
                        } else if (item.getUri() != null) {
                            intent.putExtra("imageUri", item.getUri().toString());
                        }
                        context.startActivity(intent);
                    }
                }
            });
        } else {
            holder.imagePreview.setVisibility(View.GONE);
        }
        
        // 设置删除按钮可见性
        holder.buttonDelete.setVisibility(showDeleteButton ? View.VISIBLE : View.GONE);
        
        // 设置删除按钮点击事件
        holder.buttonDelete.setOnClickListener(v -> {
            int adapterPosition = holder.getAdapterPosition();
            if (adapterPosition != RecyclerView.NO_POSITION && onItemDeleteListener != null) {
                onItemDeleteListener.onItemDelete(adapterPosition);
            }
        });
        
        // 设置整个项的点击事件
        holder.itemView.setOnClickListener(v -> {
            int adapterPosition = holder.getAdapterPosition();
            if (adapterPosition != RecyclerView.NO_POSITION && onItemClickListener != null) {
                onItemClickListener.onItemClick(adapterPosition);
            }
        });
    }

    @Override
    public int getItemCount() {
        return fileItems.size();
    }

    public void addItem(FileItem item) {
        fileItems.add(item);
        notifyItemInserted(fileItems.size() - 1);
    }
    
    /**
     * 添加一个图片文件路径
     * @param filePath 文件路径
     */
    public void addItem(String filePath) {
        FileItem item = new FileItem(filePath, true, false); // 假设这是图片文件
        addItem(item);
    }

    /**
     * 添加一个FieldFile对象
     * @param fieldFile 字段文件对象
     */
    public void addItem(io.dcloud.uniplugin.form.field.FieldFile fieldFile) {
        if (fieldFile == null) return;
        
        String filePath = fieldFile.getPath();
        Uri uri = fieldFile.getUri();
        String fileType = fieldFile.getType();
        
        // 增强类型判断逻辑
        boolean isImage = "image".equals(fileType);
        boolean isVideo = "video".equals(fileType);
        
        // 如果类型未明确，基于文件扩展名进行猜测
        if (!isImage && !isVideo && filePath != null) {
            String lowerPath = filePath.toLowerCase();
            if (lowerPath.endsWith(".jpg") || lowerPath.endsWith(".jpeg") || 
                lowerPath.endsWith(".png") || lowerPath.endsWith(".gif")) {
                isImage = true;
                Log.d("ImageAdapter", "基于扩展名推断为图片: " + filePath);
            } else if (lowerPath.endsWith(".mp4") || lowerPath.endsWith(".3gp") || 
                       lowerPath.endsWith(".mov") || lowerPath.endsWith(".avi")) {
                isVideo = true;
                Log.d("ImageAdapter", "基于扩展名推断为视频: " + filePath);
            }
        }
        
        Log.d("ImageAdapter", "添加文件: 路径=" + filePath + 
              ", 类型=" + fileType + 
              ", 是图片=" + isImage + 
              ", 是视频=" + isVideo);
        
        if (filePath != null) {
            FileItem item = new FileItem(filePath, isImage, isVideo);
            Log.d("ImageAdapter", "添加FileItem: 文件路径=" + filePath + 
                  ", 是图片=" + item.isImage() + 
                  ", 是视频=" + item.isVideo());
            addItem(item);
        } else if (uri != null) {
            FileItem item = new FileItem(uri, isImage, isVideo);
            Log.d("ImageAdapter", "添加FileItem: URI=" + uri + 
                  ", 是图片=" + item.isImage() + 
                  ", 是视频=" + item.isVideo());
            addItem(item);
        }
    }

    public void removeItem(int position) {
        if (position >= 0 && position < fileItems.size()) {
            fileItems.remove(position);
            notifyItemRemoved(position);
        }
    }

    /**
     * 清空所有项目
     */
    public void clearItems() {
        fileItems.clear();
        notifyDataSetChanged();
    }

    /**
     * 获取所有文件项目
     */
    public List<FileItem> getFileItems() {
        return fileItems;
    }

    public static class ImageViewHolder extends RecyclerView.ViewHolder {
        ImageView imagePreview;
        TextView textFileName;
        ImageButton buttonDelete;

        public ImageViewHolder(@NonNull View itemView) {
            super(itemView);
            imagePreview = itemView.findViewById(R.id.imagePreview);
            textFileName = itemView.findViewById(R.id.textFileName);
            buttonDelete = itemView.findViewById(R.id.buttonDelete);
        }
    }

    public interface OnItemDeleteListener {
        void onItemDelete(int position);
    }
    
    public interface OnItemClickListener {
        void onItemClick(int position);
    }

    public static class FileItem {
        private String filePath;
        private Uri uri;
        private boolean isImage;
        private boolean isVideo;
        private String fileName;

        public FileItem(String filePath, boolean isImage, boolean isVideo) {
            this.filePath = filePath;
            this.isImage = isImage;
            this.isVideo = isVideo;
            this.fileName = new File(filePath).getName();
        }

        public FileItem(Uri uri, boolean isImage, boolean isVideo) {
            this.uri = uri;
            this.isImage = isImage;
            this.isVideo = isVideo;
            this.fileName = uri.getLastPathSegment();
        }

        public String getFilePath() {
            return filePath;
        }

        public Uri getUri() {
            return uri;
        }

        public boolean isImage() {
            return isImage;
        }

        public boolean isVideo() {
            return isVideo;
        }

        public String getFileName() {
            return fileName;
        }
    }
} 
package io.dcloud.uniplugin.utils;

import android.content.Context;
import android.os.Build;
import android.os.Environment;
import android.util.Log;
import android.widget.Toast;

import java.io.File;

/**
 * 文件工具类，用于创建应用所需的文件夹结构
 */
public class FileUtils {
    private static final String TAG = "FileUtils";

    /**
     * 创建应用所需的文件夹结构
     * @param context 上下文
     */
    public static void createAppDirectories(Context context) {
        createSDCardDir(context);
    }

    /**
     * 创建SD卡目录
     */
    private static void createSDCardDir(Context context) {
        String folderName = "BCGDGISData"; // 要创建的文件夹名称

        Log.d(TAG, "路径测试: " + getSDPath(context) + File.separator + folderName);
        File folder = new File(getSDPath(context) + File.separator + folderName); // 创建文件夹对象
        if (!folder.exists()) { // 如果文件夹不存在，则创建它
            folder.mkdirs();
        }
        
        if (!folder.exists()) {
            if (folder.mkdirs()) {
                createOtherDir();
                Toast.makeText(context, "创建文件成功", Toast.LENGTH_SHORT).show();
            }
        } else {
            createOtherDir();
            Toast.makeText(context, "文件已存在！", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 创建其他目录
     */
    private static void createOtherDir() {
        File root = Environment.getExternalStorageDirectory();
        File BCGDGISDataDir = new File(root + "/BCGDGISData");

        if (BCGDGISDataDir.exists()) {
            // 如果BCGDGISData文件夹存在，创建BCGDSqliteData和TRSPCameraData文件夹
            File bcgdSqliteDataDir = new File(BCGDGISDataDir + "/BCGDSqliteData");
            File trsCameraDataDir = new File(BCGDGISDataDir + "/BCGDCameraData");

            if (!bcgdSqliteDataDir.exists()) {
                boolean mkdirSqlite = bcgdSqliteDataDir.mkdirs();
                if (!mkdirSqlite) {
                    Log.e(TAG, "创建BCGDSqliteData文件夹失败！");
                }
            }

            if (!trsCameraDataDir.exists()) {
                boolean mkdirCamera = trsCameraDataDir.mkdirs();
                if (!mkdirCamera) {
                    Log.e(TAG, "创建TRSPCameraData文件夹失败！");
                }
            }
        } else {
            Log.e(TAG, "BCGDGISData文件夹不存在！");
        }
    }

    /**
     * 获取SD卡路径
     */
    public static String getSDPath(Context context) {
        File sdDir = null;
        boolean sdCardExist = Environment.getExternalStorageState().equals(
                Environment.MEDIA_MOUNTED);// 判断sd卡是否存在
        if (sdCardExist) {
            if (Build.VERSION.SDK_INT >= 29) {
                //Android10之后
                File[] externalDirs = context.getExternalFilesDirs(null);
                if (externalDirs.length > 0) {
                    String rootPath = externalDirs[0].getAbsolutePath();
                    rootPath = rootPath.substring(0, rootPath.indexOf("/Android/"));
                    sdDir = new File(rootPath);
                }
            } else {
                sdDir = Environment.getExternalStorageDirectory();// 获取SD卡根目录
            }
            return sdDir.toString();
        } else {
            sdDir = Environment.getRootDirectory();// 获取根目录
        }
        return sdDir.toString();
    }
} 
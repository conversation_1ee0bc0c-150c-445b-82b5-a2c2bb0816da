package io.dcloud.uniplugin.model;

import java.util.Date;

/**
 * 样品模型
 */
public class YplzSample {
    private Long id;
    private String sampleName;
    private String sampleType;
    private Integer quantity;
    private String remark;
    private Long batchId;
    private Long sampleState;
    private Date createTime;
    private Date updateTime;
    private Long receiveOrganizationId; // 接样单位ID（检测机构ID）

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSampleName() {
        return sampleName;
    }

    public void setSampleName(String sampleName) {
        this.sampleName = sampleName;
    }

    public String getSampleType() {
        return sampleType;
    }

    public void setSampleType(String sampleType) {
        this.sampleType = sampleType;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getBatchId() {
        return batchId;
    }

    public void setBatchId(Long batchId) {
        this.batchId = batchId;
    }

    public Long getSampleState() {
        return sampleState;
    }

    public void setSampleState(Long sampleState) {
        this.sampleState = sampleState;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getReceiveOrganizationId() {
        return receiveOrganizationId;
    }

    public void setReceiveOrganizationId(Long receiveOrganizationId) {
        this.receiveOrganizationId = receiveOrganizationId;
    }

    @Override
    public String toString() {
        return "YplzSample{" +
                "id=" + id +
                ", sampleName='" + sampleName + '\'' +
                ", sampleType='" + sampleType + '\'' +
                ", quantity=" + quantity +
                ", remark='" + remark + '\'' +
                ", batchId=" + batchId +
                ", sampleState=" + sampleState +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", receiveOrganizationId=" + receiveOrganizationId +
                '}';
    }
} 
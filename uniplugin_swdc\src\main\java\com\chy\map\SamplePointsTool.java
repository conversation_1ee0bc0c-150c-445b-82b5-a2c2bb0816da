package com.chy.map;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.util.Log;
import android.view.MotionEvent;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.esri.arcgisruntime.concurrent.ListenableFuture;
import com.esri.arcgisruntime.geometry.Point;
import com.esri.arcgisruntime.mapping.Viewpoint;
import com.esri.arcgisruntime.mapping.view.DefaultMapViewOnTouchListener;
import com.esri.arcgisruntime.mapping.view.Graphic;
import com.esri.arcgisruntime.mapping.view.GraphicsOverlay;
import com.esri.arcgisruntime.mapping.view.IdentifyGraphicsOverlayResult;
import com.esri.arcgisruntime.mapping.view.MapView;
import com.esri.arcgisruntime.symbology.SimpleMarkerSymbol;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.concurrent.ExecutionException;

public class SamplePointsTool {
    private MapView mapView;
    private Context context;
    private Activity activity;
    private SwdcModule  swdcModule=new SwdcModule();
    private Point location_point;

    private int clickstate = 0;
    private String clickcode = "";
    private String type = "";

    private GraphicsOverlay pointGraphicsOverlay = new GraphicsOverlay();

    public SamplePointsTool(MapView mapView, Context context, Activity activity,String type) {
        this.mapView = mapView;
        this.context = context;
        this.activity = activity;
        this.type = type;
        mapView.getGraphicsOverlays().add(pointGraphicsOverlay);
    }

    public void addPoints(JSONArray points) {
//        pointGraphicsOverlay = new GraphicsOverlay();

        // 正确的写法
        for (int i = 0; i < points.size(); i++) {
            //获取jsonObject
            JSONObject jsonObject = points.getJSONObject(i);
            String lon1 = jsonObject.getString("lon");
            String lat1 = jsonObject.getString("lat");
            //点击事件弹窗所需的属性
            String code_1 = jsonObject.getString("code");
            //String sampleType_1 = jsonObject.getString("sampleType");
            //String samplingType_1 = jsonObject.getString("samplingType");
            String location_1 = jsonObject.getString("location");

            Double lon_d = Double.parseDouble(lon1);
            Double lat_d = Double.parseDouble(lat1);
            Point location_point = new Point(lon_d, lat_d);
            //获取状态以控制颜色,3为未调查黄色，其余为已调查红色
            String cygczt = jsonObject.getString("cygczt");

            SimpleMarkerSymbol simpleMarkerSymbol1;
            switch (cygczt) {
                case "0":
                    simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.YELLOW, 18);
                    break;
                case "1":
                    simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.GREEN, 18);
                    break;
                default:
                    simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.RED, 18);


            }
//            if (Objects.equals(sfsjptzg, "1") || Objects.equals(sfsjptzg, "-1")) {
//                simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.RED, 18);
//            } else {
//                if (Objects.equals(cygc, "1")) {
//                    simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.GREEN, 18);
//                } else {
//                    System.out.println(code_1);
//                    System.out.println(current_point);
//                    if (Objects.equals(code_1, current_point)) {
//                        simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.BLUE, 18);
//                        DrawGeometryTool drawGeometryTool = new DrawGeometryTool(context, mapView);
//                        System.out.println("执行了addPoint_plus1...");
//                        System.out.println("信息采集获取当前点的类型" + sampleType_1);
//                        System.out.println(sampleType_1);
//                        if (sampleType_1.equals("表层样品")) {
//                            System.out.println("信息采集画表层样品点");
//                            drawGeometryTool.getCircle(mapView, location_point, 0.002);
//                        } else {
//                            System.out.println("信息采集画剖面样品点");
//                            drawGeometryTool.getCircle(mapView, location_point, 0.02);
//                        }
//
//                        Viewpoint viewpoint = new Viewpoint(location_point, 50000);
//                        mapView.setViewpointCenterAsync(location_point, 50000);
//
//                    } else {
//                        simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.YELLOW, 18);
//                    }
//
//                }
//            }
            //将传递的pointlist的数据传递给attributes
            final Map<String, Object> attributes = new TreeMap<>();
            attributes.put("code", code_1);
            attributes.put("location", location_1);
            //创建Graphic
            Graphic graphic1 = new Graphic(location_point, attributes, simpleMarkerSymbol1);

            //print一下attr
            System.out.println(graphic1.getAttributes());
            //不能清除上一个点.......
            //pointGraphicsOverlay.getGraphics().clear();

            pointGraphicsOverlay.getGraphics().add(graphic1);
        }
        click_done();
        points.clear();
    }

    public void addOnePoints(JSONArray points, String point) {
//        pointGraphicsOverlay = new GraphicsOverlay();
//        mapView.getGraphicsOverlays().add(pointGraphicsOverlay);
        // 正确的写法
        for (int i = 0; i < points.size(); i++) {
            //获取jsonObject
            JSONObject jsonObject = points.getJSONObject(i);
            String lon1 = jsonObject.getString("lon");
            String lat1 = jsonObject.getString("lat");
            //点击事件弹窗所需的属性
            String code_1 = jsonObject.getString("code");
            //String sampleType_1 = jsonObject.getString("sampleType");
            //String samplingType_1 = jsonObject.getString("samplingType");
            String location_1 = jsonObject.getString("location");
            Double lon_d = Double.parseDouble(lon1);
            Double lat_d = Double.parseDouble(lat1);
            Point location_point = new Point(lon_d, lat_d);
            //获取状态以控制颜色,3为未调查黄色，其余为已调查红色
            String cygczt = jsonObject.getString("cygczt");
            SimpleMarkerSymbol simpleMarkerSymbol1;
            if (Objects.equals(cygczt, "-1") || Objects.equals(cygczt, "-2")) {
                simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.RED, 18);
            } else if (Objects.equals(cygczt, "1")) {
                simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.GREEN, 18);
            } else {
                if (Objects.equals(code_1, point)) {
                    simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.BLUE, 18);
                    DrawGeometryTool drawGeometryTool = new DrawGeometryTool(this.context, mapView);
                    drawGeometryTool.getCircle(mapView, location_point, 0.002);
                    mapView.setViewpointCenterAsync(location_point, 9017.8708819198619);
                }
                else {
                    simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.YELLOW, 18);
                }
            }



            //将传递的pointlist的数据传递给attributes
            final Map<String, Object> attributes = new TreeMap<>();
            attributes.put("code", code_1);
            attributes.put("location", location_1);
            attributes.put("cygczt", cygczt);
            //创建Graphic
            Graphic graphic1 = new Graphic(location_point, attributes, simpleMarkerSymbol1);

            //print一下attr
            System.out.println(graphic1.getAttributes());
            //不能清除上一个点.......
            //pointGraphicsOverlay.getGraphics().clear();

            pointGraphicsOverlay.getGraphics().add(graphic1);

        }


        points.clear();
    }
    @SuppressLint("ClickableViewAccessibility")
    public void click_done() {
        mapView.setOnTouchListener(new DefaultMapViewOnTouchListener(context, mapView) {
            @Override
            public boolean onSingleTapConfirmed(MotionEvent v) {
                // 获取用户点击的屏幕点
                int lon = 0;
                if (lon == 0) {
                    android.graphics.Point screenPoint = new android.graphics.Point((int) v.getX(), (int) v.getY());
                    // 识别GraphicsOverlay中的Graphic
                    Log.i("点击测试","获取到屏幕坐标"+(int) v.getX()+"," +(int) v.getY());
                    Log.i("点击测试","获取到屏幕坐标"+(pointGraphicsOverlay.getGraphics().size()));
                    final ListenableFuture<IdentifyGraphicsOverlayResult> identifyGraphic = mMapView.identifyGraphicsOverlayAsync(pointGraphicsOverlay, screenPoint, 10.0, false);
                    Log.i("点击测试","获取到要素:");
                    identifyGraphic.addDoneListener(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                Log.i("点击测试","进入运行");
                                IdentifyGraphicsOverlayResult grOverlayResult = identifyGraphic.get();
                                // 获取识别的GraphicOverlay返回的图形列表
                                List<Graphic> graphic = grOverlayResult.getGraphics();
                                Log.i("点击测试","获取图形:"+graphic.size());
                                if (!graphic.isEmpty()) {
                                    Log.i("点击测试","获取到符号不为空");
                                    Graphic chosen_graphic = grOverlayResult.getGraphics().get(0);
                                    System.out.println(chosen_graphic.getAttributes());
                                    //分别获取数值，并打开弹窗
                                    Map<String, Object> attributes = chosen_graphic.getAttributes();
                                    //String sampleType1 = attributes.get("sampleType").toString();
                                    String code_1 = attributes.get("code").toString();

//                                    String sampleType_1 = attributes.get("sampleType").toString();
//                                    String samplingType_1 = attributes.get("samplingType").toString();
//                                    String location = attributes.get("location").toString();
//                                    showMultiBtnDialog(code_1, sampleType_1, samplingType_1, location);
                                    //String cygc_1 = attributes.get("cygc").toString();
                                    //String sfsjptzg_1 = attributes.get("sfsjptzg").toString();
                                    //String cygc = attributes.get("cygc").toString();
//                                    if (Objects.equals(cygc_1, "1") && !checkNet(context)) {
//                                        Toast.makeText(context, "离线模式下不支持查看已上报样点信息", Toast.LENGTH_LONG).show();
//                                        return;
//                                    }
                                    Point location_point = (Point) chosen_graphic.getGeometry();
                                    DrawGeometryTool drawGeometryTool = new DrawGeometryTool(context, mapView);

                                    System.out.println("执行了open_attr...");
                                    System.out.println("用户点击屏幕监听");
                                    // 不一定点击到样点，此时类型为null，需做处理
//                                    System.out.println("点击获取当前点的类型" + sampleType1);
//                                    System.out.println(sampleType1);
//
//                                    if (sampleType1.equals("表层样品")) {
//                                        System.out.println("点击画表层样品点");
//                                        drawGeometryTool.getCircle(mapView, location_point, 0.002);
//                                    } else {
                                        System.out.println("点击画剖面样品点");
                                        drawGeometryTool.getCircle(mapView, location_point, 0.002);
                                    //}

                                    Viewpoint viewpoint = new Viewpoint(location_point, 50000);
                                    mapView.setViewpointCenterAsync(location_point, 50000);

                                    //Log.d("条件判断", clickcode + "");
                                    //Log.d("条件判断", (clickstate == 2 && clickcode == code_1) + "");
                                    if (clickcode == code_1) {
                                        Log.e("跳转编码确认", code_1);
                                        Intent intent = new Intent();
                                        Log.d("code_1是否有值", code_1);
                                        intent.putExtra("code", code_1);
                                        //Toast.makeText(MainActivity.this, "作业底图跳转前"+code_1, Toast.LENGTH_LONG).show();
                                        //TestModule.hasResult=false;
                                        //Log.d("offModel", offModel);
                                        //Log.d("offModel判断结果", Objects.equals(offModel, "0") + "");
                                        //if (Objects.equals(offModel, "0")) {
                                        //    activity.setResult(TestModule.REQUEST_CODE_LON_ZERO, intent);
                                        //}
                                        //if (Objects.equals(offModel, "1")) {
                                            activity.setResult(swdcModule.REQUEST_CODE_NTU, intent);
                                        //}
                                        activity.finish();
                                        clickstate = 0;
                                    } else {
                                        clickcode = code_1;
                                    }
                                    //  }
                                }
//                                // 获取结果列表的大小
//                                int identifyResultSize = graphic.size();
//
//                                    // 当识别的图形成功返回时，返回一个Toast信息提示
//                                    Toast.makeText(getApplicationContext(), "Tapped on " + identifyResultSize + " Graphic", Toast.LENGTH_SHORT).show();
//                                }
                            } catch (InterruptedException | ExecutionException ie) {
                                ie.printStackTrace();
                            }
                        }
                    });
                }
                //突出点
                else if (lon == 1) {
                    android.graphics.Point screenPoint = new android.graphics.Point((int) v.getX(), (int) v.getY());
                    // 识别GraphicsOverlay中的Graphic
                    final ListenableFuture<IdentifyGraphicsOverlayResult> identifyGraphic = mMapView.identifyGraphicsOverlayAsync(pointGraphicsOverlay, screenPoint, 10.0, false, 2);
                    identifyGraphic.addDoneListener(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                IdentifyGraphicsOverlayResult grOverlayResult = identifyGraphic.get();
                                // 获取识别的GraphicOverlay返回的图形列表
                                List<Graphic> graphic = grOverlayResult.getGraphics();
                                if (!graphic.isEmpty()) {
                                    Graphic chosen_graphic = grOverlayResult.getGraphics().get(0);
                                    System.out.println(chosen_graphic.getAttributes());
                                    //分别获取数值，并打开弹窗
                                    Map<String, Object> attributes = chosen_graphic.getAttributes();
                                    String code_1 = attributes.get("code").toString();
                                    String cygc_1 = attributes.get("cygc").toString();
                                    String sfsjptzg = attributes.get("sfsjptzg").toString();
                                    System.out.println("xxxx" + cygc_1);
                                    System.out.println("yyyy" + sfsjptzg);
                                    Intent intent = new Intent();
                                    intent.putExtra("code", code_1);

                                }
                            } catch (InterruptedException | ExecutionException ie) {
                                ie.printStackTrace();
                            }
                        }
                    });
                } else {
                    android.graphics.Point screenPoint = new android.graphics.Point((int) v.getX(), (int) v.getY());
                    // 识别GraphicsOverlay中的Graphic
                    final ListenableFuture<IdentifyGraphicsOverlayResult> identifyGraphic = mMapView.identifyGraphicsOverlayAsync(pointGraphicsOverlay, screenPoint, 10.0, false, 2);
                    identifyGraphic.addDoneListener(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                IdentifyGraphicsOverlayResult grOverlayResult = identifyGraphic.get();
                                // 获取识别的GraphicOverlay返回的图形列表
                                List<Graphic> graphic = grOverlayResult.getGraphics();
                                if (!graphic.isEmpty()) {
                                    Graphic chosen_graphic = grOverlayResult.getGraphics().get(0);
                                    System.out.println(chosen_graphic.getAttributes());
                                    //分别获取数值，并打开弹窗
                                    Map<String, Object> attributes = chosen_graphic.getAttributes();
                                    Intent intent = new Intent();
                                    //intent.putExtra("code", code);
                                    //activity.setResult(TestModule.REQUEST_CODE_LON_ELSE, intent);

                                    activity.finish();


                                }
                            } catch (InterruptedException | ExecutionException ie) {
                                ie.printStackTrace();
                            }
                        }
                    });
                }
                return super.onSingleTapConfirmed(v);

            }

        });
    }
    public  boolean checkNet(Context context) {

        try {
            ConnectivityManager connectivity = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            if (connectivity != null) {

                NetworkInfo info = connectivity.getActiveNetworkInfo();
                if (info != null && info.isConnected()) {

                    if (info.getState() == NetworkInfo.State.CONNECTED) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }
}

package io.dcloud.uniplugin.form.processor;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;

import java.util.ArrayList;
import java.util.List;

import io.dcloud.uniplugin.form.DropdownProcessor;
import io.dcloud.uniplugin.model.FormFieldConfig;

/**
 * 下拉框字段处理器
 * 负责处理和渲染下拉框字段
 */
public class DropdownFieldProcessor {
    private static final String TAG = "DropdownFieldProcessor";

    /**
     * 创建下拉选择框
     * 
     * @param context 上下文
     * @param fieldConfig 字段配置
     * @return 创建的下拉框视图
     */
    public static View createDropdownField(Context context, FormFieldConfig fieldConfig) {
        // 创建一个容器来放置Spinner和可能的"其他"输入框
        LinearLayout container = new LinearLayout(context);
        container.setOrientation(LinearLayout.VERTICAL);
        
        Spinner spinner = new Spinner(context);
        
        // 获取选项列表
        List<FormFieldConfig.OptionItem> options = fieldConfig.getOptions();
        ArrayList<String> items = new ArrayList<>();
        
        // 检查选项是否为空，为空则尝试解析
        if (options == null || options.isEmpty()) {
            Log.w(TAG, "下拉框选项为空，尝试解析选项数据");
            
            // 检查是否有字段选项字符串
            String optionsStr = fieldConfig.getOptionsString();
            if (optionsStr != null && !optionsStr.isEmpty()) {
                // 尝试解析选项字符串
                options = DropdownProcessor.parseOptions(optionsStr);
                fieldConfig.setOptions(options);
                Log.d(TAG, "从字段选项字符串解析得到 " + options.size() + " 个选项");
            } else {
                // 检查是否有默认值或占位符可以作为选项
                String defaultValue = fieldConfig.getDefaultValue();
                if (defaultValue != null && !defaultValue.isEmpty()) {
                    // 尝试解析默认值作为JSON选项
                    options = DropdownProcessor.parseOptions(defaultValue);
                    fieldConfig.setOptions(options);
                    Log.d(TAG, "从默认值解析得到 " + options.size() + " 个选项");
                }
            }
            
            // 如果仍然没有选项，创建一个默认选项
            if (options == null || options.isEmpty()) {
                Log.w(TAG, "无法解析选项，添加默认选项");
                options = new ArrayList<>();
                FormFieldConfig.OptionItem defaultOption = new FormFieldConfig.OptionItem();
                defaultOption.setLabel("请选择");
                defaultOption.setValue("");
                options.add(defaultOption);
                fieldConfig.setOptions(options);
            }
        }
        
        // 创建显示项列表
        for (FormFieldConfig.OptionItem option : options) {
            String label = option.getLabel();
            if (label == null || label.isEmpty()) {
                // 如果标签为空，使用值作为标签
                label = option.getValue() != null ? option.getValue() : "选项" + items.size();
                Log.w(TAG, "选项标签为空，使用值或默认标签: " + label);
                option.setLabel(label);
            }
            items.add(label);
        }
        
        Log.d(TAG, "下拉框添加了 " + items.size() + " 个选项");
        
        // 创建适配器
        ArrayAdapter<String> adapter = new ArrayAdapter<>(context,
                android.R.layout.simple_spinner_item, items);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinner.setAdapter(adapter);
        
        // 创建"其他"选项的输入框，初始设为不可见
        final EditText otherInput = new EditText(context);
        otherInput.setHint("请输入其他选项");
        otherInput.setVisibility(View.GONE);
        
        // 将Spinner和otherInput相互关联，方便后续获取值
        spinner.setTag(otherInput);
        otherInput.setTag(spinner);
        
        // 添加Spinner的选择监听器
        spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                // 检查是否选择了"其他"选项
                String selectedItem = parent.getItemAtPosition(position).toString();
                boolean isOtherOption = "其他".equals(selectedItem) || 
                                        "其它".equals(selectedItem) || 
                                        "other".equalsIgnoreCase(selectedItem);
                
                // 根据是否选择"其他"显示或隐藏输入框
                otherInput.setVisibility(isOtherOption ? View.VISIBLE : View.GONE);
                
                // 如果不是"其他"选项，清空输入框
                if (!isOtherOption) {
                    otherInput.setText("");
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                otherInput.setVisibility(View.GONE);
                otherInput.setText("");
            }
        });
        
        // 设置默认值
        String defaultValue = fieldConfig.getDefaultValue();
        if (defaultValue != null && !defaultValue.isEmpty()) {
            // 查找匹配的选项
            for (int i = 0; i < options.size(); i++) {
                if (defaultValue.equals(options.get(i).getValue())) {
                    spinner.setSelection(i);
                    Log.d(TAG, "下拉框设置默认值索引: " + i + ", 值: " + defaultValue);
                    break;
                }
            }
        }
        
        // 将组件添加到容器
        container.addView(spinner);
        container.addView(otherInput);
        
        // 将Spinner保存在容器的tag中，方便后续通过容器获取Spinner
        container.setTag(spinner);
        
        return container;
    }
    
    /**
     * 获取下拉框的当前选中值
     * 
     * @param view 下拉框视图（可能是Spinner或包含Spinner的容器）
     * @param fieldConfig 字段配置
     * @return 选中的值
     */
    public static String getSelectedValue(View view, FormFieldConfig fieldConfig) {
        Spinner spinner = null;
        EditText otherInput = null;
        
        // 判断view的类型，获取spinner和otherInput
        if (view instanceof Spinner) {
            spinner = (Spinner) view;
            Object tag = spinner.getTag();
            if (tag instanceof EditText) {
                otherInput = (EditText) tag;
            }
        } else if (view instanceof LinearLayout) {
            Object containerTag = view.getTag();
            if (containerTag instanceof Spinner) {
                spinner = (Spinner) containerTag;
                Object spinnerTag = spinner.getTag();
                if (spinnerTag instanceof EditText) {
                    otherInput = (EditText) spinnerTag;
                }
            }
        }
        
        if (spinner == null) {
            Log.e(TAG, "无法获取Spinner组件");
            return "";
        }
        
        int position = spinner.getSelectedItemPosition();
        List<FormFieldConfig.OptionItem> options = fieldConfig.getOptions();
        
        if (options != null && position >= 0 && position < options.size()) {
            // 获取选中项的标签和值
            String selectedLabel = spinner.getSelectedItem().toString();
            String selectedValue = options.get(position).getValue();
            
            // 检查是否选择了"其他"选项
            boolean isOtherOption = "其他".equals(selectedLabel) || 
                                    "其它".equals(selectedLabel) || 
                                    "other".equalsIgnoreCase(selectedLabel);
            
            // 如果选择了"其他"且输入框有内容，返回输入框的内容
            if (isOtherOption && otherInput != null && otherInput.getVisibility() == View.VISIBLE) {
                String otherText = otherInput.getText().toString().trim();
                if (!TextUtils.isEmpty(otherText)) {
                    Log.d(TAG, "用户选择了'其他'选项并输入: " + otherText);
                    return otherText;
                }
            }
            
            return selectedValue;
        }
        
        return "";
    }
} 
<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="io.dcloud.uniplugin.sampleflow.SampleFlowListActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="#F5F5F5">

        <!-- 筛选栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:orientation="horizontal"
            android:padding="16dp">

            <EditText
                android:id="@+id/editTextSearch"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@drawable/edit_text_background"
                android:hint="搜索样品编码或名称"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                android:textSize="14sp" />

            <Button
                android:id="@+id/buttonSearch"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginStart="8dp"
                android:background="@drawable/button_primary"
                android:text="搜索"
                android:textColor="@android:color/white"
                android:textSize="14sp" />
        </LinearLayout>

        <!-- 批次管理按钮 -->
        <Button
            android:id="@+id/buttonManageBatch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:background="@drawable/button_secondary"
            android:text="管理样品批次"
            android:textColor="#333333"
            android:drawableStart="@android:drawable/ic_menu_sort_by_size"
            android:drawablePadding="8dp"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:textSize="14sp" />

        <!-- 分割线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#E0E0E0" />

        <!-- 使用FrameLayout来包含列表和空数据视图，确保它们不会同时显示 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <!-- 列表区域 -->
            <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                android:id="@+id/swipeRefreshLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="8dp"
                    android:clipToPadding="false" />
            </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

            <!-- 空数据提示 -->
            <LinearLayout
                android:id="@+id/layoutEmpty"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:alpha="0.3"
                    android:src="@android:drawable/ic_menu_search"
                    app:tint="#666666" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="暂无样品流转数据"
                    android:textColor="#999999"
                    android:textSize="16sp" />
            </LinearLayout>
        </FrameLayout>
    </LinearLayout>

    <!-- 浮动新增按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAdd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@android:drawable/ic_input_add"
        app:tint="@android:color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout> 
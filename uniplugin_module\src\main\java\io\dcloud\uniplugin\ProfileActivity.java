package io.dcloud.uniplugin;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;

import com.google.gson.Gson;

import io.dcloud.uniplugin.db.DatabaseHelper;
import io.dcloud.uniplugin.enums.RoleEnum;
import io.dcloud.uniplugin.enums.SharedPreferencesEnum;
import io.dcloud.uniplugin.http.RetrofitManager;
import io.dcloud.uniplugin.model.ApiResponse;
import io.dcloud.uniplugin.model.AuthPermissionInfoRespVO;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import uni.dcloud.io.uniplugin_module.R;

public class ProfileActivity extends AppCompatActivity {

    private static final String TAG = "ProfileActivity";
    private TextView textViewName;
    private TextView textViewRole;
//    private TextView textViewMobile;
    private TextView textViewOrgName;
//    private TextView textViewOrgId;
    private TextView textViewGsddm;
//    private TextView textViewGsdmc;
    private Button buttonLogout;
    private Button buttonExportDb;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_profile);

        // 设置返回按钮
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setTitle("个人信息");
        }

        // 初始化视图 - 移除对布局中不存在的控件的引用
        textViewName = findViewById(R.id.textViewName);
        textViewRole = findViewById(R.id.textViewRole);
//        textViewMobile = findViewById(R.id.textViewMobile);
        textViewOrgName = findViewById(R.id.textViewOrgName);
//        textViewOrgId = findViewById(R.id.textViewOrgId);
        textViewGsddm = findViewById(R.id.textViewGsddm);
//        textViewGsdmc = findViewById(R.id.textViewGsdmc);
        buttonLogout = findViewById(R.id.buttonLogout);


        // 加载用户信息
        loadUserInfo();

        // 设置退出登录按钮点击事件
        buttonLogout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                logout();
            }
        });
    }

    private void loadUserInfo() {
        try {
            // 从SharedPreferences获取用户信息
            SharedPreferences sp = getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, MODE_PRIVATE);
            String permissionInfoStr = sp.getString(SharedPreferencesEnum.PERMISSION_INFO.value, "无法获取");
            
            if ("无法获取".equals(permissionInfoStr)) {
                Log.e(TAG, "无法获取用户权限信息");
                return;
            }
            
            Gson gson = new Gson();
            AuthPermissionInfoRespVO permissionInfoRespVO = gson.fromJson(permissionInfoStr, AuthPermissionInfoRespVO.class);
            
            if (permissionInfoRespVO == null) {
                Log.e(TAG, "解析权限信息失败");
                return;
            }
            
            // 设置用户基本信息
            AuthPermissionInfoRespVO.UserVO user = permissionInfoRespVO.getUser();
            if (user == null) {
                Log.e(TAG, "用户信息为空");
                return;
            }
            
            // 使用安全的方式设置文本，避免空指针异常
            if (textViewName != null) {
                textViewName.setText(user.getNickname() != null ? user.getNickname() : "");
            } else {
                Log.e(TAG, "textViewName 为 null");
            }
            
            // 使用RoleEnum将角色代码转换为中文名称
            if (textViewRole != null && permissionInfoRespVO.getRoles() != null) {
                String chineseRoles = RoleEnum.convertRolesToChineseNames(permissionInfoRespVO.getRoles());
                textViewRole.setText(chineseRoles);
            } else {
                Log.e(TAG, "textViewRole 为 null 或角色列表为 null");
            }

            // 设置组织信息
            AuthPermissionInfoRespVO.OrgVO org = user.getOrg();
            if (org != null && textViewOrgName != null) {
                textViewOrgName.setText(org.getName() != null ? org.getName() : "");
            } else {
                Log.e(TAG, "组织信息为空或 textViewOrgName 为 null");
            }
            
            if (textViewGsddm != null) {
                textViewGsddm.setText(user.getGsddm() != null ? user.getGsddm() : "");
            } else {
                Log.e(TAG, "textViewGsddm 为 null");
            }
        } catch (Exception e) {
            Log.e(TAG, "加载用户信息时发生异常: " + e.getMessage(), e);
            Toast.makeText(this, "加载用户信息失败", Toast.LENGTH_SHORT).show();
        }
    }

    private void logout() {
        // 显示退出中提示
        Toast.makeText(this, "正在退出登录...", Toast.LENGTH_SHORT).show();
        SharedPreferences sp = getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, MODE_PRIVATE);
        //判断是否离线登录
        if (sp.getBoolean(SharedPreferencesEnum.IS_OFFLINE_LOGIN.value, false)) {
            // 离线登录，清除本地用户信息并跳转到登录页面
            clearUserDataAndRedirect();
            return;
        }
        // 调用退出登录API
        RetrofitManager.getInstance(this)
                .getApiService()
                .logout()
                .enqueue(new Callback<ApiResponse<Boolean>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<Boolean>> call, Response<ApiResponse<Boolean>> response) {
                        // 无论成功与否，都清除本地用户信息并跳转到登录页面
                        clearUserDataAndRedirect();
                    }

                    @Override
                    public void onFailure(Call<ApiResponse<Boolean>> call, Throwable t) {
                        Log.e(TAG, "退出登录请求失败: " + t.getMessage());
                        // 即使请求失败，也清除本地用户信息并跳转到登录页面
                        clearUserDataAndRedirect();
                    }
                });
    }

    private void clearUserDataAndRedirect() {
        // 清除SharedPreferences中的用户信息
        SharedPreferences sp = getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, MODE_PRIVATE);
        sp.edit().clear().apply();

        // 显示退出成功提示
        Toast.makeText(this, "已退出登录", Toast.LENGTH_SHORT).show();

        // 跳转到登录页面
        Intent intent = new Intent(ProfileActivity.this, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    /**
     * 显示数据库位置
     */
    private void exportDatabase() {
        DatabaseHelper dbHelper = DatabaseHelper.getInstance(this);
        String dbPath = dbHelper.getDatabasePath();

        if (dbPath != null) {
            Toast.makeText(this, "数据库位置: " + dbPath, Toast.LENGTH_LONG).show();
            Log.d(TAG, "数据库位置: " + dbPath);
        } else {
            Toast.makeText(this, "无法获取数据库位置", Toast.LENGTH_SHORT).show();
            Log.e(TAG, "无法获取数据库位置");
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
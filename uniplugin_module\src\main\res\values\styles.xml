<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 基础主题定义 -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="colorPrimary">#1971AC</item>
        <item name="colorPrimaryDark">#1971AC</item>
        <item name="colorAccent">#1971AC</item>
        <item name="windowActionBar">true</item>
        <item name="windowNoTitle">false</item>
        <item name="android:homeAsUpIndicator">@android:drawable/ic_menu_revert</item>
        <item name="homeAsUpIndicator">@android:drawable/ic_menu_revert</item>
    </style>

    <!-- 自定义主题，ActionBar颜色为#1971AC -->
    <style name="AppTheme.BlueActionBar" parent="AppTheme">
        <item name="colorPrimary">#1971AC</item>
        <item name="colorPrimaryDark">#1971AC</item>
        <item name="colorAccent">#1971AC</item>
        <item name="android:windowActionBarOverlay">false</item>
    </style>
    
    <!-- 无ActionBar版本主题，用于使用Toolbar的页面 -->
    <style name="AppTheme.NoActionBar" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">#1971AC</item>
        <item name="colorPrimaryDark">#1971AC</item>
        <item name="colorAccent">#1971AC</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
</resources> 
package io.dcloud.uniplugin.model;

/**
 * BcgdYplzqdVo
 */
public class YPLZ {
    /**
     * 标识码
     */
    private String bsm;
    private Long id;
    /**
     * 备注
     */
    private String remark;
    /**
     * 关联批次ID
     */
    private Long batchId;
    /**
     * 批次编号
     */
    private String batchCode;
    /**
     * 批次名称
     */
    private String batchName;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 样品编码
     */
    private String sampleCode;
    /**
     * 颜色
     */
    private String sampleColour;
    /**
     * 外观状态
     */
    private String sampleExteriorState;
    /**
     * 样品名称
     */
    private String sampleName;
    /**
     * 包装是否破碎
     */
    private Long samplePackageBroken;
    /**
     * 包装材质
     */
    private String samplePackageMaterial;
    /**
     * 包装是否密封
     */
    private Long samplePackageSeal;
    /**
     * 保存条件
     */
    private String samplePreservationCondition;
    /**
     * 标签是否清晰
     */
    private Long sampleTagClear;
    /**
     * 标签是否完整
     */
    private Long sampleTagComplete;
    /**
     * 质地
     */
    private String sampleTexture;
    /**
     * 样品类型
     */
    private String sampleType;
    /**
     * 样品重量
     */
    private Double sampleWeight;
    /**
     * 状态
     */
    private Long status;
    /**
     * 关联项目ID
     */
    private Long supplementProjectId;
    /**
     * 关联项目名称
     */
    private String supplementProjectName;
    /**
     * 不合格照片
     */
    private String unqualifiedPhoto;
    /**
     * 不合格原因
     */
    private String unqualifiedReason;
    /**
     * 称重单位
     */
    private String weightUnit;
    
    /**
     * 接样单位ID(检测机构ID)
     */
    private Long receiveOrganizationId;

    public String getBsm() { return bsm; }
    public void setBsm(String value) { this.bsm = value; }

    public Long getId() { return id; }
    public void setId(Long value) { this.id = value; }

    public String getRemark() { return remark; }
    public void setRemark(String value) { this.remark = value; }

    public Long getBatchId() { return batchId; }
    public void setBatchId(Long value) { this.batchId = value; }

    public String getBatchCode() { return batchCode; }
    public void setBatchCode(String value) { this.batchCode = value; }

    public String getBatchName() { return batchName; }
    public void setBatchName(String value) { this.batchName = value; }

    public Long getCreateTime() { return createTime; }
    public void setCreateTime(Long value) { this.createTime = value; }

    public String getSampleCode() { return sampleCode; }
    public void setSampleCode(String value) { this.sampleCode = value; }

    public String getSampleColour() { return sampleColour; }
    public void setSampleColour(String value) { this.sampleColour = value; }

    public String getSampleExteriorState() { return sampleExteriorState; }
    public void setSampleExteriorState(String value) { this.sampleExteriorState = value; }

    public String getSampleName() { return sampleName; }
    public void setSampleName(String value) { this.sampleName = value; }

    public Long getSamplePackageBroken() { return samplePackageBroken; }
    public void setSamplePackageBroken(Long value) { this.samplePackageBroken = value; }

    public String getSamplePackageMaterial() { return samplePackageMaterial; }
    public void setSamplePackageMaterial(String value) { this.samplePackageMaterial = value; }

    public Long getSamplePackageSeal() { return samplePackageSeal; }
    public void setSamplePackageSeal(Long value) { this.samplePackageSeal = value; }

    public String getSamplePreservationCondition() { return samplePreservationCondition; }
    public void setSamplePreservationCondition(String value) { this.samplePreservationCondition = value; }

    public Long getSampleTagClear() { return sampleTagClear; }
    public void setSampleTagClear(Long value) { this.sampleTagClear = value; }

    public Long getSampleTagComplete() { return sampleTagComplete; }
    public void setSampleTagComplete(Long value) { this.sampleTagComplete = value; }

    public String getSampleTexture() { return sampleTexture; }
    public void setSampleTexture(String value) { this.sampleTexture = value; }

    public String getSampleType() { return sampleType; }
    public void setSampleType(String value) { this.sampleType = value; }

    public Double getSampleWeight() { return sampleWeight; }
    public void setSampleWeight(Double value) { this.sampleWeight = value; }

    public Long getStatus() { return status; }
    public void setStatus(Long value) { this.status = value; }

    public Long getSupplementProjectId() { return supplementProjectId; }
    public void setSupplementProjectId(Long value) { this.supplementProjectId = value; }

    public String getSupplementProjectName() { return supplementProjectName; }
    public void setSupplementProjectName(String value) { this.supplementProjectName = value; }

    public String getUnqualifiedPhoto() { return unqualifiedPhoto; }
    public void setUnqualifiedPhoto(String value) { this.unqualifiedPhoto = value; }

    public String getUnqualifiedReason() { return unqualifiedReason; }
    public void setUnqualifiedReason(String value) { this.unqualifiedReason = value; }

    public String getWeightUnit() { return weightUnit; }
    public void setWeightUnit(String value) { this.weightUnit = value; }
    
    public Long getReceiveOrganizationId() { return receiveOrganizationId; }
    public void setReceiveOrganizationId(Long value) { this.receiveOrganizationId = value; }
}
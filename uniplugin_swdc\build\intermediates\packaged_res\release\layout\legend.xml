<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <!--图例-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <!--横向布局-->
        <TextView
            android:layout_width="50dp"
            android:layout_height="40dp"
            android:id="@+id/legend_label1"
            android:background="@color/white"
            android:text="已调查"
            android:gravity="center"
            tools:ignore="MissingConstraints" />
        <!--已调查图例-->
        <ImageButton
            android:id="@+id/red"
            style="@style/color_btn"
            app:srcCompat="@drawable/green"
            tools:ignore="MissingConstraints" />
        <TextView
            android:layout_width="50dp"
            android:layout_height="40dp"
            android:id="@+id/legend_label2"
            android:background="@color/white"
            android:text="未调查"
            android:gravity="center"
            tools:ignore="MissingConstraints" />
        <!--未调查图例-->
        <ImageButton
            android:id="@+id/yellow"
            style="@style/color_btn"
            app:srcCompat="@drawable/yellow"
            tools:ignore="MissingConstraints" />
        <TextView
            android:layout_width="50dp"
            android:layout_height="40dp"
            android:id="@+id/legend_label3"
            android:background="@color/white"
            android:text="待整改"
            android:gravity="center"
            tools:ignore="MissingConstraints" />
        <!--待整改图例-->
        <ImageButton
            android:id="@+id/green"
            style="@style/color_btn"
            app:srcCompat="@drawable/red"
            tools:ignore="MissingConstraints" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
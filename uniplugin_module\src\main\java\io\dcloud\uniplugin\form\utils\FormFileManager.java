//package io.dcloud.uniplugin.form.utils;
//
//import android.util.Log;
//
//import org.json.JSONArray;
//import org.json.JSONException;
//import org.json.JSONObject;
//
//import java.io.File;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.HashSet;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//
//import io.dcloud.uniplugin.form.field.FieldFile;
//
///**
// * 表单文件管理器，负责处理表单中的文件保存和加载
// */
//public class FormFileManager {
//    private static final String TAG = "FormFileManager";
//
//    /**
//     * 保存表单文件到JSON对象
//     * @param formData 表单数据
//     * @param fieldFiles 字段文件映射
//     * @param filesData 要填充的文件数据JSON对象
//     * @throws JSONException JSON异常
//     */
//    public static void saveFormFilesToJson(JSONObject formData, Map<String, List<FieldFile>> fieldFiles,
//                                           JSONObject filesData) throws JSONException {
//        if (fieldFiles == null) {
//            Log.d(TAG, "字段文件映射为空，创建空的__files对象");
//            return;
//        }
//
//        Log.d(TAG, "开始保存表单字段文件，字段数: " + fieldFiles.size());
//
//        // 清除表单数据中可能存在的旧文件路径，防止删除文件后仍然保留
//        if (formData.has("__files")) {
//            JSONObject oldFiles = formData.getJSONObject("__files");
//            for (String fieldId : JSONUtils.getKeys(oldFiles)) {
//                // 清除formData中该字段原来的值，后面会用新值替代
//                if (formData.has(fieldId)) {
//                    formData.remove(fieldId);
//                    Log.d(TAG, "清除表单数据中字段 " + fieldId + " 的旧值");
//                }
//            }
//        }
//
//        // 记录处理过的媒体字段
//        StringBuilder processedFields = new StringBuilder("处理的媒体字段: ");
//
//        // 查找可能在表单数据根级别的媒体字段
//        Set<String> mediaFieldIds = new HashSet<>();
//        for (String fieldId : fieldFiles.keySet()) {
//            List<FieldFile> files = fieldFiles.get(fieldId);
//            // 只处理有文件的字段
//            if (files != null && !files.isEmpty()) {
//                mediaFieldIds.add(fieldId);
//                processedFields.append(fieldId).append(", ");
//
//                // 确保__files中有该字段的空数组
//                if (!filesData.has(fieldId)) {
//                    filesData.put(fieldId, new JSONArray());
//                    Log.d(TAG, "为字段[" + fieldId + "]创建空的文件数组");
//                }
//            }
//        }
//
//        Log.d(TAG, processedFields.toString());
//
//        int totalFiles = 0;
//        for (Map.Entry<String, List<FieldFile>> entry : fieldFiles.entrySet()) {
//            String fieldId = entry.getKey();
//            List<FieldFile> files = entry.getValue();
//
//            // 创建文件数组，即使文件列表为空也创建
//            JSONArray fileArray = new JSONArray();
//            filesData.put(fieldId, fileArray);
//
//            // 如果文件列表为空，确保formData中清除该字段的值
//            if (files == null || files.isEmpty()) {
//                Log.w(TAG, "字段[" + fieldId + "]的文件列表为空，确保清除相关值");
//                if (formData.has(fieldId)) {
//                    formData.remove(fieldId);
//                }
//                continue;
//            }
//
//            // 使用HashSet去重
//            Set<String> uniquePaths = new HashSet<>();
//
//            // 打印字段文件信息，帮助调试
//            Log.d(TAG, "字段[" + fieldId + "]有" + files.size() + "个文件");
//
//            // 处理所有文件
//            for (FieldFile file : files) {
//                if (file != null && file.getPath() != null) {
//                    String filePath = file.getPath();
//                    File fileObj = new File(filePath);
//
//                    if (fileObj.exists()) {
//                        if (!uniquePaths.contains(filePath)) {
//                            fileArray.put(filePath);
//                            uniquePaths.add(filePath);
//                            totalFiles++;
//
//                            // 确保在formData中也保存了文件路径（对于单文件字段）
//                            if (!formData.has(fieldId) || formData.isNull(fieldId)) {
//                                formData.put(fieldId, filePath);
//                                Log.d(TAG, "将文件路径添加到表单数据字段: " + fieldId);
//                            }
//
//                            Log.d(TAG, "添加文件: " + filePath + "，大小：" + fileObj.length() + " 字节");
//                        } else {
//                            Log.d(TAG, "跳过重复的文件路径: " + filePath);
//                        }
//                    } else {
//                        Log.w(TAG, "文件不存在: " + filePath);
//                    }
//                } else {
//                    Log.w(TAG, "文件对象为空");
//                }
//            }
//
//            // 如果没有添加任何文件，确保清除formData中的相关字段
//            if (fileArray.length() == 0) {
//                if (formData.has(fieldId)) {
//                    formData.remove(fieldId);
//                    Log.d(TAG, "字段[" + fieldId + "]没有可用文件，清除表单数据中的相关值");
//                }
//            } else {
//                Log.d(TAG, "保存字段[" + fieldId + "]的" + fileArray.length() + "个文件到__files");
//            }
//        }
//
//        Log.d(TAG, "共保存了" + totalFiles + "个文件到__files，涉及" + filesData.length() + "个字段");
//    }
//
//    /**
//     * 从表单数据JSON加载文件信息
//     * @param formData 表单数据JSON对象
//     * @return 字段文件映射
//     */
//    public static Map<String, List<FieldFile>> loadFormFilesFromJson(JSONObject formData) {
//        Map<String, List<FieldFile>> fieldFiles = new HashMap<>();
//
//        if (formData == null) {
//            Log.e(TAG, "表单数据为空，无法加载文件");
//            return fieldFiles;
//        }
//
//        try {
//            // 确保__files对象存在
//            if (!formData.has("__files")) {
//                Log.w(TAG, "表单数据中不包含__files对象，无法加载文件");
//                return fieldFiles;
//            }
//
//            // 从__files对象中获取文件信息
//            JSONObject filesData = formData.getJSONObject("__files");
//
//            int totalFilesLoaded = 0;
//            int totalFilesMissing = 0;
//            int emptyArrayCount = 0;
//
//            Log.d(TAG, "开始从__files加载表单字段文件，字段数: " + filesData.length());
//
//            // 遍历__files对象中的字段
//            for (String fieldId : JSONUtils.getKeys(filesData)) {
//                JSONArray fileArray = filesData.getJSONArray(fieldId);
//
//                // 检查文件数组是否为空，如果为空则跳过
//                if (fileArray.length() == 0) {
//                    Log.d(TAG, "字段[" + fieldId + "]的文件数组为空，跳过加载");
//                    emptyArrayCount++;
//                    continue;
//                }
//
//                // 为每个字段创建文件列表
//                List<FieldFile> files = new ArrayList<>();
//                int filesMissing = 0;
//
//                // 确定字段的媒体类型
//                String mediaType = inferMediaTypeFromFieldId(fieldId);
//
//                Log.d(TAG, "加载字段[" + fieldId + "]的" + fileArray.length() + "个文件，媒体类型: " + mediaType);
//
//                // 处理字段中的每个文件路径
//                for (int i = 0; i < fileArray.length(); i++) {
//                    String filePath = fileArray.getString(i);
//
//                    // 检查文件是否存在
//                    File file = new File(filePath);
//                    if (file.exists()) {
//                        // 创建FieldFile对象
//                        FieldFile fieldFile = new FieldFile(filePath, mediaType);
//                        files.add(fieldFile);
//                        totalFilesLoaded++;
//                        Log.d(TAG, "成功加载文件: " + filePath + " (类型: " + mediaType + ")");
//                    } else {
//                        Log.w(TAG, "文件不存在: " + filePath);
//                        filesMissing++;
//                        totalFilesMissing++;
//                    }
//                }
//
//                // 只有当有文件时才添加到结果
//                if (!files.isEmpty()) {
//                    fieldFiles.put(fieldId, files);
//                    Log.d(TAG, "为字段[" + fieldId + "]添加" + files.size() + "个文件" +
//                          (filesMissing > 0 ? "，有" + filesMissing + "个文件缺失" : ""));
//                } else if (filesMissing > 0) {
//                    Log.w(TAG, "字段[" + fieldId + "]的" + filesMissing + "个文件均不存在");
//                }
//            }
//
//            Log.d(TAG, "共从__files加载了" + totalFilesLoaded + "个文件，涉及" + fieldFiles.size() + "个字段" +
//                  (totalFilesMissing > 0 ? "，有" + totalFilesMissing + "个文件缺失" : "") +
//                  (emptyArrayCount > 0 ? "，跳过了" + emptyArrayCount + "个空文件数组" : ""));
//
//        } catch (Exception e) {
//            Log.e(TAG, "加载表单文件失败: " + e.getMessage(), e);
//        }
//
//        return fieldFiles;
//    }
//
//    /**
//     * 根据字段ID推断媒体类型（辅助方法）
//     * @param fieldId 字段ID
//     * @return 媒体类型
//     */
//    private static String inferMediaTypeFromFieldId(String fieldId) {
//        if (fieldId == null) {
//            return "file";
//        }
//
//        return MediaTypeUtils.inferMediaTypeFromField(fieldId, null);
//    }
//
//    /**
//     * JSON工具类
//     */
//    private static class JSONUtils {
//        /**
//         * 获取JSONObject的所有键
//         * @param jsonObject JSON对象
//         * @return 键列表
//         */
//        public static List<String> getKeys(JSONObject jsonObject) {
//            List<String> keys = new ArrayList<>();
//            if (jsonObject == null) return keys;
//
//            java.util.Iterator<String> iterator = jsonObject.keys();
//            while (iterator.hasNext()) {
//                keys.add(iterator.next());
//            }
//            return keys;
//        }
//    }
//}
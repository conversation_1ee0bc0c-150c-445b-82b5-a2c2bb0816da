package io.dcloud.uniplugin.sampleflow;

import android.app.ProgressDialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.dcloud.uniplugin.http.RetrofitManager;
import io.dcloud.uniplugin.model.ApiResponse;
import io.dcloud.uniplugin.model.DetectionOrg;
import io.dcloud.uniplugin.model.YPLZ;
import io.dcloud.uniplugin.model.YplzSample;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import uni.dcloud.io.uniplugin_module.R;

/**
 * 向批次添加样品页面
 */
public class AddSampleToBatchActivity extends AppCompatActivity {

    private static final String TAG = "AddSampleToBatchActivity";

    private long batchId;
    private String batchName;
    private List<YplzSample> sampleList = new ArrayList<>();
    private SampleAdapter sampleAdapter;
    private List<DetectionOrg> detectionOrgList = new ArrayList<>();
    private Map<String, Long> orgNameIdMap = new HashMap<>();
    private Long selectedOrgId;

    private TextView textViewBatchInfo;
    private RecyclerView recyclerViewSamples;
    private EditText editTextSampleName;
    private EditText editTextSampleType;
    private EditText editTextSampleQuantity;
    private EditText editTextSampleRemark;
    private Spinner spinnerReceiveOrg;
    private Button buttonAddSample;
    private Button buttonSubmit;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_add_sample_to_batch);

        // 获取批次ID和名称
        batchId = getIntent().getLongExtra("batchId", -1);
        batchName = getIntent().getStringExtra("batchName");

        if (batchId == -1 || TextUtils.isEmpty(batchName)) {
            Toast.makeText(this, "批次信息无效", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        // 设置ActionBar标题和返回按钮
        setTitle("添加样品");
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }

        initViews();
        setupListeners();
        loadDetectionOrgs();
    }

    private void initViews() {
        textViewBatchInfo = findViewById(R.id.textViewBatchInfo);
        textViewBatchInfo.setText("批次: " + batchName + " (ID: " + batchId + ")");

        recyclerViewSamples = findViewById(R.id.recyclerViewSamples);
        recyclerViewSamples.setLayoutManager(new LinearLayoutManager(this));
        
        sampleAdapter = new SampleAdapter(sampleList, position -> {
            // 删除样品
            sampleList.remove(position);
            sampleAdapter.notifyItemRemoved(position);
            sampleAdapter.notifyItemRangeChanged(position, sampleList.size());
            updateSubmitButtonState();
        });
        
        recyclerViewSamples.setAdapter(sampleAdapter);

        editTextSampleName = findViewById(R.id.editTextSampleName);
        editTextSampleType = findViewById(R.id.editTextSampleType);
        editTextSampleQuantity = findViewById(R.id.editTextSampleQuantity);
        editTextSampleRemark = findViewById(R.id.editTextSampleRemark);
        spinnerReceiveOrg = findViewById(R.id.spinnerReceiveOrg);
        buttonAddSample = findViewById(R.id.buttonAddSample);
        buttonSubmit = findViewById(R.id.buttonSubmit);

        // 初始时提交按钮禁用
        updateSubmitButtonState();
    }

    private void updateSubmitButtonState() {
        buttonSubmit.setEnabled(!sampleList.isEmpty());
    }

    private void setupListeners() {
        // 添加样品按钮
        buttonAddSample.setOnClickListener(v -> addSampleToList());

        // 提交按钮
        buttonSubmit.setOnClickListener(v -> submitSamples());
        
        // 接样单位选择监听
        spinnerReceiveOrg.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                String orgName = (String) parent.getItemAtPosition(position);
                selectedOrgId = orgNameIdMap.get(orgName);
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                selectedOrgId = null;
            }
        });
    }
    
    /**
     * 加载检测机构列表
     */
    private void loadDetectionOrgs() {
        RetrofitManager.getInstance(this)
                .getLabService()
                .getAllDetectionOrgs()
                .enqueue(new Callback<ApiResponse<List<DetectionOrg>>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<List<DetectionOrg>>> call, Response<ApiResponse<List<DetectionOrg>>> response) {
                        if (response.isSuccessful() && response.body() != null && response.body().getData() != null) {
                            detectionOrgList = response.body().getData();
                            setupOrgSpinner();
                        } else {
                            Toast.makeText(AddSampleToBatchActivity.this, "获取检测机构列表失败", Toast.LENGTH_SHORT).show();
                        }
                    }

                    @Override
                    public void onFailure(Call<ApiResponse<List<DetectionOrg>>> call, Throwable t) {
                        Toast.makeText(AddSampleToBatchActivity.this, "网络请求失败: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                        Log.e(TAG, "加载检测机构失败", t);
                    }
                });
    }
    
    /**
     * 设置检测机构下拉框
     */
    private void setupOrgSpinner() {
        List<String> orgNames = new ArrayList<>();
        orgNames.add("请选择检测机构");
        orgNameIdMap.clear();
        
        for (DetectionOrg org : detectionOrgList) {
            orgNames.add(org.getName());
            orgNameIdMap.put(org.getName(), org.getId());
        }
        
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, 
                android.R.layout.simple_spinner_item, orgNames);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerReceiveOrg.setAdapter(adapter);
    }

    /**
     * 将样品添加到列表中
     */
    private void addSampleToList() {
        String sampleName = editTextSampleName.getText().toString().trim();
        String sampleType = editTextSampleType.getText().toString().trim();
        String quantityStr = editTextSampleQuantity.getText().toString().trim();
        String remark = editTextSampleRemark.getText().toString().trim();

        // 验证输入
        if (TextUtils.isEmpty(sampleName)) {
            Toast.makeText(this, "请输入样品名称", Toast.LENGTH_SHORT).show();
            editTextSampleName.requestFocus();
            return;
        }

        if (TextUtils.isEmpty(sampleType)) {
            Toast.makeText(this, "请输入样品类型", Toast.LENGTH_SHORT).show();
            editTextSampleType.requestFocus();
            return;
        }
        
        if (selectedOrgId == null || spinnerReceiveOrg.getSelectedItemPosition() == 0) {
            Toast.makeText(this, "请选择接样单位", Toast.LENGTH_SHORT).show();
            spinnerReceiveOrg.requestFocus();
            return;
        }

        int quantity = 1;
        if (!TextUtils.isEmpty(quantityStr)) {
            try {
                quantity = Integer.parseInt(quantityStr);
                if (quantity <= 0) {
                    Toast.makeText(this, "样品数量必须大于0", Toast.LENGTH_SHORT).show();
                    editTextSampleQuantity.requestFocus();
                    return;
                }
            } catch (NumberFormatException e) {
                Toast.makeText(this, "样品数量格式无效", Toast.LENGTH_SHORT).show();
                editTextSampleQuantity.requestFocus();
                return;
            }
        }

        // 创建新样品并添加到列表
        YplzSample sample = new YplzSample();
        sample.setSampleName(sampleName);
        sample.setSampleType(sampleType);
        sample.setQuantity(quantity);
        sample.setRemark(remark);
        sample.setBatchId(batchId);
        sample.setSampleState(0L); // 默认状态为待流转
        sample.setReceiveOrganizationId(selectedOrgId); // 设置接样单位ID

        sampleList.add(sample);
        sampleAdapter.notifyItemInserted(sampleList.size() - 1);

        // 清空输入框
        editTextSampleName.setText("");
        editTextSampleType.setText("");
        editTextSampleQuantity.setText("1");
        editTextSampleRemark.setText("");
        spinnerReceiveOrg.setSelection(0);
        editTextSampleName.requestFocus();

        // 更新提交按钮状态
        updateSubmitButtonState();
    }

    /**
     * 提交所有样品
     */
    private void submitSamples() {
        if (sampleList.isEmpty()) {
            Toast.makeText(this, "请至少添加一个样品", Toast.LENGTH_SHORT).show();
            return;
        }

        // 显示进度对话框
        ProgressDialog progressDialog = new ProgressDialog(this);
        progressDialog.setMessage("正在添加样品...");
        progressDialog.setCancelable(false);
        progressDialog.show();

        // 将YplzSample列表转换为YPLZ列表
        List<YPLZ> yplzList = new ArrayList<>();
        for (YplzSample sample : sampleList) {
            YPLZ yplz = new YPLZ();
            yplz.setSampleName(sample.getSampleName());
            yplz.setSampleType(sample.getSampleType());
            yplz.setRemark(sample.getRemark());
            yplz.setBatchId(sample.getBatchId());
            yplz.setStatus(sample.getSampleState());
            yplz.setReceiveOrganizationId(sample.getReceiveOrganizationId()); // 设置接样单位ID
            // 设置其他必要的属性...
            yplzList.add(yplz);
        }

        // 发起网络请求
        RetrofitManager.getInstance(this)
                .getSampleBatchServiceApi() // 使用api包下的SampleBatchService，它需要List<YPLZ>类型参数
                .addSamplesToBatch(batchId, yplzList)
                .enqueue(new Callback<ApiResponse<Boolean>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<Boolean>> call, Response<ApiResponse<Boolean>> response) {
                        progressDialog.dismiss();

                        if (response.isSuccessful() && response.body() != null && response.body().getData()!=null) {
                            Boolean success = response.body().getData();
                            if (success != null && success) {
                                Toast.makeText(AddSampleToBatchActivity.this, "样品添加成功", Toast.LENGTH_SHORT).show();
                                
                                // 返回成功结果
                                setResult(RESULT_OK);
                                finish();
                            } else {
                                Toast.makeText(AddSampleToBatchActivity.this, "样品添加失败", Toast.LENGTH_SHORT).show();
                            }
                        } else {
                            String errorMsg = "样品添加失败";
                            if (response.body() != null) {
                                errorMsg = response.body().getMsg();
                            }
                            Toast.makeText(AddSampleToBatchActivity.this, errorMsg, Toast.LENGTH_SHORT).show();
                        }
                    }

                    @Override
                    public void onFailure(Call<ApiResponse<Boolean>> call, Throwable t) {
                        progressDialog.dismiss();
                        Toast.makeText(AddSampleToBatchActivity.this, "网络请求失败: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                        Log.e(TAG, "添加样品失败", t);
                    }
                });
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * 样品列表适配器
     */
    private static class SampleAdapter extends RecyclerView.Adapter<SampleAdapter.ViewHolder> {
        
        private final List<YplzSample> samples;
        private final OnItemClickListener listener;
        
        interface OnItemClickListener {
            void onDeleteClick(int position);
        }
        
        SampleAdapter(List<YplzSample> samples, OnItemClickListener listener) {
            this.samples = samples;
            this.listener = listener;
        }
        
        @Override
        public ViewHolder onCreateViewHolder(android.view.ViewGroup parent, int viewType) {
            View view = android.view.LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_sample, parent, false);
            return new ViewHolder(view);
        }
        
        @Override
        public void onBindViewHolder(ViewHolder holder, int position) {
            YplzSample sample = samples.get(position);
            holder.textViewSampleInfo.setText(sample.getSampleName() + " - " + sample.getSampleType() +
                    " (×" + sample.getQuantity() + ")");
            
            if (!TextUtils.isEmpty(sample.getRemark())) {
                holder.textViewRemark.setVisibility(View.VISIBLE);
                holder.textViewRemark.setText("备注: " + sample.getRemark());
            } else {
                holder.textViewRemark.setVisibility(View.GONE);
            }
            
            holder.buttonDelete.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onDeleteClick(holder.getAdapterPosition());
                }
            });
        }
        
        @Override
        public int getItemCount() {
            return samples.size();
        }
        
        static class ViewHolder extends RecyclerView.ViewHolder {
            final TextView textViewSampleInfo;
            final TextView textViewRemark;
            final Button buttonDelete;
            
            ViewHolder(View itemView) {
                super(itemView);
                textViewSampleInfo = itemView.findViewById(R.id.textViewSampleInfo);
                textViewRemark = itemView.findViewById(R.id.textViewRemark);
                buttonDelete = itemView.findViewById(R.id.buttonDelete);
            }
        }
    }
} 
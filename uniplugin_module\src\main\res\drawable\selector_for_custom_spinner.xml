<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
<!--        <item android:state_pressed="false" android:icon="@drawable/ic_action_polyline">-->
<!--        </item>-->
<!--        <item android:state_pressed="true" android:icon="@drawable/ic_action_polyline_red">-->
<!--        </item>-->

    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/white"/>
            <stroke android:width="1dp" android:color="#661886F7"/>
            <corners android:radius="6dp" />
        </shape>
   </item>
    <item>
        <bitmap
            android:gravity="end"
            android:src="@drawable/spinner_more">
        </bitmap>
    </item>
</layer-list>

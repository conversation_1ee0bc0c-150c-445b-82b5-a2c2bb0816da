package io.dcloud.uniplugin;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import java.util.Collections;


public class MyBroadcastReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        // 获取到变量并赋值
        int result = intent.getIntExtra("result", 0);
        // 将结果赋值给另一个类的全局变量
        MainActivity.LayerName = Collections.singletonList(result);
    }
}

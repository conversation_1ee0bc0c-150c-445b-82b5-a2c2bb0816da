package io.dcloud.uniplugin.samplingPoint;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import io.dcloud.uniplugin.MainActivity;
import io.dcloud.uniplugin.enums.SamplingPointStatus;
import io.dcloud.uniplugin.form.DynamicFormActivity;
import io.dcloud.uniplugin.model.DccyVO;
import io.dcloud.uniplugin.sample.SampleListActivity;
import uni.dcloud.io.uniplugin_module.R;

/**
 * 样点列表适配器，用于展示样点列表项
 */
public class SamplingPointListAdapter extends RecyclerView.Adapter<SamplingPointListAdapter.ViewHolder> {

    private List<DccyVO> samplingPoints;
    private Context context;
    private int tabPosition; // 当前标签页位置：1-待调查，2-待提交，3-已提交，4-待整改

//    public SamplingPointListAdapter(Context context, List<DccyVO> samplingPoints) {
//        this.context = context;
//        this.samplingPoints = samplingPoints != null ? samplingPoints : new ArrayList<>();
//        this.tabPosition = 1; // 默认为待调查标签页
//    }
    
    /**
     * 更新标签页位置
     * @param tabPosition 标签页位置：1-待调查，2-待提交，3-已提交，4-待整改
     */
    public void setTabPosition(int tabPosition) {
        this.tabPosition = tabPosition;
//        android.util.Log.d("SamplingPointAdapter", "更新标签页位置: " + tabPosition);
    }
    
    /**
     * 带标签页位置的构造函数
     * @param context 上下文
     * @param samplingPoints 样点列表
     * @param tabPosition 标签页位置：1-待调查，2-待提交，3-已提交，4-待整改
     */
    public SamplingPointListAdapter(Context context, List<DccyVO> samplingPoints, int tabPosition) {
        this.context = context;
        this.samplingPoints = samplingPoints != null ? samplingPoints : new ArrayList<>();
        this.tabPosition = tabPosition;
        android.util.Log.d("SamplingPointAdapter", "创建适配器，标签页位置: " + tabPosition);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_sampling_point, parent, false);
        return new ViewHolder(view);
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        final DccyVO samplingPoint = samplingPoints.get(position);
        final int pos = position;

        // 设置样点编号
        holder.tvYdbh.setText(samplingPoint.getpjdybh() != null ? samplingPoint.getpjdybh() : "未知编号");
        
        // 根据标签位置设置状态
        setStatusByTabPosition(holder.tvZt, samplingPoint);
        
        // 设置经纬度信息
        String locationText;
        if (samplingPoint.getDcjd() != null && samplingPoint.getDcwd() != null) {
            locationText = String.format("经度: %.6f, 纬度: %.6f", 
                    samplingPoint.getDcjd(), 
                    samplingPoint.getDcwd());
        } else {
            locationText = String.format("经度: %s, 纬度: %s", 
                    samplingPoint.getDcjd() != null ? String.format("%.6f", samplingPoint.getDcjd()) : "未知", 
                    samplingPoint.getDcwd() != null ? String.format("%.6f", samplingPoint.getDcwd()) : "未知");
        }
        holder.tvLocation.setText(locationText);
        
        // 设置管理单位
        holder.tvZldwmc.setText("管理单位: " + (samplingPoint.getDcdwName() != null ? samplingPoint.getDcdwName() : "未知"));
        
        // 设置调查人信息
        holder.tvDcrInfo.setText("调查人: " + (samplingPoint.getDcrName() != null ? samplingPoint.getDcrName() : "未知"));
        
        // 隐藏调查时间字段
        holder.tvSurveyTime.setVisibility(View.GONE);
        
        // 采土袋管理按钮恢复可见
        // holder.btnPrintLabel.setVisibility(View.GONE);
        
        // 设置媒体信息
        if (samplingPoint.getPhotoList() != null && !samplingPoint.getPhotoList().isEmpty() || 
            samplingPoint.getVideoList() != null && !samplingPoint.getVideoList().isEmpty()) {
            
            StringBuilder mediaInfo = new StringBuilder("媒体文件: ");
            if (samplingPoint.getPhotoList() != null && !samplingPoint.getPhotoList().isEmpty()) {
                mediaInfo.append(samplingPoint.getPhotoList().size()).append("张图片 ");
            }
            if (samplingPoint.getVideoList() != null && !samplingPoint.getVideoList().isEmpty()) {
                mediaInfo.append(samplingPoint.getVideoList().size()).append("个视频");
            }
            
            holder.tvMediaInfo.setText(mediaInfo.toString());
            holder.tvMediaInfo.setVisibility(View.VISIBLE);
        } else {
            holder.tvMediaInfo.setVisibility(View.GONE);
        }
        
        // 设置地图按钮点击事件
        holder.btnShowInMap.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 跳转到地图页面
                Intent intent = new Intent(context, MainActivity.class);
                
                // 传递经纬度信息，用现有的MainActivity能识别的参数名
                if (samplingPoint.getDcjd() != null) {
                    intent.putExtra("lon", String.valueOf(samplingPoint.getDcjd()));
                }
                
                if (samplingPoint.getDcwd() != null) {
                    intent.putExtra("lat", String.valueOf(samplingPoint.getDcwd()));
                }
                
                // 传递其他可能有用的参数
                if (samplingPoint.getpjdybh() != null) {
                    intent.putExtra("bsm", samplingPoint.getpjdybh());
                }
                
                if (samplingPoint.getDcdwName() != null) {
                    intent.putExtra("pointName", samplingPoint.getDcdwName());
                }
                
                intent.putExtra("offModel", "1"); // 确保 offModel 也作为 String 传递
                
               Log.d("SamplingPointAdapter", "跳转到地图查看样点 (String extras): " +
                        samplingPoint.getpjdybh() + 
                        ", 经度=" + samplingPoint.getDcjd() + 
                        ", 纬度=" + samplingPoint.getDcwd());
                
                context.startActivity(intent);
            }
        });
        
        // 跳转到采土袋管理页面
        holder.btnPrintLabel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(context, SampleListActivity.class);
                // 传递评价单元ID而非标识码
                intent.putExtra(SampleListActivity.EXTRA_PJDY_ID, samplingPoint.getPjdyId());
                intent.putExtra(SampleListActivity.EXTRA_PJDY_BSM, samplingPoint.getpjdybh());
                intent.putExtra(SampleListActivity.EXTRA_XFJL_ID, samplingPoint.getXfjlId());

                context.startActivity(intent);
            }
        });
        
        // 设置点击事件
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 显示加载提示对话框
                android.app.ProgressDialog progressDialog = new android.app.ProgressDialog(context);
                progressDialog.setMessage("正在加载表单...");
                progressDialog.setCancelable(false);
                progressDialog.show();

                // 获取样点ID
                String pjdybh = samplingPoint.getpjdybh() != null ? samplingPoint.getpjdybh() : "";
                String xmmc = samplingPoint.getXmmc() != null ? samplingPoint.getXmmc() : "";
                Long pjdyId = samplingPoint.getPjdyId() != null ? samplingPoint.getPjdyId() : 0;
                Long id = samplingPoint.getId() != null ? samplingPoint.getId() : 0;
                Long samplingPointRecordId = samplingPoint.getId() != null ? samplingPoint.getId() : 0L; // 获取 sampling_points 表的记录 ID

                // 跳转到动态表单页面
                Intent intent = new Intent(context, DynamicFormActivity.class);

                // 只传递必要的基本类型参数，避免序列化整个对象
                intent.putExtra("pjdybh", pjdybh);
                intent.putExtra("pjdyId", pjdyId);
                intent.putExtra("id",id);
                intent.putExtra("zgId", samplingPointRecordId); // 传递整改记录ID
                intent.putExtra("xmmc",xmmc);

                // 添加经纬度信息，如果有的话
                if (samplingPoint.getDcwd() != null) {
                    intent.putExtra("latitude", String.valueOf(samplingPoint.getDcwd()));
                } else {
                    intent.putExtra("latitude", "0.0");
                }

                if (samplingPoint.getDcjd() != null) {
                    intent.putExtra("longitude", String.valueOf(samplingPoint.getDcjd()));
                } else {
                    intent.putExtra("longitude", "0.0");
                }

                // 添加样点状态 - 根据当前标签页位置而不是样点自身状态
                SamplingPointStatus status;
                int statusCode;
                
                switch (tabPosition) {
                    case 1: // 待调查标签页
                        status = SamplingPointStatus.PENDING_SURVEY;
                        break;
                    case 2: // 待提交标签页
                        status = SamplingPointStatus.PENDING_SUBMIT;
                        break;
                    case 3: // 已提交标签页
                        status = SamplingPointStatus.SUBMITTED;
                        break;
                    case 4: // 待整改标签页
                        status = SamplingPointStatus.PENDING_RECTIFY;
                        break;
                    default:
                        status = SamplingPointStatus.PENDING_SURVEY;
                        break;
                }
                
                // 获取状态码
                statusCode = status.getCode();
                
                android.util.Log.d("SamplingPointAdapter", "打开样点 " + pjdybh + 
                    ", 标签页位置: " + tabPosition + 
                    ", 传递给表单的状态: " + status.getDescription() + 
                    ", 状态码: " + statusCode);
                
                intent.putExtra("samplingPointStatus", statusCode);
                intent.putExtra("pjdyId", pjdyId); // 确保 pjdyId 始终被传递
                intent.putExtra("samplingPointRecordId", samplingPointRecordId); // 再次确保记录ID被传递

                // 如果是已提交状态，传递表单数据JSON字段名
                if (status == SamplingPointStatus.SUBMITTED) {
                    intent.putExtra("useFormDataJson", true);
                }

                // 使用startActivityForResult而不是startActivity
                if (context instanceof android.app.Activity) {
                    ((android.app.Activity) context).startActivityForResult(intent, 1);
                    
                    // 延迟300ms后关闭进度对话框，确保新的活动已启动
                    v.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (progressDialog != null && progressDialog.isShowing()) {
                                try {
                                    progressDialog.dismiss();
                                } catch (Exception e) {
                                    // 忽略可能的IllegalArgumentException
                                }
                            }
                        }
                    }, 300);
                }
            }
        });
    }
    
    /**
     * 根据标签页位置设置状态文本和背景色
     * @param tvZt TextView
     * @param samplingPoint 样点数据
     */
    private void setStatusByTabPosition(TextView tvZt, DccyVO samplingPoint) {
        android.util.Log.d("SamplingPointAdapter", "渲染样点状态, tabPosition=" + tabPosition);
        
        // 修正：标签页位置是从1开始，而不是从0开始
        switch (tabPosition) {
            case 1: // 待调查标签页
                tvZt.setText("待调查");
                tvZt.setBackgroundResource(R.drawable.bg_status_pending);
                break;
            case 2: // 待提交标签页
                tvZt.setText("待提交");
                tvZt.setBackgroundResource(R.drawable.bg_status_pending_submit);
                break;
            case 3: // 已提交标签页
                tvZt.setText("已提交");
                tvZt.setBackgroundResource(R.drawable.bg_status_submitted);
                break;
            case 4: // 待整改标签页
                tvZt.setText("待整改");
                tvZt.setBackgroundResource(R.drawable.bg_status_rectify);
                break;
            default:
                // 根据实际状态设置（仅当不在标签页中直接使用时）
                Integer status = samplingPoint.getZt();
                if (status != null) {
                    if (status == SamplingPointStatus.PENDING_SURVEY.getCode()) {
                        tvZt.setText("待调查");
                        tvZt.setBackgroundResource(R.drawable.bg_status_pending);
                    } else if (status == SamplingPointStatus.PENDING_SUBMIT.getCode()) {
                        tvZt.setText("待提交");
                        tvZt.setBackgroundResource(R.drawable.bg_status_pending_submit);
                    } else if (status == SamplingPointStatus.SUBMITTED.getCode()) {
                        tvZt.setText("已提交");
                        tvZt.setBackgroundResource(R.drawable.bg_status_submitted);
                    } else if (status == SamplingPointStatus.PENDING_RECTIFY.getCode()) {
                        tvZt.setText("待整改");
                        tvZt.setBackgroundResource(R.drawable.bg_status_rectify);
                    } else {
                        tvZt.setText("未知");
                        tvZt.setBackgroundResource(R.drawable.bg_status_pending);
                    }
                } else {
                    tvZt.setText("未知");
                    tvZt.setBackgroundResource(R.drawable.bg_status_pending);
                }
                break;
        }
    }

    @Override
    public int getItemCount() {
        return samplingPoints != null ? samplingPoints.size() : 0;
    }

    public void updateData(List<DccyVO> newData) {
        this.samplingPoints = newData != null ? newData : new ArrayList<>();
        notifyDataSetChanged();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvYdbh, tvZt, tvLocation, tvZldwmc, tvDcrInfo, tvSurveyTime, tvMediaInfo;
        Button btnShowInMap, btnPrintLabel;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvYdbh = itemView.findViewById(R.id.textViewYdbh);
            tvZt = itemView.findViewById(R.id.textViewZt);
            tvLocation = itemView.findViewById(R.id.textViewLocation);
            tvZldwmc = itemView.findViewById(R.id.textViewZldwmc);
            tvDcrInfo = itemView.findViewById(R.id.textViewDcrInfo);
            tvSurveyTime = itemView.findViewById(R.id.textViewSurveyTime);
            tvMediaInfo = itemView.findViewById(R.id.textViewMediaInfo);
            btnShowInMap = itemView.findViewById(R.id.btnShowInMap);
            btnPrintLabel = itemView.findViewById(R.id.btnPrintLabel);
        }
    }
}
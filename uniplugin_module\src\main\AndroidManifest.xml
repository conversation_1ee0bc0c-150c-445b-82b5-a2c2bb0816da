<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="uni.dcloud.io.uniplugin_module"
    tools:ignore="MissingLeanbackLauncher">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
<!--    蓝牙模块 开始-->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
<!--    蓝牙模块结束-->

    <!-- 往sdcard中读写数据的权限 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <!-- 下载管理器权限 -->
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <!-- 相机权限 -->
    <uses-permission android:name="android.permission.CAMERA" />

    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />
    <uses-feature
        android:name="android.software.leanback"
        android:required="true" />
    <uses-feature android:name="android.hardware.camera" android:required="false" />

    <application>
            
        <activity
            android:name="io.dcloud.uniplugin.LoginActivity"
            android:theme="@style/AppTheme.BlueActionBar"
            android:label="广东省补充耕地质量验收APP"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <activity
            android:name="io.dcloud.uniplugin.HomeActivity"
            android:theme="@style/AppTheme.BlueActionBar"
            android:label="广东省补充耕地质量验收APP"
            android:exported="false" />
            
        <activity
            android:name="io.dcloud.uniplugin.ProfileActivity"
            android:theme="@style/AppTheme.BlueActionBar"
            android:label="个人信息"
            android:parentActivityName="io.dcloud.uniplugin.HomeActivity" />
            
        <activity
            android:name="io.dcloud.uniplugin.fileUpload.ImagePreviewActivity"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            android:label="图片预览" />

        <activity
            android:name="io.dcloud.uniplugin.samplingPoint.SamplingPointListActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
            
        <activity
            android:name="io.dcloud.uniplugin.samplingPoint.SamplingPointsActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.BlueActionBar"
            android:label="样点列表" />

        <activity
            android:name="io.dcloud.uniplugin.form.DynamicFormActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.BlueActionBar"
            android:label="动态表单" />

        <activity
            android:name="io.dcloud.uniplugin.form.OfflineFormsActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.BlueActionBar"
            android:label="离线表单记录" />

        <activity
            android:name="io.dcloud.uniplugin.form.SavedFormsActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.BlueActionBar"
            android:label="本地保存的表单" />

        <activity
            android:name="io.dcloud.uniplugin.sample.SampleListActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.BlueActionBar"
            android:label="样品管理" />

        <!-- 样品流转相关Activity -->
        <activity
            android:name="io.dcloud.uniplugin.sampleflow.SampleFlowListActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="portrait" 
            android:theme="@style/AppTheme"
            android:label="样品流转"/>
            
        <activity
            android:name="io.dcloud.uniplugin.sampleflow.SampleBatchListActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:label="样品批次"/>
            
        <activity
            android:name="io.dcloud.uniplugin.sampleflow.SampleFlowDetailActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:label="样品流转详情"/>

        <!-- 创建样品流转批次页面 -->
        <activity
            android:name="io.dcloud.uniplugin.sampleflow.CreateSampleFlowActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:label="新增样品流转批次"/>
            
        <!-- 二维码扫描页面 -->
        <activity
            android:name="io.dcloud.uniplugin.sampleflow.QRCodeScanActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:label="扫描二维码"/>

        <activity android:name="io.dcloud.uniplugin.sampleflow.CreateSampleBatchActivity"
            android:theme="@style/Theme.AppCompat.Light.DarkActionBar"
            android:screenOrientation="portrait"/>
            
        <activity android:name="io.dcloud.uniplugin.sampleflow.AddSampleToBatchActivity"
            android:theme="@style/Theme.AppCompat.Light.DarkActionBar"
            android:screenOrientation="portrait"/>

        <activity
            android:name="io.dcloud.uniplugin.LabHomeActivity"
            android:theme="@style/AppTheme.BlueActionBar"
            android:label="检测实验室管理系统"
            android:exported="false" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

            <!--android:theme="@android:style/Theme.DeviceDefault.Light.NoActionBar" -->
    </application>

</manifest>
package com.chy.map;

import android.content.Context;
import android.graphics.Color;
import android.util.Log;

import com.esri.arcgisruntime.geometry.Point;
import com.esri.arcgisruntime.geometry.PointCollection;
import com.esri.arcgisruntime.geometry.Polygon;
import com.esri.arcgisruntime.mapping.view.Graphic;
import com.esri.arcgisruntime.mapping.view.GraphicsOverlay;
import com.esri.arcgisruntime.mapping.view.MapView;
import com.esri.arcgisruntime.symbology.SimpleFillSymbol;
import com.esri.arcgisruntime.symbology.SimpleLineSymbol;

import java.util.List;

public class DrawGeometryTool {

    private List<Point> mPointList;

    private PointCollection mPointCollection;

    private GraphicsOverlay dzwlGraphicsOverlay;
    private Graphic graphic;

    Context context;

    public DrawGeometryTool(Context context, MapView mapView){
        this.context=context;

    }


    public void getCircle(MapView mapView,Point point, double radius) {
        // polygon.setEmpty();

        mPointCollection = new PointCollection(mapView.getSpatialReference());
        Point[] points = getPoints(point, radius);
        //mPointCollection.clear();
        for (Point p : points) {
            mPointCollection.add(p);
        }

        Polygon polygon = new Polygon(mPointCollection);
        //Toast.makeText(mapView.getContext(),polygon.toJson(),Toast.LENGTH_LONG).show();


        SimpleLineSymbol lineSymbol = new SimpleLineSymbol(SimpleLineSymbol.Style.SOLID, Color.parseColor("#FC8145"), 3.0f);
        SimpleFillSymbol simpleFillSymbol = new SimpleFillSymbol(SimpleFillSymbol.Style.SOLID, Color.parseColor("#33e97676"), lineSymbol);
        graphic = new Graphic(polygon, simpleFillSymbol);
        int size=mapView.getGraphicsOverlays().size();
        Log.d("GraphicsOverlays图层的数量",size+"");
        dzwlGraphicsOverlay=mapView.getGraphicsOverlays().get(size-1);
        dzwlGraphicsOverlay.getGraphics().clear();
        graphic.setZIndex(size-1);
        Log.d("graphic顺序",graphic.getZIndex()+"");
        dzwlGraphicsOverlay.getGraphics().add(graphic);



    }

    /** * 通过中心点和半径计算得出圆形的边线点集合 * * @param center * @param radius * @return */
    public  Point[] getPoints(Point center, double radius) {
        Point[] points = new Point[50];
        double sin;
        double cos;
        double x;
        double y;
        for (double i = 0; i < 50; i++) {
            sin = Math.sin(Math.PI * 2 * i / 50);
            cos = Math.cos(Math.PI * 2 * i / 50);
            x = center.getX() + radius * sin;
            y = center.getY() + radius * cos;
            points[(int) i] = new Point(x, y);
        }
        return points;
    }
}

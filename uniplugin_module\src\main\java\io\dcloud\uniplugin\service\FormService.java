package io.dcloud.uniplugin.service;

import android.app.Activity;
import android.util.Log;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import io.dcloud.uniplugin.form.field.FieldFile;
import io.dcloud.uniplugin.http.RetrofitManager;
import io.dcloud.uniplugin.model.ApiResponse;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 表单服务 - 处理表单的获取和提交
 */
public class FormService {
    private static final String TAG = "FormService";
    private static final String PREF_KEY_FORM_CONFIG_PREFIX = "form_config_";
    
    private final Activity activity;
    private final RetrofitManager retrofitManager;
    
    /**
     * 构造方法
     * @param activity Activity上下文
     */
    public FormService(Activity activity) {
        this.activity = activity;
        this.retrofitManager = RetrofitManager.getInstance(activity);
    }
    
    /**
     * 提交动态表单数据和文件 (修改签名以接收 pjdyId)
     * @param id         下发记录ID
     * @param pjdyId     评价单元ID (Long 类型)
     * @param formData   表单JSON数据 (不应再包含pjdyId)
     * @param files      表单文件列表，key是字段ID，value是文件列表
     * @param callback   回调函数
     */
    public void submitFormDataWithFiles(
            Long id,
            Long pjdyId, // 新增参数
            JSONObject formData,
            Map<String, List<FieldFile>> files, 
            final FormSubmitCallback callback) {
        
        try {

            // 创建文件字典JSON
            JSONArray fileDict = new JSONArray();
            
            // 创建文件列表
            List<MultipartBody.Part> fileParts = new ArrayList<>();
            
            // 处理表单字段中的文件
            for (Map.Entry<String, List<FieldFile>> entry : files.entrySet()) {
                String fieldId = entry.getKey();
                List<FieldFile> fieldFiles = entry.getValue();
                
                for (FieldFile fieldFile : fieldFiles) {
                    File file = new File(fieldFile.getPath());
                    if (!file.exists()) {
                        Log.e(TAG, "文件不存在: " + fieldFile.getPath());
                        continue;
                    }
                    
                    // 创建文件信息对象
                    JSONObject fileInfo = new JSONObject();
                    String fileName = file.getName();
                    
                    // 从字段ID和文件扩展名确定文件类型
                    String fileType = determineFileType(fieldId, fileName);
                    
                    // 设置文件信息
                    fileInfo.put("fileName", fileName);
                    fileInfo.put("fileType", fileType); // 使用推断出的类型
                    
                    // 添加坐标信息
                    if (fieldFile.getLatitude() != null && fieldFile.getLongitude() != null) {
                        fileInfo.put("jd", fieldFile.getLongitude());
                        fileInfo.put("wd", fieldFile.getLatitude());
                    } else if (formData.has("dcjd") && formData.has("dcwd")) {
                        fileInfo.put("jd", formData.optDouble("dcjd"));
                        fileInfo.put("wd", formData.optDouble("dcwd"));
                    }
                    
                    // 添加方位角
                    if (fieldFile.getDirection() != null) {
                        fileInfo.put("fwj", fieldFile.getDirection());
                    } else {
                         fileInfo.put("fwj", 0.0f); // 使用浮点数 0.0f
                    }
                    
                    // 格式化时间
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
                    String fileTime = null;
                    if (fieldFile.getFileTime() != null){
                        fileTime = fieldFile.getFileTime();
                    } else {
                        fileTime = sdf.format(new Date());
                    }
                    fileInfo.put("fileTime", fileTime);
                    
                    fileDict.put(fileInfo);
                    
                    // 创建文件部分
                    RequestBody fileRequestBody = RequestBody.create(
                            MediaType.parse(getMediaType(fileName)), file);
                    MultipartBody.Part filePart = MultipartBody.Part.createFormData(
                            "files", fileName, fileRequestBody);
                    fileParts.add(filePart);
                }
            }
            
            // 添加所有表单字段作为MultipartBody.Part
            Map<String, Object> formMap = jsonToMap(formData);
            for (Map.Entry<String, Object> entry : formMap.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                
                // 跳过已经包含在文件列表中的字段 和 可能意外存在的 pjdyId
                if (files.containsKey(key) || "pjdyId".equalsIgnoreCase(key)) continue;
                
                if (value != null) {
                    RequestBody fieldValueBody = RequestBody.create(
                            MediaType.parse("text/plain"), String.valueOf(value));
                    MultipartBody.Part fieldPart = MultipartBody.Part.createFormData(
                            key, null, fieldValueBody);
                    Log.d(TAG, "添加表单字段: " + key + " = " + value);
                    fileParts.add(fieldPart);
                }
            }
            
            // --- 新增：添加 pjdyId 字段 ---
            if (pjdyId != null) {
                RequestBody pjdyIdBody = RequestBody.create(
                        MediaType.parse("text/plain"), String.valueOf(pjdyId)); // 使用 Long 的 String 值
                MultipartBody.Part pjdyIdPart = MultipartBody.Part.createFormData(
                        "pjdyId", null, pjdyIdBody); // 确保 key 是 "pjdyId"
                fileParts.add(pjdyIdPart);
                Log.d(TAG, "添加 pjdyId 字段: " + pjdyId);
            } else {
                Log.w(TAG, "pjdyId is null, not adding to request.");
            }
            //新增下发记录ID
            if (id != null){
                RequestBody xfjlIdBody = RequestBody.create(
                        MediaType.parse("text/plain"), String.valueOf(id)); // 使用 Long 的 String 值
                MultipartBody.Part xfjlIdPart = MultipartBody.Part.createFormData(
                        "xfjlId", null, xfjlIdBody); // 确保 key 是 "pjdyId"
                fileParts.add(xfjlIdPart);
            }

            // --- 结束新增 ---

            // 添加文件字典
            RequestBody fileDictPart = RequestBody.create(
                    MediaType.parse("text/plain"), fileDict.toString());
            
            // 发起请求
            retrofitManager.getFormService().uploadDynamicForm(fileDictPart, fileParts)
                .enqueue(new Callback<ApiResponse<String>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<String>> call, Response<ApiResponse<String>> response) {
                        if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                            callback.onSuccess(response.body().getData());
                        } else {
                            String errorMsg = "未知错误";
                            if (response.body() != null && response.body().getMsg() != null) {
                                errorMsg = response.body().getMsg();
                            } else if (response.errorBody() != null) {
                                try {
                                    errorMsg = response.errorBody().string();
                                } catch (Exception e) { /* ignore */ }
                            }
                             Log.e(TAG, "Failed to submit form data: " + errorMsg);
                            callback.onFailure("提交表单失败: " + errorMsg);
                        }
                    }

                    @Override
                    public void onFailure(Call<ApiResponse<String>> call, Throwable t) {
                        Log.e(TAG, "Form submission network error: " + t.getMessage(), t);
                        callback.onFailure("网络错误: " + t.getMessage());
                    }
                });
            
        } catch (Exception e) {
            Log.e(TAG, "提交表单时出错: " + e.getMessage(), e);
            callback.onFailure("提交表单时出错: " + e.getMessage());
        }
    }
    
    /**
     * 提交整改动态表单数据和文件
     * @param pjdyId     评价单元ID (Long 类型)
     * @param zgId
     * @param formData   表单JSON数据
     * @param files      表单文件列表，key是字段ID，value是文件列表
     * @param callback   回调函数
     */
    public void submitZgFormDataWithFiles(
            Long pjdyId,
            Long zgId,
            JSONObject formData,
            Map<String, List<FieldFile>> files,
            final FormSubmitCallback callback) {
        
        try {
            Log.d(TAG, "开始提交整改表单数据 for pjdyId: " + pjdyId + ", formData: " + formData.toString());
            
            // 创建文件字典JSON
            JSONArray fileDict = new JSONArray();
            
            // 创建文件列表
            List<MultipartBody.Part> fileParts = new ArrayList<>();
            
            // 处理表单字段中的文件
            for (Map.Entry<String, List<FieldFile>> entry : files.entrySet()) {
                String fieldId = entry.getKey();
                List<FieldFile> fieldFiles = entry.getValue();
                
                for (FieldFile fieldFile : fieldFiles) {
                    File file = new File(fieldFile.getPath());
                    if (!file.exists()) {
                        Log.e(TAG, "文件不存在: " + fieldFile.getPath());
                        continue;
                    }
                    
                    // 创建文件信息对象
                    JSONObject fileInfo = new JSONObject();
                    String fileName = file.getName();
                    
                    // 从字段ID和文件扩展名确定文件类型
                    String fileType = determineFileType(fieldId, fileName);
                    
                    // 设置文件信息
                    fileInfo.put("fileName", fileName);
                    fileInfo.put("fileType", fileType); // 使用推断出的类型
                    
                    // 添加坐标信息
                    if (fieldFile.getLatitude() != null && fieldFile.getLongitude() != null) {
                        fileInfo.put("jd", fieldFile.getLongitude());
                        fileInfo.put("wd", fieldFile.getLatitude());
                    } else if (formData.has("dcjd") && formData.has("dcwd")) {
                        fileInfo.put("jd", formData.optDouble("dcjd"));
                        fileInfo.put("wd", formData.optDouble("dcwd"));
                    }
                    
                    // 添加方位角
                    if (fieldFile.getDirection() != null) {
                        fileInfo.put("fwj", fieldFile.getDirection());
                    } else {
                         fileInfo.put("fwj", 0.0f); // 使用浮点数 0.0f
                    }
                    
                    // 格式化时间
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
                    String fileTime = null;
                    if (fieldFile.getFileTime() != null){
                        fileTime = fieldFile.getFileTime();
                    } else {
                        fileTime = sdf.format(new Date());
                    }
                    fileInfo.put("fileTime", fileTime);
                    
                    fileDict.put(fileInfo);
                    
                    // 创建文件部分
                    RequestBody fileRequestBody = RequestBody.create(
                            MediaType.parse(getMediaType(fileName)), file);
                    MultipartBody.Part filePart = MultipartBody.Part.createFormData(
                            "files", fileName, fileRequestBody);
                    fileParts.add(filePart);
                }
            }
            
            // 添加所有表单字段作为MultipartBody.Part
            Map<String, Object> formMap = jsonToMap(formData);
            for (Map.Entry<String, Object> entry : formMap.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                
                // 跳过已经包含在文件列表中的字段 和 可能意外存在的 pjdyId
                if (files.containsKey(key) || "pjdyId".equalsIgnoreCase(key)) continue;
                
                if (value != null) {
                    RequestBody fieldValueBody = RequestBody.create(
                            MediaType.parse("text/plain"), String.valueOf(value));
                    MultipartBody.Part fieldPart = MultipartBody.Part.createFormData(
                            key, null, fieldValueBody);
                    Log.d(TAG, "添加表单字段: " + key + " = " + value);
                    fileParts.add(fieldPart);
                }
            }
            
            // 添加 pjdyId 字段
            if (pjdyId != null) {
                RequestBody pjdyIdBody = RequestBody.create(
                        MediaType.parse("text/plain"), String.valueOf(pjdyId));
                MultipartBody.Part pjdyIdPart = MultipartBody.Part.createFormData(
                        "pjdyId", null, pjdyIdBody);
                fileParts.add(pjdyIdPart);
                Log.d(TAG, "添加 pjdyId 字段: " + pjdyId);
            } else {
                Log.w(TAG, "pjdyId is null, not adding to request.");
            }


            // 添加 zgId 字段
            if (pjdyId != null) {
                RequestBody zgIdBody = RequestBody.create(
                        MediaType.parse("text/plain"), String.valueOf(zgId));
                MultipartBody.Part zgIdPart = MultipartBody.Part.createFormData(
                        "id", null, zgIdBody);
                fileParts.add(zgIdPart);
                Log.d(TAG, "添加 id 字段: " + zgId);
            }

            // 添加文件字典
            RequestBody fileDictPart = RequestBody.create(
                    MediaType.parse("text/plain"), fileDict.toString());
            
            // 使用整改专用API
            retrofitManager.getFormService().uploadZgDynamicForm(fileDictPart, fileParts)
                .enqueue(new Callback<ApiResponse<String>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<String>> call, Response<ApiResponse<String>> response) {
                        if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                            callback.onSuccess(response.body().getData());
                        } else {
                            String errorMsg = "未知错误";
                            if (response.body() != null && response.body().getMsg() != null) {
                                errorMsg = response.body().getMsg();
                            } else if (response.errorBody() != null) {
                                try {
                                    errorMsg = response.errorBody().string();
                                } catch (Exception e) { /* ignore */ }
                            }
                             Log.e(TAG, "Failed to submit zg form data: " + errorMsg);
                            callback.onFailure( errorMsg);
                        }
                    }

                    @Override
                    public void onFailure(Call<ApiResponse<String>> call, Throwable t) {
                        Log.e(TAG, "Zg form submission network error: " + t.getMessage(), t);
                        callback.onFailure("网络错误: " + t.getMessage());
                    }
                });
            
        } catch (Exception e) {
            Log.e(TAG, "提交整改表单时出错: " + e.getMessage(), e);
            callback.onFailure("提交整改表单时出错: " + e.getMessage());
        }
    }
    
    /**
     * 根据字段ID和文件名确定文件类型
     * @param fieldId 字段ID
     * @param fileName 文件名
     * @return 文件类型
     */
    private String determineFileType(String fieldId, String fileName) {
        // 使用字段ID作为文件类型，实现动态字段支持
        // 仅对某些特殊情况进行转换，如缺少fieldId或使用特殊文件名等

        // 如果fieldId为空，尝试从文件名推断
        if (fieldId == null || fieldId.isEmpty()) {
            if (fileName != null) {
                String lowerFileName = fileName.toLowerCase();
                String upperFileName = fileName.toUpperCase();
                
                // 从文件名推断类型
                if (upperFileName.contains("SIGNATURE")) {
                    return "signature";
                } else if (lowerFileName.endsWith(".mp4") || 
                           lowerFileName.endsWith(".mov") || 
                           lowerFileName.endsWith(".avi")) {
                    return "video";
                } else if (lowerFileName.endsWith(".jpg") || 
                           lowerFileName.endsWith(".jpeg") || 
                           lowerFileName.endsWith(".png")) {
                    return "photo";
                }
            }
            return "file"; // 默认文件类型
        }
        
        // 直接返回字段ID作为类型
        return fieldId;
    }
    
    /**
     * 根据文件名获取媒体类型
     * @param fileName 文件名
     * @return 媒体类型
     */
    private String getMediaType(String fileName) {
        String lowerCaseName = fileName.toLowerCase();
        if (lowerCaseName.endsWith(".jpg") || lowerCaseName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerCaseName.endsWith(".png")) {
            return "image/png";
        } else if (lowerCaseName.endsWith(".mp4")) {
            return "video/mp4";
        } else if (lowerCaseName.endsWith(".mov")) {
            return "video/quicktime";
        } else if (lowerCaseName.endsWith(".avi")) {
            return "video/x-msvideo";
        } else {
            return "application/octet-stream";
        }
    }
    
    /**
     * 将JSONObject转换为Map
     * @param json JSONObject对象
     * @return Map对象
     */
    private Map<String, Object> jsonToMap(JSONObject json) throws Exception {
        Map<String, Object> map = new HashMap<>();
        if (json != null) {
            Iterator<String> keys = json.keys();
            while (keys.hasNext()) {
                String key = keys.next();
                Object value = json.get(key);
                map.put(key, value);
            }
        }
        return map;
    }
    
    /**
     * 表单提交回调接口
     */
    public interface FormSubmitCallback {
        void onSuccess(String formId);
        void onFailure(String errorMsg);
    }
}
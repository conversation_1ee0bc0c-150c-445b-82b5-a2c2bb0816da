package io.dcloud.uniplugin.http;

import java.util.HashMap;
import java.util.Map;

/**
 * HTTP请求配置类
 */
public class RequestConfig {
    private String method;
    private String url;
    private String baseUrl;
    private Map<String, String> headers;
    private Map<String, Object> params;
    private Object data;
    private int timeout;
    
    private RequestConfig(Builder builder) {
        this.method = builder.method;
        this.url = builder.url;
        this.baseUrl = builder.baseUrl;
        this.headers = builder.headers;
        this.params = builder.params;
        this.data = builder.data;
        this.timeout = builder.timeout;
    }
    
    public String getMethod() {
        return method;
    }
    
    public String getUrl() {
        return url;
    }
    
    public String getBaseUrl() {
        return baseUrl;
    }
    
    public Map<String, String> getHeaders() {
        return headers;
    }
    
    public Map<String, Object> getParams() {
        return params;
    }
    
    public Object getData() {
        return data;
    }
    
    public int getTimeout() {
        return timeout;
    }
    
    /**
     * 请求配置构建器
     */
    public static class Builder {
        private String method = "GET";
        private String url;
        private String baseUrl;
        private Map<String, String> headers = new HashMap<>();
        private Map<String, Object> params = new HashMap<>();
        private Object data;
        private int timeout = 10; // 默认10秒
        
        public Builder setMethod(String method) {
            this.method = method;
            return this;
        }
        
        public Builder setUrl(String url) {
            this.url = url;
            return this;
        }
        
        public Builder setBaseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
            return this;
        }
        
        public Builder addHeader(String key, String value) {
            this.headers.put(key, value);
            return this;
        }
        
        public Builder setHeaders(Map<String, String> headers) {
            this.headers = headers;
            return this;
        }
        
        public Builder addParam(String key, Object value) {
            this.params.put(key, value);
            return this;
        }
        
        public Builder setParams(Map<String, Object> params) {
            this.params = params;
            return this;
        }
        
        public Builder setData(Object data) {
            this.data = data;
            return this;
        }
        
        public Builder setTimeout(int timeout) {
            this.timeout = timeout;
            return this;
        }
        
        public RequestConfig build() {
            return new RequestConfig(this);
        }
    }
} 
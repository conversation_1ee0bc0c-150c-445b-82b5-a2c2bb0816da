package io.dcloud.uniplugin.view;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewParent;
import android.util.Log;

import java.io.ByteArrayOutputStream;
import android.util.Base64;

public class SignatureView extends View {
    
    private Path mPath;
    private Paint mPaint;
    private Bitmap mBitmap;
    private Canvas mCanvas;
    private float mX, mY;
    private static final float TOUCH_TOLERANCE = 4;
    
    public SignatureView(Context context) {
        this(context, null);
    }
    
    public SignatureView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }
    
    public SignatureView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }
    
    private void init() {
        mPath = new Path();
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setDither(true);
        mPaint.setColor(Color.BLACK);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeJoin(Paint.Join.ROUND);
        mPaint.setStrokeCap(Paint.Cap.ROUND);
        mPaint.setStrokeWidth(6);
    }
    
    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        
        // 只有当宽度和高度大于0时才创建位图
        if (w > 0 && h > 0) {
            // 防止内存泄漏，先回收旧的位图
            if (mBitmap != null && !mBitmap.isRecycled()) {
                mBitmap.recycle();
            }
            
            mBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888);
            mCanvas = new Canvas(mBitmap);
            mCanvas.drawColor(Color.WHITE);
        } else {
            Log.w("SignatureView", "onSizeChanged with invalid dimensions: w=" + w + ", h=" + h);
        }
    }
    
    @Override
    protected void onDraw(Canvas canvas) {
        canvas.drawBitmap(mBitmap, 0, 0, mPaint);
        canvas.drawPath(mPath, mPaint);
    }
    
    private void touchStart(float x, float y) {
        mPath.reset();
        mPath.moveTo(x, y);
        mX = x;
        mY = y;
    }
    
    private void touchMove(float x, float y) {
        float dx = Math.abs(x - mX);
        float dy = Math.abs(y - mY);
        if (dx >= TOUCH_TOLERANCE || dy >= TOUCH_TOLERANCE) {
            mPath.quadTo(mX, mY, (x + mX) / 2, (y + mY) / 2);
            mX = x;
            mY = y;
        }
    }
    
    private void touchUp() {
        mPath.lineTo(mX, mY);
        mCanvas.drawPath(mPath, mPaint);
        mPath.reset();
    }
    
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        float x = event.getX();
        float y = event.getY();
        
        // 当手指按下时，请求父视图不要拦截触摸事件
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                // 请求父视图不要拦截触摸事件，这样ScrollView不会滚动
                getParent().requestDisallowInterceptTouchEvent(true);
                touchStart(x, y);
                invalidate();
                break;
            case MotionEvent.ACTION_MOVE:
                touchMove(x, y);
                invalidate();
                break;
            case MotionEvent.ACTION_UP:
                // 手指抬起时，父视图可以继续处理触摸事件
                getParent().requestDisallowInterceptTouchEvent(false);
                touchUp();
                invalidate();
                break;
            case MotionEvent.ACTION_CANCEL:
                // 如果事件被取消，也要重置父视图的拦截状态
                getParent().requestDisallowInterceptTouchEvent(false);
                touchUp();
                invalidate();
                break;
        }
        return true;
    }
    
    public void clear() {
        int width = getWidth();
        int height = getHeight();
        
        // 检查宽度和高度是否大于0
        if (width <= 0 || height <= 0) {
            // 宽度或高度无效，不执行清除操作
            Log.w("SignatureView", "Cannot clear: width=" + width + ", height=" + height);
            return;
        }
        
        mBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        mCanvas = new Canvas(mBitmap);
        mCanvas.drawColor(Color.WHITE);
        mPath.reset();
        invalidate();
    }
    
    public boolean isEmpty() {
        int width = getWidth();
        int height = getHeight();
        
        // 检查宽度和高度是否大于0
        if (width <= 0 || height <= 0 || mBitmap == null) {
            return true;
        }
        
        Bitmap emptyBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(emptyBitmap);
        canvas.drawColor(Color.WHITE);
        boolean result = mBitmap.sameAs(emptyBitmap);
        emptyBitmap.recycle(); // 释放资源
        return result;
    }
    
    public String getSignatureAsBase64() {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        mBitmap.compress(Bitmap.CompressFormat.PNG, 100, byteArrayOutputStream);
        byte[] byteArray = byteArrayOutputStream.toByteArray();
        return Base64.encodeToString(byteArray, Base64.DEFAULT);
    }
    
    public Bitmap getSignatureBitmap() {
        // 如果位图为空或尺寸无效，尝试重新创建
        if (mBitmap == null || mBitmap.getWidth() <= 0 || mBitmap.getHeight() <= 0) {
            int width = getWidth();
            int height = getHeight();
            
            if (width > 0 && height > 0) {
                // 创建一个新的位图
                try {
                    Bitmap newBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
                    Canvas canvas = new Canvas(newBitmap);
                    canvas.drawColor(Color.WHITE);
                    
                    // 如果旧位图存在但可能无效，先回收它
                    if (mBitmap != null && !mBitmap.isRecycled()) {
                        mBitmap.recycle();
                    }
                    
                    mBitmap = newBitmap;
                    mCanvas = new Canvas(mBitmap);
                    
                    // 重新绘制路径
                    if (mPath != null) {
                        mCanvas.drawPath(mPath, mPaint);
                    }
                    
                    Log.d("SignatureView", "Created new bitmap in getSignatureBitmap: " + width + "x" + height);
                } catch (Exception e) {
                    Log.e("SignatureView", "Error creating bitmap in getSignatureBitmap", e);
                    return null;
                }
            } else {
                Log.w("SignatureView", "Cannot create bitmap in getSignatureBitmap: invalid dimensions");
                return null;
            }
        }
        return mBitmap;
    }
} 
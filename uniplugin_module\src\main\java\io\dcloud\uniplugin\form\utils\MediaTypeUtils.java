package io.dcloud.uniplugin.form.utils;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;

import java.util.Map;

import io.dcloud.uniplugin.fileUpload.ImageAdapter;
import io.dcloud.uniplugin.form.field.FieldFile;
import io.dcloud.uniplugin.model.FormFieldConfig;

/**
 * 媒体类型工具类，集中处理媒体文件类型判断
 */
public class MediaTypeUtils {
    private static final String TAG = "MediaTypeUtils";
    
    // 视频字段关键词
    private static final String[] VIDEO_FIELD_KEYWORDS = {"video", "视频"};
    
    // 图片字段关键词
    private static final String[] PHOTO_FIELD_KEYWORDS = {"photo", "image", "pic", "picture", "照片", "图片"};
    
    // 签名字段关键词
    private static final String[] SIGNATURE_FIELD_KEYWORDS = {"signature", "sign", "签名", "qianming", "qianzi"};
    
    /**
     * 判断是否为视频文件
     * @param filePath 文件路径
     * @return 是否为视频文件
     */
    public static boolean isVideoFile(String filePath) {
        if (filePath == null) return false;
        String lowerPath = filePath.toLowerCase();
        return lowerPath.endsWith(".mp4") || 
               lowerPath.endsWith(".3gp") || 
               lowerPath.endsWith(".avi") || 
               lowerPath.endsWith(".mov");
    }
    
    /**
     * 判断是否为图片文件
     * @param filePath 文件路径
     * @return 是否为图片文件
     */
    public static boolean isImageFile(String filePath) {
        if (filePath == null) return false;
        String lowerPath = filePath.toLowerCase();
        return lowerPath.endsWith(".jpg") || 
               lowerPath.endsWith(".jpeg") || 
               lowerPath.endsWith(".png") || 
               lowerPath.endsWith(".gif");
    }
    
    /**
     * 判断是否为照片文件
     * @param filePath 文件路径
     * @return 是否为照片文件
     */
    public static boolean isPhotoFile(String filePath) {
        return isImageFile(filePath);
    }
    
    /**
     * 判断是否为签名文件
     * @param filePath 文件路径
     * @param fieldFiles 字段文件映射表
     * @return 是否为签名文件
     */
    public static boolean isSignatureFile(String filePath, Map<String, java.util.List<FieldFile>> fieldFiles) {
        if (filePath == null) return false;
        
        // 如果文件名包含签名关键词，则认为是签名文件
        String lowerPath = filePath.toLowerCase();
        for (String keyword : SIGNATURE_FIELD_KEYWORDS) {
            if (lowerPath.contains(keyword)) {
                return true;
            }
        }
        
        // 检查是否在签名字段的文件列表中
        if (fieldFiles != null) {
            for (Map.Entry<String, java.util.List<FieldFile>> entry : fieldFiles.entrySet()) {
                String fieldId = entry.getKey();
                if (isSignatureFieldId(fieldId, null)) {
                    for (FieldFile file : entry.getValue()) {
                        if (file != null && filePath.equals(file.getPath())) {
                            return true;
                        }
                    }
                }
            }
        }
        
        return false;
    }
    
    /**
     * 根据字段ID判断是否为视频字段
     * @param fieldId 字段ID
     * @param fieldConfig 字段配置
     * @return 是否为视频字段
     */
    public static boolean isVideoFieldId(String fieldId, FormFieldConfig fieldConfig) {
        // 先尝试使用字段配置
        if (fieldConfig != null) {
            return FormFieldConfig.TYPE_VIDEO.equals(fieldConfig.getFieldType());
        }
        
        // 配置不存在时，使用关键词判断
        if (fieldId == null) return false;
        
        String fieldIdLower = fieldId.toLowerCase();
        for (String keyword : VIDEO_FIELD_KEYWORDS) {
            if (fieldIdLower.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 根据字段ID判断是否为图片字段
     * @param fieldId 字段ID
     * @param fieldConfig 字段配置
     * @return 是否为图片字段
     */
    public static boolean isPhotoFieldId(String fieldId, FormFieldConfig fieldConfig) {
        // 先尝试使用字段配置
        if (fieldConfig != null) {
            return FormFieldConfig.TYPE_PHOTO.equals(fieldConfig.getFieldType());
        }
        
        // 配置不存在时，使用关键词判断
        if (fieldId == null) return false;
        
        String fieldIdLower = fieldId.toLowerCase();
        for (String keyword : PHOTO_FIELD_KEYWORDS) {
            if (fieldIdLower.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 判断是否为签名字段ID
     * @param fieldId 字段ID
     * @param fieldConfig 字段配置
     * @return 是否为签名字段
     */
    public static boolean isSignatureFieldId(String fieldId, FormFieldConfig fieldConfig) {
        // 先尝试使用字段配置
        if (fieldConfig != null) {
            return FormFieldConfig.TYPE_SIGNATURE.equals(fieldConfig.getFieldType());
        }
        
        if (fieldId == null) return false;
        
        String fieldIdLower = fieldId.toLowerCase();
        for (String keyword : SIGNATURE_FIELD_KEYWORDS) {
            if (fieldIdLower.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 根据字段配置和ID推断媒体类型
     * @param fieldId 字段ID
     * @param fieldConfig 字段配置
     * @return 媒体类型
     */
    public static String inferMediaTypeFromField(String fieldId, FormFieldConfig fieldConfig) {
        // 先尝试使用字段配置
        if (fieldConfig != null) {
            String fieldType = fieldConfig.getFieldType();
            if (FormFieldConfig.TYPE_VIDEO.equals(fieldType)) {
                return "video";
            } else if (FormFieldConfig.TYPE_PHOTO.equals(fieldType)) {
                return "image";
            } else if (FormFieldConfig.TYPE_SIGNATURE.equals(fieldType)) {
                return "signature";
            } else if (FormFieldConfig.TYPE_FILE.equals(fieldType)) {
                return "file";
            }
        }
        
        // 配置不存在时，使用关键词判断
        if (isVideoFieldId(fieldId, null)) {
            return "video";
        }
        
        if (isPhotoFieldId(fieldId, null)) {
            return "image";
        }
        
        if (isSignatureFieldId(fieldId, null)) {
            return "signature";
        }
        
        // 默认返回通用文件类型
        return "file";
    }
    
    /**
     * 预览签名
     * @param fieldId 字段ID
     * @param filePath 文件路径
     * @param fieldFileView 字段文件视图
     * @param formViews 表单视图映射
     */
    public static void previewSignature(String fieldId, String filePath, FieldFile fieldFileView, Map<String, View> formViews) {
        try {
            Log.d(TAG, "预览签名: " + filePath);
            
            // 如果有字段文件视图，使用它来显示签名
            if (fieldFileView != null) {
                Bitmap bitmap = BitmapFactory.decodeFile(filePath);
                if (bitmap != null) {
                    fieldFileView.setPreviewImage(bitmap);
                }
            }
            
            // 如果有表单视图映射，尝试找到对应的ImageView并设置图片
            if (formViews != null && fieldId != null) {
                View view = formViews.get(fieldId + "_preview");
                if (view instanceof ImageView) {
                    ImageView imageView = (ImageView) view;
                    Bitmap bitmap = BitmapFactory.decodeFile(filePath);
                    if (bitmap != null) {
                        imageView.setImageBitmap(bitmap);
                        imageView.setVisibility(View.VISIBLE);
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "预览签名出错: " + e.getMessage(), e);
        }
    }
    
    /**
     * 预览视频
     * @param fieldId 字段ID
     * @param filePath 文件路径
     * @param autoPlay 是否自动播放
     * @param imageAdapters 图片适配器映射
     * @param fieldFileView 字段文件视图
     */
    public static void previewVideo(String fieldId, String filePath, boolean autoPlay, Map<String, ImageAdapter> imageAdapters, FieldFile fieldFileView) {
        try {
            Log.d(TAG, "预览视频: " + filePath + ", 自动播放: " + autoPlay);
            
            // 获取对应字段的适配器
            ImageAdapter adapter = imageAdapters.get(fieldId);
            
            if (adapter != null) {
                // 检查是否已存在此视频
                boolean exists = false;
                for (ImageAdapter.FileItem item : adapter.getFileItems()) {
                    if (item.getFilePath() != null && item.getFilePath().equals(filePath)) {
                        exists = true;
                        break;
                    }
                }
                
                // 不存在则添加到适配器
                if (!exists) {
                    // 创建一个新的FileItem对象并传递给addItem方法
                    ImageAdapter.FileItem videoItem = new ImageAdapter.FileItem(filePath, false, true);
                    adapter.addItem(videoItem);
                    Log.d(TAG, "成功添加视频到适配器: " + filePath);
                    
                    // 强制刷新适配器的UI
                    adapter.notifyDataSetChanged();
                    Log.d(TAG, "已通知适配器数据集变化");
                    
                    // 如果需要自动播放
                    if (autoPlay) {
                        // 可以在这里添加自动播放逻辑
                        Log.d(TAG, "应该自动播放视频: " + filePath);
                    }
                } else {
                    Log.d(TAG, "视频已存在于适配器中: " + filePath);
                }
            } else {
                Log.w(TAG, "找不到字段 " + fieldId + " 的视频适配器");
            }
            
            // 如果有字段文件视图，更新它的预览
            if (fieldFileView != null) {
                try {
                    fieldFileView.showVideoPreview(filePath);
                    Log.d(TAG, "成功设置视频预览: " + filePath);
                } catch (Exception e) {
                    Log.e(TAG, "设置视频预览失败: " + e.getMessage(), e);
                }
            } else {
                Log.w(TAG, "字段文件视图为null，无法设置视频预览");
            }
        } catch (Exception e) {
            Log.e(TAG, "预览视频出错: " + e.getMessage(), e);
        }
    }
    
    /**
     * 预览照片
     * @param fieldId 字段ID
     * @param filePath 文件路径
     * @param imageAdapters 图片适配器映射
     * @param fieldFileView 字段文件视图
     */
    public static void previewPhoto(String fieldId, String filePath, Map<String, ImageAdapter> imageAdapters, FieldFile fieldFileView) {
        try {
            Log.d(TAG, "预览照片: " + filePath);
            
            // 获取对应字段的适配器
            ImageAdapter adapter = imageAdapters.get(fieldId);
            
            if (adapter != null) {
                // 检查是否已存在此照片
                boolean exists = false;
                for (ImageAdapter.FileItem item : adapter.getFileItems()) {
                    if (item.getFilePath() != null && item.getFilePath().equals(filePath)) {
                        exists = true;
                        break;
                    }
                }
                
                // 不存在则添加到适配器
                if (!exists) {
                    // 创建一个新的FileItem对象并传递给addItem方法
                    ImageAdapter.FileItem photoItem = new ImageAdapter.FileItem(filePath, true, false);
                    adapter.addItem(photoItem);
                    Log.d(TAG, "成功添加照片到适配器: " + filePath);
                    
                    // 强制刷新适配器的UI
                    adapter.notifyDataSetChanged();
                    Log.d(TAG, "已通知适配器数据集变化");
                } else {
                    Log.d(TAG, "照片已存在于适配器中: " + filePath);
                }
            } else {
                Log.w(TAG, "找不到字段 " + fieldId + " 的照片适配器");
            }
            
            // 如果有字段文件视图，更新它的预览
            if (fieldFileView != null) {
                try {
                    // 加载照片为缩略图
                    Bitmap bitmap = BitmapFactory.decodeFile(filePath);
                    if (bitmap != null) {
                        fieldFileView.setPreviewImage(bitmap);
                        Log.d(TAG, "成功设置照片预览图像: " + filePath);
                    } else {
                        Log.e(TAG, "无法解码图像文件: " + filePath);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "设置照片预览图像失败: " + e.getMessage(), e);
                }
            } else {
                Log.w(TAG, "字段文件视图为null，无法设置预览图像");
            }
        } catch (Exception e) {
            Log.e(TAG, "预览照片出错: " + e.getMessage(), e);
        }
    }
} 
package com.chy.map;

import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.util.Log;

import androidx.annotation.RequiresApi;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import io.dcloud.feature.uniapp.annotation.UniJSMethod;
import io.dcloud.feature.uniapp.bridge.UniJSCallback;
import io.dcloud.feature.uniapp.common.UniModule;

public class SwdcModule extends UniModule {
    public int REQUEST_CODE;
    public JSONArray points;
    public String type;
    public static int REQUEST_CODE_NTU=111111;
    public static int REQUEST_CODE_PAGE=200;
    public String point;

    //run ui thread
    @UniJSMethod(uiThread = true)
    public void swdcAsyncFunc(JSONObject options, UniJSCallback callback) {
        System.out.println(options);
        points=options.getJSONArray("points");
        this.type=options.getString("type");
        this.point = options.getString("point");
    }
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if(resultCode == REQUEST_CODE_NTU && data.hasExtra("code")) {
            Map<String,Object> params=new HashMap<>();
//            params.put("code",code);
            //通过Intene传值判断是点击了哪个按钮，同时将值传给uniapp控制跳转，规定预填报为0，上传图片为1
            params.put("code",data.getStringExtra("code"));
            mWXSDKInstance.fireGlobalEventCallback("JumpNativeToUniappEvent", params);
            //Toast.makeText(activity2, hasResult+"", Toast.LENGTH_SHORT).show();
            Log.e("TestModule", "原生页面返回----"+data.getStringExtra("code"));
            Log.e("TestModule", "LON_ZERO");
        }
        if (resultCode == REQUEST_CODE_PAGE){
            Map<String,Object> params=new HashMap<>();
            if (Objects.equals(this.type, "all")) {
                //工作台
//                intent.putExtra("backUrl", "/pages/biologicalSurvey/biologicalSurvey"); // 需要返回的uniapp页面的路由信息
                params.put("backUrl","/pages/biologicalSurvey/biologicalSurvey");
            } else if (Objects.equals(this.type, "one")) {
                // 信息采集（填报）
                params.put("backUrl","/pages/biologicalSurvey/surveyTask/surveyUpload");
            } else {
                params.put("backUrl","/pages/biologicalSurvey/surveyTask/surveyTask");
            }
            mWXSDKInstance.fireGlobalEventCallback("JumpBackToHome ", params);
        }

    }
    @RequiresApi(api = Build.VERSION_CODES.N)
    @UniJSMethod(uiThread = true)
    public void gotoNativePage(){
        if(mUniSDKInstance != null && mUniSDKInstance.getContext() instanceof Activity) {
            try {
                Intent intent = new Intent(mUniSDKInstance.getContext(), MainActivity.class);
                intent.putExtra("points",points.toString());
                intent.putExtra("type",this.type);
                intent.putExtra("point",this.point);
                // ((Activity) mUniSDKInstance.getContext()).startActivityForResult(intent, REQUEST_CODE);
                ((Activity) mUniSDKInstance.getContext()).startActivityForResult(intent, REQUEST_CODE);
                Log.d("传递请求码",REQUEST_CODE+"");

            } catch (Exception e) {
                Log.e("native page error", e.getMessage());
            }
        }
    }
}

package io.dcloud.uniplugin.form.utils;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.dcloud.uniplugin.fileUpload.ImageAdapter;
import io.dcloud.uniplugin.fileUpload.ImagePreviewActivity;
import io.dcloud.uniplugin.form.field.FieldFile;
import io.dcloud.uniplugin.form.media.MediaHandler;
import io.dcloud.uniplugin.model.FormFieldConfig;
import io.dcloud.uniplugin.model.PhotoInfo;

/**
 * 表单媒体预览管理器，负责处理表单中的媒体文件预览
 */
public class FormMediaPreviewManager {
    private static final String TAG = "FormMediaPreviewMgr";
    
    private Activity activity;
    private Map<String, ImageAdapter> imageAdapters;
    private Map<String, List<FieldFile>> fieldFiles;
    private List<FormFieldConfig> formFields;
    private Map<String, View> formViews;
    
    // 保存媒体文件集合
    private Map<String, List<String>> mediaFiles = new HashMap<>();
    
    // 保存照片信息集合
    private Map<String, List<PhotoInfo>> photoInfoMap = new HashMap<>();
    
    /**
     * 表单处理器更新监听器接口
     * 当字段文件列表发生变化时，通知外部组件更新
     */
    public interface OnFormProcessorUpdateListener {
        void onFieldFilesChanged();
    }
    
    // 文件变更监听器
    private OnFormProcessorUpdateListener onFormProcessorUpdateListener;
    
    /**
     * 设置表单处理器更新监听器
     * @param listener 监听器
     */
    public void setOnFormProcessorUpdateListener(OnFormProcessorUpdateListener listener) {
        this.onFormProcessorUpdateListener = listener;
        Log.d(TAG, "设置了表单处理器更新监听器");
    }
    
    /**
     * 构造函数（包含formViews参数）
     * @param activity 活动
     * @param imageAdapters 图片适配器
     * @param fieldFiles 字段文件
     * @param formViews 表单视图映射
     */
    public FormMediaPreviewManager(Activity activity, 
                                 Map<String, ImageAdapter> imageAdapters, 
                                 Map<String, List<FieldFile>> fieldFiles,
                                 Map<String, View> formViews) {
        this.activity = activity;
        this.imageAdapters = imageAdapters;
        this.fieldFiles = fieldFiles;
        this.formViews = formViews != null ? formViews : new HashMap<>();
    }
    
    /**
     * 设置表单字段配置
     * @param formFields 表单字段配置列表
     */
    public void setFormFields(List<FormFieldConfig> formFields) {
        this.formFields = formFields;
    }
    
    /**
     * 设置表单视图映射
     * @param formViews 表单视图映射
     */
    public void setFormViews(Map<String, View> formViews) {
        this.formViews = formViews != null ? formViews : new HashMap<>();
    }
    
    /**
     * 初始化预览监听器
     */
    public void initPreviewListeners() {
        Log.d(TAG, "初始化预览监听器");
        
        // 在设置监听器前预处理视频字段
        preprocessVideoFields();
        
        // 确保从适配器同步文件到字段文件映射
        synchronizeAdaptersToFieldFiles();
        
        // 为所有适配器设置点击和删除监听器
        for (Map.Entry<String, ImageAdapter> entry : imageAdapters.entrySet()) {
            final String fieldId = entry.getKey();
            final ImageAdapter adapter = entry.getValue();
            
            // 为每个适配器创建一个专用的点击监听器
            adapter.setOnItemClickListener(position -> {
                Log.d(TAG, "处理专用监听器点击事件: 字段ID=" + fieldId + ", 位置=" + position);

                if (position < 0 || position >= adapter.getItemCount()) {
                    Log.e(TAG, "无效的位置: " + position + ", 适配器项数: " + adapter.getItemCount());
                    return;
                }

                ImageAdapter.FileItem item = adapter.getFileItems().get(position);
                if (item == null) {
                    Log.e(TAG, "项目为空，无法处理");
                    return;
                }

                String filePath = item.getFilePath();
                Uri uri = item.getUri();
                boolean isImage = item.isImage();
                boolean isVideo = item.isVideo();

                Log.d(TAG, "字段[" + fieldId + "]中项目点击: 位置=" + position +
                      " | 文件路径=" + filePath +
                      " | 文件类型=" + (isImage ? "图片" : isVideo ? "视频" : "未知") +
                      " | URI=" + (uri != null ? uri.toString() : "null"));

                // 动态检查字段ID和实际文件类型
                FormFieldConfig fieldConfig = findFieldConfigById(fieldId);
                boolean isVideoField = MediaTypeUtils.isVideoFieldId(fieldId, fieldConfig);

                // 显式检查文件路径的后缀
                if (isVideoField && filePath != null) {
                    boolean shouldBeVideo = MediaTypeUtils.isVideoFile(filePath);
                    if (shouldBeVideo && !isVideo) {
                        Log.d(TAG, "修正: 根据文件路径判断这是一个视频文件: " + filePath);
                        isVideo = true;
                    }
                }

                // 处理文件预览
                handleFilePreview(filePath, uri, isImage, isVideo);
            });
            
            // 为每个适配器创建一个专用的删除监听器
            adapter.setOnItemDeleteListener(position -> {
                    Log.d(TAG, "删除项: 字段ID=" + fieldId + ", 位置=" + position);
                    
                    try {
                        // 确保位置有效
                        if (position < 0 || position >= adapter.getItemCount()) {
                            Log.e(TAG, "无效的位置: " + position + ", 适配器项数: " + adapter.getItemCount());
                            return;
                        }
                        
                        // 获取要删除的项
                        ImageAdapter.FileItem item = adapter.getFileItems().get(position);
                        String itemFilePath = item.getFilePath();
                        Uri itemUri = item.getUri();
                        
                        Log.d(TAG, "找到要删除的项目，字段ID: " + fieldId + ", 文件: " + 
                              (itemFilePath != null ? itemFilePath : (itemUri != null ? itemUri.toString() : "未知")));
                        
                        // 从适配器中移除项目
                        adapter.removeItem(position);
                        Log.d(TAG, "已从适配器中删除项目 position=" + position);
                        
                        // 打印当前适配器中剩余的项
                        Log.d(TAG, "适配器中剩余 " + adapter.getItemCount() + " 个项目");
                        
                    // 无论fieldFiles中是否存在，都先删除对应的字段内容
                    if (fieldFiles.containsKey(fieldId)) {
                                fieldFiles.remove(fieldId);
                        Log.d(TAG, "从fieldFiles中移除字段 " + fieldId + " 的所有文件");
                        } else {
                            Log.w(TAG, "字段 " + fieldId + " 在 fieldFiles 中不存在");
                        }
                        
                    // 从适配器中获取剩余项，完全同步到fieldFiles
                        List<ImageAdapter.FileItem> remainingItems = adapter.getFileItems();
                    if (remainingItems != null && !remainingItems.isEmpty()) {
                            Log.d(TAG, "从适配器获取剩余的" + remainingItems.size() + "个文件，同步到fieldFiles");
                            
                                // 创建新的FieldFile列表
                                List<FieldFile> newFiles = new ArrayList<>();
                                for (ImageAdapter.FileItem fileItem : remainingItems) {
                                    String path = fileItem.getFilePath();
                            if (path != null && !path.isEmpty() && new File(path).exists()) {
                                // 根据字段ID和文件类型确定文件类型
                                FormFieldConfig fieldConfig = findFieldConfigById(fieldId);
                                String fileType = MediaTypeUtils.inferMediaTypeFromField(fieldId, fieldConfig);
                                        FieldFile fieldFile = new FieldFile(path, fileType);
                                        newFiles.add(fieldFile);
                                        Log.d(TAG, "添加文件到同步列表: " + path + " (类型: " + fileType + ")");
                                    }
                                }
                                
                        // 用适配器中的剩余项更新字段的文件列表
                                if (!newFiles.isEmpty()) {
                                    fieldFiles.put(fieldId, newFiles);
                                    Log.d(TAG, "同步字段 " + fieldId + " 的文件列表，现包含 " + newFiles.size() + " 个文件");
                        }
                    } else {
                        Log.d(TAG, "适配器中没有剩余文件，确保从fieldFiles中移除字段 " + fieldId);
                        // 确保从fieldFiles中移除该字段
                        fieldFiles.remove(fieldId);
                    }
                    
                    // 通知FormProcessor更新文件列表
                    if (onFormProcessorUpdateListener != null) {
                            Log.d(TAG, "通知FormProcessor更新文件列表");
                            onFormProcessorUpdateListener.onFieldFilesChanged();
                    } else {
                            Log.w(TAG, "onFormProcessorUpdateListener 为 null，无法通知更新");
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "删除项目时出错: " + e.getMessage(), e);
                }
            });
        }
    }
    
    /**
     * 预处理视频字段，确保视频字段的文件类型正确
     */
    private void preprocessVideoFields() {
        Log.d(TAG, "开始预处理视频字段");
        
        // 遍历所有适配器，查找视频字段
        for (Map.Entry<String, ImageAdapter> entry : imageAdapters.entrySet()) {
            String fieldId = entry.getKey();
            
            // 判断是否为视频字段
            FormFieldConfig fieldConfig = findFieldConfigById(fieldId);
            if (MediaTypeUtils.isVideoFieldId(fieldId, fieldConfig)) {
                ImageAdapter videoAdapter = entry.getValue();
                List<ImageAdapter.FileItem> fileItems = videoAdapter.getFileItems();
                
                Log.d(TAG, "找到视频适配器[" + fieldId + "]，包含" + fileItems.size() + "个项目");
                
                // 修正所有项目
                for (int i = 0; i < fileItems.size(); i++) {
                    ImageAdapter.FileItem item = fileItems.get(i);
                    String filePath = item.getFilePath();
                    
                    if (filePath != null) {
                        // 检查是否为视频文件
                        boolean shouldBeVideo = MediaTypeUtils.isVideoFile(filePath);
                        
                        // 如果应该是视频但未标记为视频，记录这个问题
                        if (shouldBeVideo && !item.isVideo()) {
                            Log.w(TAG, "警告: 视频字段[" + fieldId + "]中的文件未标记为视频: " + filePath);
                        }
                        
                        // 确保FieldFile类型正确
                        List<FieldFile> fieldFileList = fieldFiles.get(fieldId);
                        if (fieldFileList != null) {
                            for (FieldFile fieldFile : fieldFileList) {
                                if (fieldFile != null && filePath.equals(fieldFile.getPath())) {
                                    if (!"video".equals(fieldFile.getType())) {
                                        fieldFile.setType("video");
                                        Log.d(TAG, "修正视频字段[" + fieldId + "]中文件类型: " + filePath);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 从所有适配器同步文件到fieldFiles映射
     */
    private void synchronizeAdaptersToFieldFiles() {
        if (imageAdapters == null || fieldFiles == null) {
            Log.e(TAG, "无法同步文件，适配器或字段文件映射为null");
            return;
        }
        
        Log.d(TAG, "开始同步从适配器同步文件到字段文件映射");
        
        // 遍历所有适配器
        for (Map.Entry<String, ImageAdapter> entry : imageAdapters.entrySet()) {
            String fieldId = entry.getKey();
            ImageAdapter adapter = entry.getValue();
            
            // 检查此字段在fieldFiles中是否已存在
            if (!fieldFiles.containsKey(fieldId) || fieldFiles.get(fieldId) == null || fieldFiles.get(fieldId).isEmpty()) {
                List<ImageAdapter.FileItem> items = adapter.getFileItems();
                
                if (items != null && !items.isEmpty()) {
                    Log.d(TAG, "字段 " + fieldId + " 在fieldFiles中不存在或为空，从适配器同步" + items.size() + "个文件");
                    
                    // 创建新的FieldFile列表
                    List<FieldFile> newFiles = new ArrayList<>();
                    
                    // 遍历适配器中的所有文件
                    for (ImageAdapter.FileItem item : items) {
                        String path = item.getFilePath();
                        if (path != null && !path.isEmpty()) {
                            // 根据字段ID动态确定文件类型
                            FormFieldConfig fieldConfig = findFieldConfigById(fieldId);
                            String fileType = MediaTypeUtils.inferMediaTypeFromField(fieldId, fieldConfig);
                            FieldFile fieldFile = new FieldFile(path, fileType);
                            newFiles.add(fieldFile);
                            Log.d(TAG, "为字段 " + fieldId + " 添加文件: " + path + " (类型: " + fileType + ")");
                        }
                    }
                    
                    // 将新列表添加到fieldFiles
                    if (!newFiles.isEmpty()) {
                        fieldFiles.put(fieldId, newFiles);
                        Log.d(TAG, "为字段 " + fieldId + " 创建新的文件列表，包含 " + newFiles.size() + " 个文件");
                    }
                }
            } else {
                Log.d(TAG, "字段 " + fieldId + " 在fieldFiles中已存在，包含 " + fieldFiles.get(fieldId).size() + " 个文件");
            }
        }
        
        // 打印同步后的字段文件映射
        Log.d(TAG, "同步后字段文件映射包含 " + fieldFiles.size() + " 个字段");
        for (Map.Entry<String, List<FieldFile>> entry : fieldFiles.entrySet()) {
            String fieldId = entry.getKey();
            List<FieldFile> files = entry.getValue();
            Log.d(TAG, "字段 " + fieldId + ": " + (files != null ? files.size() : 0) + " 个文件");
        }
    }
    
    /**
     * 处理文件预览
     */
    private void handleFilePreview(String filePath, Uri uri, boolean isImage, boolean isVideo) {
        if (filePath != null) {
            if (isImage && !isVideo) {
                // 预览图片
                Log.d(TAG, "预览图片: " + filePath);
                Intent intent = new Intent(activity, ImagePreviewActivity.class);
                intent.putExtra("imagePath", filePath);
                activity.startActivity(intent);
            } else if (isVideo || MediaTypeUtils.isVideoFile(filePath)) {
                // 预览视频
                Log.d(TAG, "预览视频: " + filePath);
                VideoPreviewHandler.showVideoPreviewDialog(activity, filePath);
            } else {
                Log.w(TAG, "无法确定文件类型: " + filePath);
                UIUtils.showToast(activity, "无法预览此文件");
            }
        } else if (uri != null) {
            // 处理URI
            if (isImage && !isVideo) {
                Log.d(TAG, "预览图片URI: " + uri);
                Intent intent = new Intent(activity, ImagePreviewActivity.class);
                intent.putExtra("imageUri", uri.toString());
                activity.startActivity(intent);
            } else {
                // 视频URI，尝试获取路径
                Log.d(TAG, "处理视频URI: " + uri);
                String path = MediaHandler.getPathFromUri(activity, uri);
                if (path != null) {
                    Log.d(TAG, "从URI获取视频路径成功: " + path);
                    VideoPreviewHandler.showVideoPreviewDialog(activity, path);
                } else {
                    Log.e(TAG, "从URI获取视频路径失败");
                    UIUtils.showToast(activity, "无法播放此视频");
                }
            }
        }
    }
    
    /**
     * 根据字段ID找到对应的字段配置
     * @param fieldId 字段ID
     * @return 字段配置
     */
    private FormFieldConfig findFieldConfigById(String fieldId) {
        if (formFields == null || fieldId == null) {
            return null;
        }
        
        for (FormFieldConfig field : formFields) {
            if (field != null && fieldId.equals(field.getFieldId())) {
                return field;
            }
        }
        
        return null;
    }
    
    /**
     * 记录所有可用的adapter
     */
    private void logAllAvailableAdapters() {
        Log.d(TAG, "当前可用的adapter列表：");
        for (String key : imageAdapters.keySet()) {
            Log.d(TAG, "  - " + key);
        }
    }
    
    /**
     * 根据适配器更新字段文件列表
     * @param fieldId 字段ID
     * @param adapter 适配器
     * @param fileType 文件类型
     */
    private void updateFieldFilesFromAdapter(String fieldId, ImageAdapter adapter, String fileType) {
        updateFieldFilesFromAdapter(fieldId, adapter, fileType, null, null, null, null);
    }
    
    /**
     * 根据适配器更新字段文件列表，包括位置信息
     * @param fieldId 字段ID
     * @param adapter 适配器
     * @param fileType 文件类型
     * @param latitude 纬度
     * @param longitude 经度
     * @param direction 方位角
     * @param locationInfo 位置信息文本
     */
    private void updateFieldFilesFromAdapter(String fieldId, ImageAdapter adapter, String fileType, 
                                          Double latitude, Double longitude, Float direction, String locationInfo) {
        // 获取适配器中的所有文件
        List<String> filePaths = new ArrayList<>();
        
        try {
            List<ImageAdapter.FileItem> fileItems = adapter.getFileItems();
            if (fileItems != null) {
                for (ImageAdapter.FileItem item : fileItems) {
                    if (item != null) {
                        String path = item.getFilePath();
                        if (path != null) {
                            filePaths.add(path);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "获取适配器文件项出错: " + e.getMessage());
        }
        
        // 更新fieldFiles
        List<FieldFile> files = new ArrayList<>();
        
        // 获取最后添加的路径（通常是最新拍摄的照片）
        String lastAddedPath = filePaths.isEmpty() ? null : filePaths.get(filePaths.size() - 1);
        
        for (String path : filePaths) {
            FieldFile fieldFile;
            
            // 如果是最后添加的照片且有位置信息，则设置位置信息
            if (path.equals(lastAddedPath) && latitude != null && longitude != null && "image".equals(fileType)) {
                fieldFile = new FieldFile(path, fileType, latitude, longitude, direction);
                if (locationInfo != null) {
                    fieldFile.setLocationInfo(locationInfo);
                }
                Log.d(TAG, "为照片添加位置信息: " + path + ", 位置: " + locationInfo);
            } else {
                fieldFile = new FieldFile(path, fileType);
            }
            
            files.add(fieldFile);
        }
        
        // 保存到字段文件映射
        if (!files.isEmpty()) {
            fieldFiles.put(fieldId, files);
            
            // 通知FormProcessor更新
            if (onFormProcessorUpdateListener != null) {
                onFormProcessorUpdateListener.onFieldFilesChanged();
            }
            
            // 如果有位置信息，记录日志
            if (latitude != null && longitude != null) {
                Log.d(TAG, "已更新带位置信息的字段文件: " + fieldId + ", 位置: " + locationInfo);
            }
        }
    }
    
    /**
     * 添加媒体文件
     * @param fieldId 字段ID
     * @param filePath 文件路径
     * @param autoPlay 是否自动播放（视频类型才有效）
     * @return 是否添加成功
     */
    public boolean addMediaFile(String fieldId, String filePath, boolean autoPlay) {
        if (TextUtils.isEmpty(fieldId) || TextUtils.isEmpty(filePath)) {
            Log.e(TAG, "添加媒体文件参数无效: fieldId=" + fieldId + ", filePath=" + filePath);
            return false;
        }
        
        File file = new File(filePath);
        if (!file.exists()) {
            Log.e(TAG, "文件不存在: " + filePath);
            return false;
        }
        
        Log.d(TAG, "添加媒体文件: fieldId=" + fieldId + ", filePath=" + filePath + ", autoPlay=" + autoPlay);

        // 添加到媒体文件集合
        if (!mediaFiles.containsKey(fieldId)) {
            mediaFiles.put(fieldId, new ArrayList<>());
        }
        
        List<String> files = mediaFiles.get(fieldId);
        if (!files.contains(filePath)) {
            files.add(filePath);
        }

        // 判断文件类型，执行相应的处理
        boolean isSignature = MediaTypeUtils.isSignatureFile(filePath, fieldFiles);
        boolean isVideo = MediaTypeUtils.isVideoFile(filePath);
        boolean isPhoto = MediaTypeUtils.isPhotoFile(filePath);

        Log.d(TAG, "字段 " + fieldId + " 的类型: photo, 是签名: " + isSignature + ", 是视频: " + isVideo + ", 是照片: " + isPhoto);

        // 获取字段对应的FieldFile视图
        FieldFile fieldFileView = getFieldFileView(fieldId);
        
        // 根据文件类型进行不同处理
        if (isSignature) {
            // 处理签名
            MediaTypeUtils.previewSignature(fieldId, filePath, fieldFileView, formViews);
        } else if (isVideo) {
            // 处理视频
            MediaTypeUtils.previewVideo(fieldId, filePath, autoPlay, imageAdapters, fieldFileView);
        } else if (isPhoto) {
            // 处理照片
            MediaTypeUtils.previewPhoto(fieldId, filePath, imageAdapters, fieldFileView);
        } else {
            Log.w(TAG, "未知的文件类型: " + filePath);
            return false;
        }

        return true;
    }
    
    /**
     * 获取字段的所有媒体文件
     * @param fieldId 字段ID
     * @return 媒体文件列表
     */
    public List<String> getMediaFiles(String fieldId) {
        if (TextUtils.isEmpty(fieldId)) {
            return new ArrayList<>();
        }
        
        if (mediaFiles.containsKey(fieldId)) {
            return mediaFiles.get(fieldId);
        }
        
        return new ArrayList<>();
    }
    
    /**
     * 清除字段的所有媒体文件
     * @param fieldId 字段ID
     */
    public void clearMediaFiles(String fieldId) {
        if (TextUtils.isEmpty(fieldId)) {
            return;
        }
        
        mediaFiles.remove(fieldId);
        
        // 同时清除照片信息
        photoInfoMap.remove(fieldId);
        
        // 清除缓存文件
        try {
            File cacheDir = activity.getCacheDir();
            File photoInfoFile = new File(cacheDir, "photo_info_" + fieldId + ".json");
            if (photoInfoFile.exists()) {
                photoInfoFile.delete();
                        }
                    } catch (Exception e) {
            Log.e(TAG, "清除照片信息缓存文件失败: " + e.getMessage());
        }
        
        // 清除UI
        FieldFile fieldFile = getFieldFileView(fieldId);
        if (fieldFile != null) {
            fieldFile.clearPreview();
        }
    }
    
    /**
     * 添加照片信息
     * @param fieldId 字段ID
     * @param photoInfo 照片信息对象
     * @param autoPlay 是否自动播放（视频类型才有效）
     * @return 是否添加成功
     */
    public boolean addPhotoInfo(String fieldId, PhotoInfo photoInfo, boolean autoPlay) {
        Log.d(TAG, "========== 开始添加照片信息 ==========");
        
        if (TextUtils.isEmpty(fieldId) || photoInfo == null || TextUtils.isEmpty(photoInfo.getFilePath())) {
            Log.e(TAG, "添加照片信息参数无效: fieldId=" + fieldId + 
                  ", photoInfo=" + (photoInfo == null ? "null" : "非null") + 
                  ", filePath=" + (photoInfo != null ? photoInfo.getFilePath() : "null"));
            return false;
        }

        String filePath = photoInfo.getFilePath();
        Log.d(TAG, "尝试添加照片: " + filePath);
        
        File file = new File(filePath);
        if (!file.exists()) {
            Log.e(TAG, "文件不存在: " + filePath);
            return false;
        }
        
        Log.d(TAG, "文件验证成功，大小: " + file.length() + " 字节");
        Log.d(TAG, "添加照片信息到表单: fieldId=" + fieldId + ", filePath=" + filePath + 
              ", 位置信息: 经度=" + photoInfo.getLongitude() + 
              ", 纬度=" + photoInfo.getLatitude() + 
              ", 方位角=" + photoInfo.getDirection());

        try {
            // 确保mediaFiles集合已初始化
            if (mediaFiles == null) {
                Log.w(TAG, "mediaFiles为null，正在初始化...");
                mediaFiles = new HashMap<>();
            }
            
            // 先添加到普通媒体文件集合，以保持兼容性
            if (!mediaFiles.containsKey(fieldId)) {
                mediaFiles.put(fieldId, new ArrayList<>());
                Log.d(TAG, "为字段 " + fieldId + " 创建了新的媒体文件列表");
            }
            
            List<String> files = mediaFiles.get(fieldId);
            if (!files.contains(filePath)) {
                files.add(filePath);
                Log.d(TAG, "将文件路径添加到mediaFiles集合: " + filePath);
            } else {
                Log.d(TAG, "文件路径已存在于mediaFiles集合中: " + filePath);
            }
            
            // 确保photoInfoMap集合已初始化
            if (photoInfoMap == null) {
                Log.w(TAG, "photoInfoMap为null，正在初始化...");
                photoInfoMap = new HashMap<>();
            }
            
            // 添加到照片信息集合
            if (!photoInfoMap.containsKey(fieldId)) {
                photoInfoMap.put(fieldId, new ArrayList<>());
                Log.d(TAG, "为字段 " + fieldId + " 创建了新的照片信息列表");
            }
            
            List<PhotoInfo> photoInfos = photoInfoMap.get(fieldId);
            
            // 检查是否已存在相同文件路径的照片信息
            boolean exists = false;
            for (int i = 0; i < photoInfos.size(); i++) {
                if (photoInfos.get(i).getFilePath().equals(filePath)) {
                    // 如果存在则替换
                    photoInfos.set(i, photoInfo);
                    exists = true;
                    Log.d(TAG, "更新已存在的照片信息: " + filePath);
                    break;
                }
            }
                    
            // 如果不存在则添加
            if (!exists) {
                photoInfos.add(photoInfo);
                Log.d(TAG, "添加新的照片信息: " + filePath);
            }
            
            // 保存照片信息到缓存文件
            savePhotoInfoToCache(fieldId, photoInfos);
            Log.d(TAG, "已保存照片信息到缓存");

            // 判断文件类型，执行相应的处理
            boolean isSignature = MediaTypeUtils.isSignatureFile(filePath, fieldFiles);
            boolean isVideo = MediaTypeUtils.isVideoFile(filePath);
            boolean isPhoto = MediaTypeUtils.isPhotoFile(filePath);

            Log.d(TAG, "字段 " + fieldId + " 的类型分析: 是签名: " + isSignature + ", 是视频: " + isVideo + ", 是照片: " + isPhoto);

            try {
                // 获取字段对应的FieldFile视图
                FieldFile fieldFileView = getFieldFileView(fieldId);
                Log.d(TAG, "获取到字段FieldFile视图: " + (fieldFileView != null ? "成功" : "失败"));
                
                // 确保有对应的ImageAdapter
                if (!imageAdapters.containsKey(fieldId)) {
                    Log.w(TAG, "字段 " + fieldId + " 没有对应的ImageAdapter，正在创建新的适配器...");
                    ImageAdapter newAdapter = new ImageAdapter(activity);
                    imageAdapters.put(fieldId, newAdapter);
                }
                
                // 获取对应字段的ImageAdapter
                ImageAdapter adapter = imageAdapters.get(fieldId);
                if (adapter != null) {
                    Log.d(TAG, "获取到字段对应的适配器: 成功，当前项目数: " + adapter.getItemCount());
                    
                    // 检查文件是否已在适配器中
                    boolean fileExistsInAdapter = false;
                    for (ImageAdapter.FileItem item : adapter.getFileItems()) {
                        if (item.getFilePath() != null && item.getFilePath().equals(filePath)) {
                            fileExistsInAdapter = true;
                            Log.d(TAG, "文件已存在于适配器中: " + filePath);
                            break;
                        }
                    }
                    
                    if (!fileExistsInAdapter) {
                        // 创建一个新的FileItem并添加到适配器
                        boolean isImageFile = isPhoto && !isVideo;
                        boolean isVideoFile = isVideo;
                        
                        // 直接使用文件路径创建FileItem
                        ImageAdapter.FileItem fileItem = new ImageAdapter.FileItem(filePath, isImageFile, isVideoFile);
                        adapter.addItem(fileItem);
                        
                        // 强制刷新适配器UI
                        adapter.notifyDataSetChanged();
                        
                        Log.d(TAG, "已添加文件到适配器并刷新UI: " + filePath);
                    }
                } else {
                    Log.e(TAG, "获取适配器失败，无法添加文件到适配器");
                }
                
                // 向字段文件集合添加文件
                if (fieldFiles == null) {
                    Log.w(TAG, "fieldFiles为null，正在初始化...");
                    fieldFiles = new HashMap<>();
                }
                
                List<FieldFile> fieldFileList = fieldFiles.get(fieldId);
                if (fieldFileList == null) {
                    fieldFileList = new ArrayList<>();
                    fieldFiles.put(fieldId, fieldFileList);
                    Log.d(TAG, "为字段 " + fieldId + " 创建了新的FieldFile列表");
                }
                
                // 检查是否已存在该文件
                boolean fileExists = false;
                for (FieldFile field : fieldFileList) {
                    if (field.getPath() != null && field.getPath().equals(filePath)) {
                        fileExists = true;
                        // 更新位置信息
                        field.setLatitude(photoInfo.getLatitude());
                        field.setLongitude(photoInfo.getLongitude());
                        field.setDirection(photoInfo.getDirection());
                        // 设置fileTime
                        field.setFileTime(photoInfo.getFileTime());
                        Log.d(TAG, "更新现有FieldFile对象的位置信息和fileTime");
                        break;
                    }
                }
                
                // 如果不存在则添加新的FieldFile
                if (!fileExists) {
                    FieldFile newFile = new FieldFile(filePath, "image", 
                                                   photoInfo.getLatitude(), 
                                                   photoInfo.getLongitude(), 
                                                   photoInfo.getDirection());
                    // 设置fileTime
                    newFile.setFileTime(photoInfo.getFileTime());
                    fieldFileList.add(newFile);
                    Log.d(TAG, "添加新的FieldFile对象到字段文件集合");
                } else {
                    // 同时更新已存在文件的fileTime
                    for (FieldFile field : fieldFileList) {
                        if (field.getPath() != null && field.getPath().equals(filePath)) {
                            field.setFileTime(photoInfo.getFileTime());
                            Log.d(TAG, "更新现有FieldFile对象的fileTime");
                            break;
                        }
                    }
                }
                
                // 根据文件类型进行不同处理
                if (isSignature) {
                    // 处理签名
                    Log.d(TAG, "处理签名文件: " + filePath);
                    MediaTypeUtils.previewSignature(fieldId, filePath, fieldFileView, formViews);
                } else if (isVideo) {
                    // 处理视频
                    Log.d(TAG, "处理视频文件: " + filePath);
                    MediaTypeUtils.previewVideo(fieldId, filePath, autoPlay, imageAdapters, fieldFileView);
                } else if (isPhoto) {
                    // 处理照片
                    Log.d(TAG, "处理照片文件: " + filePath);
                    try {
                        MediaTypeUtils.previewPhoto(fieldId, filePath, imageAdapters, fieldFileView);
                        Log.d(TAG, "MediaTypeUtils.previewPhoto执行成功");
                    } catch (Exception e) {
                        Log.e(TAG, "MediaTypeUtils.previewPhoto执行失败: " + e.getMessage(), e);
                    }
                } else {
                    Log.w(TAG, "未知的文件类型: " + filePath);
                    Log.d(TAG, "========== 结束添加照片信息（失败） ==========");
                    return false;
                }
                
                // 通知FormProcessor更新
                if (onFormProcessorUpdateListener != null) {
                    Log.d(TAG, "通知FormProcessor更新文件变更");
                    onFormProcessorUpdateListener.onFieldFilesChanged();
                } else {
                    Log.w(TAG, "onFormProcessorUpdateListener为null，无法通知更新");
                }
                
                Log.d(TAG, "========== 结束添加照片信息（成功） ==========");
                return true;
            } catch (Exception e) {
                Log.e(TAG, "添加照片信息过程中出错: " + e.getMessage(), e);
                e.printStackTrace();
                Log.d(TAG, "========== 结束添加照片信息（异常） ==========");
                return false;
            }
        } catch (Exception e) {
            Log.e(TAG, "添加照片信息出现未捕获异常: " + e.getMessage(), e);
            e.printStackTrace();
            Log.d(TAG, "========== 结束添加照片信息（未捕获异常） ==========");
            return false;
        }
    }
    
    /**
     * 保存照片信息到缓存文件
     * @param fieldId 字段ID
     * @param photoInfos 照片信息列表
     */
    private void savePhotoInfoToCache(String fieldId, List<PhotoInfo> photoInfos) {
        try {
            // 使用Gson将照片信息列表转换为JSON
            Gson gson = new Gson();
            String json = gson.toJson(photoInfos);
            
            // 将JSON保存到缓存文件
            File cacheDir = activity.getCacheDir();
            File photoInfoFile = new File(cacheDir, "photo_info_" + fieldId + ".json");
            
            FileOutputStream fos = new FileOutputStream(photoInfoFile);
            fos.write(json.getBytes());
            fos.close();
            
            Log.d(TAG, "已保存照片信息到缓存: " + photoInfoFile.getAbsolutePath());
        } catch (Exception e) {
            Log.e(TAG, "保存照片信息到缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取字段的照片信息列表
     * @param fieldId 字段ID
     * @return 照片信息列表
     */
    public List<PhotoInfo> getPhotoInfos(String fieldId) {
        if (TextUtils.isEmpty(fieldId)) {
            return new ArrayList<>();
        }
        
        if (photoInfoMap.containsKey(fieldId)) {
            return photoInfoMap.get(fieldId);
        }
        
        // 尝试从缓存加载
        List<PhotoInfo> cachedInfos = loadPhotoInfosFromCache(fieldId);
        if (cachedInfos != null && !cachedInfos.isEmpty()) {
            photoInfoMap.put(fieldId, cachedInfos);
            return cachedInfos;
        }
        
        return new ArrayList<>();
    }
    
    /**
     * 从缓存加载照片信息
     * @param fieldId 字段ID
     * @return 照片信息列表
     */
    private List<PhotoInfo> loadPhotoInfosFromCache(String fieldId) {
        try {
            File cacheDir = activity.getCacheDir();
            File photoInfoFile = new File(cacheDir, "photo_info_" + fieldId + ".json");
            
            if (!photoInfoFile.exists()) {
                return null;
            }
            
            StringBuilder json = new StringBuilder();
            FileInputStream fis = new FileInputStream(photoInfoFile);
            BufferedReader reader = new BufferedReader(new InputStreamReader(fis));
            String line;
            while ((line = reader.readLine()) != null) {
                json.append(line);
            }
            reader.close();
            
            // 使用Gson解析JSON
            Gson gson = new Gson();
            Type listType = new TypeToken<ArrayList<PhotoInfo>>(){}.getType();
            List<PhotoInfo> photoInfos = gson.fromJson(json.toString(), listType);
            
            Log.d(TAG, "已从缓存加载照片信息: " + photoInfoFile.getAbsolutePath());
            return photoInfos;
        } catch (Exception e) {
            Log.e(TAG, "从缓存加载照片信息失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取字段对应的FieldFile视图
     * @param fieldId 字段ID
     * @return FieldFile视图
     */
    private FieldFile getFieldFileView(String fieldId) {
        if (TextUtils.isEmpty(fieldId)) {
            Log.e(TAG, "获取字段FieldFile视图失败：fieldId为空");
            return null;
        }
        
        try {
            // 检查是否在fieldFiles映射中已经有该字段的文件
            Log.d(TAG, "尝试获取字段 " + fieldId + " 的FieldFile视图");
            
            if (fieldFiles != null && fieldFiles.containsKey(fieldId)) {
                List<FieldFile> files = fieldFiles.get(fieldId);
                if (files != null && !files.isEmpty()) {
                    // 返回第一个文件对象
                    FieldFile firstFile = files.get(0);
                    Log.d(TAG, "从字段文件映射中找到已存在的FieldFile: " + firstFile.getPath());
                    
                    // 确保FieldFile有正确的预览视图设置
                    setPreviewViewsForFieldFile(fieldId, firstFile);
                    
                    return firstFile;
                } else {
                    Log.d(TAG, "字段文件列表为空");
                }
            } else {
                Log.d(TAG, "字段 " + fieldId + " 在字段文件映射中不存在");
            }
            
            // 如果找不到，创建一个新的FieldFile对象
            FieldFile fieldFile = new FieldFile();
            Log.d(TAG, "创建新的FieldFile对象");
            
            // 设置预览视图
            setPreviewViewsForFieldFile(fieldId, fieldFile);
            
            // 创建一个新的列表并添加这个FieldFile，确保在fieldFiles中维护这个引用
            List<FieldFile> newFiles = new ArrayList<>();
            newFiles.add(fieldFile);
            
            // 将新的文件列表添加到fieldFiles映射中
            if (fieldFiles != null) {
                fieldFiles.put(fieldId, newFiles);
                Log.d(TAG, "将新创建的FieldFile添加到字段文件映射中: " + fieldId);
            } else {
                Log.e(TAG, "fieldFiles映射为null，无法添加新创建的FieldFile");
            }
            
            return fieldFile;
        } catch (Exception e) {
            Log.e(TAG, "获取字段FieldFile视图时出错: " + e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 为FieldFile设置正确的预览视图
     * @param fieldId 字段ID
     * @param fieldFile FieldFile对象
     */
    private void setPreviewViewsForFieldFile(String fieldId, FieldFile fieldFile) {
        if (formViews != null && fieldFile != null) {
            // 尝试找到图片预览视图
            View imagePreviewView = formViews.get(fieldId + "_preview");
            if (imagePreviewView instanceof ImageView) {
                fieldFile.setPreviewImageView((ImageView) imagePreviewView);
                Log.d(TAG, "为FieldFile设置图片预览视图: " + fieldId + "_preview");
            } else {
                Log.d(TAG, "找不到图片预览视图或类型不匹配: " + fieldId + "_preview");
            }
            
            // 尝试找到视频预览视图
            View videoPreviewView = formViews.get(fieldId + "_video");
            if (videoPreviewView != null) {
                fieldFile.setVideoPreviewView(videoPreviewView);
                Log.d(TAG, "为FieldFile设置视频预览视图: " + fieldId + "_video");
            } else {
                Log.d(TAG, "找不到视频预览视图: " + fieldId + "_video");
            }
        } else {
            Log.w(TAG, "formViews为空或fieldFile为空，无法设置预览视图");
        }
    }
}
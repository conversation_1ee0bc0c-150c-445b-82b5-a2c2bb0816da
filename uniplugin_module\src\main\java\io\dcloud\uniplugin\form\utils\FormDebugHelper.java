package io.dcloud.uniplugin.form.utils;

import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;

import org.json.JSONObject;

import java.util.Map;

/**
 * 表单调试助手，用于分析和解决表单数据处理问题
 */
public class FormDebugHelper {
    private static final String TAG = "FormDebugHelper";

    /**
     * 跟踪表单数据保存过程，打印详细日志
     * @param formViews 表单视图映射
     * @param formData 保存的表单数据
     */
    public static void traceFormDataSave(Map<String, View> formViews, JSONObject formData) {
        try {
            Log.d(TAG, "===== 跟踪表单数据保存 =====");
            Log.d(TAG, "表单视图总数: " + formViews.size());
            Log.d(TAG, "表单数据字段总数: " + formData.length());

            // 遍历所有视图，特别关注下拉框
            for (Map.Entry<String, View> entry : formViews.entrySet()) {
                String fieldId = entry.getKey();
                View view = entry.getValue();

                if (view instanceof Spinner) {
                    Spinner spinner = (Spinner) view;
                    Object selectedItem = spinner.getSelectedItem();
                    String viewValue = selectedItem != null ? selectedItem.toString() : "";
                    
                    // 检查是否是带"其他"选项的下拉框
                    Object tag = spinner.getTag();
                    if (tag instanceof EditText) {
                        EditText otherInput = (EditText) tag;
                        String otherText = otherInput.getText().toString();
                        
                        if (otherInput.getVisibility() == View.VISIBLE && !TextUtils.isEmpty(otherText)) {
                            Log.d(TAG, "下拉框 [" + fieldId + "] 选中'其他'选项，自定义输入值: " + otherText);
                            viewValue = otherText; // 应该保存的值
                        }
                    }
                    
                    // 检查保存的值是否正确
                    String savedValue = formData.optString(fieldId, null);
                    Log.d(TAG, "下拉框 [" + fieldId + "] 视图值: " + viewValue + ", 保存值: " + savedValue);
                    
                    if (!TextUtils.equals(viewValue, savedValue)) {
                        Log.w(TAG, "警告: 下拉框 [" + fieldId + "] 值不匹配!");
                    }
                } else if (view instanceof LinearLayout) {
                    // 可能是包含Spinner的容器
                    Object containerTag = view.getTag();
                    if (containerTag instanceof Spinner) {
                        Spinner spinner = (Spinner) containerTag;
                        Object selectedItem = spinner.getSelectedItem();
                        String displayValue = selectedItem != null ? selectedItem.toString() : "";
                        
                        // 检查是否有"其他"输入框
                        Object spinnerTag = spinner.getTag();
                        if (spinnerTag instanceof EditText) {
                            EditText otherInput = (EditText) spinnerTag;
                            String otherText = otherInput.getText().toString();
                            
                            if (otherInput.getVisibility() == View.VISIBLE && !TextUtils.isEmpty(otherText)) {
                                Log.d(TAG, "容器内下拉框 [" + fieldId + "] 选中'其他'选项，自定义输入值: " + otherText);
                                displayValue = otherText; // 应该保存的值
                            }
                        }
                        
                        // 检查保存的值是否正确
                        String savedValue = formData.optString(fieldId, null);
                        Log.d(TAG, "容器内下拉框 [" + fieldId + "] 显示值: " + displayValue + ", 保存值: " + savedValue);
                        
                        if (!TextUtils.equals(displayValue, savedValue)) {
                            Log.w(TAG, "警告: 容器内下拉框 [" + fieldId + "] 值不匹配!");
                        }
                    }
                }
            }
            
            Log.d(TAG, "完整表单数据: " + formData.toString());
            Log.d(TAG, "===== 表单数据保存跟踪结束 =====");
        } catch (Exception e) {
            Log.e(TAG, "跟踪表单数据保存出错: " + e.getMessage());
        }
    }
    
    /**
     * 跟踪表单数据恢复过程，打印详细日志
     * @param formViews 表单视图映射
     * @param formData 恢复的表单数据
     */
    public static void traceFormDataRestore(Map<String, View> formViews, Map<String, String> formData) {
        try {
            Log.d(TAG, "===== 跟踪表单数据恢复 =====");
            Log.d(TAG, "表单视图总数: " + formViews.size());
            Log.d(TAG, "表单数据字段总数: " + formData.size());
            
            // 特别关注下拉框字段
            for (String fieldId : formData.keySet()) {
                String value = formData.get(fieldId);
                View view = formViews.get(fieldId);
                
                if (view == null) {
                    Log.w(TAG, "字段 [" + fieldId + "] 找不到对应的视图");
                    continue;
                }
                
                if (view instanceof Spinner) {
                    Log.d(TAG, "标准下拉框 [" + fieldId + "] 将恢复值: " + value);
                } else if (view instanceof LinearLayout) {
                    Object containerTag = view.getTag();
                    if (containerTag instanceof Spinner) {
                        Log.d(TAG, "容器内下拉框 [" + fieldId + "] 将恢复值: " + value);
                    }
                }
            }
            
            Log.d(TAG, "表单数据项: " + formData);
            Log.d(TAG, "===== 表单数据恢复跟踪结束 =====");
        } catch (Exception e) {
            Log.e(TAG, "跟踪表单数据恢复出错: " + e.getMessage());
        }
    }
    
    /**
     * 验证下拉框视图状态是否与期望一致
     * @param view 视图（Spinner或包含Spinner的容器）
     * @param expectedValue 期望值
     * @return 是否匹配
     */
    public static boolean verifyDropdownValue(View view, String expectedValue) {
        try {
            if (view == null || TextUtils.isEmpty(expectedValue)) {
                return false;
            }
            
            Spinner spinner = null;
            EditText otherInput = null;
            
            if (view instanceof Spinner) {
                spinner = (Spinner) view;
                Object tag = spinner.getTag();
                if (tag instanceof EditText) {
                    otherInput = (EditText) tag;
                }
            } else if (view instanceof LinearLayout) {
                Object containerTag = view.getTag();
                if (containerTag instanceof Spinner) {
                    spinner = (Spinner) containerTag;
                    Object spinnerTag = spinner.getTag();
                    if (spinnerTag instanceof EditText) {
                        otherInput = (EditText) spinnerTag;
                    }
                }
            }
            
            if (spinner == null) {
                Log.d(TAG, "验证下拉框: 找不到Spinner组件");
                return false;
            }
            
            Object selectedItem = spinner.getSelectedItem();
            String currentValue = selectedItem != null ? selectedItem.toString() : "";
            
            // 检查是否选中了"其他"选项
            boolean isOtherOption = currentValue.equals("其他") || 
                                     currentValue.equals("其它") || 
                                     currentValue.toLowerCase().equals("other");
                                     
            if (isOtherOption && otherInput != null) {
                String otherText = otherInput.getText().toString();
                
                Log.d(TAG, "验证下拉框: 选中'其他'选项, 输入框值=" + otherText + ", 期望值=" + expectedValue);
                
                // 如果选中"其他"选项，应该比较输入框中的值
                return TextUtils.equals(otherText, expectedValue);
            } else {
                // 比较普通选项
                Log.d(TAG, "验证下拉框: 当前值=" + currentValue + ", 期望值=" + expectedValue);
                return TextUtils.equals(currentValue, expectedValue);
            }
        } catch (Exception e) {
            Log.e(TAG, "验证下拉框值出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 跟踪下拉框状态，打印详细日志
     * @param fieldId 字段ID
     * @param view 视图（LinearLayout或Spinner）
     * @param prefix 日志前缀（可用于区分不同调用点）
     */
    public static void traceDropdownState(String fieldId, View view, String prefix) {
        try {
            if (TextUtils.isEmpty(fieldId) || view == null) {
                Log.w(TAG, prefix + ": 参数为空，无法跟踪下拉框状态");
                return;
            }

            Log.d(TAG, "===== " + prefix + ": 跟踪下拉框状态 " + fieldId + " =====");
            
            Spinner spinner = null;
            EditText otherInput = null;
            
            // 获取spinner和otherInput引用
            if (view instanceof Spinner) {
                spinner = (Spinner) view;
                Object tag = spinner.getTag();
                if (tag instanceof EditText) {
                    otherInput = (EditText) tag;
                }
            } else if (view instanceof LinearLayout) {
                Object containerTag = view.getTag();
                if (containerTag instanceof Spinner) {
                    spinner = (Spinner) containerTag;
                    Object spinnerTag = spinner.getTag();
                    if (spinnerTag instanceof EditText) {
                        otherInput = (EditText) spinnerTag;
                    }
                }
            }
            
            if (spinner == null) {
                Log.w(TAG, prefix + ": 找不到Spinner组件");
                return;
            }
            
            // 记录当前选中状态
            Object selectedItem = spinner.getSelectedItem();
            int position = spinner.getSelectedItemPosition();
            
            Log.d(TAG, prefix + ": 当前选中位置: " + position);
            Log.d(TAG, prefix + ": 当前选中项: " + (selectedItem != null ? selectedItem.toString() : "null"));
            
            // 记录"其他"输入框状态
            if (otherInput != null) {
                Log.d(TAG, prefix + ": 其他输入框可见性: " + (otherInput.getVisibility() == View.VISIBLE ? "可见" : "不可见"));
                Log.d(TAG, prefix + ": 其他输入框内容: " + otherInput.getText().toString());
            } else {
                Log.d(TAG, prefix + ": 未找到其他输入框");
            }
            
            Log.d(TAG, "===== " + prefix + ": 跟踪下拉框状态结束 =====");
        } catch (Exception e) {
            Log.e(TAG, prefix + ": 跟踪下拉框状态出错: " + e.getMessage(), e);
        }
    }
} 
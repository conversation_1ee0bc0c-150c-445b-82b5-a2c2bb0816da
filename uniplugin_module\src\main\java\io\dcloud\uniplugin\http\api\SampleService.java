package io.dcloud.uniplugin.http.api;

import java.util.List;
import java.util.Map;

import io.dcloud.uniplugin.model.ApiResponse;
import io.dcloud.uniplugin.model.Sample;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.DELETE;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * 采土袋API服务接口
 * 负责处理与采土袋相关的所有网络请求
 */
public interface SampleService {
    
    /**
     * 根据评价单元ID获取关联采土袋列表
     * @param pjdyId 评价单元ID（注意：这是数字ID而非标识码）
     * @return 采土袋列表
     */
    @GET("pjdy/dccy/ctd/list/pjdy")
    Call<ApiResponse<List<Sample>>> getSamplesByPjdyId(@Query("pjdyId") String pjdyId);
    
    /**
     * 添加样品
     * @param sampleMap 样品对象Map，包含：
     *                 - pjdyId：评价单元ID
     *                 - pjdybh：评价单元标识码
     *                 - ctdbh：采土袋编号(样品编号)
     *                 - yplx：样品类型
     *                 - ypzl：样品重量
     * @return 结果
     */
    @POST("pjdy/dccy/ctd/upload")
    Call<ApiResponse<Boolean>> addSample(@Body Map<String, Object> sampleMap);
    
    /**
     * 更新样品
     * @param sampleMap 样品对象Map，包含：
     *                 - id：样品ID
     *                 - pjdyId：评价单元ID
     *                 - pjdybh：评价单元标识码
     *                 - ctdbh：采土袋编号(样品编号)
     *                 - yplx：样品类型
     *                 - ypzl：样品重量
     * @return 结果
     */
    @POST("pjdy/dccy/ctd/update")
    Call<ApiResponse<Void>> updateSample(@Body Map<String, Object> sampleMap);
    
    /**
     * 根据采土袋编号获取采土袋信息
     * @param ctdbh 采土袋编号
     * @return 采土袋信息
     */
    @GET("pjdy/dccy/ctd/detail/ctdbh")
    Call<ApiResponse<Sample>> getSampleDetail(@Query("ctdbh") String ctdbh);
    
    /**
     * 删除样品
     * @param id 样品ID
     * @return 结果
     */
    @DELETE("pjdy/dccy/ctd/delete")
    Call<ApiResponse<Void>> deleteSample(@Query("ctdId") Long id);
} 
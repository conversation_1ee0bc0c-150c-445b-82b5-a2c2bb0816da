package io.dcloud.uniplugin.utils;

import android.app.DownloadManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.util.Log;
import android.widget.Toast;

import androidx.core.content.FileProvider;

import java.io.File;

/**
 * APK下载管理器
 * 用于处理应用内下载和安装APK文件
 */
public class AppDownloadManager {
    private static final String TAG = "AppDownloadManager";
    
    private Context context;
    private DownloadManager downloadManager;
    private long downloadId;
    private String fileName;
    private DownloadStatusListener listener;
    
    /**
     * 下载状态监听接口
     */
    public interface DownloadStatusListener {
        void onDownloadComplete(Uri fileUri);
        void onDownloadFailed(String error);
        void onDownloadProgress(int progress);
    }
    
    /**
     * 构造函数
     * @param context 上下文
     */
    public AppDownloadManager(Context context) {
        this.context = context;
        this.downloadManager = (DownloadManager) context.getSystemService(Context.DOWNLOAD_SERVICE);
    }
    
    /**
     * 设置下载状态监听器
     * @param listener 监听器
     */
    public void setDownloadStatusListener(DownloadStatusListener listener) {
        this.listener = listener;
    }
    
    /**
     * 下载APK文件
     * @param url 下载地址
     * @param title 通知栏标题
     * @param description 通知栏描述
     */
    public void downloadApk(String url, String title, String description) {
        // 注册下载完成广播接收器
        registerDownloadReceiver();
        
        // 创建下载目录
        File directory = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), "BCGDApp");
        if (!directory.exists()) {
            directory.mkdirs();
        }
        
        // 生成文件名
        fileName = "BCGD_" + System.currentTimeMillis() + ".apk";
        
        // 创建下载请求
        DownloadManager.Request request = new DownloadManager.Request(Uri.parse(url));
        request.setTitle(title);
        request.setDescription(description);
        request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED);
        request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, "BCGDApp/" + fileName);
        request.setMimeType("application/vnd.android.package-archive");
        
        // 开始下载
        downloadId = downloadManager.enqueue(request);
        
        // 启动下载进度监控
        startProgressMonitor();
    }
    
    /**
     * 注册下载完成广播接收器
     */
    private void registerDownloadReceiver() {
        IntentFilter filter = new IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE);
        BroadcastReceiver receiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                long id = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1);
                if (id == downloadId) {
                    checkDownloadStatus();
                }
            }
        };
        context.registerReceiver(receiver, filter);
    }
    
    /**
     * 检查下载状态
     */
    private void checkDownloadStatus() {
        DownloadManager.Query query = new DownloadManager.Query();
        query.setFilterById(downloadId);
        Cursor cursor = downloadManager.query(query);
        
        if (cursor.moveToFirst()) {
            int statusIndex = cursor.getColumnIndex(DownloadManager.COLUMN_STATUS);
            int reasonIndex = cursor.getColumnIndex(DownloadManager.COLUMN_REASON);
            int status = cursor.getInt(statusIndex);
            int reason = cursor.getInt(reasonIndex);
            
            switch (status) {
                case DownloadManager.STATUS_SUCCESSFUL:
                    // 下载成功，安装APK
                    String filePath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS) + "/BCGDApp/" + fileName;
                    installApk(filePath);
                    break;
                    
                case DownloadManager.STATUS_FAILED:
                    // 下载失败
                    String failReason = "下载失败，错误代码: " + reason;
                    Log.e(TAG, failReason);
                    if (listener != null) {
                        listener.onDownloadFailed(failReason);
                    }
                    break;
            }
        }
        cursor.close();
    }
    
    /**
     * 启动下载进度监控
     */
    private void startProgressMonitor() {
        new Thread(() -> {
            boolean downloading = true;
            while (downloading) {
                DownloadManager.Query query = new DownloadManager.Query();
                query.setFilterById(downloadId);
                Cursor cursor = downloadManager.query(query);
                
                if (cursor.moveToFirst()) {
                    int statusIndex = cursor.getColumnIndex(DownloadManager.COLUMN_STATUS);
                    int bytesDownloadedIndex = cursor.getColumnIndex(DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR);
                    int bytesTotalIndex = cursor.getColumnIndex(DownloadManager.COLUMN_TOTAL_SIZE_BYTES);
                    
                    int status = cursor.getInt(statusIndex);
                    long bytesDownloaded = cursor.getLong(bytesDownloadedIndex);
                    long bytesTotal = cursor.getLong(bytesTotalIndex);
                    
                    if (status == DownloadManager.STATUS_SUCCESSFUL || status == DownloadManager.STATUS_FAILED) {
                        downloading = false;
                    }
                    
                    if (bytesTotal > 0) {
                        int progress = (int) ((bytesDownloaded * 100) / bytesTotal);
                        if (listener != null) {
                            final int finalProgress = progress;
                            new android.os.Handler(context.getMainLooper()).post(() -> {
                                listener.onDownloadProgress(finalProgress);
                            });
                        }
                    }
                } else {
                    downloading = false;
                }
                
                cursor.close();
                
                try {
                    Thread.sleep(1000); // 每秒更新一次进度
                } catch (InterruptedException e) {
                    Log.e(TAG, "Progress monitoring interrupted", e);
                    Thread.currentThread().interrupt();
                    downloading = false;
                }
            }
        }).start();
    }
    
    /**
     * 安装APK
     * @param filePath APK文件路径
     */
    private void installApk(String filePath) {
        File file = new File(filePath);
        if (!file.exists()) {
            Log.e(TAG, "APK文件不存在: " + filePath);
            if (listener != null) {
                listener.onDownloadFailed("APK文件不存在");
            }
            return;
        }
        
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        
        Uri apkUri;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            // Android 7.0及以上版本需要使用FileProvider
            apkUri = FileProvider.getUriForFile(context, "uni.dcloud.io.uniplugin_module.fileprovider", file);
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        } else {
            apkUri = Uri.fromFile(file);
        }
        
        intent.setDataAndType(apkUri, "application/vnd.android.package-archive");
        
        try {
            context.startActivity(intent);
            if (listener != null) {
                listener.onDownloadComplete(apkUri);
            }
        } catch (Exception e) {
            Log.e(TAG, "安装APK失败", e);
            Toast.makeText(context, "安装APK失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
            if (listener != null) {
                listener.onDownloadFailed("安装APK失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 取消下载
     */
    public void cancelDownload() {
        if (downloadId != 0) {
            downloadManager.remove(downloadId);
        }
    }
}
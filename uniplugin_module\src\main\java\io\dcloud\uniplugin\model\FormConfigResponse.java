package io.dcloud.uniplugin.model;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class FormConfigResponse {

    private List<FieldGroup> fields;

    public List<FieldGroup> getFields() {
        return fields;
    }

    public void setFields(List<FieldGroup> fields) {
        this.fields = fields;
    }

    public static class FieldGroup {
        private String name;
        private String type;
        private List<Field> fields;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public List<Field> getFields() {
            return fields;
        }

        public void setFields(List<Field> fields) {
            this.fields = fields;
        }

        @Override
        public String toString() {
            return "FieldGroup{" +
                    "name='" + name + '\'' +
                    ", type='" + type + '\'' +
                    ", fields=" + fields +
                    '}';
        }
    }
    public static class Field {
        @SerializedName("fieldName")
        private String fieldName;

        @SerializedName("fieldRemark")
        private String fieldRemark;

        @SerializedName("fieldType")
        private String fieldType;

        @SerializedName("fieldGroup")
        private String fieldGroup;

        @SerializedName("fieldLength")
        private Integer fieldLength;

        @SerializedName("fieldRequired")
        private Integer fieldRequired;

        @SerializedName("fieldElement")
        private String fieldElement;

        @SerializedName("fieldOptions")
        private String fieldOptions;

        @SerializedName("value")
        private Object value;

        @SerializedName("fieldMin")
        private Integer fieldMin;


        @SerializedName("fieldMax")
        private Integer fieldMax;


        public String getFieldName() {
            return fieldName;
        }

        public void setFieldName(String fieldName) {
            this.fieldName = fieldName;
        }

        public String getFieldRemark() {
            return fieldRemark;
        }

        public void setFieldRemark(String fieldRemark) {
            this.fieldRemark = fieldRemark;
        }

        public String getFieldType() {
            return fieldType;
        }

        public void setFieldType(String fieldType) {
            this.fieldType = fieldType;
        }

        public String getFieldGroup() {
            return fieldGroup;
        }

        public void setFieldGroup(String fieldGroup) {
            this.fieldGroup = fieldGroup;
        }

        public Integer getFieldLength() {
            return fieldLength;
        }

        public void setFieldLength(Integer fieldLength) {
            this.fieldLength = fieldLength;
        }

        public Integer getFieldRequired() {
            return fieldRequired;
        }

        public void setFieldRequired(Integer fieldRequired) {
            this.fieldRequired = fieldRequired;
        }

        public String getFieldElement() {
            return fieldElement;
        }

        public void setFieldElement(String fieldElement) {
            this.fieldElement = fieldElement;
        }

        public String getFieldOptions() {
            return fieldOptions;
        }

        public void setFieldOptions(String fieldOptions) {
            this.fieldOptions = fieldOptions;
        }

        public Object getValue() {
            return value;
        }

        public void setValue(Object value) {
            this.value = value;
        }

        public Integer getFieldMin() {
            return fieldMin;
        }

        public void setFieldMin(Integer fieldMin) {
            this.fieldMin = fieldMin;
        }

        public Integer getFieldMax() {
            return fieldMax;
        }

        public void setFieldMax(Integer fieldMax) {
            this.fieldMax = fieldMax;
        }
    }

    public static class FieldOption {
        private String label;
        private String value;

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }
}
package io.dcloud.uniplugin.fileUpload;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.github.chrisbanes.photoview.PhotoView;

import java.io.File;

import uni.dcloud.io.uniplugin_module.R;

public class ImagePreviewActivity extends AppCompatActivity {

    private PhotoView imageViewFullscreen;
    private Toolbar toolbar;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_image_preview);

        imageViewFullscreen = findViewById(R.id.imageViewFullscreen);
        toolbar = findViewById(R.id.toolbar_preview);
        
        // 设置Toolbar
        toolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        // 获取传递过来的图片路径
        String imagePath = getIntent().getStringExtra("imagePath");
        Uri imageUri = getIntent().getParcelableExtra("imageUri");

        // 加载图片
        if (imagePath != null && !imagePath.isEmpty()) {
            try {
                File imgFile = new File(imagePath);
                if (imgFile.exists()) {
                    Bitmap bitmap = BitmapFactory.decodeFile(imagePath);
                    if (bitmap != null) {
                        imageViewFullscreen.setImageBitmap(bitmap);
                    } else {
                        Log.e("ImagePreviewActivity", "Failed to decode bitmap from path: " + imagePath);
                        Toast.makeText(this, "无法加载图片", Toast.LENGTH_SHORT).show();
                        finish();
                    }
                } else {
                    Log.e("ImagePreviewActivity", "Image file does not exist: " + imagePath);
                    Toast.makeText(this, "图片文件不存在", Toast.LENGTH_SHORT).show();
                    finish();
                }
            } catch (Exception e) {
                Log.e("ImagePreviewActivity", "Error loading image: " + e.getMessage());
                Toast.makeText(this, "加载图片出错", Toast.LENGTH_SHORT).show();
                finish();
            }
        } else if (imageUri != null) {
            try {
                Bitmap bitmap = android.provider.MediaStore.Images.Media.getBitmap(getContentResolver(), imageUri);
                imageViewFullscreen.setImageBitmap(bitmap);
            } catch (Exception e) {
                Log.e("ImagePreviewActivity", "Error loading image from URI: " + e.getMessage());
                Toast.makeText(this, "加载图片出错", Toast.LENGTH_SHORT).show();
                finish();
            }
        } else {
            Toast.makeText(this, "没有图片可显示", Toast.LENGTH_SHORT).show();
            finish();
        }
    }
} 
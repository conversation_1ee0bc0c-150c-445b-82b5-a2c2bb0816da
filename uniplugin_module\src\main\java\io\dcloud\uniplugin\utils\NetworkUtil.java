package io.dcloud.uniplugin.utils;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;

/**
 * 网络工具类
 */
public class NetworkUtil {
    
    /**
     * 检查网络是否可用
     *
     * @param context 上下文
     * @return 如果网络可用返回true，否则返回false
     */
    public static boolean isNetworkAvailable(Context context) {
        if (context == null) {
            return false;
        }
        
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager == null) {
            return false;
        }
        
        NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
        return networkInfo != null && networkInfo.isConnected();
    }
    
    /**
     * 获取网络类型
     *
     * @param context 上下文
     * @return 返回网络类型（WIFI, 4G, 3G, 2G, UNKNOWN, NONE）
     */
    public static String getNetworkType(Context context) {
        if (context == null) {
            return "NONE";
        }
        
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager == null) {
            return "NONE";
        }
        
        NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
        if (networkInfo == null || !networkInfo.isConnected()) {
            return "NONE";
        }
        
        int type = networkInfo.getType();
        if (type == ConnectivityManager.TYPE_WIFI) {
            return "WIFI";
        } else if (type == ConnectivityManager.TYPE_MOBILE) {
            int subType = networkInfo.getSubtype();
            switch (subType) {
                case 1:  // GPRS
                case 2:  // EDGE
                case 4:  // CDMA
                case 7:  // 1xRTT
                case 11: // iDEN
                    return "2G";
                case 3:  // UMTS
                case 5:  // EVDO_0
                case 6:  // EVDO_A
                case 8:  // HSDPA
                case 9:  // HSUPA
                case 10: // HSPA
                case 12: // EVDO_B
                case 14: // EHRPD
                case 15: // HSPAP
                    return "3G";
                case 13: // LTE
                case 19: // LTE_CA
                    return "4G";
                default:
                    return "UNKNOWN";
            }
        } else {
            return "UNKNOWN";
        }
    }
} 
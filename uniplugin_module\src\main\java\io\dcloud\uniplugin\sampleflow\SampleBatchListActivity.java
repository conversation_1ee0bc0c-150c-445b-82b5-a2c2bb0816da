package io.dcloud.uniplugin.sampleflow;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.dcloud.uniplugin.http.RetrofitManager;
import io.dcloud.uniplugin.model.ApiResponse;
import io.dcloud.uniplugin.model.YplzBatch;
import io.dcloud.uniplugin.model.YplzPageResponse;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import uni.dcloud.io.uniplugin_module.R;

/**
 * 样品批次列表页面
 */
public class SampleBatchListActivity extends AppCompatActivity {
    
    private static final String TAG = "SampleBatchListActivity";
    
    private EditText editTextBatchCode;
    private EditText editTextReceiveOrg;
    private EditText editTextReceiverName;
    private EditText editTextSenderName;
    private EditText editTextSendOrg;
    private Button buttonSearch;
    private Button buttonReset;
    private SwipeRefreshLayout swipeRefreshLayout;
    private RecyclerView recyclerView;
    private LinearLayout layoutEmpty;
    private FloatingActionButton fabAdd;
    
    private SampleBatchAdapter adapter;
    private List<YplzBatch> batchList = new ArrayList<>();
    
    private int currentPage = 1;
    private int pageSize = 10;
    private boolean isLoading = false;
    private boolean hasMoreData = true;
    private boolean isSelectionMode = false;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sample_batch_list);
        
        // 检查是否是选择模式
        if (getIntent() != null) {
            isSelectionMode = getIntent().getBooleanExtra("isSelection", false);
        }
        
        // 设置ActionBar标题和返回按钮
        setTitle(isSelectionMode ? "选择样品批次" : "样品批次");
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        
        initViews();
        setupRecyclerView();
        setupListeners();
        loadData(true);
    }
    
    private void initViews() {
        editTextBatchCode = findViewById(R.id.editTextBatchCode);
        editTextReceiveOrg = findViewById(R.id.editTextReceiveOrg);
        editTextReceiverName = findViewById(R.id.editTextReceiverName);
        editTextSenderName = findViewById(R.id.editTextSenderName);
        editTextSendOrg = findViewById(R.id.editTextSendOrg);
        buttonSearch = findViewById(R.id.buttonSearch);
        buttonReset = findViewById(R.id.buttonReset);
        swipeRefreshLayout = findViewById(R.id.swipeRefreshLayout);
        recyclerView = findViewById(R.id.recyclerView);
        layoutEmpty = findViewById(R.id.layoutEmpty);
        fabAdd = findViewById(R.id.fabAdd);
        
        // 如果是选择模式，隐藏添加按钮
        if (isSelectionMode) {
            fabAdd.setVisibility(View.GONE);
        }
    }
    
    private void setupRecyclerView() {
        adapter = new SampleBatchAdapter(this, batchList);
        
        // 设置点击监听器
        if (isSelectionMode) {
            adapter.setOnBatchClickListener(batch -> {
                // 选择模式下返回选中的批次ID
                Intent resultIntent = new Intent();
                resultIntent.putExtra("batchId", batch.getId());
                setResult(RESULT_OK, resultIntent);
                finish();
            });
        }
        
        recyclerView.setAdapter(adapter);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        
        // 设置滚动监听器以实现分页加载
        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                
                LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
                int visibleItemCount = layoutManager.getChildCount();
                int totalItemCount = layoutManager.getItemCount();
                int firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition();
                
                if (!isLoading && hasMoreData) {
                    if ((visibleItemCount + firstVisibleItemPosition) >= totalItemCount
                            && firstVisibleItemPosition >= 0
                            && totalItemCount >= pageSize) {
                        currentPage++;
                        loadData(false);
                    }
                }
            }
        });
    }
    
    private void setupListeners() {
        // 下拉刷新
        swipeRefreshLayout.setOnRefreshListener(() -> {
            currentPage = 1;
            hasMoreData = true;
            loadData(true);
        });
        
        // 搜索按钮
        buttonSearch.setOnClickListener(v -> {
            currentPage = 1;
            hasMoreData = true;
            loadData(true);
        });
        
        // 重置按钮
        buttonReset.setOnClickListener(v -> {
            // 清空搜索条件
            editTextBatchCode.setText("");
            editTextReceiveOrg.setText("");
            editTextReceiverName.setText("");
            editTextSenderName.setText("");
            editTextSendOrg.setText("");
            
            // 重新搜索
            currentPage = 1;
            hasMoreData = true;
            loadData(true);
        });
        
        // 添加按钮
        fabAdd.setOnClickListener(v -> {
            // 跳转到创建批次页面，而不是直接创建样品流转批次
            Intent intent = new Intent(SampleBatchListActivity.this, CreateSampleBatchActivity.class);
            startActivityForResult(intent, 1001);
        });
    }
    
    private void loadData(boolean isRefresh) {
        if (isLoading) return;
        isLoading = true;
        
        if (isRefresh) {
            swipeRefreshLayout.setRefreshing(true);
        }
        
        // 获取搜索条件
        String batchCode = editTextBatchCode.getText().toString().trim();
        String receiveOrg = editTextReceiveOrg.getText().toString().trim();
        String receiver = editTextReceiverName.getText().toString().trim();
        String sender = editTextSenderName.getText().toString().trim();
        String sendOrg = editTextSendOrg.getText().toString().trim();
        
        // 发起网络请求
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("page", currentPage);
        queryParams.put("size", pageSize);
        
        // 添加搜索条件
        if (!TextUtils.isEmpty(batchCode)) {
            queryParams.put("batchCode", batchCode);
        }
        if (!TextUtils.isEmpty(sendOrg)) {
            queryParams.put("sendOrganization", sendOrg);
        }
        if (!TextUtils.isEmpty(sender)) {
            queryParams.put("senderName", sender);
        }
        if (!TextUtils.isEmpty(receiveOrg)) {
            queryParams.put("receiveOrganization", receiveOrg);
        }
        if (!TextUtils.isEmpty(receiver)) {
            queryParams.put("receiverName", receiver);
        }
        
        RetrofitManager.getInstance(this)
                .getSampleBatchServiceApi()
                .getSampleBatchPage(queryParams)
                .enqueue(new Callback<ApiResponse<YplzPageResponse<YplzBatch>>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<YplzPageResponse<YplzBatch>>> call, Response<ApiResponse<YplzPageResponse<YplzBatch>>> response) {
                        isLoading = false;
                        swipeRefreshLayout.setRefreshing(false);
                        
                        if (response.isSuccessful() && response.body() != null && response.body().getData() != null) {
                            YplzPageResponse<YplzBatch> data = response.body().getData();
                            
                            if (data != null) {
                                List<YplzBatch> newData = data.getList();
                                
                                if (isRefresh) {
                                    batchList.clear();
                                }
                                
                                if (newData != null && !newData.isEmpty()) {
                                    batchList.addAll(newData);
                                }
                                
                                hasMoreData = data.getPages() > currentPage;
                                updateUI();
                                
                                if (isRefresh && batchList.isEmpty()) {
                                    showError("未找到匹配的批次数据");
                                }
                            } else {
                                if (isRefresh) {
                                    batchList.clear();
                                    updateUI();
                                    showError("未找到匹配的批次数据");
                                }
                            }
                        }
                    }
                    
                    @Override
                    public void onFailure(Call<ApiResponse<YplzPageResponse<YplzBatch>>> call, Throwable t) {
                        isLoading = false;
                        swipeRefreshLayout.setRefreshing(false);
                        Log.e(TAG, "加载样品批次数据失败", t);
                        showError("加载失败：" + t.getMessage());
                    }
                });
    }
    
    private void updateUI() {
        adapter.notifyDataSetChanged();
        
        // 显示或隐藏空数据提示
        if (batchList.isEmpty()) {
            layoutEmpty.setVisibility(View.VISIBLE);
            recyclerView.setVisibility(View.GONE);
        } else {
            layoutEmpty.setVisibility(View.GONE);
            recyclerView.setVisibility(View.VISIBLE);
        }
    }
    
    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == 1001 && resultCode == RESULT_OK) {
            // 新增成功，刷新列表
            currentPage = 1;
            hasMoreData = true;
            loadData(true);
        }
    }
    
    @Override
    public boolean onOptionsItemSelected(android.view.MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
} 
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="请完善接收人信息并签名确认"
        android:textSize="16sp"
        android:textStyle="bold" />

    <!-- 接收人手机号 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="接收人手机号"
        android:textSize="14sp" />

    <EditText
        android:id="@+id/editTextReceiverPhone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="请输入接收人手机号"
        android:inputType="phone" />

    <!-- 签名区域标题 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="接收人签名"
        android:textSize="14sp" />

    <!-- 签名状态 -->
    <TextView
        android:id="@+id/textViewSignatureStatus"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="请在下方区域签名"
        android:textColor="#666666"
        android:textSize="12sp" />

    <!-- 签名区域 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout_marginBottom="8dp"
        android:background="#F0F0F0"
        android:padding="2dp">

        <io.dcloud.uniplugin.view.SignatureView
            android:id="@+id/signatureView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#FFFFFF" />
    </FrameLayout>

    <!-- 签名操作按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/buttonClearSignature"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="清除签名"
            android:layout_marginEnd="8dp"/>
            
        <Button
            android:id="@+id/buttonConfirmSignature"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:backgroundTint="#4CAF50"
            android:textColor="#FFFFFF"
            android:text="确认签名" />
    </LinearLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:orientation="horizontal">

        <Button
            android:id="@+id/buttonCancel"
            style="?android:attr/buttonBarButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="取消" />

        <Button
            android:id="@+id/buttonConfirm"
            style="?android:attr/buttonBarButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="确认接收" />
    </LinearLayout>

</LinearLayout> 
<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#0057a3" />
            <corners android:radius="4dp" />
            <padding android:left="12dp" android:right="12dp" android:top="6dp" android:bottom="6dp" />
        </shape>
    </item>
    
    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#0078d7" />
            <corners android:radius="4dp" />
            <padding android:left="12dp" android:right="12dp" android:top="6dp" android:bottom="6dp" />
        </shape>
    </item>
</selector> 
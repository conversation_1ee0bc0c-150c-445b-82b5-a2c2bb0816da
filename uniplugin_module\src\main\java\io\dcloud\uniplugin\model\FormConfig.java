package io.dcloud.uniplugin.model;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

/**
 * 表单配置模型
 */
public class FormConfig implements Serializable {
    
    @SerializedName("formId")
    private String formId;
    
    @SerializedName("formName")
    private String formName;
    
    @SerializedName("description")
    private String description;
    
    @SerializedName("version")
    private String version;
    
    @SerializedName("fieldGroups")
    private List<FormFieldGroup> fieldGroups;
    
    @SerializedName("fields")
    private List<FormFieldConfig> fields;
    
    @SerializedName("submitUrl")
    private String submitUrl;
    
    @SerializedName("isOfflineSupported")
    private boolean isOfflineSupported;
    
    // Getters and Setters
    public String getFormId() {
        return formId;
    }
    
    public void setFormId(String formId) {
        this.formId = formId;
    }
    
    public String getFormName() {
        return formName;
    }
    
    public void setFormName(String formName) {
        this.formName = formName;
    }
    
    /**
     * setTitle作为setFormName的别名，提高兼容性
     * @param title 表单标题
     */
    public void setTitle(String title) {
        this.formName = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getVersion() {
        return version;
    }
    
    public void setVersion(String version) {
        this.version = version;
    }
    
    public List<FormFieldGroup> getFieldGroups() {
        return fieldGroups;
    }
    
    public void setFieldGroups(List<FormFieldGroup> fieldGroups) {
        this.fieldGroups = fieldGroups;
    }
    
    public List<FormFieldConfig> getFields() {
        return fields;
    }
    
    public void setFields(List<FormFieldConfig> fields) {
        this.fields = fields;
    }
    
    public String getSubmitUrl() {
        return submitUrl;
    }
    
    public void setSubmitUrl(String submitUrl) {
        this.submitUrl = submitUrl;
    }
    
    public boolean isOfflineSupported() {
        return isOfflineSupported;
    }
    
    public void setOfflineSupported(boolean offlineSupported) {
        isOfflineSupported = offlineSupported;
    }
} 
package io.dcloud.uniplugin;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.recyclerview.widget.RecyclerView;

import com.esri.arcgisruntime.layers.FeatureLayer;
import com.esri.arcgisruntime.layers.GroupLayer;
import com.esri.arcgisruntime.layers.Layer;
import com.esri.arcgisruntime.loadable.LoadStatus;
import com.esri.arcgisruntime.mapping.ArcGISMap;
import com.esri.arcgisruntime.mapping.LayerList;
import com.esri.arcgisruntime.mapping.MobileMapPackage;
import com.esri.arcgisruntime.util.ListenableList;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.logging.Handler;

import uni.dcloud.io.uniplugin_module.R;

public class AddMMPK extends Activity implements OnLayerCheckedChangedListener{
    private GroupLayer projectAreaGroupLayer=new GroupLayer();;
    private LinearLayout dbtlLinearLayout;
    private Context context;
    private ArcGISMap mainArcGISMap;
    private List LayerName = new ArrayList<>() ;

    public FeatureLayer getTr_featureLayer() {
        return tr_featureLayer;
    }

    public void setTr_featureLayers(FeatureLayer tr_featureLayer) {
        this.tr_featureLayer = tr_featureLayer;
    }

    public FeatureLayer getTd_featureLayer() {
        return td_featureLayer;
    }

    public void setTd_featureLayer(FeatureLayer td_featureLayer) {
        this.td_featureLayer = td_featureLayer;
    }

    private FeatureLayer tr_featureLayer;
    private FeatureLayer td_featureLayer;

    public List getLayerName() {
        return LayerName;
    }

    public void setLayerName(List layerName) {
        LayerName = layerName;
    }

    public GroupLayer getProjectAreaGroupLayer() {
        return projectAreaGroupLayer;
    }

    public void setProjectAreaGroupLayer(GroupLayer projectAreaGroupLayer) {
        this.projectAreaGroupLayer = projectAreaGroupLayer;
    }

    public LinearLayout getDbtlLinearLayout() {
        return dbtlLinearLayout;
    }

    public void setDbtlLinearLayout(LinearLayout dbtlLinearLayout) {
        this.dbtlLinearLayout = dbtlLinearLayout;
    }

    public Context getContext() {
        return context;
    }

    public void setContext(Context context) {
        this.context = context;
    }

    public ArcGISMap getMainArcGISMap() {
        return mainArcGISMap;
    }

    public void setMainArcGISMap(ArcGISMap mainArcGISMap) {
        this.mainArcGISMap = mainArcGISMap;
    }


    public void LoadMMPK(String mmpkPath){
        try
        {
        MobileMapPackage mobileMapPackage = new MobileMapPackage(mmpkPath);
        projectAreaGroupLayer.setName("业务图层");
        mobileMapPackage.loadAsync();
        mobileMapPackage.addDoneLoadingListener(new Runnable() {
            @Override
            public void run() {
                LoadStatus mainLoadStasus = mobileMapPackage.getLoadStatus();
                if (mainLoadStasus == LoadStatus.LOADED) {
                    List<ArcGISMap> mainArcGISMapL = mobileMapPackage.getMaps();
                    ArcGISMap mainArcGISMapMMPK = mainArcGISMapL.get(0);
                    LayerList mainMMPKLL = mainArcGISMapMMPK.getOperationalLayers();
                    int h = mainMMPKLL.toArray().length;
                    for (int i = 0; i < h; i++) {
                        FeatureLayer mainFeatureLayer = (FeatureLayer) mainMMPKLL.get(0);
                        //报错Object is already owned.: Already owned.
                        //mapView.getMap().getOperationalLayers().add(mainFeatureLayer);
                        Log.d("mmpk当前加载的操作图层的名字", mainFeatureLayer.getName());
                        //获取土壤图的图层
                        if (Objects.equals(mainFeatureLayer.getName(),"土壤图"))
                        {
                            tr_featureLayer=mainFeatureLayer;
                        }
                        if(Objects.equals(mainFeatureLayer.getName(),"土地利用现状"))
                        {
                            td_featureLayer=mainFeatureLayer;
                        }
                        //将图层的名称存入数组中,这里的图层名称是别名
                        LayerName.add(mainFeatureLayer.getName());
                        /* //mainFeatureLayer.setOpacity(0.8f); */
                        mainArcGISMapMMPK.getOperationalLayers().remove(0);
                        //往grouplayer中加载featurelayer
                        projectAreaGroupLayer.getLayers().add(mainFeatureLayer);
//                            mainLayerList.add(mainFeatureLayer);
                        Log.d("第", i + "次循环");
                    }
//                    Intent intent = new Intent();
//                    intent.setAction("com.example.MY_ACTION");
//                    intent.putExtra("result", (CharSequence) LayerName);
//                    context.sendBroadcast(intent);
                    // add the group layer and other layers to the scene as operational layers
                    mainArcGISMap.getOperationalLayers().addAll(Arrays.asList(projectAreaGroupLayer));
                } else {
                    Exception e = mobileMapPackage.getLoadError();
                    e.printStackTrace();
                    Toast.makeText((MainActivity)context, "加载失败！", Toast.LENGTH_LONG).show();
                }
            }
        });
    }
        catch(Exception e){
        Toast.makeText(context, "离线地图加载失败，请退出后重试", Toast.LENGTH_LONG).show();
    }
}
     public  void  LayerFilter(String bsm){
         //获取底部调查未调查样点状态的图例
         dbtlLinearLayout =  (LinearLayout) findViewById(R.id.pointInfoL);

         //信息采集的时候跳转过来的，需要显示当前样点蓝色的点
         if (Objects.equals(bsm,"1"))
         {
             //底部样点状态图例可见
             dbtlLinearLayout.setVisibility(View.VISIBLE);
             //尤且仅有一个图层就是总图层
             GroupLayer layer_group = (GroupLayer)mainArcGISMap.getOperationalLayers().get(0);
             int layer_count = layer_group.getLayers().size();
             for (int i = 0;i<layer_count;i++) {
                 String layer_name = layer_group.getLayers().get(i).getName();
//                                    if(Objects.equals(layer_name, "土壤图") || Objects.equals(layer_name, "土地利用现状图")||Objects.equals(layer_name, "三普图"))
                 if (Objects.equals(layer_name, "土壤图") || Objects.equals(layer_name, "土地利用现状") || Objects.equals(layer_name, "母岩") || Objects.equals(layer_name, "等高线")) {
                     System.out.println(layer_name);
                     //layer_group.getLayers().get(i).setVisible(false);
                     FeatureLayer featureLayer = (FeatureLayer) layer_group.getLayers().get(i);
                     featureLayer.setMinScale(12000);
                     featureLayer.setMaxScale(500);
                 }
             }

         }
//                            //地图底图跳转过来的
         else if (Objects.equals(bsm, "0")){
             //底部样点状态图例不可见
             dbtlLinearLayout.setVisibility(View.VISIBLE);
             //尤且仅有一个图层就是总图层
             GroupLayer layer_group = (GroupLayer)mainArcGISMap.getOperationalLayers().get(0);
             int layer_count = layer_group.getLayers().size();
             for (int i = 0;i<layer_count;i++)
             {
                 String layer_name = layer_group.getLayers().get(i).getName();
//                                    if(Objects.equals(layer_name, "土壤图") || Objects.equals(layer_name, "土地利用现状图")||Objects.equals(layer_name, "三普图"))
                 if (Objects.equals(layer_name, "土壤图") || Objects.equals(layer_name, "土地利用现状") || Objects.equals(layer_name, "母岩") )
                 {
                     FeatureLayer featureLayer=(FeatureLayer)layer_group.getLayers().get(i);
                     featureLayer.setMinScale(12000);
                     featureLayer.setMaxScale(500);
                 }else if(Objects.equals(layer_name, "等高线")){
                     layer_group.getLayers().get(i).setVisible(false);

                 }
             }

         }
         else{
             //采样任务线路导航跳转过来的
             //底部样点状态图例可见
             dbtlLinearLayout.setVisibility(View.VISIBLE);
             //尤且仅有一个图层就是总图层
             GroupLayer layer_group = (GroupLayer)mainArcGISMap.getOperationalLayers().get(0);
             int layer_count = layer_group.getLayers().size();
             for (int i = 0;i<layer_count;i++)
             {
                 String layer_name = layer_group.getLayers().get(i).getName();
                 if(!Objects.equals(layer_name, "道路")){
                     System.out.println(layer_name);
                     layer_group.getLayers().get(i).setVisible(false);
                 }

             }

         }



         Log.d("图层组的长度", String.valueOf(projectAreaGroupLayer.getLayers().toArray().length));
         // zoom to the extent of the group layer when the child layers are loaded
//         ListenableList<Layer> layers = projectAreaGroupLayer.getLayers();
//         for (Layer childLayer : layers) {
//             childLayer.addDoneLoadingListener(() -> {
//                 if (childLayer.getLoadStatus() == LoadStatus.LOADED) {
//                     if(lon == 0)
//                     {
////                                        mapView.setViewpoint(new Viewpoint(projectAreaGroupLayer.getFullExtent()));
//                     }
//
//                 }
//             });
//         }
         setupRecyclerView(mainArcGISMap.getOperationalLayers());
     }
    //图层管理相关函数开始

    /**
     * Setup {@link RecyclerView} to display layers
     *
     * @param layers
     */
    private void setupRecyclerView(List<Layer> layers) {
        LayersAdapter mLayersAdapter = new LayersAdapter(this,this,(MainActivity)context);
        RecyclerView mLayersRecyclerView = findViewById(R.id.layersRecyclerView);
        mLayersRecyclerView.setAdapter(mLayersAdapter);
        TestModule testModule = new TestModule((MainActivity)context);

        for (Layer layer : layers) {
            // if layer can be shown in legend
            if (layer.canShowInLegend()) {
                layer.addDoneLoadingListener(() -> mLayersAdapter.addLayer(layer));
                layer.loadAsync();
            }
        }
    }

    /**
     * Called when a checkbox on a layer in the list is checked or unchecked
     *
     * @param layer   that has been checked or unchecked
     * @param checked whether the checkbox has been checked or unchecked
     */
    @Override
    public void layerCheckedChanged(Layer layer, boolean checked) {
        layer.setVisible(checked);
        if (layer instanceof GroupLayer) {
            for (Layer childLayer : ((GroupLayer) layer).getLayers()) {
                childLayer.setVisible(checked);
            }
        }
    }
}

// Top-level build file where you can add configuration options common to all sub-projects/modules.

//buildscript {
//
//    repositories {
//        google()
//        jcenter()
//    }
//    dependencies {
//        classpath 'com.android.tools.build:gradle:7.1.2'
//
//        // NOTE: Do not place your application dependencies here; they belong
//        // in the individual module build.gradle files
//    }
//}


//task clean(type: Delete) {
//    delete rootProject.buildDir
//}
buildscript {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/google' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/jcenter'}
        maven { url 'https://esri.jfrog.io/artifactory/arcgis'}
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.0.1'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}
allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/google' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/jcenter'}
        maven { url 'https://esri.jfrog.io/artifactory/arcgis'}
        maven { url 'https://www.jitpack.io' }
        mavenCentral()
    }
}
task clean(type: Delete) {
    delete rootProject.buildDir
}

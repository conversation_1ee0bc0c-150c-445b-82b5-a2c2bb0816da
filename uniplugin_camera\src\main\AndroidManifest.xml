<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.deltaphone.cameramodule"
android:versionCode="1"
android:versionName="1.0" >

<!-- 增加文件存储和访问摄像头的权限 -->
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<!-- Needed only if your app targets Android 5.0 (API level 21) or higher. -->
<uses-feature android:name="android.hardware.location.gps" />



<uses-feature android:name="android.hardware.camera" />
<uses-feature android:name="android.hardware.camera.autofocus" />

<application android:hardwareAccelerated="true"
    android:theme="@style/AppTheme">

    <activity
        android:name="com.deltaphone.cameramodule.CameraActivity"
        android:configChanges="orientation|keyboardHidden"
        android:screenOrientation="portrait"
        android:theme="@android:style/Theme.Holo.Light.NoActionBar" />

    <activity
        android:name="com.deltaphone.cameramodule.fileChangeActivity"
        android:configChanges="orientation|keyboardHidden|screenSize"
        android:screenOrientation="fullSensor"
        android:theme="@style/AppTheme"
        />
</application>

</manifest>
package io.dcloud.uniplugin.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.os.Environment;
import android.util.Log;

import java.io.File;

import io.dcloud.uniplugin.model.User;

/**
 * 数据库帮助类，用于管理应用的主要数据库
 */
public class DatabaseHelper extends SQLiteOpenHelper {
    private static final String TAG = "DatabaseHelper";

    private static final String DATABASE_NAME = DatabaseConstants.DATABASE_NAME;
    private static final int DATABASE_VERSION = DatabaseConstants.DATABASE_VERSION; // 使用 DatabaseConstants 中的版本号

    private static DatabaseHelper instance;
    private final Context mContext;
    private final String mDatabasePath;

    public static synchronized DatabaseHelper getInstance(Context context) {
        if (instance == null) {
            instance = new DatabaseHelper(context.getApplicationContext());
        }
        return instance;
    }

    private DatabaseHelper(Context context) {
        super(context, getExternalDatabasePath(context), null, DATABASE_VERSION);
        this.mContext = context;
        this.mDatabasePath = getExternalDatabasePath(context);
        
        File dbDir = new File(mDatabasePath).getParentFile();
        if (dbDir != null && !dbDir.exists()) {
            dbDir.mkdirs();
        }
    }

    /**
     * 获取外部存储中数据库的完整路径
     */
    private static String getExternalDatabasePath(Context context) {
        // 获取外部存储中的BCGDGISData/BCGDSqliteData目录
        File externalDir = new File(Environment.getExternalStorageDirectory(), "BCGDGISData/BCGDSqliteData");
        if (!externalDir.exists()) {
            boolean success = externalDir.mkdirs();
            if (!success) {
                Log.e(TAG, "创建外部存储数据库目录失败，将使用内部存储");
                return DatabaseConstants.DATABASE_NAME; // 如果创建失败，使用默认路径
            }
        }
        
        return new File(externalDir, DATABASE_NAME).getAbsolutePath();
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        try {
            // 启用外键约束
//            db.execSQL("PRAGMA foreign_keys = ON");
            
            // 创建所有表 (使用 DatabaseConstants 中的最新语句)
            db.execSQL(DatabaseConstants.CREATE_TABLE_USERS);
            db.execSQL(DatabaseConstants.CREATE_TABLE_UPLOAD_RECORDS);
            db.execSQL(DatabaseConstants.CREATE_TABLE_UPLOAD_FILES);
            db.execSQL(DatabaseConstants.CREATE_TABLE_SAMPLING_POINTS); // 这个语句现在包含 user_id
            db.execSQL(DatabaseConstants.CREATE_TABLE_SAMPLING_MEDIA);
            db.execSQL(DatabaseConstants.CREATE_TABLE_DDC_POINTS);    // 这个语句现在包含 user_id
            db.execSQL(DatabaseConstants.CREATE_TABLE_SAMPLES);      // 创建样品表

            // 创建索引 (DDC_POINTS 的索引已移至 DccyDdcDBHelper.onCreate)
            // 在这里可以为 sampling_points 创建索引
            db.execSQL("CREATE INDEX IF NOT EXISTS idx_sp_pjdy_bsm ON " + 
                       DatabaseConstants.TABLE_SAMPLING_POINTS + " (" + 
                       DatabaseConstants.COLUMN_PJDY_BSM + ")");
            db.execSQL("CREATE INDEX IF NOT EXISTS idx_sp_zt ON " + 
                       DatabaseConstants.TABLE_SAMPLING_POINTS + " (" + 
                       DatabaseConstants.COLUMN_ZT + ")");
            db.execSQL("CREATE INDEX IF NOT EXISTS idx_sp_user_id ON " + 
                       DatabaseConstants.TABLE_SAMPLING_POINTS + " (" + 
                       DatabaseConstants.COLUMN_USER_ID + ")"); // sampling_points 的 user_id 索引
                       
            Log.i(TAG, "数据库表和索引创建成功");
        } catch (Exception e) {
            Log.e(TAG, "创建数据库表或索引失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        try {

        } catch (Exception e) {

        }
    }
    
//    /**
//     * 检查表中是否存在指定列
//     */
//    private boolean isColumnExists(SQLiteDatabase db, String tableName, String columnName) {
//        Cursor cursor = null;
//        try {
//            // Limit 0 不返回任何行，只获取列信息
//            cursor = db.query(tableName, null, null, null, null, null, null, "0");
//            if (cursor != null) {
//                return cursor.getColumnIndex(columnName) != -1;
//            }
//        } catch (Exception e) {
//            Log.e(TAG, "Error checking column existence for " + tableName + "." + columnName, e);
//        } finally {
//            if (cursor != null) {
//                cursor.close();
//            }
//        }
//        return false;
//    }
//
//    /**
//     * 检查表是否存在
//     */
//    private boolean isTableExists(SQLiteDatabase db, String tableName) {
//        Cursor cursor = null;
//        try {
//            cursor = db.rawQuery(
//                    "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
//                    new String[]{tableName}
//            );
//            return cursor != null && cursor.getCount() > 0;
//        } catch (Exception e) {
//            Log.e(TAG, "Error checking table existence for " + tableName, e);
//        } finally {
//            if (cursor != null) {
//                cursor.close();
//            }
//        }
//        return false;
//    }
//
//    /**
//     * 删除所有已知表 (谨慎使用!)
//     */
//    private void dropAllTables(SQLiteDatabase db) {
//         Log.w(TAG, "Dropping all tables!");
//         db.execSQL("DROP TABLE IF EXISTS " + DatabaseConstants.TABLE_USERS);
//         db.execSQL("DROP TABLE IF EXISTS " + DatabaseConstants.TABLE_UPLOAD_RECORDS);
//         db.execSQL("DROP TABLE IF EXISTS " + DatabaseConstants.TABLE_UPLOAD_FILES);
//         db.execSQL("DROP TABLE IF EXISTS " + DatabaseConstants.TABLE_SAMPLING_POINTS);
//         db.execSQL("DROP TABLE IF EXISTS " + DatabaseConstants.TABLE_SAMPLING_MEDIA);
//         db.execSQL("DROP TABLE IF EXISTS " + DatabaseConstants.TABLE_DDC_POINTS);
//    }

    @Override
    public void onConfigure(SQLiteDatabase db) {
        super.onConfigure(db);
        db.setForeignKeyConstraintsEnabled(true);
    }

    /**
     * 获取数据库文件路径
     */
    public String getDatabasePath() {
        return mDatabasePath;
    }

    /**
     * 添加新用户
     */
    public long addUser(User user) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();

        if (user.getId() != null) {
            values.put(DatabaseConstants.COLUMN_ID, user.getId());
        }
        values.put(DatabaseConstants.COLUMN_USERNAME, user.getUsername());
        values.put(DatabaseConstants.COLUMN_PASSWORD, user.getPassword());
        values.put(DatabaseConstants.COLUMN_SALT, user.getSalt());
        values.put(DatabaseConstants.COLUMN_NICKNAME, user.getNickname());
        values.put(DatabaseConstants.COLUMN_ROLE, user.getRole());
        values.put(DatabaseConstants.COLUMN_LAST_LOGIN_TIME, user.getLastLoginTime());
        values.put(DatabaseConstants.COLUMN_PERMISSIONS, user.getPermissions());
        values.put(DatabaseConstants.COLUMN_GSDDM, user.getGsddm());
        values.put(DatabaseConstants.COLUMN_GSDMC, user.getGsdmc());

        long id = db.insert(DatabaseConstants.TABLE_USERS, null, values);
        db.close();
        return id;
    }

//    /**
//     * 获取所有用户
//     */
//    public List<User> getAllUsers() {
//        List<User> userList = new ArrayList<>();
//        String selectQuery = "SELECT * FROM " + DatabaseConstants.TABLE_USERS;
//        SQLiteDatabase db = this.getReadableDatabase();
//        Cursor cursor = db.rawQuery(selectQuery, null);
//
//        if (cursor.moveToFirst()) {
//            do {
//                User user = new User();
//                user.setId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_ID)));
//                user.setUsername(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_USERNAME)));
//                user.setPassword(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_PASSWORD)));
//                user.setSalt(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_SALT)));
//                user.setNickname(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_NICKNAME)));
//                user.setRole(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_ROLE)));
//                user.setLastLoginTime(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_LAST_LOGIN_TIME)));
//                user.setPermissions(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_PERMISSIONS)));
//                user.setGsddm(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_GSDDM)));
//                user.setGsdmc(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_GSDMC)));
//                userList.add(user);
//            } while (cursor.moveToNext());
//        }
//        cursor.close();
//        db.close();
//        return userList;
//    }
//
//    /**
//     * 通过用户ID获取用户
//     */
//    public User getUserById(Long id) {
//        SQLiteDatabase db = this.getReadableDatabase();
//
//        Cursor cursor = db.query(DatabaseConstants.TABLE_USERS, null,
//                DatabaseConstants.COLUMN_ID + "=?", new String[]{String.valueOf(id)},
//                null, null, null);
//
//        User user = null;
//        if (cursor != null && cursor.moveToFirst()) {
//            user = new User();
//            int idIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_ID);
//            if (idIndex != -1) {
//                user.setId(cursor.getLong(idIndex));
//            }
//
//            int usernameIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_USERNAME);
//            if (usernameIndex != -1) {
//                user.setUsername(cursor.getString(usernameIndex));
//            }
//
//            int passwordIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_PASSWORD);
//            if (passwordIndex != -1) {
//                user.setPassword(cursor.getString(passwordIndex));
//            }
//
//            int saltIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_SALT);
//            if (saltIndex != -1) {
//                user.setSalt(cursor.getString(saltIndex));
//            }
//
//            int nameIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_NICKNAME);
//            if (nameIndex != -1) {
//                user.setNickname(cursor.getString(nameIndex));
//            }
//
//            int roleIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_ROLE);
//            if (roleIndex != -1) {
//                user.setRole(cursor.getString(roleIndex));
//            }
//
//            int lastLoginTimeIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_LAST_LOGIN_TIME);
//            if (lastLoginTimeIndex != -1) {
//                user.setLastLoginTime(cursor.getString(lastLoginTimeIndex));
//            }
//
//            int permissionsIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_PERMISSIONS);
//            if (permissionsIndex != -1) {
//                user.setPermissions(cursor.getString(permissionsIndex));
//            }
//
//            int gsddmIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_GSDDM);
//            if (gsddmIndex != -1) {
//                user.setGsddm(cursor.getString(gsddmIndex));
//            }
//
//            int gsdmcIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_GSDMC);
//            if (gsdmcIndex != -1) {
//                user.setGsdmc(cursor.getString(gsdmcIndex));
//            }
//
//            cursor.close();
//        }
//
//        db.close();
//        return user;
//    }

    /**
     * 通过用户名获取用户
     */
    public User getUserByUsername(String username) {
        SQLiteDatabase db = this.getReadableDatabase();

        Cursor cursor = db.query(DatabaseConstants.TABLE_USERS, null,
                DatabaseConstants.COLUMN_USERNAME + "=?", new String[]{username},
                null, null, null);

        User user = null;
        if (cursor != null && cursor.moveToFirst()) {
            user = new User();
            int idIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_ID);
            if (idIndex != -1) {
                user.setId(cursor.getLong(idIndex));
            }

            int usernameIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_USERNAME);
            if (usernameIndex != -1) {
                user.setUsername(cursor.getString(usernameIndex));
            }

            int passwordIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_PASSWORD);
            if (passwordIndex != -1) {
                user.setPassword(cursor.getString(passwordIndex));
            }
            
            int saltIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_SALT);
            if (saltIndex != -1) {
                user.setSalt(cursor.getString(saltIndex));
            }

            int nameIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_NICKNAME);
            if (nameIndex != -1) {
                user.setNickname(cursor.getString(nameIndex));
            }

            int roleIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_ROLE);
            if (roleIndex != -1) {
                user.setRole(cursor.getString(roleIndex));
            }

            int lastLoginTimeIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_LAST_LOGIN_TIME);
            if (lastLoginTimeIndex != -1) {
                user.setLastLoginTime(cursor.getString(lastLoginTimeIndex));
            }
            
            int permissionsIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_PERMISSIONS);
            if (permissionsIndex != -1) {
                user.setPermissions(cursor.getString(permissionsIndex));
            }
            
            int gsddmIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_GSDDM);
            if (gsddmIndex != -1) {
                user.setGsddm(cursor.getString(gsddmIndex));
            }
            
            int gsdmcIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_GSDMC);
            if (gsdmcIndex != -1) {
                user.setGsdmc(cursor.getString(gsdmcIndex));
            }

            cursor.close();
        }

        db.close();
        return user;
    }

//    /**
//     * 验证用户登录
//     */
//    public User checkLogin(String username, String password) {
//        SQLiteDatabase db = this.getReadableDatabase();
//
//        // 先获取用户信息
//        User user = getUserByUsername(username);
//
//        if (user != null && user.getSalt() != null) {
//            // 使用PasswordUtil验证密码
//            try {
//                String encryptedPassword = user.getPassword();
//                String salt = user.getSalt();
//
//                // 这里需要在调用处使用PasswordUtil.verifyPassword方法验证
//                return user;
//            } catch (Exception e) {
//                Log.e(TAG, "验证密码失败: " + e.getMessage());
//                return null;
//            }
//        } else {
//            // 旧方式验证（不推荐，仅用于兼容）
//            Cursor cursor = db.query(DatabaseConstants.TABLE_USERS, null, DatabaseConstants.COLUMN_USERNAME + "=? AND " + DatabaseConstants.COLUMN_PASSWORD + "=?",
//                    new String[]{username, password}, null, null, null);
//
//            User oldUser = null;
//            if (cursor != null && cursor.moveToFirst()) {
//                oldUser = new User();
//                oldUser.setId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_ID)));
//                oldUser.setUsername(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_USERNAME)));
//                oldUser.setPassword(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_PASSWORD)));
//                oldUser.setNickname(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_NICKNAME)));
//                oldUser.setRole(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_ROLE)));
//                oldUser.setLastLoginTime(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_LAST_LOGIN_TIME)));
//                cursor.close();
//            }
//            db.close();
//            return oldUser;
//        }
//    }

    /**
     * 更新用户登录信息
     */
    public int updateUserLoginInfo(User user) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        values.put(DatabaseConstants.COLUMN_LAST_LOGIN_TIME, user.getLastLoginTime());

        int result = db.update(DatabaseConstants.TABLE_USERS, values, DatabaseConstants.COLUMN_ID + "=?",
                new String[]{String.valueOf(user.getId())});
        db.close();
        return result;
    }

    /**
     * 更新用户信息
     */
    public int updateUser(User user) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        values.put(DatabaseConstants.COLUMN_PASSWORD, user.getPassword());
        values.put(DatabaseConstants.COLUMN_SALT, user.getSalt());
        values.put(DatabaseConstants.COLUMN_NICKNAME, user.getNickname());
        values.put(DatabaseConstants.COLUMN_ROLE, user.getRole());
        values.put(DatabaseConstants.COLUMN_PERMISSIONS, user.getPermissions());
        values.put(DatabaseConstants.COLUMN_GSDDM, user.getGsddm());
        values.put(DatabaseConstants.COLUMN_GSDMC, user.getGsdmc());

        int result = db.update(DatabaseConstants.TABLE_USERS, values, DatabaseConstants.COLUMN_ID + "=?",
                new String[]{String.valueOf(user.getId())});
        db.close();
        return result;
    }

//    /**
//     * 删除用户
//     */
//    public void deleteUser(Long id) {
//        SQLiteDatabase db = this.getWritableDatabase();
//        db.delete(DatabaseConstants.TABLE_USERS, DatabaseConstants.COLUMN_ID + " = ?", new String[]{String.valueOf(id)});
//        db.close();
//    }
//
//    /**
//     * 导出数据库到外部存储
//     * @param context 上下文
//     * @return 导出的文件路径，如果导出失败则返回null
//     */
//    public String exportDatabase(Context context) {
//        try {
//            File dbFile = new File(mDatabasePath);
//            if (!dbFile.exists()) {
//                Log.e(TAG, "数据库文件不存在: " + dbFile.getAbsolutePath());
//                return null;
//            }
//
//            // 数据库已经在外部存储中，直接返回路径
//            Log.d(TAG, "数据库已位于外部存储: " + dbFile.getAbsolutePath());
//            return dbFile.getAbsolutePath();
//        } catch (Exception e) {
//            Log.e(TAG, "获取数据库路径失败: " + e.getMessage());
//            e.printStackTrace();
//            return null;
//        }
//    }
}
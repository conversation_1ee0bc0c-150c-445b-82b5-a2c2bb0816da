package io.dcloud.uniplugin;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;

import com.google.gson.Gson;

import java.util.Set;

import io.dcloud.uniplugin.enums.RoleEnum;
import io.dcloud.uniplugin.enums.SharedPreferencesEnum;
import io.dcloud.uniplugin.http.RetrofitManager;
import io.dcloud.uniplugin.model.ApiResponse;
import io.dcloud.uniplugin.model.AuthPermissionInfoRespVO;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import uni.dcloud.io.uniplugin_module.R;

/**
 * 检测实验室主页面
 * 提供实验室相关功能入口，如样品流转接收、个人信息管理等
 */
public class LabHomeActivity extends AppCompatActivity {

    private static final String TAG = "LabHomeActivity";
    
    private TextView textViewUsername;
    private TextView textViewRole;
    private CardView cardViewProfile;
    private CardView cardViewSampleReceive;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_lab_home);
        
        // 初始化视图
        textViewUsername = findViewById(R.id.textViewUsername);
        textViewRole = findViewById(R.id.textViewRole);
        cardViewProfile = findViewById(R.id.cardViewProfile);
        cardViewSampleReceive = findViewById(R.id.cardViewSampleReceive);
        
        // 加载用户信息
        loadUserInfo();
        
        // 设置卡片点击事件
        setupCardClickListeners();
    }
    
    /**
     * 加载用户信息
     */
    private void loadUserInfo() {
        try {
            SharedPreferences sp = getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, MODE_PRIVATE);
            String permissionInfoStr = sp.getString(SharedPreferencesEnum.PERMISSION_INFO.value, "无法获取");
            
            if ("无法获取".equals(permissionInfoStr)) {
                Log.e(TAG, "无法获取用户权限信息");
                if (textViewUsername != null) textViewUsername.setText("未知用户");
                if (textViewRole != null) textViewRole.setText("未知角色");
                return;
            }
            
            Gson gson = new Gson();
            AuthPermissionInfoRespVO permissionInfoRespVO = gson.fromJson(permissionInfoStr, AuthPermissionInfoRespVO.class);
            
            if (permissionInfoRespVO == null) {
                Log.e(TAG, "解析权限信息失败");
                if (textViewUsername != null) textViewUsername.setText("解析失败");
                if (textViewRole != null) textViewRole.setText("解析失败");
                return;
            }
            
            AuthPermissionInfoRespVO.UserVO user = permissionInfoRespVO.getUser();
            if (user == null) {
                Log.e(TAG, "用户信息为空");
                if (textViewUsername != null) textViewUsername.setText("用户信息为空");
                if (textViewRole != null) textViewRole.setText("用户信息为空");
                return;
            }
            
            // 安全地设置用户名
            String nickname = user.getNickname();
            if (textViewUsername != null) {
                textViewUsername.setText(nickname != null ? nickname : "未知用户");
            } else {
                Log.e(TAG, "textViewUsername 为 null");
            }
            
            // 安全地设置角色
            Set<String> roles = permissionInfoRespVO.getRoles();
            if (textViewRole != null) {
                if (roles != null && !roles.isEmpty()) {
                    // 使用RoleEnum将角色代码转换为中文名称
                    String chineseRoles = RoleEnum.convertRolesToChineseNames(roles);
                    textViewRole.setText(chineseRoles);
                } else {
                    textViewRole.setText("未知角色");
                    Log.w(TAG, "角色列表为空");
                }
            } else {
                Log.e(TAG, "textViewRole 为 null");
            }
        } catch (Exception e) {
            Log.e(TAG, "加载用户信息时发生异常: " + e.getMessage(), e);
            if (textViewUsername != null) textViewUsername.setText("加载失败");
            if (textViewRole != null) textViewRole.setText("加载失败");
            Toast.makeText(this, "加载用户信息失败", Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * 设置卡片点击监听器
     */
    private void setupCardClickListeners() {
        // 设置个人信息卡片点击事件
        cardViewProfile.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 跳转到个人信息页面
                Intent intent = new Intent(LabHomeActivity.this, ProfileActivity.class);
                startActivity(intent);
            }
        });
        
        // 设置样品流转接收卡片点击事件
        cardViewSampleReceive.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 跳转到样品流转接收页面
                // 暂时跳转到样品流转列表页面，后续可以替换为专门的接收页面
                Intent intent = new Intent(LabHomeActivity.this, io.dcloud.uniplugin.sampleflow.SampleFlowListActivity.class);
                intent.putExtra("VIEW_MODE", "RECEIVE"); // 添加参数表示以接收模式查看
                startActivity(intent);
            }
        });
    }
    
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_lab_home, menu);
        return true;
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == R.id.action_logout) {
            logout();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
    
    /**
     * 退出登录
     */
    private void logout() {
        // 显示退出中提示
        Toast.makeText(this, "正在退出登录...", Toast.LENGTH_SHORT).show();
        
        // 检查是否是离线登录
        SharedPreferences sp = getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, MODE_PRIVATE);
        if (sp.getBoolean(SharedPreferencesEnum.IS_OFFLINE_LOGIN.value, false)) {
            // 离线登录，直接清除本地用户信息并跳转
            Log.i(TAG, "离线登录模式，直接清除本地数据");
            clearUserDataAndRedirect();
            return;
        }
        
        // 调用退出登录API
        RetrofitManager.getInstance(this)
                .getApiService()
                .logout()
                .enqueue(new Callback<ApiResponse<Boolean>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<Boolean>> call, Response<ApiResponse<Boolean>> response) {
                        // 无论成功与否，都清除本地用户信息并跳转到登录页面
                        clearUserDataAndRedirect();
                    }
                    
                    @Override
                    public void onFailure(Call<ApiResponse<Boolean>> call, Throwable t) {
                        Log.e(TAG, "退出登录请求失败: " + t.getMessage());
                        // 即使请求失败，也清除本地用户信息并跳转到登录页面
                        clearUserDataAndRedirect();
                    }
                });
    }
    
    /**
     * 清除用户数据并重定向到登录页面
     */
    private void clearUserDataAndRedirect() {
        // 清除SharedPreferences中的用户信息
        SharedPreferences sp = getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, MODE_PRIVATE);
        sp.edit().clear().apply();
        
        // 显示退出成功提示
        Toast.makeText(this, "已退出登录", Toast.LENGTH_SHORT).show();
        
        // 跳转到登录页面
        Intent intent = new Intent(LabHomeActivity.this, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }
} 
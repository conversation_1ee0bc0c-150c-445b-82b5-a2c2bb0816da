package io.dcloud.uniplugin.model;

import com.google.gson.annotations.SerializedName;

public class LoginRequest {
    @SerializedName("mobile")
    private String mobile;

    @SerializedName("password")
    private String password;

    @SerializedName("code")
    private String code;

    @SerializedName("uuid")
    private String uuid;

    public LoginRequest(String mobile, String password) {
        this.mobile = mobile;
        this.password = password;
        this.uuid = java.util.UUID.randomUUID().toString();
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
} 
package io.dcloud.uniplugin.processor;

import android.view.View;
import android.view.ViewGroup;

import io.dcloud.uniplugin.model.FormConfigResponse;

/**
 * 表单字段处理器接口
 * 用于处理和渲染不同类型的表单字段
 */
public interface FormFieldProcessor {
    /**
     * 处理表单字段并返回渲染后的视图
     * 
     * @param container 父容器
     * @param field 要处理的字段
     * @return 渲染后的视图
     */
    View processField(ViewGroup container, FormConfigResponse.Field field);
} 
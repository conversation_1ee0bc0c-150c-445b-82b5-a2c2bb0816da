package io.dcloud.uniplugin.utils;

import android.util.Base64;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

/**
 * AES加密解密工具类
 */
public class AesEncryptUtil {
    
    /**
     * AES加密
     *
     * @param content  待加密内容
     * @param password 加密密钥
     * @return 返回Base64转码后的加密数据
     */
    public static String encrypt(String content, String password) throws Exception {
        // 创建AES密钥
        SecretKeySpec key = createKey(password);
        // 创建密码器
        Cipher cipher = Cipher.getInstance("AES");
        // 初始化为加密模式
        cipher.init(Cipher.ENCRYPT_MODE, key);
        // 执行加密
        byte[] result = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
        // 使用Base64转码并返回
        return Base64.encodeToString(result, Base64.DEFAULT);
    }

    /**
     * AES解密
     *
     * @param content  待解密内容（Base64编码）
     * @param password 解密密钥
     * @return 返回解密后的数据
     */
    public static String decrypt(String content, String password) throws Exception {
        // 创建AES密钥
        SecretKeySpec key = createKey(password);
        // 创建密码器
        Cipher cipher = Cipher.getInstance("AES");
        // 初始化为解密模式
        cipher.init(Cipher.DECRYPT_MODE, key);
        // 执行解密
        byte[] result = cipher.doFinal(Base64.decode(content, Base64.DEFAULT));
        // 返回解密后的数据
        return new String(result, StandardCharsets.UTF_8);
    }

    /**
     * 创建密钥
     *
     * @param password 密码
     * @return 返回密钥
     */
    private static SecretKeySpec createKey(String password) throws Exception {
        // 使用SHA-256摘要算法
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        // 对密码进行摘要
        byte[] bytes = digest.digest(password.getBytes(StandardCharsets.UTF_8));
        // 创建AES密钥（取前16字节）
        return new SecretKeySpec(bytes, 0, 16, "AES");
    }
} 
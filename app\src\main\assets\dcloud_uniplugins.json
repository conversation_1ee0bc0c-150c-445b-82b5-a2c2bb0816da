{"nativePlugins": [{"plugins": [{"type": "module", "name": "TestModule", "class": "io.dcloud.uniplugin.TestModule"}]}, {"plugins": [{"type": "component", "name": "myText", "class": "io.dcloud.uniplugin.TestText"}]}, {"hooksClass": "", "plugins": [{"type": "module", "name": "DCloud-<PERSON><PERSON><PERSON><PERSON>", "class": "uni.dcloud.io.uniplugin_richalert.RichAlertModule"}]}, {"plugins": [{"type": "module", "name": "swdcModule", "class": "com.chy.map.SwdcModule"}]}, {"plugins": [{"type": "module", "name": "DeltaPhone-Camera", "class": "com.deltaphone.cameramodule.CameraModule"}]}, {"plugins": [{"type": "module", "name": "uni-print", "class": "com.dothantech.demo.PrintMoudle"}]}, {"plugins": [{"type": "module", "name": "uni-qrcode", "class": "com.abdu.qrcode.QrcodeMoudle"}]}]}
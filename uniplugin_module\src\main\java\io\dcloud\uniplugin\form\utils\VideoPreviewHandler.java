package io.dcloud.uniplugin.form.utils;

import android.app.Activity;
import android.media.MediaPlayer;
import android.net.Uri;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.MediaController;
import android.widget.VideoView;

import androidx.appcompat.app.AlertDialog;

import java.io.File;

/**
 * 视频预览处理类，负责处理视频预览功能
 */
public class VideoPreviewHandler {
    private static final String TAG = "VideoPreviewHandler";
    
    /**
     * 显示视频预览对话框
     * @param activity 活动
     * @param videoPath 视频路径
     */
    public static void showVideoPreviewDialog(Activity activity, String videoPath) {
        if (activity == null) {
            Log.e(TAG, "活动为空，无法播放视频");
            return;
        }
        
        try {
            if (videoPath == null || videoPath.isEmpty()) {
                Log.e(TAG, "视频路径为空，无法播放");
                UIUtils.showToast(activity, "无法播放视频: 文件路径为空");
                return;
            }
            
            // 检查文件是否存在
            File videoFile = new File(videoPath);
            if (!videoFile.exists()) {
                Log.e(TAG, "视频文件不存在: " + videoPath);
                UIUtils.showToast(activity, "视频文件不存在");
                return;
            }
            
            // 检查文件是否可读
            if (!videoFile.canRead()) {
                Log.e(TAG, "视频文件无法读取: " + videoPath);
                UIUtils.showToast(activity, "无法读取视频文件");
                return;
            }
            
            Log.d(TAG, "准备播放视频: " + videoPath + ", 文件大小: " + videoFile.length() + " 字节");
            
            // 创建对话框
            AlertDialog.Builder builder = new AlertDialog.Builder(activity);
            View dialogView = activity.getLayoutInflater().inflate(uni.dcloud.io.uniplugin_module.R.layout.dialog_video_preview, null);
            VideoView videoView = dialogView.findViewById(uni.dcloud.io.uniplugin_module.R.id.videoView);
            Button closeButton = dialogView.findViewById(uni.dcloud.io.uniplugin_module.R.id.btnClose);
            
            // 创建并显示对话框
            builder.setView(dialogView);
            AlertDialog dialog = builder.create();
            
            // 显示加载中提示
            UIUtils.showToast(activity, "正在加载视频...");
            
            // 错误监听器
            videoView.setOnErrorListener((mp, what, extra) -> {
                Log.e(TAG, "视频播放失败: what=" + what + ", extra=" + extra);
                
                String errorMessage;
                switch (what) {
                    case MediaPlayer.MEDIA_ERROR_IO:
                        errorMessage = "视频播放出错: 读取错误";
                        break;
                    case MediaPlayer.MEDIA_ERROR_MALFORMED:
                        errorMessage = "视频播放出错: 视频格式不支持";
                        break;
                    case MediaPlayer.MEDIA_ERROR_NOT_VALID_FOR_PROGRESSIVE_PLAYBACK:
                        errorMessage = "视频播放出错: 不支持流式播放";
                        break;
                    case MediaPlayer.MEDIA_ERROR_TIMED_OUT:
                        errorMessage = "视频播放出错: 超时";
                        break;
                    case MediaPlayer.MEDIA_ERROR_UNSUPPORTED:
                        errorMessage = "视频播放出错: 不支持此视频";
                        break;
                    case MediaPlayer.MEDIA_ERROR_SERVER_DIED:
                        errorMessage = "视频播放出错: 播放器错误";
                        break;
                    default:
                        errorMessage = "视频播放失败，错误码: " + what;
                        break;
                }
                
                UIUtils.showToast(activity, errorMessage);
                
                // 关闭对话框
                dialog.dismiss();
                return true;
            });
            
            // 信息监听器
            videoView.setOnInfoListener((mp, what, extra) -> {
                if (what == MediaPlayer.MEDIA_INFO_VIDEO_RENDERING_START) {
                    Log.d(TAG, "视频开始渲染");
                } else if (what == MediaPlayer.MEDIA_INFO_BUFFERING_START) {
                    Log.d(TAG, "视频开始缓冲");
                    UIUtils.showToast(activity, "视频缓冲中...");
                } else if (what == MediaPlayer.MEDIA_INFO_BUFFERING_END) {
                    Log.d(TAG, "视频缓冲结束");
                }
                return false;
            });
            
            // 准备完成监听器
            videoView.setOnPreparedListener(mp -> {
                Log.d(TAG, "视频准备完成，时长: " + mp.getDuration() + "ms");
                
                // 设置循环播放
                mp.setLooping(true);
                
                // 视频准备好后自动播放
                videoView.start();
                
                // 显示提示
                UIUtils.showToast(activity, "视频播放开始");
            });
            
            // 完成监听器
            videoView.setOnCompletionListener(mp -> {
                Log.d(TAG, "视频播放完成");
            });
            
            // 设置媒体控制器
            MediaController mediaController = new MediaController(activity);
            mediaController.setAnchorView(videoView);
            videoView.setMediaController(mediaController);
            
            // 尝试使用不同方式设置视频源
            try {
                // 先尝试直接使用文件路径
                videoView.setVideoPath(videoPath);
                Log.d(TAG, "使用setVideoPath设置视频路径");
            } catch (Exception e) {
                Log.e(TAG, "设置视频路径失败，尝试使用URI: " + e.getMessage());
                
                try {
                    // 如果失败，尝试使用URI
                    Uri videoUri = Uri.fromFile(videoFile);
                    videoView.setVideoURI(videoUri);
                    Log.d(TAG, "使用setVideoURI设置视频URI: " + videoUri);
                } catch (Exception e2) {
                    Log.e(TAG, "设置视频URI也失败: " + e2.getMessage());
                    UIUtils.showToast(activity, "无法加载视频: " + e2.getMessage());
                    dialog.dismiss();
                    return;
                }
            }
            
            // 显示对话框
            dialog.show();
            
            // 关闭按钮点击事件
            closeButton.setOnClickListener(v -> {
                videoView.stopPlayback();
                dialog.dismiss();
            });
        } catch (Exception e) {
            Log.e(TAG, "显示视频预览对话框失败", e);
            UIUtils.showToast(activity, "显示视频预览失败: " + e.getMessage());
        }
    }
} 
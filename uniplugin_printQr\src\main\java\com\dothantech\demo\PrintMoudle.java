package com.dothantech.demo;

import android.app.Activity;
import android.content.Intent;
import android.util.Log;

import com.alibaba.fastjson.JSONObject;

import java.util.Date;

import io.dcloud.feature.uniapp.annotation.UniJSMethod;
import io.dcloud.feature.uniapp.bridge.UniJSCallback;
import io.dcloud.feature.uniapp.common.UniModule;

public class PrintMoudle extends UniModule {
    private static int REQUEST_CODE = 200;
    private UniJSCallback printCallback;

    public PrintMoudle() {

    }
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        System.out.println("进入到返回的数据：1："+requestCode+"2:"+resultCode);
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("res", "1");
//        printCallback
        this.printCallback.invoke(null);
    }

    @UniJSMethod(
            uiThread = true
    )
    public void testprint(JSONObject options, UniJSCallback callback) {
        TrscpcYprkPm obj = options.getObject("obj", TrscpcYprkPm.class);

//        String ypbh = obj.getYpbh();
//        String yplx = options.getString("yplx");
//        String cydz = options.getString("cydz");
//        Double hbgd = options.getDouble("hbgd");
//        Date cysj = options.getDate("cysj");
//        Double jd = options.getDouble("jd");
//        Double wd = options.getDouble("wd");
//        String trlx = options.getString("trlx");
//        String cyr = options.getString("cyr");
        Intent intent = new Intent(this.mUniSDKInstance.getContext(), MainActivity.class);
        intent.putExtra("obj",obj);
        ((Activity)this.mUniSDKInstance.getContext()).startActivityForResult(intent, REQUEST_CODE);
        this.printCallback=callback;
    }
}

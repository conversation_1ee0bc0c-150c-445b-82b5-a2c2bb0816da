package com.chy.map;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageButton;
import android.widget.TextView;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.esri.arcgisruntime.geometry.Point;
import com.esri.arcgisruntime.mapping.Viewpoint;
import com.esri.arcgisruntime.mapping.view.LocationDisplay;
import com.esri.arcgisruntime.mapping.view.MapView;
import com.esri.arcgisruntime.mapping.view.SketchEditor;
import com.esri.arcgisruntime.symbology.SimpleFillSymbol;
import com.esri.arcgisruntime.symbology.SimpleLineSymbol;
import com.esri.arcgisruntime.symbology.SimpleMarkerSymbol;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class  BaseTool {
     private  MapView mapView;
     private  Context context;
    private Activity activity;

     private  LocationDisplay mLocationDisplay;
     private  SketchEditor mSketchEditor;

    private SimpleMarkerSymbol mPointSymbol;
    private SimpleLineSymbol mLineSymbol;
    private SimpleFillSymbol mFillSymbol;

     public BaseTool(MapView mapView, Activity activity, Context context) {
         this.mapView = mapView;
         this.context = context;
         this.activity = activity;
         //ImageButton compassButton = (ImageButton) findViewById(R.id.compassButton);

     }
     public  void initTools(List<HashMap<String,ImageButton>> toolButtons){
         for (int i = 0; i < toolButtons.size(); i++) {
             if (toolButtons.get(i).containsKey("compassButton")) {
                 ImageButton compassButton = toolButtons.get(i).get("compassButton");
                 BtnTouth(compassButton);//设置指北针点击样式
                 //给指北针添加点击事件
                 compassButton.setOnClickListener(new View.OnClickListener() {
                     @Override
                     public void onClick(View view) {
                         mapView.setViewpointRotationAsync(0);
                     }
                 });
             }
             if (toolButtons.get(i).containsKey("zoomInButton")) {
                 ImageButton zoomInButton = toolButtons.get(i).get("zoomInButton");
                 BtnTouth(zoomInButton);
                 //添加放大按钮的点击事件
                 zoomInButton.setOnClickListener(new View.OnClickListener() {
                     @Override
                     public void onClick(View view) {
                         //获取当前的地图缩放等级
                         double mScale = mapView.getMapScale();
                         mapView.setViewpointScaleAsync(mScale * 0.5);
                     }
                 });
             }
             if (toolButtons.get(i).containsKey("zoomOutButton")) {
                 ImageButton zoomOutButton = toolButtons.get(i).get("zoomOutButton");
                 BtnTouth(zoomOutButton);
                 //添加放大按钮的点击事件
                 zoomOutButton.setOnClickListener(new View.OnClickListener() {
                     @Override
                     public void onClick(View view) {
                         //获取当前的地图缩放等级
                         double mScale = mapView.getMapScale();
                         mapView.setViewpointScaleAsync(mScale * 2);
                     }
                 });
             }
             if (toolButtons.get(i).containsKey("fullPicButton")) {
                 ImageButton fullPicButton = toolButtons.get(i).get("fullPicButton");
                 BtnTouth(fullPicButton);
                 //添加放大按钮的点击事件
                 fullPicButton.setOnClickListener(new View.OnClickListener() {
                     @Override
                     public void onClick(View view) {
                         //这里也是偷懒直接用底图图层的范围确定范围
                         Viewpoint viewpoint = new Viewpoint(new Point(113.5, 23), 4617149.8915429693);
                         mapView.setViewpointAsync(viewpoint);
                     }
                 });
             }
             if (toolButtons.get(i).containsKey("currentLoButton")) {
                 ImageButton currentLoButton = toolButtons.get(i).get("currentLoButton");
                 BtnTouth(currentLoButton);
                 final boolean[] loBtnBoolean = {false};
                 //添加放大按钮的点击事件
                 currentLoButton.setOnClickListener(new View.OnClickListener() {
                     @Override
                     public void onClick(View view) {
                         if (loBtnBoolean[0] == false) {
                             loBtn();
                             currentLoButton.setBackgroundColor(Color.BLUE);//设置图片透明度0-255，0完全透明
                             currentLoButton.invalidate();
                             loBtnBoolean[0] = true;
                         } else {
                             mLocationDisplay.stop();
                             currentLoButton.setBackgroundColor(Color.WHITE);//设置图片透明度0-255，0完全透明
                             currentLoButton.invalidate();
                             loBtnBoolean[0] = false;
                         }
                     }
                 });
             }
             if (toolButtons.get(i).containsKey("measurementButton")) {
                 ImageButton measurementButton = toolButtons.get(i).get("measurementButton");
                 BtnTouth(measurementButton);//添加点击样式
                 final boolean[] btnBoolean = {false};
                 MeasurementTool measurementTool=new MeasurementTool(context,activity,mapView);
                 measurementButton.setOnClickListener(new View.OnClickListener() {
                     @Override
                     public void onClick(View view) {
                         if (btnBoolean[0] == false) {
                             activity.findViewById(R.id.group).setVisibility(View.VISIBLE);
                             measurementButton.setBackgroundColor(Color.BLUE);//设置图片透明度0-255，0完全透明
                             measurementButton.invalidate();
                             btnBoolean[0] = true;
                         } else {
                             activity.findViewById(R.id.group).setVisibility(View.GONE);
                             mapView.getSketchEditor().stop();
                             measurementTool.resetButtons();
                             measurementButton.setBackgroundColor(Color.WHITE);//设置图片透明度0-255，0完全透明
                             measurementButton.invalidate();
                             //关闭时文字恢复默认
                             TextView textView = (TextView) activity.findViewById(R.id.result_text);
                             textView.setText("请开始绘制！");
                             btnBoolean[0] = false;
                         }

                     }
                 });
             }
             if (toolButtons.get(i).containsKey("layersButton")) {
                 ImageButton layersButton = toolButtons.get(i).get("layersButton");
                 BtnTouth(layersButton);//添加点击样式
                 final boolean[] btnBoolean = {false};
                 layersButton.setOnClickListener(new View.OnClickListener() {
                     @Override
                     public void onClick(View view) {
                         if (btnBoolean[0] == false) {
                             mapView.getMap().getBasemap().getBaseLayers().get(0).setVisible(false);
                             mapView.getMap().getBasemap().getBaseLayers().get(1).setVisible(true);
                             btnBoolean[0] = true;
                         } else {
                             mapView.getMap().getBasemap().getBaseLayers().get(0).setVisible(true);
                             mapView.getMap().getBasemap().getBaseLayers().get(1).setVisible(false);
                             btnBoolean[0] = false;
                         }

                     }
                 });
             }
         }
     }
    private void BtnTouth(ImageButton btn) {
        btn.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent motionEvent) {
                if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                    btn.setBackgroundColor(Color.BLUE);//设置图片透明度0-255，0完全透明
                    btn.invalidate();
                } else {
                    btn.setBackgroundColor(Color.WHITE);//还原图片
                    btn.invalidate();
                }
                return false;
            }
        });
    }
    //定位相关函数
    private void loBtn() {
        //请求权限
        List<String> permissionList = new ArrayList<>();
        if (ContextCompat.checkSelfPermission(context, Manifest.
                permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            permissionList.add(Manifest.permission.ACCESS_FINE_LOCATION);
        }
        if (ContextCompat.checkSelfPermission(context, Manifest.
                permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            permissionList.add(Manifest.permission.ACCESS_COARSE_LOCATION);
        }
        if (!permissionList.isEmpty()) {
            String[] permissions = permissionList.toArray(new String[permissionList.size()]);
            ActivityCompat.requestPermissions((MainActivity)context, permissions, 1);
        }

        mLocationDisplay = mapView.getLocationDisplay();

        mLocationDisplay.setAutoPanMode(LocationDisplay.AutoPanMode.RECENTER);
        //显示定位的符号
        mLocationDisplay.isShowLocation();
        mLocationDisplay.setShowAccuracy(false);
        if (!mLocationDisplay.isStarted())
            mLocationDisplay.startAsync();
    }
}

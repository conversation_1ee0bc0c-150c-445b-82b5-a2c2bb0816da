<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="background_gradient_end">#DDDDDD</color>
    <color name="background_gradient_start">#000000</color>
    <color name="black">#FF000000</color>
    <color name="colorPrimary">#FF018786</color>
    <color name="default_background">#3d3d3d</color>
    <color name="fastlane_background">#0096a6</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="search_opaque">#ffaa3f</color>
    <color name="selected_background">#ffaa3f</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="toolbar_background">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <string name="image_2023_service">http://***********:6080/geoscene/rest/services/Image2023/MapServer</string>
    <string name="polygonButtonDescription">绘制面</string>
    <string name="polylineButtonDescription">绘制线</string>
    <string name="redoButtonDescription">恢复</string>
    <string name="stopButtonDescription">完成</string>
    <string name="undoButtonDescription">撤回</string>
    <style name="color_btn">
        <item name="layout_constraintLeft_toLeftOf">parent</item>
        <item name="layout_constraintTop_toTopOf">parent</item>
        <item name="android:layout_width">40dp</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:padding">5dp</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:radius">10dp</item>
        <item name="android:background">@color/white</item>
    </style>
    <style name="map_btn">
        <item name="layout_constraintLeft_toLeftOf">parent</item>
        <item name="layout_constraintTop_toTopOf">parent</item>
        <item name="android:layout_width">40dp</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:padding">5dp</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:radius">10dp</item>
        <item name="android:background">@color/white</item>
    </style>
    <style name="measurement_btn">
        <item name="android:background">@drawable/button_bar_background</item>
        <item name="android:layout_alignParentTop">true</item>
        <item name="android:layout_height">30dp</item>
        <item name="android:layout_width">30dp</item>
        <item name="android:padding">5dp</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="layout_constraintBottom_toBottomOf">parent</item>
        <item name="layout_constraintHorizontal_bias">0.5</item>
    </style>
</resources>
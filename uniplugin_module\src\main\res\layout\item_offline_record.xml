<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="样点编号: "
                android:textStyle="bold" />

            <TextView
                android:id="@+id/textViewYdbh"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="YD001" />

            <TextView
                android:id="@+id/textViewStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="未上传"
                android:textColor="#FF0000"
                android:layout_marginStart="8dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="状态: "
                android:textStyle="bold" />

            <TextView
                android:id="@+id/textViewZt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="正常" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="描述: "
                android:textStyle="bold" />

            <TextView
                android:id="@+id/textViewDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="这是一条描述信息"
                android:maxLines="2"
                android:ellipsize="end" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="时间: "
                android:textStyle="bold" />

            <TextView
                android:id="@+id/textViewTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="2023-05-01 12:30:45" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/buttonView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="查看"
                android:textSize="12sp"
                android:layout_marginEnd="4dp"
                android:backgroundTint="#1971AC" />

            <Button
                android:id="@+id/buttonUpload"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="上传"
                android:textSize="12sp"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:backgroundTint="#1971AC" />

            <Button
                android:id="@+id/buttonDelete"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="删除"
                android:textSize="12sp"
                android:layout_marginStart="4dp"
                android:backgroundTint="#FF5722" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView> 
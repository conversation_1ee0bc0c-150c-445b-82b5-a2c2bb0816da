<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000"
    tools:context=".ImagePreviewActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar_preview"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="#33000000"
        app:navigationIcon="@android:drawable/ic_menu_close_clear_cancel"
        app:navigationIconTint="#FFFFFF"
        android:elevation="4dp" />

    <com.github.chrisbanes.photoview.PhotoView
        android:id="@+id/imageViewFullscreen"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitCenter" />

</RelativeLayout> 
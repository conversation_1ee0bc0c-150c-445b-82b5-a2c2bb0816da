package com.deltaphone.cameramodule.camera;

import android.Manifest;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ImageFormat;
import android.graphics.Matrix;
import android.graphics.Rect;
import android.graphics.RectF;
import android.hardware.Camera;
import android.media.CamcorderProfile;
import android.media.MediaCodecInfo;
import android.media.MediaCodecList;
import android.media.MediaRecorder;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.MotionEvent;
import android.view.OrientationEventListener;
import android.view.Surface;
import android.view.SurfaceHolder;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.RequiresPermission;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * Created by Lyq on 2017/6/29.
 */

public class ApiCamera {
    protected final String TAG = getClass().getSimpleName();
    private Context mContext;
    private Camera camera;
    private MediaRecorder mediaRecorder;
    private int currentFacing = Camera.CameraInfo.CAMERA_FACING_BACK;
    private static final int maxDefaultFileCount = 100;
    private boolean isPreview = true;
    private boolean isRecording = false;
    private File mediaFile = null;
    private int currentOrientation = 0;
    private int photoRotation = 0;
    private OrientationEventListener mOrientationEventListener;
    private Camera.Size bestSize;

    private static final float DEFAULT_SCALE = 4f / 3;

    public ApiCamera(Context context) {
        this.mContext = context;
    }

    public int getCurrentFacing() {
        return currentFacing;
    }

    public void setCurrentFacing(int currentFacing) {
        this.currentFacing = currentFacing;
    }

    //TODO:录制视频的暂停功能

    /**
     * @param holder
     * @param defaultFacing {@link Camera.CameraInfo#CAMERA_FACING_BACK} or {@link Camera.CameraInfo#CAMERA_FACING_FRONT}
     * @return
     */
    @RequiresPermission(Manifest.permission.CAMERA)
    public boolean openCamera(SurfaceHolder holder, int defaultFacing) {
        try {
            camera = openCamera(defaultFacing);
            if (camera == null) return false;
            currentFacing = defaultFacing;
            camera.setPreviewDisplay(holder);
        } catch (IOException e) {
            e.printStackTrace();
            release();
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            release();
            return false;
        }
        return true;
    }

    private Camera openCamera(int cameraFacing) {
        int id = getCameraId(cameraFacing);
        if (id == -1) return null;
        Camera camera = null;
        try {
            camera = Camera.open(id);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (camera == null) return null;
        initParameters(camera);
        return camera;
    }

    //    获取照相机摄像头的id
    private void initParameters(Camera camera) {
        if (camera == null) return;
        Camera.Parameters parameters = camera.getParameters();
        setCameraPreviewSize(parameters);
        if (currentFacing == Camera.CameraInfo.CAMERA_FACING_BACK) {
            parameters.setFocusMode(Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE);
        }
        currentOrientation = getCameraDisplayOrientation(mContext, getCameraId(currentFacing));
        camera.setParameters(parameters);
        camera.setDisplayOrientation(currentOrientation);
    }

    public void startPreview() {
        camera.startPreview();//开启相机预览
        isPreview = true;//相机正在处于预览状态
        if (mOrientationEventListener == null) {
            //这里是定义一个旋转事件监听器
            mOrientationEventListener = new OrientationEventListener(mContext) {
                @Override
                public void onOrientationChanged(int orientation) {
                    if (ORIENTATION_UNKNOWN == orientation) {
                        return;
                    }
                    int screenOrientation = mContext.getResources().getConfiguration().orientation;
                    if (camera != null) {
                        if (orientation >= 45 && orientation < 135) {
                            //90
                            photoRotation = screenOrientation == Configuration.ORIENTATION_PORTRAIT ? 90 : 180;
                        } else if (orientation >= 135 && orientation < 225) {
                            //180
                            photoRotation = screenOrientation == Configuration.ORIENTATION_PORTRAIT ? 180 : 270;
                        } else if (orientation >= 225 && orientation < 315) {
                            //270
                            photoRotation = screenOrientation == Configuration.ORIENTATION_PORTRAIT ? 270 : 0;
                        } else {
                            //0
                            photoRotation = screenOrientation == Configuration.ORIENTATION_PORTRAIT ? 0 : 90;
                        }
                    }
                }
            };
        }
        //启用屏幕旋转事件监听器，开启监听
        mOrientationEventListener.enable();
    }

    public void stopPreview() {
        camera.stopPreview();
        isPreview = false;
        mOrientationEventListener.disable();
    }

    public boolean isPreview() {
        return isPreview;
    }

    //获取相机显示方向
    private int getCameraDisplayOrientation(Context context, int cameraId) {
        android.hardware.Camera.CameraInfo info =
                new android.hardware.Camera.CameraInfo();
        android.hardware.Camera.getCameraInfo(cameraId, info);
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        int rotation = windowManager.getDefaultDisplay()
                .getRotation();
        int degrees = 0;
        switch (rotation) {
            case Surface.ROTATION_0:
                degrees = 0;
                break;
            case Surface.ROTATION_90:
                degrees = 90;
                break;
            case Surface.ROTATION_180:
                degrees = 180;
                break;
            case Surface.ROTATION_270:
                degrees = 270;
                break;
        }

        int result;
        if (info.facing == Camera.CameraInfo.CAMERA_FACING_FRONT) {
            result = (info.orientation + degrees) % 360;
            result = (360 - result) % 360;  // 将前置摄像头旋转方向调整为镜像
        } else {  // 后置摄像头
            result = (info.orientation - degrees + 360) % 360;
        }
        return result;
    }

    private int getCameraId(int cameraFacing) {
        int numberOfCameras = Camera.getNumberOfCameras();
        Camera.CameraInfo cameraInfo = new Camera.CameraInfo();
        for (int i = 0; i < numberOfCameras; i++) {
            Camera.getCameraInfo(i, cameraInfo);
            if (cameraInfo.facing == cameraFacing) {
                return i;
            }
        }
        return -1;
    }

    //设置相机预览大小
    private void setCameraPreviewSize(Camera.Parameters parameters) {
        List<Camera.Size> sizes = getSupportedPreviewSizes(parameters);
        if (sizes == null || sizes.size() == 0) return;
//        Camera.Size bestSize = null;
        int w = getScreenWidth(mContext);
        int h = w * 4 / 3;
        Log.d("size", "全屏宽度和计算高度" + w + "*" + h);
//        bestSize = (Camera.Size) getOptimalPreviewSize(sizes,w,h);
        bestSize = chooseOptimalSize(sizes, w, h);
//        bestSize = changePreviewSize(sizes,w,h);
//        for (Camera.Size s : sizes) {
////            if ((float) s.width / s.height == DEFAULT_SCALE) {
////                bestSize = s;
////                break;
////            }
////        }
////        if (bestSize == null) bestSize = sizes.get(0);
        Log.d("size", "setCameraPreviewSize: " + bestSize.width + "*" + bestSize.height);
        parameters.setPreviewSize(bestSize.width, bestSize.height);
    }

    public static int getScreenWidth(Context context) {
        DisplayMetrics displayMetrics = new DisplayMetrics();
        ((WindowManager) (context.getSystemService(Context.WINDOW_SERVICE))).getDefaultDisplay().getMetrics(displayMetrics);
        return displayMetrics.widthPixels;
    }

    public static int getScreenHeight(Context context) {
        DisplayMetrics displayMetrics = new DisplayMetrics();
        ((WindowManager) (context.getSystemService(Context.WINDOW_SERVICE))).getDefaultDisplay().getMetrics(displayMetrics);
        return displayMetrics.heightPixels;
    }

    private Camera.Size getOptimalPreviewSize(List<Camera.Size> sizes, int w, int h) {
        final double ASPECT_TOLERANCE = 0.2;
        double targetRatio = (double) w / h;
        if (sizes == null)
            return null;
        Camera.Size optimalSize = null;
        double minDiff = Double.MAX_VALUE;
        int targetHeight = h;
        for (Camera.Size size : sizes) {
            Log.d("size", "Checking size " + size.width + "w " + size.height + "h");
            double ratio = (double) size.width / size.height;
            if (Math.abs(ratio - targetRatio) > ASPECT_TOLERANCE)
                continue;
            if (Math.abs(size.height - targetHeight) < minDiff) {
                optimalSize = size;
                minDiff = Math.abs(size.height - targetHeight);
            }
        }

        if (optimalSize == null) {
            minDiff = Double.MAX_VALUE;
            for (Camera.Size size : sizes) {
                if (Math.abs(size.height - targetHeight) < minDiff) {
                    optimalSize = size;
                    minDiff = Math.abs(size.height - targetHeight);
                }
            }
        }
        Log.d("Size", "Using size: " + optimalSize.width + "w " + optimalSize.height + "h");
        return optimalSize;
    }

    /**
     * 修改相机的预览尺寸，调用此方法就行
     *
     * @param sizeList   相机支持的尺寸列表
     * @param viewWidth  预览的surfaceView的宽
     * @param viewHeight 预览的surfaceView的高
     * @return
     */
    protected Camera.Size changePreviewSize(List<Camera.Size> sizeList, int viewWidth, int viewHeight) {
        Camera.Size closelySize = null;//储存最合适的尺寸
        for (Camera.Size size : sizeList) { //先查找preview中是否存在与surfaceview相同宽高的尺寸
            if ((size.width == viewWidth) && (size.height == viewHeight)) {
                closelySize = size;
            }
        }
        if (closelySize == null) {
            // 得到与传入的宽高比最接近的size
            float reqRatio = ((float) viewWidth) / viewHeight;
            float curRatio, deltaRatio;
            float deltaRatioMin = Float.MAX_VALUE;
            for (Camera.Size size : sizeList) {
                if (size.width < 1024) continue;//1024表示可接受的最小尺寸，否则图像会很模糊，可以随意修改
                curRatio = ((float) size.width) / size.height;
                deltaRatio = Math.abs(reqRatio - curRatio);
                if (deltaRatio < deltaRatioMin) {
                    deltaRatioMin = deltaRatio;
                    closelySize = size;
                }
            }
        }
        Log.d("size", "changePreviewSize: " + closelySize.width + "*" + closelySize.height);
        if (closelySize != null) {
            Log.i("changePreviewSize", "预览尺寸修改为：" + closelySize.width + "*" + closelySize.height);
            return closelySize;
        }
        return closelySize;
    }

    private static boolean isRatioEqual(Camera.Size size) {
        int x = size.width;
        int y = size.height;
        // 求最大公约数
        int gcd = getGCD(x, y);
        // 约分后得到的分子和分母
        int numerator = x / gcd;
        int denominator = y / gcd;
        // 判断是否等于3：4
        return numerator == 4 && denominator == 3;
    }

    // 辗转相除法求最大公约数
    private static int getGCD(int x, int y) {
        if (y == 0) {
            return x;
        }
        return getGCD(y, x % y);
    }

    /**
     * 选择最符合4：3的尺寸
     *
     * @param previewSizes
     * @param surfaceHeight 这个高度是根据实际屏幕宽度和3：4的比例来计算的
     * @param surfaceWidth
     * @return
     */
    private Camera.Size chooseOptimalSize(List<Camera.Size> previewSizes, int surfaceHeight, int surfaceWidth) {
        //从预览尺寸列表中选择一个最接近SurfaceView尺寸的3:4的尺寸
        Camera.Size previewSize = null;
        int minDiff = Integer.MAX_VALUE;
        for (Camera.Size size : previewSizes) {
//                Log.d("size", "循环的宽高: "+size.width+"*"+size.height);
            if (isRatioEqual(size) && size.height <= 1080) {
//                Log.d("size", "符合4：3的宽高比且高度<=1080: "+size.width+"*"+size.height);
                int diff = Math.abs(size.width - surfaceWidth) + Math.abs(size.height - surfaceHeight);
                if (diff < minDiff) {
                    minDiff = diff;
                    previewSize = size;
                }
            }
        }
//        Log.d("size", "最终的previewSize: "+ previewSize.width + "*" + previewSize.height);
        return previewSize;
    }


    /**
     * 只有在open camera之后使用有效，获取相机的预览大小
     *
     * @return
     */
    public Camera.Size getCameraPreviewSize() {
        if (camera == null || camera.getParameters() == null) return null;
        return camera.getParameters().getPreviewSize();
    }

    //设置相机图片像素
    private void setCameraPictureSize(Camera.Parameters parameters, long maxPixels) {
        Camera.Size previewSize = getCameraPreviewSize();
        List<Camera.Size> sizes = getSupportedPictureSizes(parameters);
        if (previewSize == null || sizes == null || sizes.size() == 0) return;
        Camera.Size bestSize1 = null;
        int w = getScreenWidth(mContext);
        int h = w * 4 / 3;
        Log.d("size", "图片中全屏宽度和计算高度" + w + "*" + h);
        bestSize1 = chooseOptimalSize(sizes, w, h);
//        int w = getScreenWidth(mContext);
//        int h = getScreenHeight(mContext);
//        bestSize = (Camera.Size) getOptimalPreviewSize(sizes,w,h);
//        for (Camera.Size s : sizes) {
//            if ((float) s.width / s.height == (float) previewSize.width / previewSize.height) {
//                bestSize = s;
//                if (bestSize.width * bestSize.height <= maxPixels) {
//                    break;
//                }
//            }
//        }
//        if (bestSize == null) bestSize = sizes.get(0);
        Log.d("size", "setCameraPictureSize: " + bestSize1.width + "*" + bestSize1.height);
        parameters.setPictureSize(bestSize1.width, bestSize1.height);
    }

    /**
     * 检查并设置支持的对焦模式
     *
     * @param parameters
     */
    private void setCameraFocusModes(Camera.Parameters parameters) {
        List<String> supportedFocusModes = parameters.getSupportedFocusModes();
        if (supportedFocusModes != null && supportedFocusModes.contains(Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE)) {
            parameters.setFocusMode(Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE);
        }
        camera.setParameters(parameters);
    }

    private void setCameraVideoSize(MediaRecorder mediaRecorder, Camera.Parameters parameters) {
        Camera.Size previewSize = parameters.getPreviewSize();
        List<Camera.Size> sizes = getSupportedVideoSizes(parameters);
        if (previewSize == null || sizes == null || sizes.size() == 0) return;
        Camera.Size bestSize = null;
        int w = getScreenWidth(mContext);
        int h = w * 4 / 3;
        Log.d("size", "视频中全屏宽度和计算高度" + w + "*" + h);
        bestSize = chooseOptimalSize(sizes, w, h);
//        for (Camera.Size s : sizes) {
//            if ((float) s.width / s.height == (float) previewSize.width / previewSize.height) {
//                bestSize = s;
//                if (bestSize.width * bestSize.height <= previewSize.width * previewSize.height) {
//                    break;
//                }
//            }
//        }
//        if (bestSize == null) bestSize = sizes.get(0);
        mediaRecorder.setVideoSize(bestSize.width, bestSize.height);
    }

    private List<Camera.Size> getSupportedPreviewSizes(Camera.Parameters parameters) {
        if (parameters == null) {
            return new ArrayList<Camera.Size>();
        }
        List<Camera.Size> sizes = parameters.getSupportedPreviewSizes();
        Collections.sort(sizes, new Comparator<Camera.Size>() {
            @Override
            public int compare(Camera.Size o1, Camera.Size o2) {
                return o2.width * o2.height - o1.width * o1.height;
            }
        });
        return sizes;
    }

    private List<Camera.Size> getSupportedPictureSizes(Camera.Parameters parameters) {
        if (parameters == null) {
            return new ArrayList<Camera.Size>();
        }
        List<Camera.Size> sizes = parameters.getSupportedPictureSizes();
        Collections.sort(sizes, new Comparator<Camera.Size>() {
            @Override
            public int compare(Camera.Size o1, Camera.Size o2) {
                return o2.width * o2.height - o1.width * o1.height;
            }
        });
        return sizes;
    }

    private List<Camera.Size> getSupportedVideoSizes(Camera.Parameters parameters) {
        if (parameters == null) {
            return new ArrayList<Camera.Size>();
        }
        List<Camera.Size> sizes = parameters.getSupportedVideoSizes();
        Collections.sort(sizes, new Comparator<Camera.Size>() {
            @Override
            public int compare(Camera.Size o1, Camera.Size o2) {
                return o2.width * o2.height - o1.width * o1.height;
            }
        });
        return sizes;
    }

    public boolean isSupportZoom() {
        if (camera == null) return false;
        if (camera.getParameters() == null) return false;
        return camera.getParameters().isZoomSupported();
    }

    //计算手指间距
    private static float getFingerSpacing(MotionEvent event) {
        float x = event.getX(0) - event.getX(1);
        float y = event.getY(0) - event.getY(1);
        return (float) Math.sqrt(x * x + y * y);
    }

    private float oldDist = 1f;
    private int cameraZoomValue = 1;

    public boolean onTouchEvent(MotionEvent event, TextView textView) {
        if (event.getPointerCount() == 1) {
            // handle single touch event
        } else {
            if (camera != null) {
                switch (event.getAction() & MotionEvent.ACTION_MASK) {
                    case MotionEvent.ACTION_POINTER_DOWN:
                        oldDist = getFingerSpacing(event);
                        break;
                    case MotionEvent.ACTION_MOVE:
                        float newDist = getFingerSpacing(event);
                        if (newDist > oldDist) {
                            cameraZoomValue = zoomIn();
                        } else if (newDist < oldDist) {
                            cameraZoomValue = zoomOut();
                        }
                        oldDist = newDist;
                        break;
                }
                Log.d("camera zoom", "onTouchEvent: " + cameraZoomValue + "x");
                if (cameraZoomValue + "x" != textView.getTag()) {
                    textView.setText(cameraZoomValue + "x");
                    textView.setTag(cameraZoomValue);
                }
            }
        }
        return true;
    }

    public int zoomIn() {
        if (isSupportZoom()) {
            try {
                Camera.Parameters params = camera.getParameters();
                final int MAX = params.getMaxZoom();
                if (MAX == 0) return MAX;
                int zoomValue = params.getZoom();
                Log.i("camera zoom", "previous " + zoomValue);
                zoomValue += 5;
                params.setZoom(Math.min(zoomValue, MAX));
                Log.i("camera zoom", "current " + Math.min(zoomValue, MAX));
                camera.setParameters(params);
                return zoomValue;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return 0;
    }

    public int zoomOut() {
        if (isSupportZoom()) {
            try {
                Camera.Parameters params = camera.getParameters();
                final int MAX = params.getMaxZoom();
                if (MAX == 0) return MAX;
                int zoomValue = params.getZoom();
                Log.i("camera zoom", "previous " + zoomValue);
                zoomValue -= 5;
                params.setZoom(Math.max(zoomValue, 0));
                Log.i("camera zoom", "current " + Math.max(zoomValue, 0));
                camera.setParameters(params);
                return zoomValue;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return 0;
    }


    public boolean isZoomMax() {
        if (isSupportZoom()) {
            try {
                Camera.Parameters params = camera.getParameters();
                final int MAX = params.getMaxZoom();
                int zoomValue = params.getZoom();
                return zoomValue == MAX;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return true;
    }

    public boolean isZoomMin() {
        if (isSupportZoom()) {
            try {
                Camera.Parameters params = camera.getParameters();
                int zoomValue = params.getZoom();
                return zoomValue == 0;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return true;
    }

    public boolean onTouchEventS(MotionEvent event, int sw, int sh) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            //获取触摸的坐标
            float x = event.getX();
            float y = event.getY();
            //计算对焦区域
            Rect focusRect = calculateTapArea(x, y, 1f, sw, sh);
            //设置对焦区域
            Camera.Parameters parameters = camera.getParameters();
            parameters.setFocusMode(Camera.Parameters.FOCUS_MODE_AUTO);
            if (parameters.getMaxNumFocusAreas() > 0) {
                //只有在支持对焦区域的情况下才设置对焦区域
                List<Camera.Area> areas = new ArrayList<>();
                areas.add(new Camera.Area(focusRect, 1000));
                parameters.setFocusAreas(areas);
            }
            //设置完对焦区域后，要调用autoFocus方法进行自动对焦
            try {
                camera.cancelAutoFocus();
                camera.setParameters(parameters);
                camera.autoFocus(new Camera.AutoFocusCallback() {
                    @Override
                    public void onAutoFocus(boolean success, Camera camera) {
                        //对焦成功后的回调
                    }
                });
            } catch (Exception e) {
                Log.e("CustomCameraView", "Error focusing camera: " + e.getMessage());
            }
        }
        return true;
    }

    private Rect calculateTapArea(float x, float y, float coefficient, int sw, int sh) {
        int padding = 50;
        int areaSize = Float.valueOf(200 * coefficient).intValue();

        int left = clamp(Float.valueOf(x - areaSize / 2).intValue(), padding, sw - padding - areaSize);
        int top = clamp(Float.valueOf(y - areaSize / 2).intValue(), padding, sh - padding - areaSize);

        RectF rectF = new RectF(left, top, left + areaSize, top + areaSize);
        return new Rect(Math.round(rectF.left), Math.round(rectF.top), Math.round(rectF.right), Math.round(rectF.bottom));
    }

    private int clamp(int x, int min, int max) {
        if (x < min) {
            return min;
        }
        if (x > max) {
            return max;
        }
        return x;
    }

    private void setCameraVideoFocusModes(Camera.Parameters parameters) {
        List<String> supportedFocusModes = parameters.getSupportedFocusModes();
        if (supportedFocusModes.contains(Camera.Parameters.FOCUS_MODE_CONTINUOUS_VIDEO)) {
            // 设备支持连续自动对焦模式
            parameters.setFocusMode(Camera.Parameters.FOCUS_MODE_CONTINUOUS_VIDEO);
        }
        camera.setParameters(parameters);
    }

    //增加判断手机是否支持用h264编码
    public boolean isH264Supported() {
        boolean isSupported = false;
        // 获取手机上支持的视频编码格式列表
        int codecCount = MediaCodecList.getCodecCount();
        for (int i = 0; i < codecCount; i++) {
            MediaCodecInfo codecInfo = MediaCodecList.getCodecInfoAt(i);
            if (!codecInfo.isEncoder()) {
                continue;
            }
            String[] supportedTypes = codecInfo.getSupportedTypes();
            for (String type : supportedTypes) {
                if (type.equalsIgnoreCase(String.valueOf(MediaRecorder.OutputFormat.MPEG_4)) &&
                        codecInfo.getName().equalsIgnoreCase(String.valueOf(MediaRecorder.VideoEncoder.H264))) {
                    isSupported = true;
                    break;
                }
            }
            if (isSupported) {
                break;
            }
        }
        return isSupported;
    }


    private boolean prepareVideoRecorder(File file, Surface surface) {
        if (!isPreview) return false;
        try {
            if (mediaRecorder != null) {
                releaseMediaRecorder();
            }
            Camera.Parameters parameters = camera.getParameters();
//            parameters.setFocusMode(Camera.Parameters.FOCUS_MODE_CONTINUOUS_VIDEO);//可能有些手机没有这种聚焦模式
            setCameraVideoFocusModes(parameters);
            camera.setParameters(parameters);
            mediaRecorder = new MediaRecorder();
            camera.unlock();
            mediaRecorder.setCamera(camera);

            //设置音频和视频源编码质量
            mediaRecorder.setAudioSource(MediaRecorder.AudioSource.CAMCORDER);
            mediaRecorder.setVideoSource(MediaRecorder.VideoSource.CAMERA);
            CamcorderProfile profile = CamcorderProfile.get(CamcorderProfile.QUALITY_LOW);
            profile.fileFormat = MediaRecorder.OutputFormat.MPEG_4;//输出格式
            if (isH264Supported()) {
                profile.videoCodec = MediaRecorder.VideoEncoder.H264;
            }
            //Log.d("size", "CamcorderProfile.get(CamcorderProfile.QUALITY_LOW)"+profile.videoFrameWidth+"*"+profile.videoFrameHeight);
            mediaRecorder.setProfile(profile);
            mediaFile = getTargetFile(file);
            if (mediaFile == null) return false;
//            Camera.Size videoSize = getVideoSize(currentCameraSize);
            setCameraVideoSize(mediaRecorder, parameters);
//            mediaRecorder.setVideoSize(1280, 960);
//            mediaRecorder.setVideoSize(profile.videoFrameWidth, profile.videoFrameHeight);
//            mediaRecorder.setVideoSize(parameters.getPreviewSize().width, parameters.getPreviewSize().height);
//            mediaRecorder.setVideoSize(currentPreviewSize1.width, currentPreviewSize1.height);
            mediaRecorder.setOrientationHint(currentOrientation);
            mediaRecorder.setOutputFile(mediaFile.getPath());
            mediaRecorder.setPreviewDisplay(surface);
            mediaRecorder.prepare();
        } catch (Exception e) {
            e.printStackTrace();
            releaseMediaRecorder();
            return false;
        }
        return true;
    }

    private boolean prepareAudioRecorder(File file, Surface surface) {
        try {
            if (mediaRecorder != null) {
                releaseMediaRecorder();
            }
            mediaRecorder = new MediaRecorder();
            mediaRecorder.setAudioSource(MediaRecorder.AudioSource.MIC);
            mediaRecorder.setOutputFormat(MediaRecorder.OutputFormat.THREE_GPP);
            mediaRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.AMR_NB);
            mediaFile = getTargetFile(file);
            if (mediaFile == null) return false;
            mediaRecorder.setOutputFile(mediaFile.getPath());
            mediaRecorder.prepare();
        } catch (Exception e) {
            e.printStackTrace();
            releaseMediaRecorder();
            return false;
        }
        return true;
    }

    public boolean startVideoRecorder(File file, Surface surface) {
        try {
            if (prepareVideoRecorder(file, surface)) {
                mediaRecorder.start();
                isRecording = true;
                return true;
            } else {
                isRecording = false;
                return false;
            }
        } catch (Exception e) {
            isRecording = false;
            return false;
        }
    }

    public boolean isRecording() {
        return isRecording;
    }

    public File endMediaRecorder() {
        releaseMediaRecorder();
        isRecording = false;
        File file = mediaFile;
        mediaFile = null;
        return file;
    }

    public boolean startAudioRecorder(File file, Surface surface) {
        if (prepareAudioRecorder(file, surface)) {
            mediaRecorder.start();
            isRecording = true;
            return true;
        } else {
            isRecording = false;
            return false;
        }
    }

    private File getTargetFile(File file) {
        String fileName = file.getName();
        String array[] = fileName.split("\\.");
        if (array == null || array.length < 2) {
            return null;
        }
        String suffix = "." + array[array.length - 1];
        String prefix = fileName.substring(0, fileName.lastIndexOf(suffix));
        file = file.getParentFile();
        String folderPath = file.getPath();
        if (file.exists()) {
            if (!file.isDirectory()) {
                file.mkdirs();
            }
        } else {
            file.mkdirs();
        }
        file = new File(folderPath + "/" + fileName);
        if (file.exists()) {
            for (int i = 1; i < maxDefaultFileCount; i++) {
                String aliasName = prefix + "_（" + i + "）" + suffix;
                file = new File(folderPath + "/" + aliasName);
                if (!file.exists()) {
                    break;
                }
                if (i == maxDefaultFileCount - 1) {
                    file = null;
                }
            }
        }
        return file;
    }

    /**
     * 切换相机之后应该StartPreview
     *
     * @param holder
     * @return
     */
    @RequiresPermission(Manifest.permission.CAMERA)
    public boolean changeCamera(SurfaceHolder holder) {
        if (isRecording) return false;
        if (currentFacing == Camera.CameraInfo.CAMERA_FACING_BACK) {
            currentFacing = Camera.CameraInfo.CAMERA_FACING_FRONT;
        } else if (currentFacing == Camera.CameraInfo.CAMERA_FACING_FRONT) {
            currentFacing = Camera.CameraInfo.CAMERA_FACING_BACK;
        }
        if (camera != null) {
            release();
        }
        return openCamera(holder, currentFacing);
    }

    /**
     * 切换相机之后应该StartPreview
     *
     * @param holder
     * @param cameraFacing {@link Camera.CameraInfo#CAMERA_FACING_BACK} or {@link Camera.CameraInfo#CAMERA_FACING_FRONT}
     * @return
     */
    @RequiresPermission(Manifest.permission.CAMERA)
    public boolean changeCamera(SurfaceHolder holder, int cameraFacing) {
        if (isRecording) return false;
        if (camera != null) {
            if (cameraFacing == currentFacing) return true;
            release();
        }
        if (isRecording) return false;
        currentFacing = cameraFacing;
        return openCamera(holder, currentFacing);
    }

    public void takePhoto(final File file, long maxPixels, final TakePictureCallback callback) {
        if (camera == null || isRecording) {
            //判断相机是否初始化或正在录像
            callback.onPictureTaken(null, photoRotation);
            return;
        }

        //获取相机的参数Parameters，设置照片为jpeg格式，压缩质量为100，连续自动对焦
        Camera.Parameters parameters = camera.getParameters();
        parameters.setPictureFormat(ImageFormat.JPEG);
        parameters.setJpegQuality(85);//一百是最好的质量，但是这里不需要那么高
        setCameraPictureSize(parameters, maxPixels);//设置像素
//        parameters.setFocusMode(Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE);//遇到了不支持这种模式的情况，用函数单独判断好了
        setCameraFocusModes(parameters);//设置对焦模式
        camera.setParameters(parameters);

        try {
            camera.takePicture(null, null, new Camera.PictureCallback() {
                @Override
                public void onPictureTaken(byte[] data, Camera camera) {
                    isPreview = false;
                    File pictureFile = getTargetFile(file);
                    if (pictureFile == null) {
                        callback.onPictureTaken(null, photoRotation);
                        return;
                    }
                    try {
                        Bitmap bitmap = BitmapFactory.decodeByteArray(data, 0, data.length);
                        Bitmap fileBitmap = null;
                        if (currentFacing == Camera.CameraInfo.CAMERA_FACING_FRONT) {
                            //前置摄像头
                            fileBitmap = rotateBitmap(bitmap, currentOrientation);
                        } else {
                            //后置摄像头
                            Matrix matrix = new Matrix();
                            matrix.setRotate(currentOrientation + photoRotation);
                            fileBitmap = Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, false);
                        }
                        FileOutputStream fos = new FileOutputStream(pictureFile);
                        fileBitmap.compress(Bitmap.CompressFormat.JPEG, 85, fos);//这里的一百同样是最好的画质，不用这么高
                        fos.close();
                        if (!fileBitmap.isRecycled()) {
                            fileBitmap.recycle();
                        }
                        if (!bitmap.isRecycled()) {
                            bitmap.recycle();
                        }
                        callback.onPictureTaken(pictureFile, photoRotation);
                    } catch (Exception e) {
                        e.printStackTrace();
                        callback.onPictureTaken(null, photoRotation);
                    }
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
            callback.onPictureTaken(null, 410086);
        }
    }

    /**
     * 前置摄像头获取的图片被旋转了180度，要旋转回来
     *
     * @param bitmap
     * @param rotation
     * @return
     */
    private Bitmap rotateBitmap(Bitmap bitmap, int rotation) {
//        Log.d("size", "rotateBitmap: "+currentOrientation+"-"+photoRotation+"-"+rotation);
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();
        Matrix matrix = new Matrix();
        matrix.postScale(-1, 1);
        matrix.postRotate(rotation + photoRotation);
        try {
            Bitmap rotatedBitmap = Bitmap.createBitmap(bitmap, 0, 0, width, height, matrix, true);
            if (bitmap != rotatedBitmap) {
                bitmap.recycle();
            }
            return rotatedBitmap;
        } catch (OutOfMemoryError ex) {
            // 如果内存不足，返回原始位图
            return bitmap;
        }
    }

    public interface TakePictureCallback {
        public void onPictureTaken(File file, int rotation);
    }

    public boolean focusOn(final float x, final float y, final int rectSize) {
        if (camera != null) {
            if (isRecording) return false;
            Camera.Parameters parameters = null;
            try {
                parameters = camera.getParameters();
            } catch (Exception e) {
                e.printStackTrace();
                return false;
            }
            Camera.Size previewSize = getCameraPreviewSize();
            int coordinateX = (int) (2000f / previewSize.width * x - 1000f);
            int coordinateY = (int) (2000f / previewSize.height * y - 1000f);
            int left = Math.max(Math.min(coordinateX - rectSize / 2, 1000), -1000);
            int top = Math.max(Math.min(coordinateY - rectSize / 2, 1000), -1000);
            int right = Math.max(Math.min(left + rectSize, 1000), -1000);
            int bottom = Math.max(Math.min(top + rectSize, 1000), -1000);
            Rect rect = new Rect(left, top, right, bottom);
            Log.d("focusOn---->", rect.toString());
            Camera.Area area = new Camera.Area(rect, 1000);
            if (parameters.getMaxNumFocusAreas() > 0) {
                List<Camera.Area> areas = new ArrayList<Camera.Area>();
                areas.add(area);
                parameters.setFocusAreas(areas);
            } else {
                Log.i(TAG, "focus areas not supported");
                return false;
            }

            try {
                final String currentFocusMode = parameters.getFocusMode();
                camera.cancelAutoFocus();
                parameters.setFocusMode(Camera.Parameters.FOCUS_MODE_AUTO);
                camera.setParameters(parameters);
                camera.autoFocus(new Camera.AutoFocusCallback() {
                    @Override
                    public void onAutoFocus(boolean success, Camera camera) {
                        Log.i("onAutoFocus", String.valueOf(success));
                        if (success) {
                            Camera.Parameters params = camera.getParameters();
                            params.setFocusMode(currentFocusMode);
                            camera.setParameters(params);
                        } else {
                            focusOn(x, y, rectSize);
                        }
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "autoFocus failer");
                return false;
            }
            return true;
        } else {
            return false;
        }
    }

    public void releaseMediaRecorder() {
        if (mediaFile != null) {
            if (mediaFile.exists() && mediaFile.isFile()) {
                long size = mediaFile.length();
                if (size == 0) {
                    mediaFile.delete();
                }
            }
        }
        if (mediaRecorder != null) {
            try {
                mediaRecorder.stop();
            } catch (Exception e) {
            }
            try {
                mediaRecorder.release();
                if (camera != null) {
                    camera.lock();
                }
            } catch (Exception ignored) {
            }
        }
        mediaRecorder = null;
    }

    public boolean isReleaseVideoRecorder() {
        return mediaRecorder == null;
    }

    public boolean isReleaseCamera() {
        return camera == null;
    }

    public void release() {
        releaseMediaRecorder();
        if (camera == null) return;
        try {
            stopPreview();
        } catch (Exception e) {
        }
        try {
            camera.release();
        } catch (Exception e) {
        }
        camera = null;
    }

    public class Size {
        /**
         * Sets the dimensions for pictures.
         *
         * @param w the photo width (pixels)
         * @param h the photo height (pixels)
         */
        public Size(int w, int h, float rate) {
            width = w;
            height = h;
            differRate = rate - (float) width / (float) height;
        }

        public Size(Camera.Size s, float rate) {
            this(s.width, s.height, rate);
        }

        /**
         * Compares {@code obj} to this size.
         *
         * @param obj the object to compare this size with.
         * @return {@code true} if the width and height of {@code obj} is the
         * same as those of this size. {@code false} otherwise.
         */
        @Override
        public boolean equals(Object obj) {
            if (!(obj instanceof Camera.Size)) {
                return false;
            }
            Camera.Size s = (Camera.Size) obj;
            return width == s.width && height == s.height;
        }

        @Override
        public int hashCode() {
            return width * 32713 + height;
        }

        /**
         * width of the picture
         */
        public int width;
        /**
         * height of the picture
         */
        public int height;

        public float differRate;
    }
}
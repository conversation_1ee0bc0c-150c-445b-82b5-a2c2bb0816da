package io.dcloud.uniplugin.samplingPoint;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;

import java.util.ArrayList;
import java.util.List;

import io.dcloud.uniplugin.model.DccyVO;

/**
 * 样点管理ViewPager适配器，用于管理不同状态的样点页面
 */
public class SamplingPointsPagerAdapter extends FragmentStateAdapter {

    List<DccyVO> pendingSurveyPoints = new ArrayList<>();
    List<DccyVO> pendingSubmitPoints = new ArrayList<>();
    List<DccyVO> submittedPoints = new ArrayList<>();
    List<DccyVO> pendingRectifyPoints = new ArrayList<>();
    private Context context;
    private ViewPager2 viewPager;

    public SamplingPointsPagerAdapter(@NonNull FragmentActivity fragmentActivity) {
        super(fragmentActivity);
        this.context = fragmentActivity;
    }
    
    /**
     * 设置ViewPager2
     * @param viewPager ViewPager2实例
     */
    public void setViewPager(ViewPager2 viewPager) {
        this.viewPager = viewPager;
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        // 转换position为1开始的索引
        int tabPosition = position + 1;
        
        // 获取最新数据，确保Fragment创建时使用最新数据
        Log.d("SamplingPointsPagerAdapter", "创建Fragment: position=" + position + ", tabPosition=" + tabPosition);
        
        switch (position) {
            case 0: // 待调查
                Log.d("SamplingPointsPagerAdapter", "创建待调查Fragment，数据量: " + (pendingSurveyPoints != null ? pendingSurveyPoints.size() : 0));
                return SamplingPointListFragment.newInstance(pendingSurveyPoints != null ? pendingSurveyPoints : new ArrayList<>(), 1);
            case 1: // 待提交
                Log.d("SamplingPointsPagerAdapter", "创建待提交Fragment，数据量: " + (pendingSubmitPoints != null ? pendingSubmitPoints.size() : 0));
                return SamplingPointListFragment.newInstance(pendingSubmitPoints != null ? pendingSubmitPoints : new ArrayList<>(), 2);
            case 2: // 已提交
                Log.d("SamplingPointsPagerAdapter", "创建已提交Fragment，数据量: " + (submittedPoints != null ? submittedPoints.size() : 0));
                return SamplingPointListFragment.newInstance(submittedPoints != null ? submittedPoints : new ArrayList<>(), 3);
            case 3: // 待整改
                Log.d("SamplingPointsPagerAdapter", "创建待整改Fragment，数据量: " + (pendingRectifyPoints != null ? pendingRectifyPoints.size() : 0));
                return SamplingPointListFragment.newInstance(pendingRectifyPoints != null ? pendingRectifyPoints : new ArrayList<>(), 4);
            default:
                Log.w("SamplingPointsPagerAdapter", "未知position: " + position + ", 创建默认Fragment");
                return SamplingPointListFragment.newInstance(new ArrayList<>(), 1);
        }
    }

    @Override
    public int getItemCount() {
        return 4; // 四个标签页：待调查、待提交、已提交、待整改
    }

    /**
     * 更新数据
     * @param pendingSurveyPoints 待调查样点
     * @param pendingSubmitPoints 待提交样点
     * @param submittedPoints 已提交样点
     * @param pendingRectifyPoints 待整改样点
     */
    public void updateData(List<DccyVO> pendingSurveyPoints, List<DccyVO> pendingSubmitPoints,
                          List<DccyVO> submittedPoints, List<DccyVO> pendingRectifyPoints) {
        this.pendingSurveyPoints = pendingSurveyPoints != null ? pendingSurveyPoints : new ArrayList<>();
        this.pendingSubmitPoints = pendingSubmitPoints != null ? pendingSubmitPoints : new ArrayList<>();
        this.submittedPoints = submittedPoints != null ? submittedPoints : new ArrayList<>();
        this.pendingRectifyPoints = pendingRectifyPoints != null ? pendingRectifyPoints : new ArrayList<>();
        
        // 更新所有可见的Fragment，而不仅仅是当前的
        updateAllFragments();
        
        // 通知适配器数据已更改
        notifyDataSetChanged();
    }
    
//    /**
//     * 更新当前可见的Fragment
//     */
//    private void updateCurrentFragment() {
//        // 这个方法保留为兼容性目的
//        updateAllFragments();
//    }
    
    /**
     * 更新所有Fragment
     */
    private void updateAllFragments() {
        try {
            FragmentActivity activity = (FragmentActivity) context;
            if (activity != null && !activity.isFinishing()) {
                // 获取FragmentManager
                androidx.fragment.app.FragmentManager fragmentManager = activity.getSupportFragmentManager();
                
                // 更新所有已创建的Fragment
                for (int position = 0; position < getItemCount(); position++) {
                    Fragment fragment = fragmentManager.findFragmentByTag("f" + getItemId(position));
                    if (fragment instanceof SamplingPointListFragment) {
                        SamplingPointListFragment listFragment = (SamplingPointListFragment) fragment;
                        
                        // 转换position为1开始的索引
                        int tabPosition = position + 1;
                        
                        // 确保fragment设置了正确的tabPosition
                        listFragment.setTabPosition(tabPosition);
                        
                        switch (position) {
                            case 0: // 待调查
                                listFragment.updateData(pendingSurveyPoints);
                                break;
                            case 1: // 待提交
                                listFragment.updateData(pendingSubmitPoints);
                                break;
                            case 2: // 已提交
                                listFragment.updateData(submittedPoints);
                                break;
                            case 3: // 待整改
                                listFragment.updateData(pendingRectifyPoints);
                                break;
                        }
                        
                        Log.d("SamplingPointsPagerAdapter", "已更新Fragment: position=" + position + ", tabPosition=" + tabPosition);
                    }
                }
                
                // 强制通知适配器数据已更改，这会重新创建必要的Fragment
                notifyDataSetChanged();
                
                Log.d("SamplingPointsPagerAdapter", "已通知适配器数据更改");
            }
        } catch (Exception e) {
            Log.e("SamplingPointsPagerAdapter", "更新Fragment失败: " + e.getMessage());
        }
    }

    /**
     * 强制更新指定位置的Fragment
     * @param position Fragment位置
     * @param newData 新数据
     */
    public void forceUpdateFragment(int position, List<DccyVO> newData) {
        try {
            FragmentActivity activity = (FragmentActivity) context;
            if (activity != null && !activity.isFinishing()) {
                androidx.fragment.app.FragmentManager fragmentManager = activity.getSupportFragmentManager();
                
                // 尝试多种方式查找Fragment
                Fragment fragment = fragmentManager.findFragmentByTag("f" + position);
                if (fragment == null) {
                    fragment = fragmentManager.findFragmentByTag("f" + getItemId(position));
                }
                
                if (fragment instanceof SamplingPointListFragment) {
                    SamplingPointListFragment listFragment = (SamplingPointListFragment) fragment;
                    listFragment.setTabPosition(position + 1);
                    listFragment.updateData(newData);
                    Log.d("SamplingPointsPagerAdapter", "强制更新Fragment成功: position=" + position + ", 数据量=" + newData.size());
                } else {
                    Log.w("SamplingPointsPagerAdapter", "强制更新Fragment失败，未找到Fragment: position=" + position);
                }
            }
        } catch (Exception e) {
            Log.e("SamplingPointsPagerAdapter", "强制更新Fragment异常: " + e.getMessage());
        }
    }
}
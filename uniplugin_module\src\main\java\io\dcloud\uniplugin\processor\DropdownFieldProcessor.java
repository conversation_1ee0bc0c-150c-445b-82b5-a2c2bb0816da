package io.dcloud.uniplugin.processor;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.Spinner;
import android.widget.TextView;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

import io.dcloud.uniplugin.model.FormConfigResponse;
import uni.dcloud.io.uniplugin_module.R;

public class DropdownFieldProcessor implements FormFieldProcessor {

    @Override
    public View processField(ViewGroup container, FormConfigResponse.Field field) {
        View view = LayoutInflater.from(container.getContext())
                .inflate(R.layout.layout_dropdown_field, container, false);

        TextView labelView = view.findViewById(R.id.field_label);
        Spinner spinner = view.findViewById(R.id.field_spinner);

        labelView.setText(field.getFieldRemark());

        // 将JSON字符串解析为选项列表
        List<FormConfigResponse.FieldOption> options = parseFieldOptions(field.getFieldOptions());
        
        // 准备下拉框数据
        List<String> optionLabels = new ArrayList<>();
        for (FormConfigResponse.FieldOption option : options) {
            optionLabels.add(option.getLabel());
        }

        // 设置下拉框适配器
        ArrayAdapter<String> adapter = new ArrayAdapter<>(
                container.getContext(),
                android.R.layout.simple_spinner_item,
                optionLabels
        );
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinner.setAdapter(adapter);

        // 如果有默认值，设置选中项
        if (field.getValue() != null) {
            String value = field.getValue().toString();
            for (int i = 0; i < options.size(); i++) {
                if (options.get(i).getValue().equals(value)) {
                    spinner.setSelection(i);
                    break;
                }
            }
        }

        return view;
    }

    // 解析JSON字符串为FieldOption列表
    private List<FormConfigResponse.FieldOption> parseFieldOptions(String jsonOptions) {
        if (jsonOptions == null || jsonOptions.isEmpty()) {
            return new ArrayList<>();
        }
        
        Gson gson = new Gson();
        Type optionListType = new TypeToken<List<FormConfigResponse.FieldOption>>(){}.getType();
        return gson.fromJson(jsonOptions, optionListType);
    }
} 
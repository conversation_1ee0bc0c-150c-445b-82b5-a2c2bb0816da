<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.android.UniPlugin" >

    <supports-screens
        android:anyDensity="true"
        android:largeScreens="true"
        android:normalScreens="true"
        android:resizeable="true"
        android:smallScreens="true" /> <!-- Barcode(二维码)  begin -->
    <uses-permission android:name="android.permission.CAMERA" />

    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.READ_LOGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />

    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.FLASHLIGHT" /> <!-- Barcode(二维码)  end -->
    <!-- 往sdcard中读写数据的权限-->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <uses-permission android:name="android.permission.MANAGE_DOCUMENTS"
        tools:ignore="ProtectedPermissions" />

    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>

    <application
        android:name="io.dcloud.application.DCloudApplication"
        android:allowBackup="true"
        android:allowClearUserData="true"
        android:icon="@drawable/app_icon"
        android:label="广东省补充耕地质量验收APP"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        tools:ignore="ScopedStorage">
        <!-- 应用入口 -->
        <activity
            android:name=".NewPandoraEntry"
            android:configChanges="orientation|keyboardHidden|screenSize|mcc|mnc|fontScale"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:theme="@style/TranslucentTheme"
            android:requestLegacyExternalStorage="true"
            android:windowSoftInputMode="adjustResize" >
            <intent-filter>
                <data android:scheme="hbuilder" />
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <intent-filter>
                <data android:scheme="myapp" />
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <intent-filter>
                <data android:mimeType="image/*" />
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <meta-data
            android:name="dcloud_uninview_background"
            android:value="true" />

        <activity
            android:name="io.dcloud.PandoraEntryActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|mcc|mnc|fontScale|keyboard|smallestScreenSize|screenLayout|screenSize"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTask"
            android:permission="com.miui.securitycenter.permission.AppPermissionsEditor"
            android:screenOrientation="user"
            android:theme="@style/DCloudTheme"
            android:windowSoftInputMode="adjustResize" >
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <action android:name="android.intent.action.VIEW" />

                <data android:scheme=" " />
            </intent-filter>
        </activity>
        <activity
            android:name="io.dcloud.uniplugin.MainActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:hardwareAccelerated="true"
            android:launchMode="singleTask">
        </activity>

        <!--        <activity-->
        <!--            android:name="io.dcloud.uniplugin.com.abdu.qrcode.MainActivity" />-->
        <meta-data
            android:name="dcloud_appkey"
            android:value="70de7aa64782ef86128d10681add7f71" />
        <meta-data android:name="com.amap.api.v2.apikey" android:value="cca80c4ea0cef8660e68438708788b8b"></meta-data>
        <service android:name="com.amap.api.location.APSService"></service>
    </application>

</manifest>
package io.dcloud.uniplugin.sample;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import io.dcloud.uniplugin.model.Sample;
import uni.dcloud.io.uniplugin_module.R;

/**
 * 采土袋管理适配器
 */
public class SampleListAdapter extends RecyclerView.Adapter<SampleListAdapter.ViewHolder> {

    private List<Sample> samples;
    private Context context;
    private OnSampleActionListener actionListener;

    /**
     * 样品操作监听接口
     */
    public interface OnSampleActionListener {
        void onPrintSample(Sample sample);
        void onEditSample(Sample sample);
        void onDeleteSample(Sample sample);
    }

    public SampleListAdapter(Context context, List<Sample> samples) {
        this.context = context;
        this.samples = samples != null ? samples : new ArrayList<>();
    }

    public void setOnSampleActionListener(OnSampleActionListener listener) {
        this.actionListener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_sample_list, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        final Sample sample = samples.get(position);

        // 设置样品类型名称 - 修正ID名称
        holder.textViewSampleType.setText(sample.getSampleTypeName() != null ? 
                sample.getSampleTypeName() : "未知类型");

        // 设置样品编号
        holder.textViewSampleNumber.setText(sample.getSampleNumber() != null ? 
                sample.getSampleNumber() : "无编号");

        // 设置样品重量
        if (sample.getSampleWeight() != null) {
            holder.textViewSampleWeight.setText(String.format(Locale.getDefault(), 
                    "样品重量: %.2fg", sample.getSampleWeight()));
        } else {
            holder.textViewSampleWeight.setText("样品重量: 未知");
        }

        // 设置评价单元标识码
        holder.textViewSampleCode.setText("评价单元标识码: " + 
                (sample.getpjdybh() != null ? sample.getpjdybh() : "无"));

        // 设置创建时间
        if (sample.getCreateTime() != null && !sample.getCreateTime().isEmpty()) {
            // 直接使用API返回的日期字符串，不再尝试解析
            holder.textViewCreateTime.setText("创建时间: " + sample.getCreateTime());
        } else {
            holder.textViewCreateTime.setText("创建时间: 未知");
        }

        // 设置打印按钮点击事件
        holder.btnPrintSample.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 确保不是自动触发的，而是用户明确点击的
                if (v.isPressed() && actionListener != null) {
                    actionListener.onPrintSample(sample);
                }
            }
        });

        // 设置修改按钮点击事件
        holder.btnEditSample.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (actionListener != null) {
                    actionListener.onEditSample(sample);
                } else {
                    Toast.makeText(context, "修改样品: " + sample.getSampleNumber(), 
                            Toast.LENGTH_SHORT).show();
                }
            }
        });

        // 设置删除按钮点击事件
        holder.btnDeleteSample.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (actionListener != null) {
                    actionListener.onDeleteSample(sample);
                } else {
                    Toast.makeText(context, "删除样品: " + sample.getSampleNumber(), 
                            Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return samples != null ? samples.size() : 0;
    }

    /**
     * 更新数据
     */
    public void updateData(List<Sample> newSamples) {
        this.samples = newSamples != null ? newSamples : new ArrayList<>();
        notifyDataSetChanged();
    }

    /**
     * 添加样品
     */
    public void addSample(Sample sample) {
        if (sample != null) {
            samples.add(0, sample); // 添加到顶部
            notifyItemInserted(0);
        }
    }

    /**
     * ViewHolder类
     */
    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView textViewSampleType, textViewSampleNumber, textViewSampleWeight, 
                textViewSampleCode, textViewCreateTime;
        Button btnPrintSample, btnEditSample, btnDeleteSample;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            textViewSampleType = itemView.findViewById(R.id.textViewSampleType);
            textViewSampleNumber = itemView.findViewById(R.id.textViewSampleNumber);
            textViewSampleWeight = itemView.findViewById(R.id.textViewSampleWeight);
            textViewSampleCode = itemView.findViewById(R.id.textViewSampleCode);
            textViewCreateTime = itemView.findViewById(R.id.textViewCreateTime);
            btnPrintSample = itemView.findViewById(R.id.btnPrintSample);
            btnEditSample = itemView.findViewById(R.id.btnEditSample);
            btnDeleteSample = itemView.findViewById(R.id.btnDeleteSample);
        }
    }
} 
package io.dcloud.uniplugin.utils;

import android.util.Log;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.util.ArrayList;
import java.util.List;

import io.dcloud.uniplugin.model.DccyDdcVO;
import io.dcloud.uniplugin.model.NewFormResponse;

/**
 * 表单响应转换工具类
 * 用于将新的表单响应格式转换为应用使用的DccyDdcVO对象
 */
public class FormResponseConverter {
    private static final String TAG = "FormResponseConverter";

    /**
     * 将NewFormResponse转换为DccyDdcVO列表
     * @param newResponse 新的表单响应对象
     * @return DccyDdcVO对象列表
     */
    public static List<DccyDdcVO> convertToDccyVOList(NewFormResponse newResponse) {
        List<DccyDdcVO> dccyList = new ArrayList<>();
        
        try {
            // 检查响应是否有效
            if (newResponse == null || newResponse.getData() == null || newResponse.getData().getFields() == null) {
                Log.e(TAG, "响应对象为空或缺少必要数据");
                return dccyList;
            }
            
            // 遍历字段组
            for (NewFormResponse.FieldGroup fieldGroup : newResponse.getData().getFields()) {
                DccyDdcVO dccyDdcVO = new DccyDdcVO();
                
                // 处理字段组中的字段
                if (fieldGroup.getFields() != null) {
                    for (NewFormResponse.FormField field : fieldGroup.getFields()) {
                        if (field.getFieldName() == null || field.getValue() == null) {
                            continue;
                        }
                        
                        // 根据字段名设置DccyDdcVO的属性
                        switch (field.getFieldName()) {
                            case "id":
                                if (field.getValue() instanceof Number) {
                                    dccyDdcVO.setId(((Number) field.getValue()).longValue());
                                } else if (field.getValue() instanceof String) {
                                    try {
                                        dccyDdcVO.setId(Long.parseLong((String) field.getValue()));
                                    } catch (NumberFormatException e) {
                                        Log.e(TAG, "无法解析id字段值: " + field.getValue());
                                    }
                                }
                                break;
                            case "pjdyId":
                                if (field.getValue() instanceof Number) {
                                    dccyDdcVO.setPjdyId(((Number) field.getValue()).longValue());
                                } else if (field.getValue() instanceof String) {
                                    try {
                                        dccyDdcVO.setPjdyId(Long.parseLong((String) field.getValue()));
                                    } catch (NumberFormatException e) {
                                        Log.e(TAG, "无法解析pjdyId字段值: " + field.getValue());
                                    }
                                }
                                break;
                            case "pjdybh":
                                dccyDdcVO.setpjdybh(field.getValue().toString());
                                break;
                            case "dcdwId":
                                if (field.getValue() instanceof Number) {
                                    dccyDdcVO.setDcdwId(((Number) field.getValue()).longValue());
                                }
                                break;
                            case "dcdwName":
                                dccyDdcVO.setDcdw(field.getValue().toString());
                                break;
                            case "dcrId":
                                if (field.getValue() instanceof Number) {
                                    dccyDdcVO.setDcrId(((Number) field.getValue()).longValue());
                                }
                                break;
                            case "dcr":
                                dccyDdcVO.setDcr(field.getValue().toString());
                                break;
                            case "xfrId":
                                if (field.getValue() instanceof Number) {
                                    dccyDdcVO.setXfrId(((Number) field.getValue()).longValue());
                                }
                                break;
                            case "xfr":
                                dccyDdcVO.setXfr(field.getValue().toString());
                                break;
                            case "dcTime":
                                if (field.getValue() instanceof Number) {
                                    dccyDdcVO.setDcTime(((Number) field.getValue()).longValue());
                                }
                                break;
                            case "xfsj":
                                if (field.getValue() instanceof Number) {
                                    dccyDdcVO.setXfsj(((Number) field.getValue()).longValue());
                                }
                                break;
                            case "zt":
                                if (field.getValue() instanceof Number) {
                                    dccyDdcVO.setZt(((Number) field.getValue()).intValue());
                                }
                                break;
                            case "chrId":
                                if (field.getValue() instanceof Number) {
                                    dccyDdcVO.setChrId(((Number) field.getValue()).longValue());
                                }
                                break;
                            case "chr":
                                dccyDdcVO.setChr(field.getValue().toString());
                                break;
                            case "chsj":
                                if (field.getValue() instanceof Number) {
                                    dccyDdcVO.setChsj(((Number) field.getValue()).longValue());
                                }
                                break;
                        }
                    }
                }
                
                dccyList.add(dccyDdcVO);
            }
        } catch (Exception e) {
            Log.e(TAG, "转换NewFormResponse时发生错误: " + e.getMessage(), e);
        }
        
        return dccyList;
    }
    
    /**
     * 从JsonObject直接解析DccyDdcVO列表
     * @param jsonObject API响应的JsonObject
     * @return DccyDdcVO对象列表
     */
    public static List<DccyDdcVO> parseFromJsonObject(JsonObject jsonObject) {
        List<DccyDdcVO> dccyList = new ArrayList<>();
        
        try {
            if (jsonObject == null || !jsonObject.has("data") || jsonObject.get("data").isJsonNull()) {
                Log.e(TAG, "无效的JSON响应或data字段为空");
                return dccyList;
            }
            
            JsonElement dataElement = jsonObject.get("data");
            
            // 处理data是数组的情况
            if (dataElement.isJsonArray()) {
                JsonArray dataArray = dataElement.getAsJsonArray();
                
                for (int i = 0; i < dataArray.size(); i++) {
                    JsonObject item = dataArray.get(i).getAsJsonObject();
                    DccyDdcVO dccyDdcVO = parseItem(item);
                    if (dccyDdcVO != null) {
                        dccyList.add(dccyDdcVO);
                    }
                }
            } 
            // 处理data是对象的情况
            else if (dataElement.isJsonObject()) {
                DccyDdcVO dccyDdcVO = parseItem(dataElement.getAsJsonObject());
                if (dccyDdcVO != null) {
                    dccyList.add(dccyDdcVO);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "解析JsonObject时发生错误: " + e.getMessage(), e);
        }
        
        return dccyList;
    }
    
    /**
     * 解析单个JsonObject为DccyDdcVO对象
     */
    private static DccyDdcVO parseItem(JsonObject item) {
        DccyDdcVO dccyDdcVO = new DccyDdcVO();
        
        try {
            // 读取基本字段
            if (item.has("id") && !item.get("id").isJsonNull()) {
                dccyDdcVO.setId(item.get("id").getAsLong());
            }
            
            if (item.has("pjdyId") && !item.get("pjdyId").isJsonNull()) {
                dccyDdcVO.setPjdyId(item.get("pjdyId").getAsLong());
            }
            
            if (item.has("pjdybh") && !item.get("pjdybh").isJsonNull()) {
                dccyDdcVO.setpjdybh(item.get("pjdybh").getAsString());
            }
            
            if (item.has("dcdwId") && !item.get("dcdwId").isJsonNull()) {
                dccyDdcVO.setDcdwId(item.get("dcdwId").getAsLong());
            }
            
            if (item.has("dcdw") && !item.get("dcdw").isJsonNull()) {
                dccyDdcVO.setDcdw(item.get("dcdw").getAsString());
            }
            
            if (item.has("dcrId") && !item.get("dcrId").isJsonNull()) {
                dccyDdcVO.setDcrId(item.get("dcrId").getAsLong());
            }
            
            if (item.has("dcr") && !item.get("dcr").isJsonNull()) {
                dccyDdcVO.setDcr(item.get("dcr").getAsString());
            }
            
            if (item.has("xfrId") && !item.get("xfrId").isJsonNull()) {
                dccyDdcVO.setXfrId(item.get("xfrId").getAsLong());
            }
            
            if (item.has("xfr") && !item.get("xfr").isJsonNull()) {
                dccyDdcVO.setXfr(item.get("xfr").getAsString());
            }
            
            if (item.has("dcTime") && !item.get("dcTime").isJsonNull()) {
                dccyDdcVO.setDcTime(item.get("dcTime").getAsLong());
            }
            
            if (item.has("xfsj") && !item.get("xfsj").isJsonNull()) {
                dccyDdcVO.setXfsj(item.get("xfsj").getAsLong());
            }
            
            if (item.has("zt") && !item.get("zt").isJsonNull()) {
                dccyDdcVO.setZt(item.get("zt").getAsInt());
            }
            
            if (item.has("chrId") && !item.get("chrId").isJsonNull()) {
                dccyDdcVO.setChrId(item.get("chrId").getAsLong());
            }
            
            if (item.has("chr") && !item.get("chr").isJsonNull()) {
                dccyDdcVO.setChr(item.get("chr").getAsString());
            }
            
            if (item.has("chsj") && !item.get("chsj").isJsonNull()) {
                dccyDdcVO.setChsj(item.get("chsj").getAsLong());
            }
            
            return dccyDdcVO;
        } catch (Exception e) {
            Log.e(TAG, "解析单个项目时发生错误: " + e.getMessage(), e);
            return null;
        }
    }
} 
package io.dcloud.uniplugin.form.utils;


import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Bundle;
import android.os.Handler;
import android.provider.Settings;
import android.util.Log;
import android.widget.TextView;

import androidx.appcompat.app.AlertDialog;

import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationServices;

import java.util.Locale;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 表单位置管理器，负责处理表单中的位置相关功能
 */
public class FormLocationManager {
    private static final String TAG = "FormLocationManager";
    private static final int REQUEST_PERMISSION_LOCATION = 2003;
    private static final float DEFAULT_GEOFENCE_RADIUS = 2000000.0f; // 默认电子围栏半径（米）
    private static final long LOCATION_TIMEOUT_MS = 15000; // 位置获取超时时间（15秒）
    private static final long LOCATION_MIN_UPDATE_INTERVAL = 1000; // 最小位置更新间隔（1秒）
    private static final float LOCATION_MIN_DISTANCE = 0; // 最小位置更新距离

    private Activity activity;
    private LocationManager locationManager;
    private TextView locationText;
    private Location lastKnownLocation;
    private Double targetLatitude;
    private Double targetLongitude;
    private double latitude;  // 当前纬度
    private double longitude; // 当前经度
    private float geofenceRadius;
    private FormUIController uiController;
    private boolean allowSubmitOutsideGeofence = false; // 是否允许在电子围栏外提交

    // 位置状态
    private boolean isInGeofence = false;
    private float distanceToTarget = -1;
    private boolean locationCheckComplete = false;

    // 对话框
    private androidx.appcompat.app.AlertDialog locationDialog;

    private volatile boolean isShowingDialog = false;

    // 成员变量
    private LocationListener locationListener;
    private static final int SINGLE_LOCATION_TIMEOUT_MS = 10000; // 10秒超时

    private FusedLocationProviderClient fusedLocationClient;
    private Location lastLocation;

    // 定义表单渲染器位置监听器接口
    public interface FormRendererLocationListener {
        void onLocationUpdated(double latitude, double longitude);
    }
    
    // 表单渲染器位置监听器
    private FormRendererLocationListener formRendererLocationListener;

    /**
     * 位置更新回调接口
     */
    public interface LocationUpdateCallback {
        /**
         * 位置更新时触发
         * @param location 位置信息
         * @param isInGeofence 是否在电子围栏内
         */
        void onLocationUpdated(Location location, boolean isInGeofence);
    }

    // 位置更新回调
    private LocationUpdateCallback locationUpdateCallback;

    /**
     * 设置位置更新回调
     * @param callback 位置更新回调
     */
    public void setLocationUpdateCallback(LocationUpdateCallback callback) {
        this.locationUpdateCallback = callback;
        Log.d(TAG, "设置位置更新回调");

        // 如果已经有位置信息，立即触发回调
        if (lastKnownLocation != null && locationCheckComplete) {
            if (locationUpdateCallback != null) {
                locationUpdateCallback.onLocationUpdated(lastKnownLocation, isInGeofence);
                Log.d(TAG, "立即触发位置更新回调，位置: " + lastKnownLocation.getLatitude() +
                        ", " + lastKnownLocation.getLongitude() +
                        ", 在电子围栏内: " + isInGeofence);
            }
        }
    }

    /**
     * 设置表单渲染器位置监听器
     * @param listener 监听器
     */
    public void setFormRendererLocationListener(FormRendererLocationListener listener) {
        this.formRendererLocationListener = listener;
        Log.d(TAG, "设置表单渲染器位置监听器");
        
        // 如果已经有位置信息，立即通知
        if (latitude != 0.0 || longitude != 0.0) {
            if (formRendererLocationListener != null) {
                formRendererLocationListener.onLocationUpdated(latitude, longitude);
                Log.d(TAG, "立即通知表单渲染器当前位置: 纬度=" + latitude + ", 经度=" + longitude);
            }
        }
    }

    /**
     * 获取当前纬度
     * @return 纬度
     */
    public double getLatitude() {
        return latitude;
    }
    
    /**
     * 获取当前经度
     * @return 经度
     */
    public double getLongitude() {
        return longitude;
    }

    /**
     * 构造函数 (只接收 Activity 参数)
     * @param activity 活动
     */
    public FormLocationManager(Activity activity) {
        this.activity = activity;
        this.locationManager = (LocationManager) activity.getSystemService(Context.LOCATION_SERVICE);
        this.geofenceRadius = DEFAULT_GEOFENCE_RADIUS;
        this.fusedLocationClient = LocationServices.getFusedLocationProviderClient(activity);
    }

    /**
     * 设置位置文本视图
     * @param locationText 位置文本视图
     */
    public void setLocationText(TextView locationText) {
        this.locationText = locationText;
        Log.d(TAG, "设置了位置文本视图: " + (locationText != null ? "成功" : "失败"));
    }

    /**
     * 设置UIController，用于控制表单状态
     * @param uiController UI控制器
     */
    public void setUIController(FormUIController uiController) {
        this.uiController = uiController;
        Log.d(TAG, "设置了表单UI控制器");
    }

    /**
     * 设置目标坐标
     * @param latitude 纬度
     * @param longitude 经度
     */
    public void setTargetCoordinates(Double latitude, Double longitude) {
        this.targetLatitude = latitude;
        this.targetLongitude = longitude;
    }

    /**
     * 获取电子围栏半径
     * @return 半径，单位米
     */
    public float getGeofenceRadius() {
        return this.geofenceRadius;
    }

    /**
     * 请求位置并等待结果
     * @return 是否在地理围栏内
     */
    public boolean requestLocationAndWait() {
        Log.d(TAG, "开始获取位置信息");

        if (!LocationPermissionUtils.hasLocationPermission(activity)) {
            LocationPermissionUtils.requestLocationPermission(activity);
            return false;
        }

        if (targetLatitude == null || targetLongitude == null ||
                targetLatitude == 0.0 && targetLongitude == 0.0) {
            if (locationText != null) {
                locationText.setText("未设置电子围栏，允许填报");
            }
            return true;
        }

        if (locationText != null) {
            locationText.setText("正在获取GPS坐标...");
        }

        // 在主线程中显示加载对话框
        activity.runOnUiThread(() -> {
            UIUtils.showLoadingDialog(activity, "正在获取位置信息，请稍候...");
        });

        Thread locationThread = new Thread(() -> {
            Handler mainHandler = new Handler(activity.getMainLooper());
            Location location = getCurrentLocationSync(mainHandler);

            // 在主线程中处理结果
            activity.runOnUiThread(() -> {
                try {
                    // 关闭加载对话框
                    UIUtils.dismissLoadingDialog();
                    processLocationResult(location);
                } catch (Exception e) {
                    Log.e(TAG, "处理位置结果异常: " + e.getMessage());
                    // 确保在异常情况下也关闭对话框
                    UIUtils.dismissLoadingDialog();
                }
            });
        });

        locationThread.start();
        return true;
    }

    /**
     * 同步获取当前位置
     * @param mainHandler 主线程Handler，用于在主线程上运行位置更新
     * @return 当前位置，如果获取失败返回null
     */
    private Location getCurrentLocationSync(Handler mainHandler) {
        Log.d(TAG, "开始获取位置");

        if (!LocationPermissionUtils.hasLocationPermission(activity)) {
            Log.e(TAG, "没有位置权限");
            return null;
        }

        final AtomicReference<Location> locationResult = new AtomicReference<>();
        final CountDownLatch latch = new CountDownLatch(1);

        Location initialLocation = null;
        try {
            initialLocation = LocationUtils.getLastKnownLocation(activity, locationManager);
            if (LocationUtils.isLocationRecent(initialLocation)) {
                return initialLocation;
            }
        } catch (Exception e) {
            Log.e(TAG, "处理最后已知位置时出错: " + e.getMessage());
        }

        boolean isGpsEnabled = LocationPermissionUtils.isGpsEnabled(activity);
        boolean isNetworkEnabled = LocationPermissionUtils.isNetworkLocationEnabled(activity);

        if (!isGpsEnabled && !isNetworkEnabled) {
            Log.e(TAG, "位置服务未启用");
            return initialLocation;
        }

        final boolean finalIsGpsEnabled = isGpsEnabled;
        final boolean finalIsNetworkEnabled = isNetworkEnabled;

        locationListener = new LocationListener() {
            @Override
            public void onLocationChanged(Location location) {
                if (location != null) {
                    locationResult.set(location);
                    latch.countDown();
                }
            }

            @Override
            public void onStatusChanged(String provider, int status, Bundle extras) {}

            @Override
            public void onProviderEnabled(String provider) {}

            @Override
            public void onProviderDisabled(String provider) {}
        };

        mainHandler.post(() -> {
            try {
                LocationUtils.LocationUpdateParams params = new LocationUtils.LocationUpdateParams();
                if (finalIsGpsEnabled) {
                    locationManager.requestLocationUpdates(
                            LocationManager.GPS_PROVIDER,
                            params.minTime,
                            params.minDistance, locationListener);
                }
                
                if (finalIsNetworkEnabled) {
                    locationManager.requestLocationUpdates(
                            LocationManager.NETWORK_PROVIDER, 
                            params.minTime, 
                            params.minDistance, locationListener);
                }
            } catch (Exception e) {
                Log.e(TAG, "请求位置更新失败: " + e.getMessage());
            }
        });
        
        try {
            latch.await(LOCATION_TIMEOUT_MS, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            Log.e(TAG, "位置等待被中断: " + e.getMessage());
            Thread.currentThread().interrupt();
        }
        
        mainHandler.post(() -> {
            try {
                locationManager.removeUpdates(locationListener);
            } catch (Exception e) {
                Log.e(TAG, "移除位置监听器失败: " + e.getMessage());
            }
        });
        
        Location result = locationResult.get();
        if (result == null && initialLocation != null) {
            result = initialLocation;
        }
        
        return result;
    }
    
    /**
     * 处理位置结果
     * @param location 位置
     * @return 是否在地理围栏内
     */
    private boolean processLocationResult(Location location) {
        if (location != null) {
            lastKnownLocation = location;
            checkGeofence(location);
            locationCheckComplete = true;
            
            if (locationText != null) {
                String statusText = isInGeofence ? "在围栏内" : "在围栏外";
                locationText.setText("经度: " + location.getLongitude() +
                        "\n纬度: " + location.getLatitude() +
                        "\n精度: " + location.getAccuracy() + "米" +
                        "\n距离目标: " + String.format("%.2f", distanceToTarget) + "米" +
                        "\n状态: " + statusText);
            }
            
            // 仅在非忽略地理围栏状态且不在围栏内时显示对话框
            if (!isInGeofence && !ignoreGeofenceCheck) {
                showLocationOutOfRangeDialog(location);
            }
            
            if (uiController != null) {
                // 如果忽略电子围栏检查，始终启用表单
                if (ignoreGeofenceCheck) {
                    uiController.setFormEnabled(true);
                    Log.d(TAG, "忽略电子围栏检查，表单完全启用");
                } 
                // 如果不在围栏内，但允许在围栏外提交
                else if (!isInGeofence && allowSubmitOutsideGeofence) {
                    // 设置表单只读但提交按钮可用
                    uiController.setFormReadOnlyButSubmittable(false);
                    Log.d(TAG, "不在围栏内但允许提交，设置表单只读但提交按钮可用，保存到本地按钮禁用");
                }
                // 其他情况下，根据是否在围栏内决定表单状态
                else {
                    uiController.setFormEnabled(isInGeofence);
                    Log.d(TAG, "根据围栏状态设置表单: " + (isInGeofence ? "启用" : "禁用"));
                }
            }
            
            if (locationUpdateCallback != null) {
                locationUpdateCallback.onLocationUpdated(location, isInGeofence);
            }
            
            return isInGeofence;
        } else {
            Log.e(TAG, "获取位置失败");
            if (locationText != null) {
                locationText.setText("获取位置失败，请重试");
            }
            locationCheckComplete = true;
            
            // 仅在非忽略地理围栏状态时显示位置错误对话框
            if (!ignoreGeofenceCheck) {
                showLocationErrorDialog();
            }
            
            if (uiController != null) {
                // 如果忽略电子围栏检查，始终启用表单
                if (ignoreGeofenceCheck) {
                    uiController.setFormEnabled(true);
                } 
                // 如果允许在围栏外提交，则即使获取位置失败也允许提交按钮可用
                else if (allowSubmitOutsideGeofence) {
                    uiController.setFormReadOnlyButSubmittable(false);
                    Log.d(TAG, "位置获取失败但允许在围栏外提交，设置表单只读但提交按钮可用，保存到本地按钮禁用");
                }
                else {
                    uiController.setFormEnabled(false);
                    Log.d(TAG, "位置获取失败且不允许在围栏外提交，禁用表单");
                }
            }
            
            return false;
        }
    }
    
    /**
     * 重新定位
     */
    public void relocate() {
        Log.d(TAG, "手动触发重新定位");
        requestLocationAndWait();
        // 因为位置获取是异步的，所以UI更新会在processLocationResult方法中处理
    }
    
    /**
     * 处理权限请求结果
     * @param requestCode 请求码
     * @param permissions 权限
     * @param grantResults 授权结果
     */
    public void handlePermissionResult(int requestCode, String[] permissions, int[] grantResults) {
        if (requestCode == REQUEST_PERMISSION_LOCATION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "位置权限已授予");
                // 权限获取后，重新定位
                relocate();
            } else {
                Log.w(TAG, "位置权限被拒绝");
                UIUtils.showToast(activity, "无法获取位置：位置权限被拒绝");
                
                // 明确禁用表单
                if (uiController != null) {
                    uiController.setFormEnabled(false);
                    Log.d(TAG, "位置权限被拒绝，表单已禁用");
                }
                
                // 显示权限设置对话框
                showLocationPermissionDialog();
            }
        }
    }
    
    /**
     * 显示位置权限设置对话框
     */
    private void showLocationPermissionDialog() {
        AlertDialog dialog = UIUtils.showConfirmDialog(activity, 
                "位置权限", 
                "需要位置权限才能获取当前位置。请在设置中启用位置权限。",
                "去设置", 
                "仅查看",
                (dialogInterface, which) -> {
                    // 先禁用表单，防止用户从设置返回后未授权就能操作
                    if (uiController != null) {
                        uiController.setFormEnabled(false);
                    }
                    
                    // 打开应用设置
                    Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                    intent.setData(android.net.Uri.parse("package:" + activity.getPackageName()));
                    activity.startActivity(intent);
                    
                    // 监听应用从设置页返回后的操作
                    activity.getWindow().getDecorView().post(() -> {
                        // 延迟检查权限，确保设置页面关闭后执行
                        new Handler().postDelayed(() -> {
                            if (LocationPermissionUtils.hasLocationPermission(activity)) {
                                // 获得权限后，重新定位
                                Log.d(TAG, "从设置返回后，位置权限已授予，重新定位");
                                relocate();
                            } else {
                                // 仍然没有权限，保持表单禁用状态
                                Log.d(TAG, "从设置返回后，位置权限仍被拒绝，保持表单禁用状态");
                                if (uiController != null) {
                                    uiController.setFormEnabled(false);
                                }
                            }
                        }, 500); // 500ms延迟确保设置页面已关闭
                    });
                },
                (dialogInterface, which) -> {
                    // 设置为只读模式
                    if (uiController != null) {
                        uiController.setFormEnabled(false);
                    }
                });
        
        // 设置对话框不可通过点击外部取消
        if (dialog != null) {
            dialog.setCanceledOnTouchOutside(false);
            dialog.setCancelable(false);
        }
    }
    /**
     * 释放资源
     * 在Activity的onDestroy方法中调用
     */
    @SuppressWarnings("deprecation")
    public void release() {
        Log.d(TAG, "释放位置管理器资源");
        
        // 确保对话框已关闭
        if (locationDialog != null && locationDialog.isShowing()) {
            try {
                locationDialog.dismiss();
                Log.d(TAG, "已关闭位置对话框");
            } catch (Exception e) {
                Log.e(TAG, "关闭位置对话框失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 检查位置是否在允许范围内，如果不在，显示确认对话框
     */
    private void showLocationOutOfRangeDialog(Location location) {
        // 计算距离
        float distance = LocationUtils.calculateDistance(location, targetLatitude, targetLongitude);
        String distanceText = String.format(Locale.getDefault(), "%.2f", distance);

        // 显示确认对话框
        try {
            activity.runOnUiThread(() -> {
                AlertDialog dialog = new AlertDialog.Builder(activity)
                    .setTitle("位置提醒")
                    .setMessage("当前位置距离电子围栏中心点 " + distanceText + " 米，超出允许范围 " + geofenceRadius + " 米。")
                    .setPositiveButton("重新定位", (dialogInterface, i) -> {
                        try {
                            // 关闭对话框
                            if (dialogInterface != null) {
                                dialogInterface.dismiss();
                            }
                            // 重新请求位置
                            requestLocationAndWait();
                        } catch (Exception e) {
                            Log.e(TAG, "重新定位出错: " + e.getMessage());
                        }
                    })
                    .setNegativeButton("继续填报", (dialogInterface, i) -> {
                        try {
                            // 关闭对话框
                            if (dialogInterface != null) {
                                dialogInterface.dismiss();
                            }
                            // 更新UI
                            if (locationText != null) {
                                locationText.setText("位置不在范围内，已选择继续填报");
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "继续填报出错: " + e.getMessage());
                        }
                    })
                    .create();
                dialog.show();
            });
        } catch (Exception e) {
            Log.e(TAG, "显示位置超出范围对话框时出错: " + e.getMessage());
            // 更新UI
            if (locationText != null) {
                activity.runOnUiThread(() -> {
                    locationText.setText("位置不在范围内");
                });
            }
        }
    }
    
    /**
     * 显示位置错误对话框
     */
    private void showLocationErrorDialog() {
        if (isShowingDialog) {
            return;
        }
        
        isShowingDialog = true;
        activity.runOnUiThread(() -> {
            try {
                if (locationDialog != null && locationDialog.isShowing()) {
                    locationDialog.dismiss();
                }
                
                boolean isGpsEnabled = LocationPermissionUtils.isGpsEnabled(activity);
                boolean isNetworkEnabled = LocationPermissionUtils.isNetworkLocationEnabled(activity);
                
                LocationDialogUtils.showLocationServiceDialog(
                    activity,
                    isGpsEnabled,
                    isNetworkEnabled,
                    (dialog, which) -> {
                        try {
                            Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                            activity.startActivity(intent);
                        } catch (Exception e) {
                            Log.e(TAG, "打开位置设置失败: " + e.getMessage());
                            UIUtils.showToast(activity, "无法打开位置设置");
                        }
                        isShowingDialog = false;
                    },
                    (dialog, which) -> {
                        locationDialog.dismiss();
                        isShowingDialog = false;
                        relocate();
                    },
                    (dialog, which) -> {
                        if (uiController != null) {
                            uiController.setFormEnabled(false);
                        }
                        isShowingDialog = false;
                    }
                );
            } catch (Exception e) {
                Log.e(TAG, "显示位置错误对话框失败: " + e.getMessage());
                isShowingDialog = false;
            }
        });
    }
    
    /**
     * 设置是否允许在电子围栏外提交
     * @param allow 是否允许
     */
    public void setAllowSubmitOutsideGeofence(boolean allow) {
        this.allowSubmitOutsideGeofence = allow;
        Log.d(TAG, "设置是否允许在电子围栏外提交: " + allow);
        
        // 如果已经有位置信息，重新检查电子围栏状态
        if (lastKnownLocation != null) {
            checkGeofence(lastKnownLocation);
        }
    }
    
    /**
     * 获取是否允许在电子围栏外提交
     * @return 是否允许
     */
    public boolean getAllowSubmitOutsideGeofence() {
        return this.allowSubmitOutsideGeofence;
    }

    private boolean ignoreGeofenceCheck = false; // 是否忽略电子围栏检查

    /**
     * 设置是否忽略电子围栏检查
     * @param ignore 是否忽略
     * @param enableSubmitButton 是否同时启用提交按钮
     */
    public void setIgnoreGeofenceCheck(boolean ignore, boolean enableSubmitButton) {
        this.ignoreGeofenceCheck = ignore;
        Log.d(TAG, "设置是否忽略电子围栏检查: " + ignore + ", 启用提交按钮: " + enableSubmitButton);
        
        // 如果设置为忽略，则自动设置isInGeofence为true
        if (ignore) {
            this.isInGeofence = true;
            
            // 更新UI控制器
            if (uiController != null && enableSubmitButton) {
                uiController.setSubmitEnabled(true);
                Log.d(TAG, "忽略电子围栏检查，启用表单提交按钮");
            } else if (uiController != null) {
                Log.d(TAG, "忽略电子围栏检查，但不启用表单提交按钮");
            }
        } else if (lastKnownLocation != null) {
            // 如果取消忽略，且有上次位置记录，重新检查电子围栏状态
            checkGeofence(lastKnownLocation);
        }
    }

    /**
     * 向后兼容的方法，默认启用提交按钮
     * @param ignore 是否忽略
     */
    public void setIgnoreGeofenceCheck(boolean ignore) {
        setIgnoreGeofenceCheck(ignore, true);
    }

    /**
     * 获取是否忽略电子围栏检查
     * @return 是否忽略
     */
    public boolean getIgnoreGeofenceCheck() {
        return this.ignoreGeofenceCheck;
    }

    /**
     * 检查是否在电子围栏范围内
     * @param location 当前位置
     */
    private void checkGeofence(Location location) {
        // 更新当前位置的纬度和经度
        if (location != null) {
            this.latitude = location.getLatitude();
            this.longitude = location.getLongitude();
            
            // 通知表单渲染器位置已更新
            if (formRendererLocationListener != null) {
                formRendererLocationListener.onLocationUpdated(latitude, longitude);
                Log.d(TAG, "通知表单渲染器位置已更新: 纬度=" + latitude + ", 经度=" + longitude);
            }
        }

        // 如果设置为忽略电子围栏检查，则直接返回
        if (ignoreGeofenceCheck) {
            isInGeofence = true;
            
            // 注意：这里不再自动启用提交按钮，而是保持当前状态
            // 如果需要启用按钮，应该通过setIgnoreGeofenceCheck(true, true)显式指定
            return;
        }

        if (targetLatitude != null && targetLongitude != null &&
                targetLatitude != 0.0 && targetLongitude != 0.0) {
            
            float[] results = new float[1];
            Location.distanceBetween(location.getLatitude(), location.getLongitude(), 
                    targetLatitude, targetLongitude, results);
            
            distanceToTarget = results[0];
            isInGeofence = distanceToTarget <= geofenceRadius;
            
            Log.d(TAG, "距离目标点: " + distanceToTarget + "米, 电子围栏半径: " + 
                    geofenceRadius + "米, 在范围内: " + isInGeofence);
            
            // 更新位置文本显示
            updateLocationTextWithDistance(location);
            
            // 更新UI控制器
            if (uiController != null) {
                // 如果允许在电子围栏外提交，则无论是否在范围内都允许提交，但会显示不同的提示信息
                if (allowSubmitOutsideGeofence) {
                    if (isInGeofence) {
                        // 在围栏内，正常启用表单
                        uiController.setFormEnabled(true);
                        Log.d(TAG, "在电子围栏内，启用完整表单编辑");
                    } else {
                        // 在围栏外，设置表单只读但允许提交
                        uiController.setFormReadOnlyButSubmittable(false);
                        Log.d(TAG, "在电子围栏外但允许提交，设置表单只读但保持提交按钮可用，保存到本地按钮禁用");
                    }
                } else {
                    // 如果不允许在电子围栏外提交，则只有在范围内才允许提交
                    uiController.setFormEnabled(isInGeofence);
                    Log.d(TAG, "不允许在电子围栏外提交，表单提交按钮状态: " + isInGeofence);
                }
            }
            
        } else {
            // 没有设置目标位置，默认在范围内
            isInGeofence = true;
            Log.d(TAG, "未设置目标位置，默认在范围内");
            
            // 更新位置文本显示（不包含距离信息）
            if (locationText != null && location != null) {
                final String locationString = String.format(
                    "经度: %.6f\n纬度: %.6f\n精度: %.1f米\n注意：未设置目标坐标", 
                    location.getLongitude(), 
                    location.getLatitude(), 
                    location.getAccuracy()
                );
                
                activity.runOnUiThread(() -> {
                    locationText.setText(locationString);
                });
            }
            
            // 如果没有设置目标位置，默认允许提交
            if (uiController != null) {
                uiController.setFormEnabled(true);
                Log.d(TAG, "未设置目标位置，默认允许提交表单");
            }
        }
        
        // 通知位置更新回调
        if (locationUpdateCallback != null) {
            locationUpdateCallback.onLocationUpdated(location, isInGeofence);
            Log.d(TAG, "通知位置更新回调，位置: " + location.getLatitude() + 
                    ", " + location.getLongitude() + 
                    ", 在电子围栏内: " + isInGeofence);
        }
    }
    
    /**
     * 使用距离信息更新位置文本
     * @param location 当前位置
     */
    private void updateLocationTextWithDistance(Location location) {
        if (locationText != null && location != null) {
            String distanceInfo = "";
            String statusInfo = "";
            
            // 如果有目标坐标，添加距离信息
            if (targetLatitude != null && targetLongitude != null 
                && targetLatitude != 0.0 && targetLongitude != 0.0) {
                
                distanceInfo = String.format("\n距离目标: %.1f米", distanceToTarget);
                
                // 添加电子围栏状态信息
                if (isInGeofence) {
                    statusInfo = "\n状态: 在有效范围内";
                } else {
                    float exceedDistance = distanceToTarget - geofenceRadius;
                    statusInfo = String.format("\n状态: 超出有效范围 %.1f米", exceedDistance);
                }
            }
            
            final String locationString = String.format(
                "经度: %.6f\n纬度: %.6f\n精度: %.1f米%s%s", 
                location.getLongitude(), 
                location.getLatitude(), 
                location.getAccuracy(),
                distanceInfo,
                statusInfo
            );
            
            activity.runOnUiThread(() -> {
                locationText.setText(locationString);
                
                // 为超出范围的情况添加红色高亮
                if (!isInGeofence && targetLatitude != null && targetLongitude != null 
                    && targetLatitude != 0.0 && targetLongitude != 0.0) {
                    locationText.setTextColor(activity.getResources().getColor(android.R.color.holo_red_dark));
                } else {
                    locationText.setTextColor(activity.getResources().getColor(android.R.color.black));
                }
            });
        }
    }

    /**
     * 获取是否在电子围栏内
     * @return 是否在电子围栏内
     */
    public boolean isInGeofence() {
        return this.isInGeofence;
    }
} 
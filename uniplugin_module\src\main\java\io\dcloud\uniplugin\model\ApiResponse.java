package io.dcloud.uniplugin.model;

import com.google.gson.annotations.SerializedName;

/**
 * 通用API响应包装类
 * @param <T> 响应数据类型
 */
public class ApiResponse<T> {
    
    @SerializedName("code")
    private int code;
    
    @SerializedName("data")
    private T data;
    
    @SerializedName("msg")
    private String msg;
    
    public int getCode() {
        return code;
    }
    
    public void setCode(int code) {
        this.code = code;
    }
    
    public T getData() {
        return data;
    }
    
    public void setData(T data) {
        this.data = data;
    }
    
    public String getMsg() {
        return msg;
    }
    
    public void setMsg(String msg) {
        this.msg = msg;
    }
    
    /**
     * 判断响应是否成功
     */
    public boolean isSuccess() {
        return code == 0;
    }
} 
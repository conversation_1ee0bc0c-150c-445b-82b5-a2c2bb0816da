package io.dcloud.uniplugin.model;

import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;

import java.io.IOException;

/**
 * 自定义TypeAdapter，用于处理数字(1/0)到布尔值(true/false)的转换
 */
public class NumberBooleanAdapter extends TypeAdapter<Boolean> {

    @Override
    public void write(JsonWriter out, Boolean value) throws IOException {
        if (value == null) {
            out.nullValue();
        } else {
            out.value(value);
        }
    }

    @Override
    public Boolean read(JsonReader in) throws IOException {
        JsonToken token = in.peek();
        switch (token) {
            case BOOLEAN:
                return in.nextBoolean();
            case NUMBER:
                // 将非零数字视为true，0视为false
                return in.nextInt() != 0;
            case STRING:
                // 尝试将字符串解析为布尔值或数字
                String str = in.nextString();
                if ("true".equalsIgnoreCase(str)) {
                    return true;
                } else if ("false".equalsIgnoreCase(str)) {
                    return false;
                } else {
                    try {
                        return Integer.parseInt(str) != 0;
                    } catch (NumberFormatException e) {
                        return false;
                    }
                }
            case NULL:
                in.nextNull();
                return false;
            default:
                throw new IllegalStateException("Expected BOOLEAN, NUMBER, STRING or NULL but was " + token);
        }
    }
} 
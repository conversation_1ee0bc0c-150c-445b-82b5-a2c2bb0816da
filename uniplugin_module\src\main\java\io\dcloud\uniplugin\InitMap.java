package io.dcloud.uniplugin;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageManager;
import android.util.Log;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.esri.arcgisruntime.geometry.Envelope;
import com.esri.arcgisruntime.geometry.SpatialReference;
import com.esri.arcgisruntime.layers.ArcGISTiledLayer;
import com.esri.arcgisruntime.mapping.ArcGISMap;
import com.esri.arcgisruntime.mapping.Basemap;
import com.esri.arcgisruntime.mapping.Viewpoint;
import com.esri.arcgisruntime.mapping.view.MapView;

public class InitMap {
    private Context context;
    public InitMap(Activity context){
        this.context =context;
    }
    public ArcGISMap loadTPK(String imageTpkPath, ArcGISMap mainArcGISMap, MapView mapView) {
        Log.d("初始化地图","执行了！");
        //加载tpk文件
        String[] reqPermission = new String[]{Manifest.permission.READ_EXTERNAL_STORAGE};
        int requestCode = 2;
        // 在API23版本以上中，权限需要在运行时进行请求
        if (ContextCompat.checkSelfPermission(context,
                reqPermission[0]) == PackageManager.PERMISSION_GRANTED) {
//                //String fileTPK = getExternalFilesDir(null) + "/gaozhou.tpk";
            //imageTpkPath = getSDPath(this) + "/TRSPGISData/image.tpk";
////                imageTpkPath = getExternalFilesDir(null) + "/image.tpk";
            //demTpkPath = getSDPath(this) + "/TRSPGISData/dem.tpk";
//                demTpkPath = getExternalFilesDir(null) + "/dem.tpk";
            Log.d("imageTpkPath", imageTpkPath);
            //Log.d("demTpkPath", demTpkPath);
            String fileTPK = imageTpkPath;
            ArcGISTiledLayer arcGISTiledLayer = new ArcGISTiledLayer(fileTPK);
            Basemap basemap = new Basemap(arcGISTiledLayer);
            //TODO:一开始没有设置Map，然后定位无效，现在添加了坐标系，然后定位就可以了
            if (mainArcGISMap == null) {
                mainArcGISMap = new ArcGISMap(basemap);//确定为这个类但不添加
            }
            mainArcGISMap.setBasemap(basemap);
                /*mainArcGISMap.addDoneLoadingListener(new Runnable() {
                    @Override
                    public void run() {
                        if (mainArcGISMap.getLoadStatus() == LoadStatus.LOADED) {
                            // Remove the done loading listener when the map has finished loading
                            mainArcGISMap.removeDoneLoadingListener(this);

                            // TODO: Add your code here to do something when the map is loaded
                        }
                    }
                });*/

            mapView.setMap(mainArcGISMap);
        } else {
            // 请求权限
            ActivityCompat.requestPermissions((Activity) context, reqPermission, requestCode);
        }
        //requestWritePermission();

        // create an initial extent envelope
        Envelope initialExtent = new Envelope(12198107.235, 2279034.9, 13054039.508, 2937630.595,
                SpatialReference.create(102100));
        // create a viewpoint from envelope
        Viewpoint viewpoint = new Viewpoint(initialExtent);

        // set initial map extent
        mapView.setViewpoint(viewpoint);
        return mainArcGISMap;

    }
}

package io.dcloud.uniplugin.sampleflow;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import io.dcloud.uniplugin.model.YPLZ;
import uni.dcloud.io.uniplugin_module.R;

/**
 * 样品详情列表适配器
 */
public class SampleDetailAdapter extends RecyclerView.Adapter<SampleDetailAdapter.ViewHolder> {
    
    private Context context;
    private List<YPLZ> sampleList;
    private OnSampleOperationListener operationListener;
    private boolean showOperationButtons = false; // 是否显示操作按钮
    
    /**
     * 样品操作监听器接口
     */
    public interface OnSampleOperationListener {
        void onEdit(YPLZ sample, int position);
        void onDelete(YPLZ sample, int position);
    }
    
    public SampleDetailAdapter(Context context, List<YPLZ> sampleList) {
        this.context = context;
        this.sampleList = sampleList;
    }
    
    /**
     * 设置样品操作监听器
     */
    public void setOnSampleOperationListener(OnSampleOperationListener listener) {
        this.operationListener = listener;
    }
    
    /**
     * 设置是否显示操作按钮
     * @param show 是否显示
     */
    public void setShowOperationButtons(boolean show) {
        this.showOperationButtons = show;
        notifyDataSetChanged(); // 更新所有项
    }
    
    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_sample_detail, parent, false);
        return new ViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        YPLZ sample = sampleList.get(position);
        
        // 设置样品编号
        holder.textViewSampleCode.setText("样品编号: " + sample.getSampleCode());
        
        // 设置样品名称
        holder.textViewSampleName.setText(sample.getSampleName() != null ? sample.getSampleName() : "");
        
        // 设置样品类型
        holder.textViewSampleType.setText(sample.getSampleType() != null ? sample.getSampleType() : "");
        
        // 设置样品重量
        if (sample.getSampleWeight() != null) {
            String weightUnit = sample.getWeightUnit() != null ? sample.getWeightUnit() : "g";
            holder.textViewSampleWeight.setText(sample.getSampleWeight() + " " + weightUnit);
        } else {
            holder.textViewSampleWeight.setText("");
        }
        
        // 设置采样点 - 先使用固定文本，避免方法调用错误
        holder.textViewSamplingPoint.setText("");
        
        // 设置状态
        String statusText;
        int backgroundColor;
        
        if (sample.getStatus() != null) {
            // 添加日志输出，检查样品的状态值
            Log.d("SampleDetailAdapter", "样品ID: " + sample.getId() + ", 状态值: " + sample.getStatus());
            
            // 根据样品状态设置显示文本
            if (sample.getStatus() == 0 || Long.valueOf(0).equals(sample.getStatus())) {
                statusText = "待流转";
                backgroundColor = R.drawable.tag_blue;
            } else if (sample.getStatus() == 1 || Long.valueOf(1).equals(sample.getStatus())) {
                statusText = "已入库";
                backgroundColor = R.drawable.tag_blue;
            } else if (sample.getStatus() == 2 || Long.valueOf(2).equals(sample.getStatus())) {
                statusText = "已检测";
                backgroundColor = R.drawable.tag_blue;
            } else {
                statusText = "未知状态(" + sample.getStatus() + ")";
                backgroundColor = R.drawable.tag_blue;
            }
        } else {
            statusText = "未知";
            backgroundColor = R.drawable.tag_blue;
        }
        
//        holder.textViewStatus.setText(statusText);
//        holder.textViewStatus.setBackgroundResource(backgroundColor);
        
        // 根据全局设置控制操作按钮的显示/隐藏，不再依赖样品状态
        if (showOperationButtons && holder.buttonEdit != null && holder.buttonDelete != null) {
            Log.d("SampleDetailAdapter", "显示编辑和删除按钮 - 样品ID: " + sample.getId());
            holder.buttonEdit.setVisibility(View.VISIBLE);
            holder.buttonDelete.setVisibility(View.VISIBLE);
            
            // 设置编辑按钮点击事件
            holder.buttonEdit.setOnClickListener(v -> {
                if (operationListener != null) {
                    operationListener.onEdit(sample, holder.getAdapterPosition());
                }
            });
            
            // 设置删除按钮点击事件
            holder.buttonDelete.setOnClickListener(v -> {
                if (operationListener != null) {
                    operationListener.onDelete(sample, holder.getAdapterPosition());
                }
            });
        } else if (holder.buttonEdit != null && holder.buttonDelete != null) {
            Log.d("SampleDetailAdapter", "隐藏编辑和删除按钮 - 样品ID: " + sample.getId());
            holder.buttonEdit.setVisibility(View.GONE);
            holder.buttonDelete.setVisibility(View.GONE);
        }
    }
    
    @Override
    public int getItemCount() {
        return sampleList != null ? sampleList.size() : 0;
    }
    
    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView textViewSampleCode;
        TextView textViewSampleName;
        TextView textViewSampleType;
        TextView textViewSampleWeight;
        TextView textViewSamplingPoint;
//        TextView textViewStatus;
        Button buttonEdit;
        Button buttonDelete;
        
        ViewHolder(View itemView) {
            super(itemView);
            textViewSampleCode = itemView.findViewById(R.id.textViewSampleCode);
            textViewSampleName = itemView.findViewById(R.id.textViewSampleName);
            textViewSampleType = itemView.findViewById(R.id.textViewSampleType);
            textViewSampleWeight = itemView.findViewById(R.id.textViewSampleWeight);
            textViewSamplingPoint = itemView.findViewById(R.id.textViewSamplingPoint);
//            textViewStatus = itemView.findViewById(R.id.textViewStatus);
            buttonEdit = itemView.findViewById(R.id.buttonEdit);
            buttonDelete = itemView.findViewById(R.id.buttonDelete);
        }
    }
} 
package io.dcloud.uniplugin.db;

/**
 * 数据库常量类，用于定义数据库相关的常量
 */
public class DatabaseConstants {
    // 数据库基本信息
    public static final String DATABASE_NAME = "bcgd.db";
    public static final int DATABASE_VERSION = 2;
    
    // 通用字段
    public static final String COLUMN_USER_ID = "user_id";
    
    // 用户表
    public static final String TABLE_USERS = "users";
    public static final String COLUMN_ID = "id";
    public static final String COLUMN_USERNAME = "username";
    public static final String COLUMN_PASSWORD = "password";
    public static final String COLUMN_SALT = "salt";
    public static final String COLUMN_NICKNAME = "nickname";
    public static final String COLUMN_ROLE = "role";
    public static final String COLUMN_LAST_LOGIN_TIME = "last_login_time";
    public static final String COLUMN_PERMISSIONS = "permissions";
    public static final String COLUMN_GSDDM = "gsddm";
    public static final String COLUMN_GSDMC = "gsdmc";

    // 上传记录表
    public static final String TABLE_UPLOAD_RECORDS = "upload_records";
    public static final String COLUMN_YDBH = "ydbh";
    public static final String COLUMN_DESCRIPTION = "description";
    public static final String COLUMN_UPLOAD_ZT = "zt";
    public static final String COLUMN_TIMESTAMP = "timestamp";
    public static final String COLUMN_UPLOADED = "uploaded";

    // 上传文件表
    public static final String COLUMN_FILE_ID = "id";
    public static final String TABLE_UPLOAD_FILES = "upload_files";
    public static final String COLUMN_RECORD_ID = "record_id";
    public static final String COLUMN_FILE_NAME = "file_name";
    public static final String COLUMN_FILE_PATH = "file_path";
    public static final String COLUMN_IS_IMAGE = "is_image";

    //已调查采样点表
    public static final String TABLE_SAMPLING_POINTS = "sampling_points";
    public static final String COLUMN_SAMPLING_ID = "id";
    public static final String COLUMN_PJDY_ID = "pjdyId";
    public static final String COLUMN_PJDY_BSM = "pjdybh";
    public static final String COLUMN_DCDW_ID = "dcdwId";
    public static final String COLUMN_DCDW_NAME = "dcdwName";
    public static final String COLUMN_DCR_ID = "dcrId";
    public static final String COLUMN_DCR_NAME = "dcrName";
    public static final String COLUMN_DCJD = "dcjd";
    public static final String COLUMN_DCWD = "dcwd";
    public static final String COLUMN_sfShiCj = "sfShiCj";
    public static final String COLUMN_sfShengCj = "sfShengCj";
    public static final String COLUMN_ZT = "zt";
    public static final String COLUMN_BZ = "bz";
    public static final String COLUMN_TRMY = "trMy";
    public static final String COLUMN_TRMZ = "trMz";
    public static final String COLUMN_XFJL_ID = "xfjlId";
    public static final String COLUMN_DATAJSON = "dataJson";
    // 新增字段：用于存储动态表单数据的JSON
    public static final String COLUMN_FORM_DATA_JSON = "formDataJson";

    
    // 采样点媒体表
    public static final String TABLE_SAMPLING_MEDIA = "sampling_media";
    public static final String COLUMN_MEDIA_ID = "id";
    public static final String COLUMN_MEDIA_PJDY_BSM = "pjdybh";
    public static final String COLUMN_MEDIA_FILE_NAME = "fileName";
    public static final String COLUMN_MEDIA_FILE_PATH = "filePath";
    public static final String COLUMN_MEDIA_FILE_TYPE = "fileType";
    public static final String COLUMN_MEDIA_TYPE = "mediaType";
    public static final String COLUMN_MEDIA_JD = "jd";
    public static final String COLUMN_MEDIA_WD = "wd";
    public static final String COLUMN_MEDIA_FWJ = "fwj";
    public static final String COLUMN_MEDIA_FILE_TIME = "fileTime";
    
    // 待调查表
    public static final String TABLE_DDC_POINTS = "sampling_ddc_points";
    public static final String COLUMN_DDC_ID = "id";
    public static final String COLUMN_DDC_PJDY_ID = "pjdy_id";
    public static final String COLUMN_DDC_PJDY_BSM = "pjdy_bsm";
    public static final String COLUMN_DDC_DCDW_ID = "dcdw_id";
    public static final String COLUMN_DDC_DCDW_NAME = "dcdw";
    public static final String COLUMN_DDC_DCR_ID = "dcr_id";
    public static final String COLUMN_DDC_DCR_NAME = "dcr";
    public static final String COLUMN_DDC_XFR_ID = "xfr_id";
    public static final String COLUMN_DDC_XFR = "xfr";
    public static final String COLUMN_DDC_DC_TIME = "dc_time";
    public static final String COLUMN_DDC_XFSJ = "xfsj";
    public static final String COLUMN_DDC_ZT = "zt";
    public static final String COLUMN_DDC_CHR_ID = "chr_id";
    public static final String COLUMN_DDC_CHR = "chr";
    public static final String COLUMN_DDC_CHSJ = "chsj";
    public static final String COLUMN_DDC_DWJD = "dwjd";
    public static final String COLUMN_DDC_DWWD = "dwwd";
//    public static final String COLUM_DDC_PROJECT_NAME = "project_name";
    public static final String COLUMN_HAS_LOCAL_DATA = "has_local_data";
    public static final String COLUMN_XMMC = "xmmc";

    // 样品表
    public static final String TABLE_SAMPLES = "samples";
    public static final String COLUMN_SAMPLE_ID = "id";
    public static final String COLUMN_SAMPLE_PJDY_BSM = "pjdybh";
    public static final String COLUMN_SAMPLE_TYPE = "sampleType";
    public static final String COLUMN_SAMPLE_TYPE_NAME = "sampleTypeName";
    public static final String COLUMN_SAMPLE_WEIGHT = "sampleWeight";
    public static final String COLUMN_SAMPLE_CODE = "sampleCode";
    public static final String COLUMN_SAMPLE_NUMBER = "sampleNumber";
    public static final String COLUMN_SAMPLE_CREATE_TIME = "createTime";
    public static final String COLUMN_SAMPLE_UPDATE_TIME = "updateTime";

    // 创建用户表的SQL语句
    public static final String CREATE_TABLE_USERS = "CREATE TABLE IF NOT EXISTS " + TABLE_USERS + "("
            + COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT,"
            + COLUMN_USERNAME + " TEXT UNIQUE NOT NULL,"
            + COLUMN_PASSWORD + " TEXT NOT NULL,"
            + COLUMN_SALT + " TEXT,"
            + COLUMN_NICKNAME + " TEXT,"
            + COLUMN_ROLE + " TEXT,"
            + COLUMN_LAST_LOGIN_TIME + " TEXT,"
            + COLUMN_PERMISSIONS + " TEXT,"
            + COLUMN_GSDDM + " TEXT,"
            + COLUMN_GSDMC + " TEXT"
            + ");";

    // 创建上传记录表的SQL语句
    public static final String CREATE_TABLE_UPLOAD_RECORDS = "CREATE TABLE " + TABLE_UPLOAD_RECORDS + "("
            + COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT,"
            + COLUMN_YDBH + " TEXT,"
            + COLUMN_DESCRIPTION + " TEXT,"
            + COLUMN_UPLOAD_ZT + " INTEGER,"
            + COLUMN_TIMESTAMP + " INTEGER,"
            + COLUMN_UPLOADED + " INTEGER"
            + ")";

    // 创建上传文件表的SQL语句
    public static final String CREATE_TABLE_UPLOAD_FILES = "CREATE TABLE " + TABLE_UPLOAD_FILES + "("
            + COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT,"
            + COLUMN_RECORD_ID + " INTEGER,"
            + COLUMN_FILE_NAME + " TEXT,"
            + COLUMN_FILE_PATH + " TEXT,"
            + COLUMN_IS_IMAGE + " INTEGER,"
            + "FOREIGN KEY(" + COLUMN_RECORD_ID + ") REFERENCES " + TABLE_UPLOAD_RECORDS + "(" + COLUMN_ID + ") ON DELETE CASCADE"
            + ")";
    
    // 创建采样点表的SQL语句
    public static final String CREATE_TABLE_SAMPLING_POINTS = "CREATE TABLE IF NOT EXISTS " + TABLE_SAMPLING_POINTS + "("
            + COLUMN_SAMPLING_ID + " LONG PRIMARY KEY,"
            + COLUMN_PJDY_ID + " TEXT,"
            + COLUMN_PJDY_BSM + " TEXT UNIQUE,"
            + COLUMN_DCDW_ID + " TEXT,"
            + COLUMN_DCDW_NAME + " TEXT,"
            + COLUMN_DCR_ID + " TEXT,"
            + COLUMN_DCR_NAME + " TEXT,"
            + COLUMN_DCJD + " TEXT,"
            + COLUMN_DCWD + " TEXT,"
            + COLUMN_sfShiCj + " INTEGER,"
            + COLUMN_sfShengCj + " INTEGER,"
            + COLUMN_ZT + " INTEGER,"
            + COLUMN_BZ + " TEXT,"
            + COLUMN_TRMY + " TEXT,"
            + COLUMN_DATAJSON + " TEXT,"
            + COLUMN_TRMZ + " TEXT,"
            + COLUMN_FORM_DATA_JSON + " TEXT,"
            + COLUMN_XMMC + " TEXT,"
            + COLUMN_XFJL_ID + " INTEGER,"
            + COLUMN_USER_ID + " INTEGER"
            + ");";
    
    // 创建采样点媒体表的SQL语句
    public static final String CREATE_TABLE_SAMPLING_MEDIA = "CREATE TABLE IF NOT EXISTS " + TABLE_SAMPLING_MEDIA + "("
            + COLUMN_MEDIA_ID + " LONG PRIMARY KEY,"
            + COLUMN_MEDIA_PJDY_BSM + " TEXT,"
            + COLUMN_MEDIA_FILE_NAME + " TEXT,"
            + COLUMN_MEDIA_FILE_PATH + " TEXT,"
            + COLUMN_MEDIA_FILE_TYPE + " TEXT,"
            + COLUMN_MEDIA_TYPE + " TEXT,"
            + COLUMN_MEDIA_JD + " TEXT,"
            + COLUMN_MEDIA_WD + " TEXT,"
            + COLUMN_MEDIA_FWJ + " TEXT,"
            + COLUMN_MEDIA_FILE_TIME + " TEXT"
            + ");";
            
    // 创建待调查表的SQL语句
    public static final String CREATE_TABLE_DDC_POINTS = "CREATE TABLE IF NOT EXISTS " + TABLE_DDC_POINTS + "("
            + COLUMN_DDC_ID + " INTEGER PRIMARY KEY,"
            + COLUMN_DDC_PJDY_ID + " INTEGER,"
            + COLUMN_DDC_PJDY_BSM + " TEXT UNIQUE,"
            + COLUMN_DDC_DCDW_ID + " INTEGER,"
            + COLUMN_DDC_DCDW_NAME + " TEXT,"
            + COLUMN_DDC_DCR_ID + " INTEGER,"
            + COLUMN_DDC_DCR_NAME + " TEXT,"
            + COLUMN_DDC_XFR_ID + " INTEGER,"
            + COLUMN_DDC_XFR + " TEXT,"
            + COLUMN_DDC_DC_TIME + " INTEGER,"
            + COLUMN_DDC_XFSJ + " INTEGER,"
            + COLUMN_DDC_ZT + " INTEGER,"
            + COLUMN_DDC_DWJD + " TEXT,"
            + COLUMN_DDC_DWWD + " TEXT,"
            + COLUMN_DDC_CHR_ID + " INTEGER,"
            + COLUMN_DDC_CHR + " TEXT,"
            + COLUMN_XMMC + " TEXT,"
            + COLUMN_DDC_CHSJ + " INTEGER,"
            + COLUMN_HAS_LOCAL_DATA + " INTEGER DEFAULT 0,"
            + COLUMN_USER_ID + " INTEGER"
            + ");";
            
    // 创建样品表的SQL语句
    public static final String CREATE_TABLE_SAMPLES = "CREATE TABLE IF NOT EXISTS " + TABLE_SAMPLES + "("
            + COLUMN_SAMPLE_ID + " INTEGER PRIMARY KEY AUTOINCREMENT,"
            + COLUMN_SAMPLE_PJDY_BSM + " TEXT NOT NULL,"
            + COLUMN_SAMPLE_TYPE + " INTEGER NOT NULL,"
            + COLUMN_SAMPLE_TYPE_NAME + " TEXT NOT NULL,"
            + COLUMN_SAMPLE_WEIGHT + " REAL NOT NULL,"
            + COLUMN_SAMPLE_CODE + " TEXT NOT NULL,"
            + COLUMN_SAMPLE_NUMBER + " TEXT NOT NULL UNIQUE,"
            + COLUMN_SAMPLE_CREATE_TIME + " LONG DEFAULT (strftime('%s','now') * 1000),"
            + COLUMN_SAMPLE_UPDATE_TIME + " LONG DEFAULT (strftime('%s','now') * 1000),"
            + COLUMN_USER_ID + " INTEGER,"
            + "UNIQUE(" + COLUMN_SAMPLE_PJDY_BSM + ", " + COLUMN_SAMPLE_TYPE + ")"
            + ");";
}
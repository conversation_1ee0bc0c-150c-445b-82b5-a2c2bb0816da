package io.dcloud.uniplugin.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.text.TextUtils;
import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

import io.dcloud.uniplugin.model.DccyVO;
import io.dcloud.uniplugin.model.FormConfigResponse;

/**
 * 样点数据管理类，专门负责管理sampling_points表的操作
 */
public class SamplingPointsManager {
    private static final String TAG = "SamplingPointsManager";
    private static SamplingPointsManager instance;
    private final Context context;
    private final DatabaseHelper dbHelper;

    /**
     * 获取SamplingPointsManager单例
     *
     * @param context 上下文
     * @return SamplingPointsManager实例
     */
    public static synchronized SamplingPointsManager getInstance(Context context) {
        if (instance == null) {
            instance = new SamplingPointsManager(context.getApplicationContext());
        }
        return instance;
    }

    /**
     * 私有构造函数
     *
     * @param context 上下文
     */
    private SamplingPointsManager(Context context) {
        this.context = context;
        this.dbHelper = DatabaseHelper.getInstance(this.context);
    }

    /**
     * 保存样点列表到数据库
     *
     * @param dccyList 样点列表
     * @param data     原始响应数据 (可能未使用)
     * @param userId   当前用户ID
     * @return 成功保存的记录数
     */
    public int saveSamplingPoints(List<DccyVO> dccyList, List<FormConfigResponse> data, Long userId) {
        if (dccyList == null || dccyList.isEmpty()) {
            Log.w(TAG, "无数据可保存");
            return 0;
        }
        if (userId == null) {
             Log.e(TAG, "User ID is null, cannot save sampling points.");
             return -1;
        }

        SQLiteDatabase db = null;
        int successCount = 0;
        try {
            db = dbHelper.getWritableDatabase();
            db.beginTransaction();

            // 清空指定用户的已调查样点表数据 - 确保每次同步时只保留最新数据
            int deletedRows = db.delete(DatabaseConstants.TABLE_SAMPLING_POINTS, 
                    DatabaseConstants.COLUMN_USER_ID + " = ?", 
                    new String[]{String.valueOf(userId)});
            Log.d(TAG, "已清空用户ID=" + userId + "的已调查样点表数据，删除了 " + deletedRows + " 行");

            for (DccyVO dccy : dccyList) {
                if (dccy == null || dccy.getpjdybh() == null) {
                    Log.w(TAG, "Skipping invalid sampling point data.");
                    continue;
                }

                ContentValues values = dccyVoToContentValues(dccy);
                values.put(DatabaseConstants.COLUMN_USER_ID, userId);

                boolean recordExists = isRecordExists(db, dccy.getpjdybh(), userId);

                try {
                    if (recordExists) {
                        int updated = db.update(DatabaseConstants.TABLE_SAMPLING_POINTS, values,
                                DatabaseConstants.COLUMN_PJDY_BSM + " = ? AND " + DatabaseConstants.COLUMN_USER_ID + " = ?",
                                new String[]{dccy.getpjdybh(), String.valueOf(userId)});
                        if (updated > 0) {
                            successCount++;
                        } else {
                             Log.w(TAG, "Failed to update existing record for BSM: " + dccy.getpjdybh() + " and user " + userId);
                        }
                    } else {
                        long id = db.insert(DatabaseConstants.TABLE_SAMPLING_POINTS, null, values);
                        if (id != -1) {
                            successCount++;
                        } else {
                            Log.e(TAG, "Failed to insert new sampling point for BSM: " + dccy.getpjdybh() + " and user " + userId);
                        }
                    }
                } catch (Exception e) {
                    Log.e(TAG, "保存样点数据失败 (BSM: " + dccy.getpjdybh() + "): " + e.getMessage(), e);
                }
            }

            db.setTransactionSuccessful();
            Log.i(TAG, "成功保存样点数据事务 for user " + userId + ", 影响行数: " + successCount + "/" + dccyList.size());

        } catch (Exception e) {
            Log.e(TAG, "保存样点数据事务失败 for user " + userId + ": " + e.getMessage(), e);
        } finally {
            if (db != null && db.inTransaction()) {
                db.endTransaction();
            }
        }
        return successCount;
    }
    
     /**
     * 将 DccyVO 对象转换为 ContentValues
     * @param dccy DccyVO 对象
     * @return ContentValues 对象
     */
    private ContentValues dccyVoToContentValues(DccyVO dccy) {
        ContentValues values = new ContentValues();
        if (dccy == null) return values;

        if (dccy.getpjdybh() != null) values.put(DatabaseConstants.COLUMN_PJDY_BSM, dccy.getpjdybh());
        if (dccy.getDcdwName() != null) values.put(DatabaseConstants.COLUMN_DCDW_NAME, dccy.getDcdwName());
        if (dccy.getDcrName() != null) values.put(DatabaseConstants.COLUMN_DCR_NAME, dccy.getDcrName());
        if (dccy.getBz() != null) values.put(DatabaseConstants.COLUMN_BZ, dccy.getBz());
        if (dccy.getTrMy() != null) values.put(DatabaseConstants.COLUMN_TRMY, dccy.getTrMy());
        if (dccy.getTrMz() != null) values.put(DatabaseConstants.COLUMN_TRMZ, dccy.getTrMz());
        if (dccy.getFormData() != null) values.put(DatabaseConstants.COLUMN_FORM_DATA_JSON, dccy.getFormData());

        if (dccy.getId() != null) values.put(DatabaseConstants.COLUMN_SAMPLING_ID, dccy.getId());
        if (dccy.getPjdyId() != null) values.put(DatabaseConstants.COLUMN_PJDY_ID, dccy.getPjdyId());
        if (dccy.getDcdwId() != null) values.put(DatabaseConstants.COLUMN_DCDW_ID, dccy.getDcdwId());
        if (dccy.getDcrId() != null) values.put(DatabaseConstants.COLUMN_DCR_ID, dccy.getDcrId());

        if (dccy.getDcjd() != null) values.put(DatabaseConstants.COLUMN_DCJD, dccy.getDcjd());
        if (dccy.getDcwd() != null) values.put(DatabaseConstants.COLUMN_DCWD, dccy.getDcwd());

        if (dccy.getsfShiCj() != null) values.put(DatabaseConstants.COLUMN_sfShiCj, dccy.getsfShiCj());
        if (dccy.getsfShengCj() != null) values.put(DatabaseConstants.COLUMN_sfShengCj, dccy.getsfShengCj());
        if (dccy.getZt() != null) values.put(DatabaseConstants.COLUMN_ZT, dccy.getZt());
        if (dccy.getXfjlId() != null) values.put(DatabaseConstants.COLUMN_XFJL_ID, dccy.getXfjlId());
        if (dccy.getXmmc() != null) values.put(DatabaseConstants.COLUMN_XMMC, dccy.getXmmc());

        return values;
    }

    /**
     * 将 Cursor 当前行数据转换为 DccyVO 对象
     * @param cursor 数据库游标
     * @return DccyVO 对象，如果转换失败返回 null
     */
    private DccyVO cursorToDccyVO(Cursor cursor) {
        if (cursor == null) return null;
        DccyVO dccyVO = new DccyVO();
        try {
             for (String columnName : cursor.getColumnNames()) {
                 int columnIndex = cursor.getColumnIndex(columnName);
                 if (columnIndex == -1) continue;
                 
                 if (cursor.isNull(columnIndex)) continue;
                 
                 switch (columnName) {
                     case DatabaseConstants.COLUMN_SAMPLING_ID:
                         dccyVO.setId(cursor.getLong(columnIndex));
                         break;
                     case DatabaseConstants.COLUMN_PJDY_ID:
                         dccyVO.setPjdyId(cursor.getLong(columnIndex));
                         break;
                     case DatabaseConstants.COLUMN_PJDY_BSM:
                         dccyVO.setpjdybh(cursor.getString(columnIndex));
                         break;
                     case DatabaseConstants.COLUMN_DCDW_ID:
                         dccyVO.setDcdwId(cursor.getLong(columnIndex));
                         break;
                     case DatabaseConstants.COLUMN_DCDW_NAME:
                         dccyVO.setDcdwName(cursor.getString(columnIndex));
                         break;
                     case DatabaseConstants.COLUMN_DCR_ID:
                         dccyVO.setDcrId(cursor.getLong(columnIndex));
                         break;
                     case DatabaseConstants.COLUMN_DCR_NAME:
                         dccyVO.setDcrName(cursor.getString(columnIndex));
                         break;
                     case DatabaseConstants.COLUMN_DCJD:
                         dccyVO.setDcjd(cursor.getDouble(columnIndex));
                         break;
                     case DatabaseConstants.COLUMN_DCWD:
                         dccyVO.setDcwd(cursor.getDouble(columnIndex));
                         break;
                     case DatabaseConstants.COLUMN_sfShiCj:
                         dccyVO.setsfShiCj(cursor.getInt(columnIndex));
                         break;
                     case DatabaseConstants.COLUMN_sfShengCj:
                         dccyVO.setsfShengCj(cursor.getInt(columnIndex));
                         break;
                     case DatabaseConstants.COLUMN_ZT:
                         dccyVO.setZt(cursor.getInt(columnIndex));
                         break;
                     case DatabaseConstants.COLUMN_BZ:
                         dccyVO.setBz(cursor.getString(columnIndex));
                         break;
                     case DatabaseConstants.COLUMN_TRMY:
                         dccyVO.setTrMy(cursor.getString(columnIndex));
                         break;
                     case DatabaseConstants.COLUMN_TRMZ:
                         dccyVO.setTrMz(cursor.getString(columnIndex));
                         break;
                     case DatabaseConstants.COLUMN_FORM_DATA_JSON:
                         dccyVO.setFormData(cursor.getString(columnIndex));
                         break;
                     case DatabaseConstants.COLUMN_USER_ID:
                         dccyVO.setUserId(cursor.getLong(columnIndex));
                         break;
                     default:
                         break;
                 }
             }
        } catch (Exception e) {
             Log.e(TAG, "Error converting cursor to DccyVO: " + e.getMessage());
             return null;
        }
        return dccyVO;
    }

    /**
     * 通过标识码查询指定用户的样点数据
     *
     * @param pjdybh 标识码
     * @param userId  用户ID
     * @return 样点数据
     */
    public DccyVO getSamplingPointByBsm(String pjdybh, Long userId) {
        if (TextUtils.isEmpty(pjdybh) || userId == null) {
            Log.w(TAG, "BSM or User ID is null, cannot query sampling point.");
            return null;
        }

        SQLiteDatabase db = null;
        Cursor cursor = null;
        DccyVO dccyVO = null;

        try {
            db = dbHelper.getReadableDatabase();
            
            String selection = DatabaseConstants.COLUMN_PJDY_BSM + " = ? AND " + DatabaseConstants.COLUMN_USER_ID + " = ?";
            String[] selectionArgs = {pjdybh, String.valueOf(userId)};
            
            cursor = db.query(
                    DatabaseConstants.TABLE_SAMPLING_POINTS,
                    null,
                    selection,
                    selectionArgs,
                    null,
                    null,
                    null
            );
            
            if (cursor != null && cursor.moveToFirst()) {
                 dccyVO = cursorToDccyVO(cursor);
            }
        } catch (Exception e) {
            Log.e(TAG, "查询样点数据失败 (BSM: " + pjdybh + ", User: " + userId + "): " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        
        return dccyVO;
    }

    /**
     * 根据标识码获取指定用户的表单数据JSON
     *
     * @param pjdybh 标识码
     * @return 表单数据JSON字符串，如果不存在则返回null
     */
    public String getFormDataJsonByBsm(String pjdybh) {
        if (TextUtils.isEmpty(pjdybh) ) {
             Log.w(TAG, "BSM or User ID is null, cannot query form data JSON.");
            return null;
        }
        SQLiteDatabase db = null;
        Cursor cursor = null;
        String formDataJson = null;

        try {
            db = dbHelper.getReadableDatabase();
            
            String[] projection = {DatabaseConstants.COLUMN_FORM_DATA_JSON};
            String selection = DatabaseConstants.COLUMN_PJDY_BSM + " = ?";
            String[] selectionArgs = {pjdybh};
            
            cursor = db.query(
                    DatabaseConstants.TABLE_SAMPLING_POINTS,
                    projection,
                    selection,
                    selectionArgs,
                    null,
                    null,
                    null
            );
            
            if (cursor != null && cursor.moveToFirst()) {
                int formDataJsonIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_FORM_DATA_JSON);
                if (formDataJsonIndex != -1 && !cursor.isNull(formDataJsonIndex)) {
                    formDataJson = cursor.getString(formDataJsonIndex);
                }
            }
        } catch (Exception e) {
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return formDataJson;
    }
    
    /**
     * 根据标识码获取指定用户的表单数据JSONObject
     *
     * @param pjdybh 标识码
     * @return 表单数据JSONObject，如果不存在或解析失败则返回null
     */
    public JSONObject getFormDataJsonObjectByBsm(String pjdybh) {
        String formDataJson = getFormDataJsonByBsm(pjdybh);
        if (formDataJson != null) {
            try {
                return new JSONObject(formDataJson);
            } catch (JSONException e) {
                Log.e(TAG, "解析表单数据JSON失败 (BSM: " + pjdybh + "): " + e.getMessage(), e);
            }
        }
        return null;
    }

    /**
     * 检查记录是否已存在
     *
     * @param db      数据库实例
     * @param pjdybh 标识码
     * @param userId 用户ID
     * @return 如果记录存在返回true，否则返回false
     */
    private boolean isRecordExists(SQLiteDatabase db, String pjdybh, Long userId) {
        if (db == null || TextUtils.isEmpty(pjdybh) || userId == null) {
            return false;
        }
        Cursor cursor = null;
        try {
            String selection = DatabaseConstants.COLUMN_PJDY_BSM + " = ? AND " + DatabaseConstants.COLUMN_USER_ID + " = ?";
            String[] selectionArgs = {pjdybh, String.valueOf(userId)};
            cursor = db.query(DatabaseConstants.TABLE_SAMPLING_POINTS, new String[]{"1"},
                    selection, selectionArgs, null, null, null, "1");
            return cursor != null && cursor.getCount() > 0;
        } catch (Exception e) {
            Log.e(TAG, "检查记录是否存在时出错: " + e.getMessage(), e);
            return false;
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
    }

    /**
     * 根据状态获取指定用户的样点列表
     *
     * @param status 状态码
     * @param userId 用户ID
     * @return 样点列表
     */
    public List<DccyVO> getSamplingPointsByStatus(int status, Long userId) {
         if (userId == null) {
             Log.w(TAG, "User ID is null, cannot query sampling points by status.");
             return new ArrayList<>();
        }
        List<DccyVO> dccyList = new ArrayList<>();
        SQLiteDatabase db = null;
        Cursor cursor = null;

        try {
            db = dbHelper.getReadableDatabase();
            String selection = DatabaseConstants.COLUMN_ZT + " = ? AND " + DatabaseConstants.COLUMN_USER_ID + " = ?";
            String[] selectionArgs = {String.valueOf(status), String.valueOf(userId)};
            String orderBy = DatabaseConstants.COLUMN_SAMPLING_ID + " ASC";
            
            cursor = db.query(
                    DatabaseConstants.TABLE_SAMPLING_POINTS,
                    null,
                    selection,
                    selectionArgs,
                    null,
                    null,
                    orderBy
            );
            
            if (cursor != null && cursor.moveToFirst()) {
                do {
                     DccyVO dccyVO = cursorToDccyVO(cursor);
                     if (dccyVO != null) {
                         dccyList.add(dccyVO);
                     }
                } while (cursor.moveToNext());
            }
        } catch (Exception e) {
            Log.e(TAG, "根据状态查询样点失败 (Status: " + status + ", User: " + userId + "): " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return dccyList;
    }
    
    /**
     * 获取指定用户的所有样点列表
     *
     * @param userId 用户ID
     * @return 所有样点列表
     */
    public List<DccyVO> getAllSamplingPoints(Long userId) {
        if (userId == null) {
             Log.w(TAG, "User ID is null, cannot query all sampling points.");
             return new ArrayList<>();
        }
        List<DccyVO> dccyList = new ArrayList<>();
        SQLiteDatabase db = null;
        Cursor cursor = null;

        try {
            db = dbHelper.getReadableDatabase();
            String selection = DatabaseConstants.COLUMN_USER_ID + " = ?";
            String[] selectionArgs = { String.valueOf(userId) };
            String orderBy = DatabaseConstants.COLUMN_SAMPLING_ID + " ASC"; 
            
            cursor = db.query(
                    DatabaseConstants.TABLE_SAMPLING_POINTS,
                    null,
                    selection,
                    selectionArgs,
                    null,
                    null,
                    orderBy
            );
            
            if (cursor != null && cursor.moveToFirst()) {
                do {
                    DccyVO dccyVO = cursorToDccyVO(cursor);
                    if (dccyVO != null) {
                        dccyList.add(dccyVO);
                    }
                } while (cursor.moveToNext());
            }
        } catch (Exception e) {
            Log.e(TAG, "查询所有样点失败 (User: " + userId + "): " + e.getMessage(), e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return dccyList;
    }

    /**
     * 更新指定用户样点的状态
     *
     * @param pjdybh 标识码
     * @param status 新的状态码
     * @param userId 用户ID
     * @return 是否更新成功
     */
    public boolean updateSamplingPointStatus(String pjdybh, int status, Long userId) {
        if (TextUtils.isEmpty(pjdybh) || userId == null) {
            Log.w(TAG, "BSM or User ID is null, cannot update status.");
            return false;
        }
        SQLiteDatabase db = null;
        boolean success = false;
        try {
            db = dbHelper.getWritableDatabase();
            ContentValues values = new ContentValues();
            values.put(DatabaseConstants.COLUMN_ZT, status);
            
            String whereClause = DatabaseConstants.COLUMN_PJDY_BSM + " = ? AND " + DatabaseConstants.COLUMN_USER_ID + " = ?";
            String[] whereArgs = {pjdybh, String.valueOf(userId)};
            
            int rowsAffected = db.update(DatabaseConstants.TABLE_SAMPLING_POINTS, values, whereClause, whereArgs);
            success = rowsAffected > 0;
            if (success) {
                Log.i(TAG, "样点状态更新成功 (BSM: " + pjdybh + ", Status: " + status + ", User: " + userId + ")");
            } else {
                 Log.w(TAG, "样点状态更新失败或未找到记录 (BSM: " + pjdybh + ", User: " + userId + ")");
            }
        } catch (Exception e) {
            Log.e(TAG, "更新样点状态失败 (BSM: " + pjdybh + "): " + e.getMessage(), e);
        } finally {
        }
        return success;
    }
}

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="io.dcloud.uniplugin.sampleflow.QRCodeScanActivity">

    <!-- 扫描区域 -->
    <com.journeyapps.barcodescanner.DecoratedBarcodeView
        android:id="@+id/zxing_barcode_scanner"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        app:zxing_scanner_layout="@layout/custom_barcode_scanner" />

    <!-- 底部提示文字 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#80000000"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="将二维码放在扫描框内"
            android:textColor="@android:color/white"
            android:textSize="16sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="扫描成功后将自动返回样品编码"
            android:textColor="#CCFFFFFF"
            android:textSize="14sp" />
    </LinearLayout>
</LinearLayout> 
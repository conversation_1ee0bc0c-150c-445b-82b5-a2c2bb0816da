package io.dcloud.uniplugin.http.api;

import java.util.List;

import io.dcloud.uniplugin.model.ApiResponse;
import io.dcloud.uniplugin.model.FormConfigResponse;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.Part;

/**
 * 动态表单API服务
 */
public interface FormService {

    /**
     * 获取所有表单配置列表
     */
    @GET("pjdy/dccy/form/upload/form")
    Call<ApiResponse<FormConfigResponse>> getForm();
    
    /**
     * 上传动态表单数据和文件
     * 支持动态字段和文件上传
     * @param fileDict 文件信息JSON字符串
     * @param files 上传的文件列表
     * @return 上传结果
     */
    @Multipart
    @POST("pjdy/dccy/upload")
    Call<ApiResponse<String>> uploadDynamicForm(
            @Part("fileDict") RequestBody fileDict,
            @Part List<MultipartBody.Part> files
    );


    /**
     * 上传动态表单数据和文件
     * 支持动态字段和文件上传
     * @param fileDict 文件信息JSON字符串
     * @param files 上传的文件列表
     * @return 上传结果
     */
    @Multipart
    @POST("pjdy/dccy/zg")
    Call<ApiResponse<String>> uploadZgDynamicForm(
            @Part("fileDict") RequestBody fileDict,
            @Part List<MultipartBody.Part> files
    );
} 

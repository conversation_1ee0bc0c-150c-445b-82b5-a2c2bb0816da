<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F5F5F5"
    tools:context=".sampleflow.SampleFlowDetailActivity">

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- 批次信息 -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="批次信息"
                            android:textColor="#333333"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="12dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="85dp"
                                android:layout_height="wrap_content"
                                android:text="批次编号："
                                android:textColor="#666666"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/textViewBatchCode"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#333333"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="85dp"
                                android:layout_height="wrap_content"
                                android:text="批次类型："
                                android:textColor="#666666"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/textViewBsm"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#333333"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="85dp"
                                android:layout_height="wrap_content"
                                android:text="项目名称："
                                android:textColor="#666666"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/textViewBatchName"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#333333"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="85dp"
                                android:layout_height="wrap_content"
                                android:text="批次状态："
                                android:textColor="#666666"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/textViewBatchState"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#333333"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="85dp"
                                android:layout_height="wrap_content"
                                android:text="创建时间："
                                android:textColor="#666666"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/textViewCreateTime"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#333333"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="送样信息"
                            android:textColor="#333333"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:layout_marginTop="8dp"
                            android:layout_marginBottom="8dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="85dp"
                                android:layout_height="wrap_content"
                                android:text="送样人："
                                android:textColor="#666666"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/textViewSenderName"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#333333"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="85dp"
                                android:layout_height="wrap_content"
                                android:text="联系方式："
                                android:textColor="#666666"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/textViewSenderPhone"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#333333"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="85dp"
                                android:layout_height="wrap_content"
                                android:text="送样单位："
                                android:textColor="#666666"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/textViewSendOrg"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#333333"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <!-- 送样人签名区域 - 优化布局 -->
                        <LinearLayout
                            android:id="@+id/layoutSenderSignature"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp"
                            android:gravity="center_vertical">
                            
                            <TextView
                                android:layout_width="85dp"
                                android:layout_height="wrap_content"
                                android:text="送样人签名："
                                android:textColor="#666666"
                                android:textSize="14sp" />
                            
                            <FrameLayout
                                android:layout_width="120dp"
                                android:layout_height="60dp"
                                android:background="@drawable/signature_background"
                                android:padding="2dp">
                                
                                <ImageView
                                    android:id="@+id/imageViewSenderSignature"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:scaleType="centerInside" />
                            </FrameLayout>
                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="接样信息"
                            android:textColor="#333333"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:layout_marginTop="8dp"
                            android:layout_marginBottom="8dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="85dp"
                                android:layout_height="wrap_content"
                                android:text="接样人："
                                android:textColor="#666666"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/textViewReceiverName"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#333333"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="85dp"
                                android:layout_height="wrap_content"
                                android:text="联系方式："
                                android:textColor="#666666"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/textViewReceiverPhone"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#333333"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="85dp"
                                android:layout_height="wrap_content"
                                android:text="接样单位："
                                android:textColor="#666666"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/textViewReceiveOrg"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#333333"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <!-- 接样人签名区域 - 优化布局 -->
                        <LinearLayout
                            android:id="@+id/layoutReceiverSignature"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp"
                            android:gravity="center_vertical">
                            
                            <TextView
                                android:layout_width="85dp"
                                android:layout_height="wrap_content"
                                android:text="接样人签名："
                                android:textColor="#666666"
                                android:textSize="14sp" />
                            
                            <FrameLayout
                                android:layout_width="120dp"
                                android:layout_height="60dp"
                                android:background="@drawable/signature_background"
                                android:padding="2dp">
                                
                                <ImageView
                                    android:id="@+id/imageViewReceiverSignature"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:scaleType="centerInside" />
                            </FrameLayout>
                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="运送信息"
                            android:textColor="#333333"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:layout_marginTop="8dp"
                            android:layout_marginBottom="8dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="85dp"
                                android:layout_height="wrap_content"
                                android:text="样品数量："
                                android:textColor="#666666"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/textViewSampleNum"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#333333"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="85dp"
                                android:layout_height="wrap_content"
                                android:text="运送方式："
                                android:textColor="#666666"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/textViewDeliveryType"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#333333"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:layout_width="85dp"
                                android:layout_height="wrap_content"
                                android:text="运送信息："
                                android:textColor="#666666"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/textViewDeliveryMessage"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#333333"
                                android:textSize="14sp" />
                        </LinearLayout>
                        
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <!-- 新增：接收样品操作区域 -->
                <LinearLayout
                    android:id="@+id/layoutReceiveAction"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:background="@drawable/bg_card"
                    android:orientation="vertical"
                    android:padding="16dp"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="样品接收"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <Button
                        android:id="@+id/buttonReceive"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:backgroundTint="#4CAF50"
                        android:text="确认接收样品" />
                </LinearLayout>

                <!-- 样品列表 -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="8dp"
                    app:cardCornerRadius="4dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <!-- 样品列表标题和按钮 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:padding="16dp"
                            android:gravity="center_vertical">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="样品列表"
                                android:textColor="#333333"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/textViewSampleCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="共0个样品"
                                android:textColor="#666666"
                                android:textSize="14sp"/>
                        </LinearLayout>

                        <!-- 样品操作区域 - 美化样式 -->
                        <LinearLayout
                            android:id="@+id/layoutSampleActions"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingLeft="16dp"
                            android:paddingRight="16dp"
                            android:paddingTop="8dp"
                            android:paddingBottom="8dp"
                            android:gravity="center_vertical"
                            android:background="#F0F6FF">
                            
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="样品操作："
                                android:textColor="#333333"
                                android:textSize="14sp"
                                android:layout_marginRight="12dp"/>
                            
                            <Button
                                android:id="@+id/buttonScanSample"
                                android:layout_width="wrap_content"
                                android:layout_height="36dp"
                                android:text="扫码添加"
                                android:textSize="14sp"
                                android:textColor="#FFFFFF"
                                android:background="@drawable/button_primary"
                                android:layout_marginRight="12dp"
                                android:paddingLeft="12dp"
                                android:paddingRight="12dp"/>
                            
                            <Button
                                android:id="@+id/buttonAddSample"
                                android:layout_width="wrap_content"
                                android:layout_height="36dp"
                                android:text="手动添加"
                                android:textSize="14sp"
                                android:textColor="#FFFFFF"
                                android:background="@drawable/button_primary"
                                android:paddingLeft="12dp"
                                android:paddingRight="12dp"/>
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#E0E0E0" />

                        <!-- 样品列表内容 -->
                        <FrameLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingLeft="16dp"
                            android:paddingRight="16dp"
                            android:paddingTop="12dp"
                            android:paddingBottom="12dp"
                            android:minHeight="100dp">

                            <!-- 空状态 -->
                            <LinearLayout
                                android:id="@+id/layoutEmptySamples"
                                android:layout_width="match_parent"
                                android:layout_height="160dp"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="16dp"
                                android:visibility="gone">

                                <ImageView
                                    android:layout_width="48dp"
                                    android:layout_height="48dp"
                                    android:src="@drawable/ic_empty_box"
                                    android:alpha="0.3"
                                    android:layout_marginBottom="12dp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="暂无样品"
                                    android:textColor="#999999"
                                    android:textSize="14sp"/>

                            </LinearLayout>

                            <!-- 样品列表 -->
                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recyclerViewSamples"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:clipToPadding="false" />

                        </FrameLayout>

                        <!-- 加载更多按钮 -->
                        <LinearLayout
                            android:id="@+id/loadMoreView"
                            android:layout_width="match_parent"
                            android:layout_height="48dp"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:background="#F5F5F5"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?attr/selectableItemBackground"
                            android:visibility="gone">

                            <ProgressBar
                                android:id="@+id/progressBarLoading"
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:layout_marginEnd="8dp" />

                            <TextView
                                android:id="@+id/textViewLoadingMore"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="点击加载更多"
                                android:textColor="#666666"
                                android:textSize="14sp" />
                        </LinearLayout>

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>
        </ScrollView>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
</LinearLayout> 
package io.dcloud.uniplugin.utils;

import android.app.Activity;
import android.os.Environment;
import android.util.Log;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import io.dcloud.uniplugin.model.DccyVO;

/**
 * 媒体文件下载管理器
 * 负责下载和管理样点相关的媒体文件
 */
public class MediaDownloadManager {
    private static final String TAG = "MediaDownloadManager";
    
    private Activity activity;
    private AlertDialog progressDialog;
    
    /**
     * 构造函数
     * @param activity 上下文Activity
     */
    public MediaDownloadManager(Activity activity) {
        this.activity = activity;
    }
    
    /**
     * 同步下载所有样点的媒体文件，并显示进度对话框
     * @param dccyList 样点数据列表
     */
    public void downloadMediaFilesWithProgress(List<DccyVO> dccyList) {
        // 计算需要下载的文件总数
        int totalFiles = 0;
        for (DccyVO dccyVO : dccyList) {
            if (dccyVO.getPhotoList() != null) {
                for (DccyVO.DccyMediaVO mediaVO : dccyVO.getPhotoList()) {
                    if (mediaVO.isNeedDownload()) {
                        totalFiles++;
                    }
                }
            }
            if (dccyVO.getVideoList() != null) {
                for (DccyVO.DccyMediaVO mediaVO : dccyVO.getVideoList()) {
                    if (mediaVO.isNeedDownload()) {
                        totalFiles++;
                    }
                }
            }
        }
        
        // 如果没有需要下载的文件，直接返回
        if (totalFiles == 0) {
            Log.d(TAG, "没有需要下载的媒体文件");
            // 当没有需要下载的文件时，直接调用回调，并返回成功和失败计数都为0
            if (downloadCompletionListener != null) {
                activity.runOnUiThread(() -> {
                    downloadCompletionListener.onDownloadCompleted(0, 0);
                });
            }
            return;
        }
        
        // 创建并显示进度对话框
        final AlertDialog.Builder builder = new AlertDialog.Builder(activity);
        builder.setTitle("下载媒体文件");
        builder.setMessage("正在下载媒体文件，请稍候...\n0/" + totalFiles);
        builder.setCancelable(false); // 设置对话框不可取消
        progressDialog = builder.create();
        progressDialog.show();
        
        // 创建原子整数计数器以便在Lambda表达式中安全使用
        final AtomicInteger successCounter = new AtomicInteger(0);
        final AtomicInteger failureCounter = new AtomicInteger(0);
        final AtomicInteger downloadedCounter = new AtomicInteger(0);
        
        // 保存总文件数为final变量，以便在Lambda中使用
        final int finalTotalFiles = totalFiles;
        
        // 创建下载线程
        new Thread(() -> {
            // 下载所有样点的媒体文件
            for (DccyVO dccyVO : dccyList) {
                // 下载照片
                if (dccyVO.getPhotoList() != null) {
                    for (DccyVO.DccyMediaVO mediaVO : dccyVO.getPhotoList()) {
                        if (mediaVO.isNeedDownload() && mediaVO.getPath() != null && mediaVO.getFileName() != null) {
                            int current = downloadedCounter.incrementAndGet();
                            
                            // 更新进度对话框
                            updateProgressDialog(current, finalTotalFiles, successCounter.get(), failureCounter.get());
                            
                            // 下载文件
                            try {
                                // 准备下载参数
                                File bcgdCameraDir = new File(Environment.getExternalStorageDirectory() + "/BCGDGISData/BCGDCameraData");
                                if (!bcgdCameraDir.exists()) {
                                    bcgdCameraDir.mkdirs();
                                }
                                File localFile = new File(bcgdCameraDir, mediaVO.getFileName());
                                
                                // 执行下载
                                boolean success = downloadMediaFileSynchronously(mediaVO.getPath(), localFile);
                                
                                // 更新下载状态
                                if (success) {
                                    mediaVO.setPath(localFile.getAbsolutePath());
                                    mediaVO.setNeedDownload(false);
                                    successCounter.incrementAndGet();
                                } else {
                                    failureCounter.incrementAndGet();
                                }
                                
                                // 短暂暂停，避免服务器过载
                                Thread.sleep(100);
                            } catch (Exception e) {
                                Log.e(TAG, "下载照片文件失败: " + mediaVO.getPath(), e);
                                failureCounter.incrementAndGet();
                            }
                        }
                    }
                }
                
                // 下载视频
                if (dccyVO.getVideoList() != null) {
                    for (DccyVO.DccyMediaVO mediaVO : dccyVO.getVideoList()) {
                        if (mediaVO.isNeedDownload() && mediaVO.getPath() != null && mediaVO.getFileName() != null) {
                            int current = downloadedCounter.incrementAndGet();
                            
                            // 更新进度对话框
                            updateProgressDialog(current, finalTotalFiles, successCounter.get(), failureCounter.get());
                            
                            // 下载文件
                            try {
                                // 准备下载参数
                                File bcgdCameraDir = new File(Environment.getExternalStorageDirectory() + "/BCGDGISData/BCGDCameraData");
                                if (!bcgdCameraDir.exists()) {
                                    bcgdCameraDir.mkdirs();
                                }
                                File localFile = new File(bcgdCameraDir, mediaVO.getFileName());
                                
                                // 执行下载
                                boolean success = downloadMediaFileSynchronously(mediaVO.getPath(), localFile);
                                
                                // 更新下载状态
                                if (success) {
                                    mediaVO.setPath(localFile.getAbsolutePath());
                                    mediaVO.setNeedDownload(false);
                                    successCounter.incrementAndGet();
                                } else {
                                    failureCounter.incrementAndGet();
                                }
                                
                                // 短暂暂停，避免服务器过载
                                Thread.sleep(100);
                            } catch (Exception e) {
                                Log.e(TAG, "下载视频文件失败: " + mediaVO.getPath(), e);
                                failureCounter.incrementAndGet();
                            }
                        }
                    }
                }
            }
            
            // 下载完成，关闭进度对话框
            activity.runOnUiThread(() -> {
                if (progressDialog != null && progressDialog.isShowing()) {
                    progressDialog.dismiss();
                }
                
                // 获取最终计数结果
                int successCount = successCounter.get();
                int failureCount = failureCounter.get();
                
                // 显示下载结果
                String resultMessage;
                if (successCount > 0 && failureCount == 0) {
                    resultMessage = "已成功下载 " + successCount + " 个媒体文件";
                } else if (successCount > 0 && failureCount > 0) {
                    resultMessage = "已下载 " + successCount + " 个文件，" + failureCount + " 个文件下载失败";
                } else if (successCount == 0 && failureCount > 0) {
                    resultMessage = "所有 " + failureCount + " 个媒体文件下载失败";
                } else {
                    resultMessage = "没有需要下载的媒体文件";
                }
                
                Toast.makeText(activity, resultMessage, Toast.LENGTH_LONG).show();
                
                // 通知调用者下载已完成
                if (downloadCompletionListener != null) {
                    downloadCompletionListener.onDownloadCompleted(successCount, failureCount);
                }
            });
        }).start();
    }
    
    /**
     * 更新进度对话框
     */
    private void updateProgressDialog(int current, int total, int success, int failure) {
        activity.runOnUiThread(() -> {
            if (progressDialog != null && progressDialog.isShowing()) {
                progressDialog.setMessage("正在下载媒体文件，请稍候...\n" + 
                                      current + "/" + total + 
                                      "\n成功: " + success + ", 失败: " + failure);
            }
        });
    }
    
    /**
     * 同步下载单个媒体文件
     * @param remoteUrl 远程URL
     * @param localFile 本地文件
     * @return 是否下载成功
     */
    private boolean downloadMediaFileSynchronously(String remoteUrl, File localFile) {
        try {
            // 创建目录（如果不存在）
            File parentDir = localFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            // 如果文件已存在，则跳过下载
            if (localFile.exists() && localFile.length() > 0) {
                Log.d(TAG, "文件已存在，跳过下载: " + localFile.getAbsolutePath());
                return true;
            }
            
            // 创建临时文件用于下载
            File tempFile = new File(parentDir, localFile.getName() + ".downloading");
            
            // 如果存在旧的临时文件，先删除
            if (tempFile.exists()) {
                tempFile.delete();
            }
            
            Log.d(TAG, "开始下载文件: " + remoteUrl + " -> " + tempFile.getAbsolutePath());
            
            // 使用HttpURLConnection下载文件
            URL url = new URL(remoteUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(30000); // 30秒连接超时
            connection.setReadTimeout(60000);    // 60秒读取超时
            
            boolean downloadSuccess = false;
            
            try {
                if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                    InputStream input = connection.getInputStream();
                    FileOutputStream output = new FileOutputStream(tempFile);
                    
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = input.read(buffer)) != -1) {
                        output.write(buffer, 0, bytesRead);
                    }
                    
                    output.close();
                    input.close();
                    
                    // 下载成功
                    downloadSuccess = true;
                    Log.d(TAG, "临时文件下载成功: " + tempFile.getAbsolutePath());
                } else {
                    String errorMsg = "下载文件失败，HTTP响应码: " + connection.getResponseCode();
                    Log.e(TAG, errorMsg);
                }
            } finally {
                connection.disconnect();
            }
            
            // 如果下载成功，将临时文件重命名为最终文件名
            if (downloadSuccess && tempFile.exists() && tempFile.length() > 0) {
                if (localFile.exists()) {
                    localFile.delete(); // 确保目标文件不存在，避免重命名失败
                }
                
                boolean renamed = tempFile.renameTo(localFile);
                if (renamed) {
                    Log.d(TAG, "文件下载完成并重命名成功: " + localFile.getAbsolutePath());
                    return true;
                } else {
                    Log.e(TAG, "文件重命名失败: " + tempFile.getAbsolutePath() + " -> " + localFile.getAbsolutePath());
                    // 尝试复制然后删除
                    try {
                        FileInputStream inStream = new FileInputStream(tempFile);
                        FileOutputStream outStream = new FileOutputStream(localFile);
                        byte[] buffer = new byte[4096];
                        int bytesRead;
                        while ((bytesRead = inStream.read(buffer)) != -1) {
                            outStream.write(buffer, 0, bytesRead);
                        }
                        inStream.close();
                        outStream.close();
                        tempFile.delete();
                        Log.d(TAG, "文件重命名失败但复制成功: " + localFile.getAbsolutePath());
                        return true;
                    } catch (Exception e) {
                        Log.e(TAG, "复制文件失败: " + e.getMessage(), e);
                        return false;
                    }
                }
            } else if (tempFile.exists()) {
                // 如果下载失败但临时文件存在，删除临时文件
                tempFile.delete();
                Log.d(TAG, "下载失败，删除临时文件: " + tempFile.getAbsolutePath());
            }
            return false;
        } catch (Exception e) {
            String errorMsg = "下载文件时出错: " + (e.getMessage() != null ? e.getMessage() : e.getClass().getSimpleName());
            Log.e(TAG, errorMsg, e);
            return false;
        }
    }
    
    /**
     * 下载完成监听器
     */
    public interface DownloadCompletionListener {
        void onDownloadCompleted(int successCount, int failureCount);
    }
    
    private DownloadCompletionListener downloadCompletionListener;
    
    /**
     * 设置下载完成监听器
     * @param listener 监听器
     */
    public void setDownloadCompletionListener(DownloadCompletionListener listener) {
        this.downloadCompletionListener = listener;
    }
    
//    /**
//     * 取消下载
//     */
//    public void cancelDownload() {
//        activity.runOnUiThread(() -> {
//            if (progressDialog != null && progressDialog.isShowing()) {
//                progressDialog.dismiss();
//            }
//        });
//    }
} 
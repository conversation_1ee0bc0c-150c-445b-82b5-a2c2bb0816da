package io.dcloud.uniplugin;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.text.method.ScrollingMovementMethod;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import com.esri.arcgisruntime.concurrent.ListenableFuture;
import com.esri.arcgisruntime.data.Feature;
import com.esri.arcgisruntime.geometry.Point;
import com.esri.arcgisruntime.mapping.GeoElement;
import com.esri.arcgisruntime.mapping.view.Callout;
import com.esri.arcgisruntime.mapping.view.DefaultMapViewOnTouchListener;
import com.esri.arcgisruntime.mapping.view.IdentifyLayerResult;
import com.esri.arcgisruntime.mapping.view.MapView;
import com.google.android.material.bottomsheet.BottomSheetBehavior;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;

import uni.dcloud.io.uniplugin_module.R;

public class IdentifyFeature {
    private Spinner spner;
    private Callout mCallout = null;
    private Callout callout = null;
    private Point clickPoint;

    public void identifyResult(MapView mapView,BottomSheetBehavior mBottomSheetBehavior) {

        mapView.setOnTouchListener(new DefaultMapViewOnTouchListener(mapView.getContext(), mapView) {
            @SuppressLint("ClickableViewAccessibility")
            @Override
            public boolean onSingleTapConfirmed(MotionEvent v) {
                if (mBottomSheetBehavior.getState() == BottomSheetBehavior.STATE_EXPANDED) {
                    mBottomSheetBehavior.setState(BottomSheetBehavior.STATE_HIDDEN);
                } else {
                    android.graphics.Point screenPoint = new android.graphics.Point(Math.round(v.getX()), Math.round(v.getY()));
                    clickPoint = mapView.screenToLocation(screenPoint);//
                    final ListenableFuture<List<IdentifyLayerResult>> identifyLayerResultsFuture = mapView
                            .identifyLayersAsync(screenPoint, 12, false, 10);

                    identifyLayerResultsFuture.addDoneListener(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                List<IdentifyLayerResult> identifyLayerResults = identifyLayerResultsFuture.get();
                                handleIdentifyResults(identifyLayerResults,mapView,mMapView);
                            } catch (InterruptedException | ExecutionException e) {
                                Log.e("TAG", "Error identifying results: " + e.getMessage());
                            }
                        }
                    });
                }
                return false;
            }

        });
    }
    private void handleIdentifyResults(List<IdentifyLayerResult> identifyLayerResults,MapView mapView,MapView mMapView) {
        StringBuilder message = new StringBuilder();
        List LayerName = new ArrayList<>();
        Map<String,Object> LayerNameAndAtrrbuit=new HashMap<String,Object>();
        int totalCount = 0;
        for (IdentifyLayerResult identifyLayerResult : identifyLayerResults) {
            int count = geoElementsCountFromResult(identifyLayerResult);
            String layerName = identifyLayerResult.getLayerContent().getName();
            LayerName.add(layerName);
            GeoElement element=identifyLayerResult.getElements().get(0);
            Feature feature =(Feature) element;
            Map<String,Object> attr =feature.getAttributes();
            Set<String> keys=attr.keySet();
            StringBuilder attributes=new StringBuilder();
            for (String key:keys){
                String  aliasName=feature.getFeatureTable().getField(key).getAlias();
                if(!aliasName.contains("OBJECTID")&&!aliasName.contains("FID")) {
                    Object value = attr.get(key);
                    if (value instanceof GregorianCalendar) {
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd-MMM-yyyy", Locale.US);
                        value = simpleDateFormat.format(((GregorianCalendar) value).getTime());
                    }
                    attributes.append(aliasName + ":" + value + "\n");
                }

            }
            LayerNameAndAtrrbuit.put(layerName,attributes);
//            Iterator<GeoElement> iterator=identifyLayerResult.getElements().iterator();
//            while (iterator.hasNext()){
//                GeoElement geoElement=iterator.next();
//                LayerNameAndAtrrbuit=geoElement.getAttributes();
//            }
            Log.d("要素属性",LayerNameAndAtrrbuit.values().toString());

            //LayerName.add(layerName+"个数:"+count);
            //message.append(layerName).append(": ").append(count);

            // 如果不是数组中的最后元素，则添加新的行字符
           // if (!identifyLayerResult.equals(identifyLayerResults.get(identifyLayerResults.size() - 1))) {
           //     message.append("\n");
           //}
            totalCount += count;
        }

        LayoutInflater inflater = LayoutInflater.from(mapView.getContext());
        View view2 = inflater.inflate(R.layout.calloutdisplay, null);
        TextView calloutContent = (TextView) view2.findViewById(R.id.identifyContent);

        //退出按钮
        Button button = (Button) view2.findViewById(R.id.callexit);
        button.setOnClickListener(view1 -> callExit());
        //实例化spinner控件
        // get buttons from layouts
        if(LayerName.size()==0){
            calloutContent.append("图层未选择或者图层不可见！");
            callout.show(view2,clickPoint);
            return;
        }
        spner = view2.findViewById(R.id.spinner3);
        //将图层名称数组写入spinner中
        spner.setPrompt("请选择属性查询的图层:");
        ArrayAdapter eduAdapter = new ArrayAdapter(mapView.getContext(), android.R.layout.simple_spinner_item, LayerName);
        eduAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spner.setAdapter(eduAdapter);
        if (mCallout != null)
        {
            mCallout.dismiss();
        }
        mCallout = mapView.getCallout();
        callout = mMapView.getCallout();
        //获取选中的图层的序号
        spner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                calloutContent.setText("");
                calloutContent.setTextColor(Color.BLACK);
                calloutContent.setSingleLine(false);
                calloutContent.setVerticalScrollBarEnabled(true);
                calloutContent.setScrollBarStyle(View.SCROLLBARS_INSIDE_INSET);
                calloutContent.setMovementMethod(new ScrollingMovementMethod());
                calloutContent.setLines(5);
                String name=LayerName.get(i).toString();
                calloutContent.append(LayerNameAndAtrrbuit.get(name).toString());

            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {
                Toast.makeText(mapView.getContext(), "请选择图层！", Toast.LENGTH_LONG);
            }
        });
        callout.show(view2,clickPoint);

        // 如果有元素被发现则展示识别结果，否则通知用户没有元素被找到
//        if (totalCount > 0) {
//            showAlertDialog(message,mapView.getContext());
//        } else {
//            Toast.makeText(mapView.getContext(), "No element found", Toast.LENGTH_SHORT).show();
//            Log.i("TAG", "No element found.");
//        }
    }
    private int geoElementsCountFromResult(IdentifyLayerResult result) {
        // 创建一个临时的数组
        List<IdentifyLayerResult> tempResults = new ArrayList<>();
        tempResults.add(result);

        // 使用深度优先搜索方法来处理递归
        int count = 0;
        int index = 0;

        while (index < tempResults.size()) {
            // 从array数组中获取结果对象
            IdentifyLayerResult identifyResult = tempResults.get(index);
            // 更新结果中的地理元素数量
            // update count with geoElements from the result
            count += identifyResult.getElements().size();

            // 如果子图层中有结果，在当前的结果后面添加结果对象
            if (identifyResult.getSublayerResults().size() > 0) {
                tempResults.add(identifyResult.getSublayerResults().get(index));
            }

            // update the count and repeat
            // 更新计数重复循环
            index += 1;
        }
        return count;
    }
    private void showAlertDialog(StringBuilder message, Context context) {
        AlertDialog.Builder alertDialogBuilder = new AlertDialog.Builder(context);
        // 设置标题
        alertDialogBuilder.setTitle("Number of elements found");

        // 设置dialog message消息
        alertDialogBuilder
                .setMessage(message)
                .setCancelable(false)
                .setPositiveButton("Ok", new DialogInterface.OnClickListener() {
                    @Override public void onClick(DialogInterface dialog, int id) {
                    }
                });

        // 创建AlertDialog对象
        AlertDialog alertDialog = alertDialogBuilder.create();
        // 显示AlertDialog
        alertDialog.show();
    }
    private void callExit()
    {
        if (callout != null)
        {
            callout.dismiss();
        }

    }
}

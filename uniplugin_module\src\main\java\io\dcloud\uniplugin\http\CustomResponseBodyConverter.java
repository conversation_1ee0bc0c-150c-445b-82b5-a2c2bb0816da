package io.dcloud.uniplugin.http;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.TypeAdapter;

import java.io.IOException;

import okhttp3.ResponseBody;
import retrofit2.Converter;

public class CustomResponseBodyConverter<T> implements Converter<ResponseBody, T> {
    private final Gson gson;
    private final TypeAdapter<T> adapter;
    private final Context context;

    public CustomResponseBodyConverter(Gson gson, TypeAdapter<T> adapter, Context context) {
        this.gson = gson;
        this.adapter = adapter;
        this.context = context;
    }

    @Override
    public T convert(ResponseBody value) throws IOException {
        String jsonString = value.string();
        try {
            // 先将返回数据解析为BaseResponse
            BaseResponse<?> baseResponse = gson.fromJson(jsonString, BaseResponse.class);
            
            // 检查响应码
            if (baseResponse.getCode() == BaseResponse.CODE_UNAUTHORIZED) {
                // 在主线程中显示登录过期对话框
                Log.e("相应处理器", "重新登录提熊" );
                new android.os.Handler(android.os.Looper.getMainLooper()).post(() -> {
                    new android.app.AlertDialog.Builder(context)
                        .setTitle("提示")
                        .setMessage("登录已过期，是否重新登录？")
                        .setPositiveButton("确定", (dialog, which) -> {
                            // 跳转到登录页面
                            Intent intent = new Intent(context, io.dcloud.uniplugin.LoginActivity.class);
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            context.startActivity(intent);
                        })
                        .setNegativeButton("取消", null)
                        .show();
                });
                throw new ApiException(baseResponse);
            } else if (baseResponse.getCode() != BaseResponse.CODE_SUCCESS) {
                // 其他错误，抛出异常
                throw new ApiException(baseResponse);
            }
            
            // 如果是成功响应，解析实际数据
            return adapter.fromJson(jsonString);
        } finally {
            value.close();
        }
    }
}
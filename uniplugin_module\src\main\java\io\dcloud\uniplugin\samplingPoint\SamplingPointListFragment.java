package io.dcloud.uniplugin.samplingPoint;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import io.dcloud.uniplugin.model.DccyVO;
import uni.dcloud.io.uniplugin_module.R;

/**
 * 样点列表Fragment，用于展示特定状态的样点
 */
public class SamplingPointListFragment extends Fragment {

    private static final String ARG_SAMPLING_POINTS = "sampling_points";
    private static final String ARG_TAB_POSITION = "tab_position";

    private RecyclerView recyclerView;
    private TextView textViewEmpty;
    private List<DccyVO> samplingPoints = new ArrayList<>();
    private SamplingPointListAdapter adapter;
    private int tabPosition; // 当前标签页位置：1-待调查，2-待提交，3-已提交，4-待整改

    public SamplingPointListFragment() {
        // Required empty public constructor
    }

//    /**
//     * 创建Fragment实例
//     * @param samplingPoints 样点列表
//     * @return Fragment实例
//     */
//    public static SamplingPointListFragment newInstance(List<DccyVO> samplingPoints) {
//        SamplingPointListFragment fragment = new SamplingPointListFragment();
//        Bundle args = new Bundle();
//        args.putSerializable(ARG_SAMPLING_POINTS, new ArrayList<>(samplingPoints));
//        fragment.setArguments(args);
//        return fragment;
//    }
    
    /**
     * 创建Fragment实例，带标签页位置
     * @param samplingPoints 样点列表
     * @param tabPosition 标签页位置：1-待调查，2-待提交，3-已提交，4-待整改
     * @return Fragment实例
     */
    public static SamplingPointListFragment newInstance(List<DccyVO> samplingPoints, int tabPosition) {
        SamplingPointListFragment fragment = new SamplingPointListFragment();
        Bundle args = new Bundle();
        args.putSerializable(ARG_SAMPLING_POINTS, new ArrayList<>(samplingPoints));
        args.putInt(ARG_TAB_POSITION, tabPosition);
        fragment.setArguments(args);
        return fragment;
    }
    
    /**
     * 设置标签页位置
     * @param tabPosition 标签页位置：1-待调查，2-待提交，3-已提交，4-待整改
     */
    public void setTabPosition(int tabPosition) {
        this.tabPosition = tabPosition;
        if (adapter != null) {
            adapter.setTabPosition(tabPosition);
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            samplingPoints = (List<DccyVO>) getArguments().getSerializable(ARG_SAMPLING_POINTS);
            tabPosition = getArguments().getInt(ARG_TAB_POSITION, 1); // 默认为待调查标签页
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_sampling_point_list, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // 初始化视图
        recyclerView = view.findViewById(R.id.recyclerViewSamplingPoints);
        textViewEmpty = view.findViewById(R.id.textViewEmpty);

        // 设置RecyclerView
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        adapter = new SamplingPointListAdapter(getContext(), samplingPoints, tabPosition);
        recyclerView.setAdapter(adapter);

        // 更新UI
        updateUI();
    }

    /**
     * 更新UI
     */
    private void updateUI() {
        if (samplingPoints == null || samplingPoints.isEmpty()) {
            recyclerView.setVisibility(View.GONE);
            textViewEmpty.setVisibility(View.VISIBLE);
        } else {
            recyclerView.setVisibility(View.VISIBLE);
            textViewEmpty.setVisibility(View.GONE);
            adapter.updateData(samplingPoints);
            // 确保适配器有正确的标签页位置
            adapter.setTabPosition(tabPosition);
        }
    }

    /**
     * 更新数据
     * @param newData 新数据
     */
    public void updateData(List<DccyVO> newData) {
        this.samplingPoints = newData != null ? newData : new ArrayList<>();
        if (adapter != null) {
            adapter.updateData(this.samplingPoints);
            // 确保适配器有正确的标签页位置
            adapter.setTabPosition(tabPosition);
        }
        if (textViewEmpty != null && recyclerView != null) {
            updateUI();
        }
    }
}
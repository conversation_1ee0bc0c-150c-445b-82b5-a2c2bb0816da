package io.dcloud.uniplugin.form.utils;

import android.app.Activity;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.Adapter;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.dcloud.uniplugin.form.SignatureView;
import io.dcloud.uniplugin.form.field.FieldFile;
import io.dcloud.uniplugin.model.FormFieldConfig;
import uni.dcloud.io.uniplugin_module.R;

/**
 * 表单数据工具类，提供表单数据填充和处理功能
 */
public class FormDataUtils {
    private static final String TAG = "FormDataUtils";

    /**
     * 填充表单数据到UI
     *
     * @param activity 活动上下文
     * @param formViews 表单视图映射
     * @param allFields 所有字段配置
     * @param formData 表单数据
     * @param fieldFiles 字段文件映射
     * @param locationManager 位置管理器（可选，如果为null则不更新位置信息）
     */
    public static void fillFormWithData(Activity activity, 
                                        Map<String, View> formViews, 
                                        List<FormFieldConfig> allFields, 
                                        JSONObject formData, 
                                        Map<String, List<FieldFile>> fieldFiles,
                                        FormLocationManager locationManager) {
        try {
            // 遍历所有字段
            for (FormFieldConfig field : allFields) {
                String fieldId = field.getFieldId();
                String fieldType = field.getFieldType();

                // 检查表单数据中是否包含该字段的值
                if (formData.has(fieldId)) {
                    String value = formData.optString(fieldId, "");

                    // 获取字段视图
                    View fieldView = formViews.get(fieldId);
                    if (fieldView != null) {
                        // 根据字段类型设置值
                        switch (fieldType) {
                            case FormFieldConfig.TYPE_TEXT:
                            case FormFieldConfig.TYPE_NUMBER:
                            case FormFieldConfig.TYPE_DATE:
                            case FormFieldConfig.TYPE_TEXTAREA:
                                // 先尝试通过tag查找
                                android.widget.EditText editText = fieldView.findViewWithTag("input_" + fieldId);

                                // 如果通过tag找不到，尝试直接查找第一个EditText
                                if (editText == null) {
                                    if (fieldView instanceof android.widget.EditText) {
                                        editText = (android.widget.EditText) fieldView;
                                    } else if (fieldView instanceof ViewGroup) {
                                        ViewGroup viewGroup = (ViewGroup) fieldView;
                                        for (int i = 0; i < viewGroup.getChildCount(); i++) {
                                            View child = viewGroup.getChildAt(i);
                                            if (child instanceof android.widget.EditText) {
                                                editText = (android.widget.EditText) child;
                                                break;
                                            }
                                        }
                                    }
                                }

                                if (editText != null) {
                                    editText.setText(value);
                                }
                                break;

                            case FormFieldConfig.TYPE_DROPDOWN:
                                // 先尝试通过tag查找
                                android.widget.Spinner spinner = fieldView.findViewWithTag("input_" + fieldId);

                                // 如果通过tag找不到，尝试直接查找第一个Spinner
                                if (spinner == null) {
                                    if (fieldView instanceof android.widget.Spinner) {
                                        spinner = (android.widget.Spinner) fieldView;
                                    } else if (fieldView instanceof ViewGroup) {
                                        ViewGroup viewGroup = (ViewGroup) fieldView;
                                        for (int i = 0; i < viewGroup.getChildCount(); i++) {
                                            View child = viewGroup.getChildAt(i);
                                            if (child instanceof android.widget.Spinner) {
                                                spinner = (android.widget.Spinner) child;
                                                break;
                                            }
                                        }
                                    }
                                }

                                if (spinner != null) {
                                    // 找到对应的选项索引
                                    List<FormFieldConfig.OptionItem> options = field.getOptions();
                                    if (options != null && !options.isEmpty()) {
                                        boolean foundMatch = false;

                                        for (int i = 0; i < options.size(); i++) {
                                            FormFieldConfig.OptionItem item = options.get(i);
                                            if (item.getValue().equals(value)) {
                                                spinner.setSelection(i);
                                                foundMatch = true;
                                                break;
                                            }
                                        }

                                        if (!foundMatch) {
                                            // 特殊处理某些特定字段
                                            if ("soil_type".equals(fieldId)) {
                                                // 土壤类型特殊处理
                                                for (int i = 0; i < options.size(); i++) {
                                                    if (options.get(i).getLabel().equals(value)) {
                                                        spinner.setSelection(i);
                                                        foundMatch = true;
                                                        break;
                                                    }
                                                }
                                            } else if ("district".equals(fieldId) || "city".equals(fieldId)) {
                                                // 城市和区县字段特殊处理

                                                // 如果是城市，先确保选择了城市，然后手动触发区县选项加载
                                                if ("city".equals(fieldId) && spinner.getSelectedItemPosition() >= 0) {
                                                    // 模拟城市选择变化事件
                                                    try {
                                                        AdapterView.OnItemSelectedListener listener = spinner.getOnItemSelectedListener();
                                                        if (listener != null) {
                                                            listener.onItemSelected(spinner, spinner.getSelectedView(),
                                                                    spinner.getSelectedItemPosition(), spinner.getSelectedItemId());
                                                        }
                                                    } catch (Exception e) {
                                                        Log.e(TAG, "触发城市选择事件失败: " + e.getMessage());
                                                    }
                                                }
                                                // 对区县字段，可能需要延迟处理，等待选项加载完成
                                                else if ("district".equals(fieldId)) {
                                                    final String districtValue = value;
                                                    final android.widget.Spinner finalSpinner = spinner;
                                                    finalSpinner.post(() -> {
                                                        // 延迟200ms再尝试设置区县值，以便选项有时间加载
                                                        finalSpinner.postDelayed(() -> {
                                                            Adapter adapter = finalSpinner.getAdapter();
                                                            if (adapter != null && adapter.getCount() > 0) {
                                                                for (int i = 0; i < adapter.getCount(); i++) {
                                                                    Object item = adapter.getItem(i);
                                                                    if (item != null && item.toString().equals(districtValue)) {
                                                                        finalSpinner.setSelection(i);
                                                                        break;
                                                                    }
                                                                }
                                                            }
                                                        }, 500);
                                                    });
                                                }
                                            } else {
                                                // 如果没有匹配项，尝试查找"其他"选项
                                                int otherIndex = -1;
                                                for (int i = 0; i < options.size(); i++) {
                                                    String label = options.get(i).getLabel();
                                                    if ("其他".equals(label) || "其它".equals(label) || "other".equalsIgnoreCase(label)) {
                                                        otherIndex = i;
                                                        break;
                                                    }
                                                }
                                                
                                                // 如果找到"其他"选项，选中它并填充输入框
                                                if (otherIndex >= 0) {
                                                    spinner.setSelection(otherIndex);
                                                    
                                                    // 触发选择事件，确保输入框显示
                                                    try {
                                                        AdapterView.OnItemSelectedListener listener = spinner.getOnItemSelectedListener();
                                                        if (listener != null) {
                                                            listener.onItemSelected(spinner, spinner.getSelectedView(),
                                                                    otherIndex, spinner.getSelectedItemId());
                                                        }
                                                    } catch (Exception e) {
                                                        Log.e(TAG, "触发其他选项选择事件失败: " + e.getMessage());
                                                    }
                                                    
                                                    // 查找并填充"其他"输入框
                                                    Object tag = spinner.getTag();
                                                    if (tag instanceof android.widget.EditText) {
                                                        android.widget.EditText otherInput = (android.widget.EditText) tag;
                                                        otherInput.setText(value);
                                                        otherInput.setVisibility(View.VISIBLE);
                                                    }
                                                    
                                                    foundMatch = true;
                                                }
                                            }
                                        }
                                    }
                                }
                                break;

                            case FormFieldConfig.TYPE_LOCATION:
                                // 处理位置字段
                                TextView locationText = fieldView.findViewById(R.id.locationText);
                                if (locationText != null) {
                                    // 检查value是否以"{"开头，判断是否为JSON对象
                                    if (value.trim().startsWith("{")) {
                                        try {
                                            // 尝试解析为JSON对象
                                            JSONObject locationObj = new JSONObject(value);
                                            String locationStr = locationObj.optString("locationText", "");

                                            locationText.setText(locationStr);
                                        } catch (Exception e) {
                                            // JSON解析失败，直接显示原始文本
                                            locationText.setText(value);
                                        }
                                    } else {
                                        // 不是JSON格式，检查是否含有经纬度信息
                                        String locationStr = value;

                                        try {
                                            // 尝试从locationStr中提取经纬度信息
                                            if (locationStr.contains("经度:") && locationStr.contains("纬度:")) {
                                                Double longitude = null;
                                                Double latitude = null;

                                                // 提取经度
                                                int lonIndex = locationStr.indexOf("经度:") + 3;
                                                int latIndex = locationStr.indexOf("纬度:");
                                                if (lonIndex > 3 && latIndex > lonIndex) {
                                                    String lonStr = locationStr.substring(lonIndex, latIndex).trim();
                                                    try {
                                                        longitude = Double.parseDouble(lonStr);
                                                    } catch (NumberFormatException nfe) {
                                                        Log.e(TAG, "解析经度失败: " + lonStr);
                                                    }
                                                }

                                                // 提取纬度
                                                int endIndex = locationStr.indexOf("精度:");
                                                if (endIndex < 0) endIndex = locationStr.length();
                                                if (latIndex > 0 && endIndex > latIndex) {
                                                    String latStr = locationStr.substring(latIndex + 3, endIndex).trim();
                                                    try {
                                                        latitude = Double.parseDouble(latStr);
                                                    } catch (NumberFormatException nfe) {
                                                        Log.e(TAG, "解析纬度失败: " + latStr);
                                                    }
                                                }

                                                // 更新位置管理器中的目标坐标
                                                if (longitude != null && latitude != null && locationManager != null) {
                                                    locationManager.setTargetCoordinates(latitude, longitude);
                                                }
                                            }

                                            // 直接显示原始文本
                                            locationText.setText(locationStr);
                                        } catch (Exception e) {
                                            // 如果处理过程发生错误，直接显示原始文本
                                            locationText.setText(value);
                                        }
                                    }
                                }
                                break;

                            case FormFieldConfig.TYPE_FILE:
                                // 文件字段的回显已通过加载图片适配器的方式处理
                                Log.d(TAG, "文件字段: " + fieldId + " 已通过ImageAdapter处理回显");
                                break;

                            case FormFieldConfig.TYPE_SIGNATURE:
                                // 处理签名字段
                                handleSignatureField(activity, fieldId, fieldView, value, fieldFiles);
                                break;

                            default:
                                break;
                        }
                    }
                }
            }

            Log.d(TAG, "表单数据已填充到UI");
        } catch (Exception e) {
            Log.e(TAG, "填充表单数据到UI出错: " + e.getMessage());
        }
    }

    /**
     * 处理签名字段的数据填充
     *
     * @param activity 活动上下文
     * @param fieldId 字段ID
     * @param fieldView 字段视图
     * @param value 字段值
     * @param fieldFiles 字段文件映射
     */
    private static void handleSignatureField(Activity activity, String fieldId, View fieldView, 
                                            String value, Map<String, List<FieldFile>> fieldFiles) {
        if (TextUtils.isEmpty(fieldId) || fieldView == null) {
            return;
        }

        // 获取签名文件路径
        String filePath = null;

        String fieldIdLower = fieldId.toLowerCase();
        Log.d(TAG, "处理签名字段: " + fieldId);

        // 首先检查fieldFiles中是否已有该字段的文件
        if (fieldFiles != null && fieldFiles.containsKey(fieldId)) {
            List<FieldFile> files = fieldFiles.get(fieldId);
            if (files != null && !files.isEmpty() && files.get(0) != null) {
                filePath = files.get(0).getPath();
                Log.d(TAG, "从字段文件列表获取签名路径: " + filePath);
            } else if (!TextUtils.isEmpty(value) && !value.equals("null")) {
                // 如果fieldFiles中没有，但表单数据中有值，则使用表单数据中的值
                filePath = value;
                Log.d(TAG, "从表单数据获取签名路径: " + filePath);
            } else {
                Log.w(TAG, "未找到签名文件路径");
            }
        } else if (!TextUtils.isEmpty(value) && !value.equals("null")) {
            // 如果fieldFiles为空但表单数据中有值
            filePath = value;
            Log.d(TAG, "从表单数据获取签名路径: " + filePath);

            // 创建FieldFile并添加到fieldFiles
            FieldFile fieldFile = new FieldFile(filePath, "signature");
            List<FieldFile> files = new ArrayList<>();
            files.add(fieldFile);
            if (fieldFiles == null) {
                Map<String, List<FieldFile>> newFieldFiles = new HashMap<>();
                newFieldFiles.put(fieldId, files);
                // 注意：在这种情况下，需要外部接收返回的fieldFiles
            } else {
                fieldFiles.put(fieldId, files);
            }
            Log.d(TAG, "为字段 " + fieldId + " 创建了新的FieldFile: " + filePath);
        }

        // 如果有文件路径，加载签名图片
        if (!TextUtils.isEmpty(filePath)) {
            File file = new File(filePath);
            if (file.exists()) {
                Log.d(TAG, "签名文件存在，大小: " + file.length() + " 字节");
                loadSignatureImage(activity, fieldId, fieldView, filePath);
            } else {
                Log.e(TAG, "签名文件不存在: " + filePath);
            }
        } else {
            Log.w(TAG, fieldId + " 没有找到有效的签名文件路径");
        }
    }

    /**
     * 加载签名图片并显示
     *
     * @param activity 活动上下文
     * @param fieldId 字段ID
     * @param fieldView 字段视图
     * @param filePath 文件路径
     */
    private static void loadSignatureImage(Activity activity, String fieldId, View fieldView, String filePath) {
        if (activity == null || fieldView == null || TextUtils.isEmpty(filePath)) {
            return;
        }

        // 获取签名相关视图
        ImageView signatureImage = fieldView.findViewById(R.id.signatureImage);
        SignatureView signatureView = fieldView.findViewById(R.id.signatureView);
        TextView savedSignatureLabel = fieldView.findViewById(R.id.savedSignatureLabel);

        // 获取面板和按钮布局
        ViewGroup signaturePanel = null;
        View buttonLayout = null;

        if (signatureView != null) {
            ViewParent parent = signatureView.getParent();
            if (parent instanceof ViewGroup) {
                signaturePanel = (ViewGroup) parent;
            }
        }

        // 查找按钮布局
        for (int i = 0; i < ((ViewGroup) fieldView).getChildCount(); i++) {
            View child = ((ViewGroup) fieldView).getChildAt(i);
            if (child instanceof ViewGroup &&
                    child.findViewById(R.id.btnClear) != null) {
                buttonLayout = child;
                break;
            }
        }

        // 加载签名图片并显示
        Bitmap bitmap = android.graphics.BitmapFactory.decodeFile(filePath);
        if (bitmap != null && signatureImage != null) {
            Log.d(TAG, "成功加载签名图片，尺寸: " + bitmap.getWidth() + "x" + bitmap.getHeight());

            // 显示签名图片
            signatureImage.setImageBitmap(bitmap);
            signatureImage.setVisibility(View.VISIBLE);
            // 设置标签以便我们以后可以找到这个文件路径
            signatureImage.setTag(filePath);

            // 隐藏签名面板
            if (signaturePanel != null) {
                signaturePanel.setVisibility(View.GONE);
            }

            // 隐藏按钮布局
            if (buttonLayout != null) {
                buttonLayout.setVisibility(View.GONE);
            }

            // 显示已保存标签
            if (savedSignatureLabel != null) {
                savedSignatureLabel.setVisibility(View.VISIBLE);
            }

            Log.d(TAG, "成功显示签名图片: " + fieldId);
        } else {
            Log.e(TAG, "无法加载或显示签名图片: " + (bitmap == null ? "bitmap为null" : "signatureImage为null"));
        }
    }

    /**
     * 根据字段ID和文件路径推断文件类型
     * @param fieldId 字段ID
     * @param filePath 文件路径
     * @param fieldConfig 字段配置，可以为null
     * @return 文件类型
     */
    public static String inferFileTypeFromFieldIdAndPath(String fieldId, String filePath, FormFieldConfig fieldConfig) {
        // 先根据字段配置确定类型
        if (fieldConfig != null) {
            String fieldType = fieldConfig.getFieldType();
            if (FormFieldConfig.TYPE_PHOTO.equals(fieldType)) {
                return "image";
            } else if (FormFieldConfig.TYPE_VIDEO.equals(fieldType)) {
                return "video";
            } else if (FormFieldConfig.TYPE_SIGNATURE.equals(fieldType)) {
                return "signature";
            } else if (FormFieldConfig.TYPE_FILE.equals(fieldType)) {
                return "file";
            }
        }
        
        // 根据字段ID推断类型
        String fieldIdLower = fieldId.toLowerCase();
        if (fieldIdLower.contains("photo") || fieldIdLower.contains("image") || 
            fieldIdLower.contains("pic") || fieldIdLower.endsWith("_photos")) {
            return "image";
        } else if (fieldIdLower.contains("video")) {
            return "video";
        } else if (fieldIdLower.contains("signature") || fieldIdLower.contains("sign")) {
            return "signature";
        }
        
        // 最后根据文件扩展名判断
        String filePathLower = filePath.toLowerCase();
        if (filePathLower.endsWith(".jpg") || filePathLower.endsWith(".jpeg") || 
            filePathLower.endsWith(".png") || filePathLower.endsWith(".gif")) {
            return "image";
        } else if (filePathLower.endsWith(".mp4") || filePathLower.endsWith(".mov") || 
                 filePathLower.endsWith(".avi") || filePathLower.endsWith(".3gp")) {
            return "video";
        } else if (filePathLower.contains("signature")) {
            return "signature";
        }
        
        // 默认类型
        return "file";
    }
    
    /**
     * 根据字段ID和文件路径推断文件类型
     * @param fieldId 字段ID
     * @param filePath 文件路径
     * @return 文件类型
     */
    public static String inferFileTypeFromFieldIdAndPath(String fieldId, String filePath) {
        return inferFileTypeFromFieldIdAndPath(fieldId, filePath, null);
    }
} 
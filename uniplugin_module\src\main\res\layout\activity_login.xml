<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="io.dcloud.uniplugin.LoginActivity">

    <!-- 背景图片 -->
    <ImageView
        android:id="@+id/imageViewBackground"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/login_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Logo区域 -->
    <LinearLayout
        android:id="@+id/logoContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:layout_marginTop="100dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 应用标题 -->
        <TextView
            android:id="@+id/textViewAppTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:textColor="#1E88E5"
            android:textSize="32sp"
            android:textStyle="bold"
            android:elevation="4dp"
            android:shadowColor="#40000000"
            android:shadowDx="2"
            android:shadowDy="2"
            android:shadowRadius="4" />

    </LinearLayout>

    <!-- 登录表单区域 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/loginFormCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="30dp"
        android:layout_marginTop="80dp"
        android:layout_marginEnd="30dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/logoContent">

        <LinearLayout
            android:id="@+id/loginFormContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- 用户名输入框 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/input_background"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="15dp"
                android:paddingEnd="15dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_myplaces"
                    android:contentDescription="@string/username"
                    app:tint="#757575" />

                <EditText
                    android:id="@+id/editTextUsername"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="10dp"
                    android:background="@null"
                    android:hint="请输入账号"
                    android:inputType="text"
                    android:textColor="#333333"
                    android:textColorHint="#999999"
                    android:textSize="16sp" />
            </LinearLayout>

            <!-- 密码输入框 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/input_background"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="15dp"
                android:paddingEnd="15dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_lock_lock"
                    android:contentDescription="@string/password"
                    app:tint="#757575" />

                <EditText
                    android:id="@+id/editTextPassword"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="10dp"
                    android:background="@null"
                    android:hint="请输入密码"
                    android:inputType="textPassword"
                    android:textColor="#333333"
                    android:textColorHint="#999999"
                    android:textSize="16sp" />
            </LinearLayout>

            <!-- 记住密码选项 -->
            <CheckBox
                android:id="@+id/checkBoxRememberPassword"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="记住密码"
                android:textColor="#666666"
                android:textSize="14sp"
                android:checked="true" />

            <!-- 登录按钮 -->
            <Button
                android:id="@+id/buttonLogin"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="30dp"
                android:background="@drawable/button_blue_background"
                android:text="登录"
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:elevation="2dp" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- 版本信息 -->
    <TextView
        android:id="@+id/textViewVersion"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:text="当前版本：1.0.0"
        android:textColor="#757575"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 
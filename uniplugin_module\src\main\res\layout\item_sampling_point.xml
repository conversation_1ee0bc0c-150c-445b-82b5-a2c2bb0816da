<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/textViewYdbh"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:text="样点编号"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/textViewZt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:background="@drawable/bg_status_pending"
                android:paddingStart="8dp"
                android:paddingTop="4dp"
                android:paddingEnd="8dp"
                android:paddingBottom="4dp"
                android:text="状态"
                android:textColor="#FFFFFF"
                android:textSize="12sp" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:background="#E0E0E0" />

        <TextView
            android:id="@+id/textViewLocation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="经纬度信息"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/textViewZldwmc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="管理单位"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/textViewDcrInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="调查人"
            android:textSize="14sp" />
            
        <TextView
            android:id="@+id/textViewSurveyTime"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="调查时间"
            android:textSize="14sp"
            android:visibility="gone" />
            
        <TextView
            android:id="@+id/textViewMediaInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="媒体信息"
            android:textSize="14sp"
            android:textColor="#3F51B5"
            android:visibility="gone" />
            
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp"
            android:gravity="end">
            
            <Button
                android:id="@+id/btnShowInMap"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="地图查看"
                android:textSize="12sp"
                android:backgroundTint="#3F51B5"
                android:textColor="#FFFFFF"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:drawableStart="@android:drawable/ic_dialog_map"
                android:drawablePadding="4dp"
                android:layout_marginEnd="8dp" />
                
            <Button
                android:id="@+id/btnPrintLabel"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="采土袋管理"
                android:textSize="12sp"
                android:backgroundTint="#FF9800"
                android:textColor="#FFFFFF"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:drawableStart="@android:drawable/ic_menu_save"
                android:drawablePadding="4dp" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView> 
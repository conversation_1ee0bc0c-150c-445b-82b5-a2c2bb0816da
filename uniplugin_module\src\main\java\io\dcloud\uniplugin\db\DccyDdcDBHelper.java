package io.dcloud.uniplugin.db;

import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.os.Environment;
import android.util.Log;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.dcloud.uniplugin.model.DccyDdcVO;

/**
 * 数据库助手类，用于处理待调查点(Ddc)数据的存储和检索
 */
public class DccyDdcDBHelper extends SQLiteOpenHelper {
    private static final String TAG = "DccyDdcDBHelper";
    
    // 使用DatabaseConstants中定义的常量
    private static final int DATABASE_VERSION = DatabaseConstants.DATABASE_VERSION;
    private static final String DATABASE_NAME = DatabaseConstants.DATABASE_NAME;
    
    // 使用DatabaseConstants中定义的表名
    private static final String TABLE_DDC_POINTS = DatabaseConstants.TABLE_DDC_POINTS;
    
    // 使用DatabaseConstants中定义的列名
    private static final String COLUMN_ID = DatabaseConstants.COLUMN_DDC_ID;
    private static final String COLUMN_PJDY_ID = DatabaseConstants.COLUMN_DDC_PJDY_ID;
    private static final String COLUMN_PJDY_BSM = DatabaseConstants.COLUMN_DDC_PJDY_BSM;
    private static final String COLUMN_DCDW_ID = DatabaseConstants.COLUMN_DDC_DCDW_ID;
    private static final String COLUMN_DCDW_NAME = DatabaseConstants.COLUMN_DDC_DCDW_NAME;
    private static final String COLUMN_DCR_ID = DatabaseConstants.COLUMN_DDC_DCR_ID;
    private static final String COLUMN_DCR_NAME = DatabaseConstants.COLUMN_DDC_DCR_NAME;
    private static final String COLUMN_XFR_ID = DatabaseConstants.COLUMN_DDC_XFR_ID;
    private static final String COLUMN_XFR = DatabaseConstants.COLUMN_DDC_XFR;
    private static final String COLUMN_DC_TIME = DatabaseConstants.COLUMN_DDC_DC_TIME;
    private static final String COLUMN_XFSJ = DatabaseConstants.COLUMN_DDC_XFSJ;
    private static final String COLUMN_ZT = DatabaseConstants.COLUMN_DDC_ZT;
    private static final String COLUMN_CHR_ID = DatabaseConstants.COLUMN_DDC_CHR_ID;
    private static final String COLUMN_CHR = DatabaseConstants.COLUMN_DDC_CHR;
    private static final String COLUMN_CHSJ = DatabaseConstants.COLUMN_DDC_CHSJ;
    private static final String COLUMN_DWJD = DatabaseConstants.COLUMN_DDC_DWJD;
    private static final String COLUMN_DWWD = DatabaseConstants.COLUMN_DDC_DWWD;
    public static final String COLUMN_HAS_LOCAL_DATA = DatabaseConstants.COLUMN_HAS_LOCAL_DATA;
    private static final String COLUMN_USER_ID = DatabaseConstants.COLUMN_USER_ID;

    // 使用DatabaseConstants中定义的创建表SQL语句
    private static final String SQL_CREATE_TABLE = DatabaseConstants.CREATE_TABLE_DDC_POINTS;
    
    private static DccyDdcDBHelper instance;
    private final String mDatabasePath;
    
    /**
     * 获取单例
     */
    public static synchronized DccyDdcDBHelper getInstance(Context context) {
        if (instance == null) {
            instance = new DccyDdcDBHelper(context.getApplicationContext());
        }
        return instance;
    }
    
    private DccyDdcDBHelper(Context context) {
        // 使用相同的外部存储路径创建数据库
        super(context, getExternalDatabasePath(context), null, DATABASE_VERSION);
        this.mDatabasePath = getExternalDatabasePath(context);
        
        // 确保数据库目录存在
        File dbDir = new File(mDatabasePath).getParentFile();
        if (dbDir != null && !dbDir.exists()) {
            dbDir.mkdirs();
        }
    }
    
    /**
     * 获取外部存储中数据库的完整路径
     */
    private static String getExternalDatabasePath(Context context) {
        // 获取外部存储中的BCGDGISData/BCGDSqliteData目录
        File externalDir = new File(Environment.getExternalStorageDirectory(), "BCGDGISData/BCGDSqliteData");
        if (!externalDir.exists()) {
            boolean success = externalDir.mkdirs();
            if (!success) {
                Log.e(TAG, "创建外部存储数据库目录失败，将使用内部存储");
                return DATABASE_NAME; // 如果创建失败，使用默认路径
            }
        }
        
        return new File(externalDir, DATABASE_NAME).getAbsolutePath();
    }

    
    @Override
    public void onCreate(SQLiteDatabase db) {
        db.execSQL(SQL_CREATE_TABLE);
        
        // 创建索引以提高查询性能
        db.execSQL("CREATE INDEX IF NOT EXISTS idx_ddc_pjdy_bsm ON " + TABLE_DDC_POINTS + "(" + COLUMN_PJDY_BSM + ")");
        db.execSQL("CREATE INDEX IF NOT EXISTS idx_ddc_dcr_id ON " + TABLE_DDC_POINTS + "(" + COLUMN_DCR_ID + ")");
        db.execSQL("CREATE INDEX IF NOT EXISTS idx_ddc_zt ON " + TABLE_DDC_POINTS + "(" + COLUMN_ZT + ")");
        db.execSQL("CREATE INDEX IF NOT EXISTS idx_ddc_user_id ON " + TABLE_DDC_POINTS + "(" + COLUMN_USER_ID + ")");
        
        Log.d(TAG, "数据库表创建成功: " + TABLE_DDC_POINTS);
    }
    
    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.w(TAG, "Upgrading database from version " + oldVersion + " to " + newVersion);
        if (oldVersion < 5) {
             try {
                 Log.i(TAG, "Adding column " + COLUMN_USER_ID + " to table " + TABLE_DDC_POINTS);
                 db.execSQL("ALTER TABLE " + TABLE_DDC_POINTS + " ADD COLUMN " + COLUMN_USER_ID + " INTEGER;");
                 Log.i(TAG, "Successfully added column " + COLUMN_USER_ID);
                 
                 db.execSQL("CREATE INDEX IF NOT EXISTS idx_ddc_user_id ON " + TABLE_DDC_POINTS + "(" + COLUMN_USER_ID + ")");
                 Log.i(TAG, "Successfully created index for " + COLUMN_USER_ID);
             } catch (Exception e) {
                 Log.e(TAG, "Error adding column " + COLUMN_USER_ID + " to " + TABLE_DDC_POINTS + ". Recreating table.", e);
                 db.execSQL("DROP TABLE IF EXISTS " + TABLE_DDC_POINTS);
                 onCreate(db);
             }
        }
    }
    
    /**
     * 获取指定用户的所有待调查点列表数据
     * @param userId 要查询的用户ID。如果为 null，则返回所有用户的数据（可能不推荐）
     */
    public List<DccyDdcVO> getAllDcrList(Long userId) {
        List<DccyDdcVO> dcrList = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();
        Cursor cursor = null;
        String selection = null;
        String[] selectionArgs = null;

        if (userId != null) {
            selection = COLUMN_USER_ID + " = ?";
            selectionArgs = new String[]{String.valueOf(userId)};
            Log.d(TAG, "Querying DDC points for user ID: " + userId);
        } else {
             Log.w(TAG, "Querying DDC points for all users (userId is null).");
        }
        
        try {
            cursor = db.query(TABLE_DDC_POINTS, null, selection, selectionArgs, null, null, COLUMN_ID + " ASC");
            
            if (cursor != null && cursor.moveToFirst()) {
                do {
                    DccyDdcVO item = cursorToDccyDdcVO(cursor);
                    if (item != null) {
                         dcrList.add(item);
                    }
                } while (cursor.moveToNext());
            }
        } catch (Exception e) {
            Log.e(TAG, "读取数据失败: " + e.getMessage(), e);
        } finally {
            if (cursor != null && !cursor.isClosed()) {
                cursor.close();
            }
        }
        
        Log.d(TAG, "Found " + dcrList.size() + " DDC points for user ID: " + userId);
        return dcrList;
    }
    
    /**
     * 获取指定用户的单个待调查点信息
     * @param id 待调查点的主键ID
     * @param userId 要查询的用户ID
     */
    public DccyDdcVO getDcrById(Long id, Long userId) {
        if (id == null) {
            Log.w(TAG, "DDC point ID为空");
            return null;
        }
         if (userId == null) {
            Log.w(TAG, "User ID为空");
            return null;
        }
        
        SQLiteDatabase db = this.getReadableDatabase();
        Cursor cursor = null;
        DccyDdcVO item = null;
        String selection = COLUMN_ID + " = ? AND " + COLUMN_USER_ID + " = ?";
        String[] selectionArgs = new String[]{String.valueOf(id), String.valueOf(userId)};
        
        try {
             cursor = db.query(TABLE_DDC_POINTS, null, selection, selectionArgs, null, null, null);
            if (cursor != null && cursor.moveToFirst()) {
                item = cursorToDccyDdcVO(cursor);
            }
        } catch (Exception e) {
             Log.e(TAG, "读取数据失败 by ID: " + e.getMessage(), e);
        } finally {
            if (cursor != null && !cursor.isClosed()) {
                cursor.close();
            }
        }
        return item;
    }

    /**
     * 将 Cursor 当前行的数据转换为 DccyDdcVO 对象
     * @param cursor 数据库游标
     * @return DccyDdcVO 对象，如果转换失败则返回 null
     */
     private DccyDdcVO cursorToDccyDdcVO(Cursor cursor) {
         if (cursor == null) return null;
         try {
             DccyDdcVO item = new DccyDdcVO();
             int idIndex = cursor.getColumnIndex(COLUMN_ID);
             if (idIndex != -1) item.setId(cursor.getLong(idIndex));
             int pjdyIdIndex = cursor.getColumnIndex(COLUMN_PJDY_ID);
             if (pjdyIdIndex != -1) item.setPjdyId(cursor.getLong(pjdyIdIndex));
             int pjdybhIndex = cursor.getColumnIndex(COLUMN_PJDY_BSM);
             if (pjdybhIndex != -1) item.setpjdybh(cursor.getString(pjdybhIndex));
             int dcdwIdIndex = cursor.getColumnIndex(COLUMN_DCDW_ID);
             if (dcdwIdIndex != -1) item.setDcdwId(cursor.getLong(dcdwIdIndex));
             int dcdwNameIndex = cursor.getColumnIndex(COLUMN_DCDW_NAME);
             if (dcdwNameIndex != -1) item.setDcdw(cursor.getString(dcdwNameIndex));
             int dcrIdIndex = cursor.getColumnIndex(COLUMN_DCR_ID);
             if (dcrIdIndex != -1) item.setDcrId(cursor.getLong(dcrIdIndex));
             int dcrNameIndex = cursor.getColumnIndex(COLUMN_DCR_NAME);
             if (dcrNameIndex != -1) item.setDcr(cursor.getString(dcrNameIndex));
             int xfrIdIndex = cursor.getColumnIndex(COLUMN_XFR_ID);
             if (xfrIdIndex != -1) item.setXfrId(cursor.getLong(xfrIdIndex));
             int xfrIndex = cursor.getColumnIndex(COLUMN_XFR);
             if (xfrIndex != -1) item.setXfr(cursor.getString(xfrIndex));
             int dcTimeIndex = cursor.getColumnIndex(COLUMN_DC_TIME);
             if (dcTimeIndex != -1) item.setDcTime(cursor.getLong(dcTimeIndex));
             int xfsjIndex = cursor.getColumnIndex(COLUMN_XFSJ);
             if (xfsjIndex != -1) item.setXfsj(cursor.getLong(xfsjIndex));
             int ztIndex = cursor.getColumnIndex(COLUMN_ZT);
             if (ztIndex != -1) item.setZt(cursor.getInt(ztIndex));
             int chrIdIndex = cursor.getColumnIndex(COLUMN_CHR_ID);
             if (chrIdIndex != -1) item.setChrId(cursor.getLong(chrIdIndex));
             int chrIndex = cursor.getColumnIndex(COLUMN_CHR);
             if (chrIndex != -1) item.setChr(cursor.getString(chrIndex));
             int chsjIndex = cursor.getColumnIndex(COLUMN_CHSJ);
             if (chsjIndex != -1) item.setChsj(cursor.getLong(chsjIndex));
             int dwjdIndex = cursor.getColumnIndex(COLUMN_DWJD);
             if (dwjdIndex != -1) item.setDwjd(cursor.getDouble(dwjdIndex));
             int dwwdIndex = cursor.getColumnIndex(COLUMN_DWWD);
             if (dwwdIndex != -1) item.setDwwd(cursor.getDouble(dwwdIndex));
             int localDataIndex = cursor.getColumnIndex(COLUMN_HAS_LOCAL_DATA);
             if (localDataIndex != -1) item.setHasLocalData(cursor.getInt(localDataIndex));
             int userIdIndex = cursor.getColumnIndex(COLUMN_USER_ID);
             if (userIdIndex != -1) item.setUserId(cursor.getLong(userIdIndex));
             return item;
         } catch (Exception e) {
             Log.e(TAG, "Error converting cursor to DccyDdcVO: " + e.getMessage());
             return null;
         }
     }
    
    /**
     * 清空表数据
     */
    public void clearTable() {
        SQLiteDatabase db = this.getWritableDatabase();
        try {
            int deletedRows = db.delete(TABLE_DDC_POINTS, null, null);
            Log.d(TAG, "Cleared table " + TABLE_DDC_POINTS + ", deleted " + deletedRows + " rows.");
        } catch (Exception e) {
             Log.e(TAG, "Error clearing table " + TABLE_DDC_POINTS + ": " + e.getMessage());
        }
    }
    
    /**
     * 获取所有记录
     *
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getList() {
        return getList(null, null, null, null, null);
    }

    /**
     * 根据条件获取记录列表，支持分页和条件查询
     *
     * @param whereClause WHERE子句，不包含"WHERE"关键字，例如："zt = ?"
     * @param whereArgs WHERE子句中占位符的参数值
     * @param orderBy 排序方式，例如："update_time DESC"
     * @param limit 限制返回的行数，例如："10"
     * @param offset 起始行的偏移量，例如："0"
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> getList(String whereClause, String[] whereArgs, String orderBy, String limit, String offset) {
        Log.d(TAG, "getList");
        List<Map<String, Object>> list = new ArrayList<>();
        SQLiteDatabase db = getReadableDatabase();
        Cursor cursor = null;
        try {
            String limitStr = null;
            if (limit != null) {
                limitStr = limit;
                if (offset != null) {
                    limitStr += " OFFSET " + offset;
                }
            }
            cursor = db.query(TABLE_DDC_POINTS, null, whereClause, whereArgs, null, null, orderBy, limitStr);
            while (cursor.moveToNext()) {
                Map<String, Object> map = new HashMap<>();
                for (int i = 0; i < cursor.getColumnCount(); i++) {
                    String columnName = cursor.getColumnName(i);
                    int type = cursor.getType(i);
                    if (type == Cursor.FIELD_TYPE_STRING) {
                        map.put(columnName, cursor.getString(i));
                    } else if (type == Cursor.FIELD_TYPE_INTEGER) {
                        map.put(columnName, cursor.getLong(i));
                    } else if (type == Cursor.FIELD_TYPE_FLOAT) {
                        map.put(columnName, cursor.getDouble(i));
                    } else if (type == Cursor.FIELD_TYPE_BLOB) {
                        map.put(columnName, cursor.getBlob(i));
                    } else if (type == Cursor.FIELD_TYPE_NULL) {
                        map.put(columnName, null);
                    }
                }
                list.add(map);
            }
        } catch (Exception e) {
            Log.e(TAG, "getList error: " + e.getMessage());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            db.close();
        }
        return list;
    }
} 
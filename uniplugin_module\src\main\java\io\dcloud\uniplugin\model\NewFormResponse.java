package io.dcloud.uniplugin.model;

import java.util.List;

/**
 * 新的表单数据响应结构
 */
public class NewFormResponse {
    private FormData data;

    public FormData getData() {
        return data;
    }

    public void setData(FormData data) {
        this.data = data;
    }

    public static class FormData {
        private List<FieldGroup> fields;

        public List<FieldGroup> getFields() {
            return fields;
        }

        public void setFields(List<FieldGroup> fields) {
            this.fields = fields;
        }
    }

    public static class FieldGroup {
        private String name;
        private String type;
        private List<FormField> fields;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public List<FormField> getFields() {
            return fields;
        }

        public void setFields(List<FormField> fields) {
            this.fields = fields;
        }
    }

    public static class FormField {
        private String fieldName;
        private String fieldRemark;
        private String fieldType;
        private String fieldGroup;
        private Integer fieldLength;
        private Integer fieldRequired;
        private String fieldElement;
        private String fieldOptions;
        private Integer fieldMin;
        private Integer fieldMax;
        private Object value; // 可以是不同类型的值

        public String getFieldName() {
            return fieldName;
        }

        public void setFieldName(String fieldName) {
            this.fieldName = fieldName;
        }

        public String getFieldRemark() {
            return fieldRemark;
        }

        public void setFieldRemark(String fieldRemark) {
            this.fieldRemark = fieldRemark;
        }

        public String getFieldType() {
            return fieldType;
        }

        public void setFieldType(String fieldType) {
            this.fieldType = fieldType;
        }

        public String getFieldGroup() {
            return fieldGroup;
        }

        public void setFieldGroup(String fieldGroup) {
            this.fieldGroup = fieldGroup;
        }

        public Integer getFieldLength() {
            return fieldLength;
        }

        public void setFieldLength(Integer fieldLength) {
            this.fieldLength = fieldLength;
        }

        public Integer getFieldRequired() {
            return fieldRequired;
        }

        public void setFieldRequired(Integer fieldRequired) {
            this.fieldRequired = fieldRequired;
        }

        public String getFieldElement() {
            return fieldElement;
        }

        public void setFieldElement(String fieldElement) {
            this.fieldElement = fieldElement;
        }

        public String getFieldOptions() {
            return fieldOptions;
        }

        public void setFieldOptions(String fieldOptions) {
            this.fieldOptions = fieldOptions;
        }

        public Integer getFieldMin() {
            return fieldMin;
        }

        public void setFieldMin(Integer fieldMin) {
            this.fieldMin = fieldMin;
        }

        public Integer getFieldMax() {
            return fieldMax;
        }

        public void setFieldMax(Integer fieldMax) {
            this.fieldMax = fieldMax;
        }

        public Object getValue() {
            return value;
        }

        public void setValue(Object value) {
            this.value = value;
        }
    }

    public static class MediaValue {
        private long id;
        private long dccyId;
        private String pjdybh;
        private String fileType;
        private long fileId;
        private String fileName;
        private String path;
        private double jd;
        private double wd;
        private double fwj;
        private long fileTime;

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public long getDccyId() {
            return dccyId;
        }

        public void setDccyId(long dccyId) {
            this.dccyId = dccyId;
        }

        public String getpjdybh() {
            return pjdybh;
        }

        public void setpjdybh(String pjdybh) {
            this.pjdybh = pjdybh;
        }

        public String getFileType() {
            return fileType;
        }

        public void setFileType(String fileType) {
            this.fileType = fileType;
        }

        public long getFileId() {
            return fileId;
        }

        public void setFileId(long fileId) {
            this.fileId = fileId;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public double getJd() {
            return jd;
        }

        public void setJd(double jd) {
            this.jd = jd;
        }

        public double getWd() {
            return wd;
        }

        public void setWd(double wd) {
            this.wd = wd;
        }

        public double getFwj() {
            return fwj;
        }

        public void setFwj(double fwj) {
            this.fwj = fwj;
        }

        public long getFileTime() {
            return fileTime;
        }

        public void setFileTime(long fileTime) {
            this.fileTime = fileTime;
        }
    }
} 
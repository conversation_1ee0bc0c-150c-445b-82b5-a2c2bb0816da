package io.dcloud.uniplugin;

import android.app.Activity;
import android.content.Intent;
import android.util.Log;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import io.dcloud.feature.uniapp.annotation.UniJSMethod;
import io.dcloud.feature.uniapp.bridge.UniJSCallback;
import io.dcloud.feature.uniapp.common.UniModule;


public class TestModule extends UniModule{

    public MainActivity activity2;
    public String TAG = "TestModule";
    public static int REQUEST_CODE_LON_ZERO = 0;
    public static int REQUEST_CODE_LON_ZERO_OFFLINE = 10;
    public static int REQUEST_CODE_LON_ONE = 1;
    public static int REQUEST_CODE_LON_ELSE = 1000;
    public static int REQUEST_BACK_PAGE = 99999;
    public static int REQUEST_CODE_FILE_UPLOAD = 2000;
    public int REQUEST_CODE;
    public MainActivity mainActivity;
    public JSONObject options1;
    public String lon;
    public String lat;
    public String code;
    public String location;
    public String sampleType;
    public String samplingType;
    public String offModel;

    private int functionCallCount = 0;

    private static OnResultCallback resultCallback;


    //pointList是一个JSONArray
    public JSONArray pointList;

    //currentpoint是一个对象
    public String current_point;

    public static boolean hasResult = false;

    public TestModule(){}
    public TestModule(MainActivity mainActivity)
    {
        this.setActivity(mainActivity);
    }

    public void setActivity(Activity activity) {
        this.activity2 = (MainActivity) activity;
    }

    //run ui thread
    @UniJSMethod(uiThread = true)
    public void testAsyncFunc(JSONObject options, UniJSCallback callback) {
        System.out.println(options);

        options1 = options;
        lon = options1.getString("lon");
        offModel=options1.getString("offModel");

        System.out.println(lon);
        if(Objects.equals(lon,"0")){
            //这里是绝对正确的，就是从jsonobject中获取jsonarray
            System.out.println(2);
            pointList = options1.getJSONArray("pointList");
        }
        else if(Objects.equals(lon,"1")){
            //这里是绝对正确的，就是从jsonobject中获取jsonarray
            System.out.println(3);
            pointList = options1.getJSONArray("pointList");
            current_point = options1.getString("cuttentPoint");
            //System.out.println(pointList);
        }
        else {
            lat = options1.getString("lat");
            code = options1.getString("code");
            location = options1.getString("location");
            sampleType = options1.getString("sampleType");
            samplingType = options1.getString("samplingType");
            System.out.println(1);
        }
        //获取事实样点

//        //这个options就是传递过来的数据
//        if(callback != null) {
//            JSONObject data = new JSONObject();
//            data.put("code", "success");
//            callback.invoke(data);
//            //callback.invokeAndKeepAlive(data);
//        }
    }


    //run JS thread
    @UniJSMethod (uiThread = false)
    public JSONObject testSyncFunc(){
        JSONObject data = new JSONObject();
        data.put("code", "success");
        return data;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if(resultCode==-1){
            REQUEST_CODE=0;
        }else {
            REQUEST_CODE = resultCode;
        }
        Log.e("REQUEST_CODE",requestCode+"");
        Log.e("RESULT_CODE",resultCode+"");
        if(resultCode == REQUEST_CODE_LON_ZERO && data.hasExtra("code")) {
            options1.clear();
            lon = "0";
            Map<String,Object> params=new HashMap<>();
//            params.put("code",code);
            //通过Intene传值判断是点击了哪个按钮，同时将值传给uniapp控制跳转，规定预填报为0，上传图片为1
            params.put("code",data.getStringExtra("code"));
            mWXSDKInstance.fireGlobalEventCallback("LON_ZERO", params);
            //Toast.makeText(activity2, hasResult+"", Toast.LENGTH_SHORT).show();
            Log.e("TestModule", "原生页面返回----"+data.getStringExtra("code"));
            Log.e("TestModule", "LON_ZERO");
        } else if(resultCode == REQUEST_CODE_LON_ZERO_OFFLINE && data.hasExtra("code")) {
            options1.clear();
            lon = "0";
            Map<String,Object> params=new HashMap<>();
//            params.put("code",code);
            //通过Intene传值判断是点击了哪个按钮，同时将值传给uniapp控制跳转，规定预填报为0，上传图片为1
            params.put("code",data.getStringExtra("code"));
            mWXSDKInstance.fireGlobalEventCallback("LON_ZERO_OFFLINE", params);
            //Toast.makeText(activity2, hasResult+"", Toast.LENGTH_SHORT).show();
            Log.e("TestModule", "原生页面返回----"+data.getStringExtra("code"));
            Log.e("TestModule", "LON_ZERO_OFFLINE");

        } else if(resultCode == REQUEST_CODE_LON_ONE && data.hasExtra("code")) {
            options1.clear();
            lon = "1";
            Map<String,Object> params=new HashMap<>();
//            params.put("code",code);
            //通过Intene传值判断是点击了哪个按钮，同时将值传给uniapp控制跳转，规定预填报为0，上传图片为1
            params.put("code",data.getStringExtra("code"));
            mWXSDKInstance.fireGlobalEventCallback("LON_ONE", params);
            //Toast.makeText(activity2, hasResult+"", Toast.LENGTH_SHORT).show();
            Log.e("TestModule", "原生页面返回----"+data.getStringExtra("code"));
            Log.e("TestModule", "LON_ONE");
        } else  if(resultCode == REQUEST_CODE_LON_ELSE && data.hasExtra("code")) {
            options1.clear();
            lon = "2";
            Map<String,Object> params=new HashMap<>();
//            params.put("code",code);
            //通过Intene传值判断是点击了哪个按钮，同时将值传给uniapp控制跳转，规定预填报为0，上传图片为1
            params.put("code",data.getStringExtra("code"));
            mWXSDKInstance.fireGlobalEventCallback("LON_ELSE", params);
            //Toast.makeText(activity2, hasResult+"", Toast.LENGTH_SHORT).show();
            Log.e("TestModule", "原生页面返回----"+data.getStringExtra("code"));
            Log.e("TestModule", "LON_ELSE");
        } else  if(resultCode == REQUEST_BACK_PAGE) {
            Log.e("TestModule", "进来了");
            Map<String,Object> params=new HashMap<>();
            params.put("backUrl",data.getStringExtra("backUrl"));
            mWXSDKInstance.fireGlobalEventCallback("backToPage", params);
            Log.e("TestModule", "原生页面返回----"+data.getStringExtra("backUrl"));
            Log.e("TestModule", "backToPage");
        }
        
        // 处理文件上传页面返回的结果
        if (requestCode == REQUEST_CODE_FILE_UPLOAD && resultCode == Activity.RESULT_OK && data != null) {
            Map<String, Object> params = new HashMap<>();
            params.put("description", data.getStringExtra("description"));
            
            // 处理多个文件路径
            String filePathsJson = data.getStringExtra("filePaths");
            if (filePathsJson != null) {
                try {
                    JSONArray filePathsArray = JSONArray.parseArray(filePathsJson);
                    params.put("filePaths", filePathsArray);
                } catch (Exception e) {
                    e.printStackTrace();
                    params.put("filePaths", new JSONArray());
                }
            } else {
                params.put("filePaths", new JSONArray());
            }
            
            mUniSDKInstance.fireGlobalEventCallback("FILE_UPLOAD_RESULT", params);
            Log.e(TAG, "文件上传页面返回----" + filePathsJson);
        }
    }

}

package io.dcloud.uniplugin;

import static android.content.Context.MODE_PRIVATE;
import static io.src.dcloud.adapter.DCloudAdapterUtil.getSharedPreferences;

import android.Manifest;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.preference.PreferenceManager;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.arch.core.util.Function;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.SavedStateViewModelFactory;
import androidx.lifecycle.Transformations;
import androidx.lifecycle.ViewModelProvider;
import androidx.savedstate.SavedStateRegistryController;
import androidx.savedstate.SavedStateRegistryOwner;






import com.esri.arcgisruntime.geometry.Envelope;
import com.esri.arcgisruntime.geometry.Geometry;
import com.esri.arcgisruntime.geometry.Point;
import com.esri.arcgisruntime.geometry.SpatialReference;
import com.esri.arcgisruntime.geometry.SpatialReferences;
import com.esri.arcgisruntime.loadable.LoadStatus;
import com.esri.arcgisruntime.loadable.LoadStatusChangedEvent;
import com.esri.arcgisruntime.loadable.LoadStatusChangedListener;
import com.esri.arcgisruntime.mapping.ArcGISMap;
import com.esri.arcgisruntime.mapping.Viewpoint;
import com.esri.arcgisruntime.mapping.view.MapView;


import java.io.File;

import uni.dcloud.io.uniplugin_module.R;

public class MyFragment extends Fragment {
    private MapViewState mMapViewState;
    private View mView;
    private MapView mMapView;
    ArcGISMap mainArcGISMap;
    private MyViewModel mViewModel;
    private SavedStateHandle savedStateHandle;
    private String mMyString;
    private Context context;

    public MapView getmMapView() {
        return mMapView;
    }



    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //mViewModel = new ViewModelProvider(this).get(MyViewModel.class);
        context = getContext();


    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        mView = inflater.inflate(R.layout.fragment_blank, container, false);
        mMapView = mView.findViewById(R.id.mapView);
        mViewModel = new ViewModelProvider(this).get(MyViewModel.class);

        return mView;
    }
    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
//        mMapViewState = new MapViewState(mMapView.getCurrentViewpoint(Viewpoint.Type.BOUNDING_GEOMETRY), mMapView.getMap().getOperationalLayers());
//        outState.putSerializable("MapViewState",mMapViewState);
    }

    @Override
    public void onViewStateRestored(Bundle savedInstanceState) {
        super.onViewStateRestored(savedInstanceState);
        Log.d("状态恢复","恢复执行！");
        //mViewModel = new ViewModelProvider(this).get(MyViewModel.class);
//        if(mViewModel.getMapScale().getValue()!=null) {
//            Log.d("比例尺",mViewModel.getMapScale().getValue()+"");
//            mMapView.setViewpointScaleAsync(mViewModel.getMapScale().getValue());
//        }else {
//            Log.d("状态恢复","我是空空");
//        }
//        // 观察 LiveData 对象的变化
//        mViewModel.getMapScale().observe(getViewLifecycleOwner(), data -> {
//            Log.d("状态测试",data+"");
//
//            // 处理数据变化事件
//            mMapView.getMap().addLoadStatusChangedListener(event -> {
//                if (event.getNewLoadStatus() == LoadStatus.LOADED) {
//                    SharedPreferences prefs = context.getSharedPreferences("map_state", MODE_PRIVATE);
//                    String json = prefs.getString("geometry", "");
//                    Log.d("json是否为空",!json.isEmpty()+"");
//                    if (!json.isEmpty()) {
//                        Geometry geometry = Geometry.fromJson(json);
//                        Viewpoint viewpoint = new Viewpoint((Envelope) geometry);
//                        mMapView.setViewpoint(viewpoint);
//                    }
//                }
//            });

//        });
        Log.d("mViewModel.setData测试",mViewModel.getData()+"");
        Log.d("mViewModel.getModelData()测试",mViewModel.getModelData().getValue()+"");
        mViewModel.getModelData().observe(getViewLifecycleOwner(), s -> {
            Log.d("SCALE",s+"");
            mMapView.setViewpointScaleAsync(s);
        });
        LiveData<Double> transformedLiveData = Transformations.map(mViewModel.getModelData(), input -> input + 0.0001);
        transformedLiveData.observe(getViewLifecycleOwner(), s -> Log.d("TAG", "Changed2:" + s));
    }

    @Override
    public void onResume() {
        super.onResume();
        mMapView.resume();
//        Viewpoint viewpoint= mMapView.getCurrentViewpoint(Viewpoint.Type.CENTER_AND_SCALE);
//        if(viewpoint!=null) {
//            double centerY = viewpoint.getTargetGeometry().getExtent().getCenter().getY();
//            double centerX = viewpoint.getTargetGeometry().getExtent().getCenter().getX();
//
//            mViewModel.setMapX(Double.valueOf(centerX));
//
//            mViewModel.setMapY(Double.valueOf(centerY));
//            double scale = mMapView.getMapScale();
//            mViewModel.setMapScale(Double.valueOf(scale));
//        }
        //mViewModel.getModelData().setValue("我是ViewModel onResume中保存的数据：" + System.currentTimeMillis());
    }

    @Override
    public void onPause() {
        super.onPause();
        mMapView.pause();
        Viewpoint viewpoint= mMapView.getCurrentViewpoint(Viewpoint.Type.CENTER_AND_SCALE);
//        double centerY = viewpoint.getTargetGeometry().getExtent().getCenter().getY();
//        double centerX = viewpoint.getTargetGeometry().getExtent().getCenter().getX();
        double scale = mMapView.getMapScale();
//        mViewModel.setMapX(Double.valueOf(centerX));
//        mViewModel.setMapScale(Double.valueOf(scale));
//        mViewModel.setMapY(Double.valueOf(centerY));
        Log.d("Scale",scale+"");
        mViewModel.getModelData().setValue(Double.valueOf(scale));
        mViewModel.setData(Double.valueOf(scale));
    }
    @Override
    public void onStop() {
        super.onStop();
        mMapViewState = new MapViewState(mMapView.getCurrentViewpoint(Viewpoint.Type.CENTER_AND_SCALE));
//        Viewpoint viewpoint= mMapView.getCurrentViewpoint(Viewpoint.Type.CENTER_AND_SCALE);
//        double centerY = viewpoint.getTargetGeometry().getExtent().getCenter().getY();
//        double centerX = viewpoint.getTargetGeometry().getExtent().getCenter().getX();
//        double scale = mMapView.getMapScale();
//        mViewModel.setMapX(Double.valueOf(centerX));
//        mViewModel.setMapScale(Double.valueOf(scale));
//        mViewModel.setMapY(Double.valueOf(centerY));
        //mViewModel.getModelData().setValue("我是ViewModel onStop中保存的数据：" + System.currentTimeMillis());
    }
       @Override
    public void onDestroyView() {
        super.onDestroyView();
           //mViewModel.setMyString("Hello, ViewModel!");
//        Log.d("状态保存","保存执行！");
//        Bundle arg=new Bundle();
//        mMapViewState = new MapViewState(mMapView.getCurrentViewpoint(Viewpoint.Type.BOUNDING_GEOMETRY), mMapView.getMap().getOperationalLayers());
//        arg.putSerializable("MapViewState",mMapViewState);
//        this.setArguments(arg);
          // ((ViewGroup) mMapView.getParent()).removeView(mMapView);
           // 在 Fragment 销毁时将数据保存到 ViewModel 中
           //mViewModel.getData().removeObservers(getViewLifecycleOwner());
           //mViewModel.getModelData().setValue("我是ViewModel onDestroyView中保存的数据：" + System.currentTimeMillis());
           //SharedPreferences.Editor editor = context.getSharedPreferences("map_state", MODE_PRIVATE).edit();

           //editor.putString("geometry", geometry.toJson());
           //editor.apply();
    }
    @Override
    public void onDestroy() {
        //super.onDestroy();
        super.onDestroy();
        Log.d("fragment 销毁", "销毁执行！");

//        Bundle arg=new Bundle();
//        mMapViewState = new MapViewState(mMapView.getCurrentViewpoint(Viewpoint.Type.BOUNDING_GEOMETRY), mMapView.getMap().getOperationalLayers());
//        arg.putSerializable("MapViewState",mMapViewState);
//        this.setArguments(arg);
        mMapView.dispose();
    }
    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        Log.d("onAttach测试:", "onAttach() called");
    }
}

package io.dcloud.uniplugin.model;

import com.google.gson.annotations.SerializedName;

import java.util.Set;

/**
 * 登录用户的权限信息响应
 */
public class AuthPermissionInfoRespVO {

    @SerializedName("user")
    private UserVO user;

    @SerializedName("roles")
    private Set<String> roles;

    @SerializedName("permissions")
    private Set<String> permissions;


    @SerializedName("mapToken")
    private String mapToken;

    /**
     * 用户信息VO
     */
    public static class UserVO {

        @SerializedName("id")
        private Long id;

        @SerializedName("username")
        private String username;

        @SerializedName("nickname")
        private String nickname;

        @SerializedName("mobile")
        private String mobile;

        @SerializedName("avatar")
        private String avatar;

        @SerializedName("org")
        private OrgVO org;

        @SerializedName("gsddm")
        private String gsddm;

        @SerializedName("gsdmc")
        private String gsdmc;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getMobile() {
            return mobile;
        }

        public void setMobile(String mobile) {
            this.mobile = mobile;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public OrgVO getOrg() {
            return org;
        }

        public void setOrg(OrgVO org) {
            this.org = org;
        }

        public String getGsddm() {
            return gsddm;
        }

        public void setGsddm(String gsddm) {
            this.gsddm = gsddm;
        }

        public String getGsdmc() {
            return gsdmc;
        }

        public void setGsdmc(String gsdmc) {
            this.gsdmc = gsdmc;
        }
    }

    /**
     * 组织信息VO
     */
    public static class OrgVO {
        // 根据实际情况添加字段
        @SerializedName("id")
        private Long id;

        @SerializedName("name")
        private String name;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public UserVO getUser() {
        return user;
    }

    public void setUser(UserVO user) {
        this.user = user;
    }

    public Set<String> getRoles() {
        return roles;
    }

    public void setRoles(Set<String> roles) {
        this.roles = roles;
    }

    public Set<String> getPermissions() {
        return permissions;
    }

    public void setPermissions(Set<String> permissions) {
        this.permissions = permissions;
    }

    public String getMapToken() {
        return mapToken;
    }

    public void setMapToken(String mapToken) {
        this.mapToken = mapToken;
    }
} 
package io.dcloud.uniplugin.model;

public class AppVersionVO {
    private Long id;
    private String version;
    private String filename;
    private Long createTime;
    private Long fileid;
    private String appUrl;
    private boolean sfqzgx;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getFileid() {
        return fileid;
    }

    public void setFileid(Long fileid) {
        this.fileid = fileid;
    }

    public String getAppUrl() {
        return appUrl;
    }

    public void setAppUrl(String appUrl) {
        this.appUrl = appUrl;
    }

    public boolean isSfqzgx() {
        return sfqzgx;
    }

    public void setSfqzgx(boolean sfqzgx) {
        this.sfqzgx = sfqzgx;
    }
}
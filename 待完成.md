V/AudioManager: querySoundEffectsEnabled...
I/DecorView[]: pkgName:com.android.BcgdUniPlugin old windowMode:0 new windoMode:1, isFixedSize:false
D/MouseWheelSynthesizer: mMoveStepInDp: 64, mMoveStepInPixel: 192, mUpTimeDelayed: 100
D/ViewRootImpl: ViewRootImpl mIsInProductivePCDisplay: false
D/InputEventReceiver: dispatchInputInterval 1000000
D/HiTouch_PressGestureDetector: onAttached, package=com.android.BcgdUniPlugin, windowType=2, mIsHiTouchRestricted=false
D/mali_winsys: EGLint new_window_surface(egl_winsys_display *, void *, EGLSurface, EGLConfig, egl_winsys_surface **, EGLBoolean) returns 0x3000
I/HwViewRootImpl: removeInvalidNode all the node in jank list is out of time
D/OpenGLRenderer: disableOutlineDraw is true
I/DecorView[]: pkgName:com.android.BcgdUniPlugin old windowMode:0 new windoMode:1, isFixedSize:false
D/MouseWheelSynthesizer: mMoveStepInDp: 64, mMoveStepInPixel: 192, mUpTimeDelayed: 100
D/ViewRootImpl: ViewRootImpl mIsInProductivePCDisplay: false
D/InputEventReceiver: dispatchInputInterval 1000000
D/SamplingPointSyncManager: Current User ID: 19
I/okhttp.OkHttpClient: --> GET http://***************:48082/admin-api/pjdy/xfjl/list/dcr?dccyZt=0
I/okhttp.OkHttpClient: --> END GET
D/HiTouch_PressGestureDetector: onAttached, package=com.android.BcgdUniPlugin, windowType=2, mIsHiTouchRestricted=false
D/mali_winsys: EGLint new_window_surface(egl_winsys_display *, void *, EGLSurface, EGLConfig, egl_winsys_surface **, EGLBoolean) returns 0x3000
I/ViewRootImpl: dispatchDetachedFromWindow in doDie
W/libEGL: EGLNativeWindowType 0xb877d0c8 disconnect failed
D/SamplingPointsActivity: 待调查样点查询SQL: SELECT * FROM sampling_ddc_points WHERE has_local_data = ?, 参数: 0
D/SamplingPointsActivity: 待调查样点查询结果数量: 0
D/SamplingPointsActivity: sampling_ddc_points 表中的总记录数: 0
D/SamplingPointsActivity: 已提交样点查询SQL: SELECT * FROM sampling_points WHERE zt NOT IN (-1, 0)
D/SamplingPointsActivity: 待整改样点查询SQL: SELECT * FROM sampling_points WHERE zt = ?
D/SamplingPointsActivity: 加载到的数据: 待调查=0, 待提交=0, 已提交=0, 待整改=5
D/DecorView: showOrHideHighlightView: hasFocus=false; winMode=1; isMrgNull=true
D/OpenGLRenderer: disableOutlineDraw is true
D/MouseWheelSynthesizer: mMoveStepInDp: 64, mMoveStepInPixel: 192, mUpTimeDelayed: 100
D/ViewRootImpl: ViewRootImpl mIsInProductivePCDisplay: false
D/InputEventReceiver: dispatchInputInterval 1000000
D/mali_winsys: EGLint new_window_surface(egl_winsys_display *, void *, EGLSurface, EGLConfig, egl_winsys_surface **, EGLBoolean) returns 0x3000
D/SamplingPointsPagerAdapter: 已更新Fragment: position=0, tabPosition=1
D/SamplingPointsPagerAdapter: 已更新Fragment: position=1, tabPosition=2
D/SamplingPointsPagerAdapter: 已更新Fragment: position=2, tabPosition=3
D/SamplingPointsPagerAdapter: 已更新Fragment: position=3, tabPosition=4
D/SamplingPointsPagerAdapter: 已通知适配器数据更改
D/SamplingPointsActivity: 强制刷新Fragment: position=3
D/SamplingPointsActivity: 待调查样点数量: 0
D/SamplingPointsActivity: 待提交样点数量: 0
D/SamplingPointsActivity: sampling_ddc_points 表中的总记录数: 0
D/SamplingPointsActivity: 已提交样点数量: 0
D/SamplingPointsActivity: 待整改样点数量: 5
D/SamplingPointsActivity: 数据已加载，强制更新UI
D/SamplingPointAdapter: 创建适配器，标签页位置: 1
D/SamplingPointAdapter: 创建适配器，标签页位置: 2
D/SamplingPointAdapter: 创建适配器，标签页位置: 3
D/OpenGLRenderer: disableOutlineDraw is true
D/SamplingPointAdapter: 创建适配器，标签页位置: 4
D/SamplingPointAdapter: 渲染样点状态, tabPosition=4
D/SamplingPointAdapter: 渲染样点状态, tabPosition=4
D/SamplingPointsActivity: 待调查样点查询SQL: SELECT * FROM sampling_ddc_points WHERE has_local_data = ?, 参数: 0
D/SamplingPointsActivity: 待调查样点查询结果数量: 0
D/SamplingPointsActivity: sampling_ddc_points 表中的总记录数: 0
D/SamplingPointsActivity: 已提交样点查询SQL: SELECT * FROM sampling_points WHERE zt NOT IN (-1, 0)
D/SamplingPointsActivity: 待整改样点查询SQL: SELECT * FROM sampling_points WHERE zt = ?
D/SamplingPointsPagerAdapter: 已更新Fragment: position=0, tabPosition=1
D/SamplingPointsPagerAdapter: 已更新Fragment: position=1, tabPosition=2
D/SamplingPointsPagerAdapter: 已更新Fragment: position=2, tabPosition=3
D/SamplingPointsPagerAdapter: 已更新Fragment: position=3, tabPosition=4
D/SamplingPointsPagerAdapter: 已通知适配器数据更改
D/SamplingPointsPagerAdapter: 强制更新Fragment成功: position=3, 数据量=5
D/SamplingPointsActivity: 延迟刷新成功: position=3, 数据量=5
D/SamplingPointsActivity: 待调查样点数量: 0
D/SamplingPointsActivity: 待提交样点数量: 0
D/SamplingPointsActivity: sampling_ddc_points 表中的总记录数: 0
D/SamplingPointsActivity: 已提交样点数量: 0
D/SamplingPointsActivity: 待整改样点数量: 5
D/SamplingPointsActivity: 已更新Tab标签统计数字
D/SamplingPointAdapter: 渲染样点状态, tabPosition=4
D/SamplingPointAdapter: 渲染样点状态, tabPosition=4
I/ViewRootImpl: dispatchDetachedFromWindow in doDie
W/libEGL: EGLNativeWindowType 0xb877d0c8 disconnect failed
I/okhttp.OkHttpClient: <-- 200 http://***************:48082/admin-api/pjdy/xfjl/list/dcr?dccyZt=0 (2535ms)
I/okhttp.OkHttpClient: Vary: Origin
I/okhttp.OkHttpClient: Vary: Access-Control-Request-Method
I/okhttp.OkHttpClient: Vary: Access-Control-Request-Headers
I/okhttp.OkHttpClient: trace-id: 
I/okhttp.OkHttpClient: X-Content-Type-Options: nosniff
I/okhttp.OkHttpClient: X-XSS-Protection: 1; mode=block
I/okhttp.OkHttpClient: Cache-Control: no-cache, no-store, max-age=0, must-revalidate
I/okhttp.OkHttpClient: Pragma: no-cache
I/okhttp.OkHttpClient: Expires: 0
I/okhttp.OkHttpClient: Content-Type: application/json
I/okhttp.OkHttpClient: Transfer-Encoding: chunked
I/okhttp.OkHttpClient: Date: Fri, 23 May 2025 01:28:26 GMT
I/okhttp.OkHttpClient: Keep-Alive: timeout=60
I/okhttp.OkHttpClient: Connection: keep-alive
I/okhttp.OkHttpClient: {"code":0,"data":[],"msg":""}
I/okhttp.OkHttpClient: <-- END HTTP (29-byte body)
D/FormLocalStorageMgr: 从数据库加载了3个已保存的表单
D/SamplingPointSyncManager: 本地已保存的表单BSM: 44098111217478794274702068
D/SamplingPointSyncManager: 本地已保存的表单BSM: 44098111217478794260989053
D/SamplingPointSyncManager: 本地已保存的表单BSM: 44098112617477246630181921
D/SamplingPointSyncManager: 从本地存储获取到 3 个已保存的BSM
D/FormJsonUtils: 手动解析成功，总字段数量: 37, 字段组数量: 6
D/SamplingPointSyncManager: 从表单配置中获取的BSM: default_form
D/SamplingPointSyncManager: 已清空表: sampling_ddc_points, 删除了 0 行
D/SamplingPointSyncManager: 数据列表为空，仅清空了数据表
E/SamplingPointSyncManager: 没有有效的待调查点数据
D/SamplingPointSyncManager: 数据同步成功回调
D/SamplingPointSyncManager: 数据同步过程结束，关闭对话框
I/ViewRootImpl: dispatchDetachedFromWindow in doDie
I/okhttp.OkHttpClient: --> GET http://***************:48082/admin-api/pjdy/dccy/list/dcr
I/okhttp.OkHttpClient: --> END GET
W/libEGL: EGLNativeWindowType 0xd87bed08 disconnect failed
D/DecorView: showOrHideHighlightView: hasFocus=true; winMode=1; isMrgNull=true
W/HwRemoteInputMethodManager: isCasting false because IHwDistributedWindowManager is invalid.
I/okhttp.OkHttpClient: <-- 200 http://***************:48082/admin-api/pjdy/dccy/list/dcr (2687ms)
I/okhttp.OkHttpClient: Vary: Origin
I/okhttp.OkHttpClient: Vary: Access-Control-Request-Method
I/okhttp.OkHttpClient: Vary: Access-Control-Request-Headers
I/okhttp.OkHttpClient: trace-id: 
I/okhttp.OkHttpClient: X-Content-Type-Options: nosniff
I/okhttp.OkHttpClient: X-XSS-Protection: 1; mode=block
I/okhttp.OkHttpClient: Cache-Control: no-cache, no-store, max-age=0, must-revalidate
I/okhttp.OkHttpClient: Pragma: no-cache
I/okhttp.OkHttpClient: Expires: 0
I/okhttp.OkHttpClient: Content-Type: application/json
I/okhttp.OkHttpClient: Transfer-Encoding: chunked
I/okhttp.OkHttpClient: Date: Fri, 23 May 2025 01:28:29 GMT
I/okhttp.OkHttpClient: Keep-Alive: timeout=60
I/okhttp.OkHttpClient: Connection: keep-alive

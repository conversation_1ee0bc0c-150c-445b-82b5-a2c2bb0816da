package com.abdu.qrcode;

import android.Manifest;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.google.zxing.ResultPoint;
import com.journeyapps.barcodescanner.BarcodeCallback;
import com.journeyapps.barcodescanner.BarcodeResult;
import com.journeyapps.barcodescanner.CompoundBarcodeView;

import java.util.List;

public class ScanActivity extends AppCompatActivity {
    private final Context context = ScanActivity.this;
    private static final String TAG = ScanActivity.class.getSimpleName();
    private CompoundBarcodeView barcodeView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_scan);
        //返回按钮
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        initView();
        scan();
    }

    // 处理按钮点击事件
    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed(); // 或者你可以调用自定义的返回逻辑
        return true;
    }

    private void initView() {
        barcodeView = findViewById(R.id.barcode_scanner);
    }

    private void scan(){
        barcodeView.resume();
        barcodeView.decodeContinuous(new BarcodeCallback() {
            @Override
            public void barcodeResult(BarcodeResult result) {
                if (result.getText() != null) {
                    barcodeView.pause();
                    showScanResultDialog(context, result.getText());
                }
            }

            @Override
            public void possibleResultPoints(List<ResultPoint> resultPoints) {

            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                != PackageManager.PERMISSION_GRANTED) {
            // 如果应用没有摄像头权限，请求权限
            Toast.makeText(this, "请设置摄像头权限", Toast.LENGTH_SHORT).show();
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.CAMERA},
                    200);
        } else {
            scan();
        }
    }



    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                return true;
            default:
                return super.onOptionsItemSelected(item);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        barcodeView.pause();
    }

    private   void showScanResultDialog(Context context, String scanResult) {
//        AlertDialog.Builder builder = new AlertDialog.Builder(context);
//
//        builder.setTitle("扫码结果");
//        builder.setCancelable(false);
//
//        LayoutInflater inflater = LayoutInflater.from(context);
//        View dialogView = inflater.inflate(R.layout.dialog_scan_result, null);
//        builder.setView(dialogView);
//        TextView textView = dialogView.findViewById(R.id.text_scan_result);
//        textView.setText(scanResult);

        // 当用户点击“确定”按钮时设置结果并结束Activity
        Intent intent = new Intent();
        intent.putExtra("result", scanResult); // 添加扫描结果到Intent中
        setResult(200, intent); // 设置返回码和结果
        finish(); // 结束当前Activity


//        builder.setPositiveButton("复制", new DialogInterface.OnClickListener() {
//            @Override
//            public void onClick(DialogInterface dialogInterface, int i) {
//                copyToClipboard(scanResult);
//                scan();
//            }
//        });
//
//        builder.setNegativeButton("关闭", new DialogInterface.OnClickListener() {
//            @Override
//            public void onClick(DialogInterface dialogInterface, int i) {
//                scan();
//            }
//        });


//        AlertDialog dialog = builder.create();
//        dialog.show();
    }

    @Override
    public void onBackPressed() {
        setResult(404,null);
        finish(); // 关闭当前页面
    }
    private void copyToClipboard(String text) {
        // 获取剪贴板管理器
        ClipboardManager clipboardManager = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);

        // 创建 ClipData 对象
        ClipData clipData = ClipData.newPlainText("Scanned Result", text);

        // 将 ClipData 设置到剪贴板
        clipboardManager.setPrimaryClip(clipData);
    }
}
package io.dcloud.uniplugin.sampleflow;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import io.dcloud.uniplugin.model.YplzBatch;
import uni.dcloud.io.uniplugin_module.R;

/**
 * 样品流转列表适配器
 */
public class SampleFlowAdapter extends RecyclerView.Adapter<SampleFlowAdapter.ViewHolder> {
    
    private static final String TAG = "SampleFlowAdapter";
    private Context context;
    private List<YplzBatch> batchList;
    private OnBatchOperationListener operationListener;
    
    // 新增：是否为接收模式（实验室接收样品）
    private boolean isReceiveMode = false;
    
    // 新增：详情页请求码
    private static final int REQUEST_DETAIL = 1001;
    
    /**
     * 批次操作监听器接口
     */
    public interface OnBatchOperationListener {
        void onEdit(YplzBatch batch, int position);
        void onDelete(YplzBatch batch, int position);
        void onConfirmShipment(YplzBatch batch, int position);
    }
    
    public SampleFlowAdapter(Context context, List<YplzBatch> batchList) {
        this.context = context;
        this.batchList = batchList;
        Log.d(TAG, "适配器初始化，列表大小: " + (batchList != null ? batchList.size() : 0));
    }
    
    /**
     * 设置批次操作监听器
     */
    public void setOnBatchOperationListener(OnBatchOperationListener listener) {
        this.operationListener = listener;
    }
    
    /**
     * 设置模式：是否为接收模式（实验室接收样品）
     * @param receiveMode true表示接收模式，false表示发送模式
     */
    public void setReceiveMode(boolean receiveMode) {
        this.isReceiveMode = receiveMode;
        notifyDataSetChanged();
    }
    
    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        Log.d(TAG, "创建ViewHolder");
        View view = LayoutInflater.from(context).inflate(R.layout.item_sample_flow, parent, false);
        return new ViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        if (batchList == null || position >= batchList.size()) {
            Log.e(TAG, "绑定数据错误：列表为空或位置超出范围");
            return;
        }
        
        YplzBatch batch = batchList.get(position);
        
        Log.d(TAG, "绑定ViewHolder: 位置=" + position + ", ID=" + batch.getId() + 
             ", 批次编号=" + batch.getBatchCode() + ", 批次类型=" + getBatchTypeName(batch.getBatchType()) + ", 送样人=" + batch.getSenderName());
        
        // 设置批次编号
        holder.textViewBatchCode.setText("批次编号：" + (batch.getBatchCode() != null ? batch.getBatchCode() : "未设置"));
        
        // 设置批次类型
        holder.textViewBsm.setText("批次类型：" + getBatchTypeName(batch.getBatchType()));
        
        // 设置项目名称
        holder.textViewBatchName.setText("项目名称：" + (batch.getBatchName() != null ? batch.getBatchName() : "未设置"));
        
        // 设置送样人姓名
        holder.textViewSenderName.setText(batch.getSenderName() != null ? batch.getSenderName() : "未设置");
        
        // 设置送样单位
        holder.textViewSendOrg.setText(batch.getSendOrganization() != null ? batch.getSendOrganization() : "未设置");
        
        // 设置配送方式
        holder.textViewDeliveryType.setText(batch.getDeliveryType() != null ? batch.getDeliveryType() : "未设置");
        
        // 设置批次状态
        String stateText = "未知";
        boolean isEditableState = false; // 标记是否为可编辑状态（可编辑、删除）
        boolean canConfirmShipment = false; // 标记是否可进行确认寄送
        boolean canReceive = false; // 新增：标记是否可接收
        
        if (batch.getBatchState() != null) {
            Long state = batch.getBatchState();
            switch (state.intValue()) {
                case 0:
                    stateText = "待寄送";
                    isEditableState = true; // 待寄送状态可编辑和删除
                    canConfirmShipment = true; // 待寄送状态可确认寄送
                    canReceive = false; // 待寄送状态不可接收
                    break;
                case 1:
                    stateText = "寄送中";
                    isEditableState = false; // 寄送中状态不可编辑和删除
                    canConfirmShipment = false; // 寄送中状态不可确认寄送
                    canReceive = true; // 寄送中状态可接收
                    break;
                case 100:
                    stateText = "寄送完成";
                    isEditableState = false; // 寄送完成状态不可编辑和删除
                    canConfirmShipment = false; // 寄送完成状态不可确认寄送
                    canReceive = false; // 寄送完成状态不可接收，已修改
                    break;
                case -1:
                    stateText = "已退回";
                    isEditableState = false; // 退回状态不可编辑和删除
                    canConfirmShipment = false; // 退回状态不可确认寄送
                    canReceive = false; // 退回状态不可接收
                    break;
                default:
                    stateText = "未知状态(" + state + ")";
                    isEditableState = false;
                    canConfirmShipment = false;
                    canReceive = false;
            }
        }
        holder.textViewBatchState.setText(stateText);
        
        // 根据模式和批次状态控制按钮的可见性
        if (isReceiveMode) {
            // 接收模式：隐藏所有发送模式的按钮
            holder.buttonEdit.setVisibility(View.GONE);
            holder.buttonDelete.setVisibility(View.GONE);
            holder.buttonConfirmShipment.setVisibility(View.GONE);
            
            // 把确认寄送按钮改为确认接收按钮（复用UI控件，仅在接收模式下显示对应状态的批次）
            if (canReceive) {
                holder.buttonConfirmShipment.setText("确认接收");
                holder.buttonConfirmShipment.setVisibility(View.VISIBLE);
            } else {
                holder.buttonConfirmShipment.setVisibility(View.GONE);
            }
        } else {
            // 发送模式：原有逻辑
            holder.buttonEdit.setVisibility(isEditableState ? View.VISIBLE : View.GONE);
            holder.buttonDelete.setVisibility(isEditableState ? View.VISIBLE : View.GONE);
            holder.buttonConfirmShipment.setText("确认寄送");
            holder.buttonConfirmShipment.setVisibility(canConfirmShipment ? View.VISIBLE : View.GONE);
        }
        
        // 设置创建时间
        if (batch.getCreateTime() != null) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
                String formattedTime = sdf.format(new Date(batch.getCreateTime()));
                holder.textViewCreateTime.setText(formattedTime);
            } catch (Exception e) {
                Log.e(TAG, "格式化时间失败", e);
                holder.textViewCreateTime.setText("");
            }
        } else {
            holder.textViewCreateTime.setText("");
        }
        
        // 设置点击事件
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 跳转到详情页
                Intent intent = new Intent(context, SampleFlowDetailActivity.class);
                intent.putExtra("batchId", batch.getId());
                
                // 新增：传递模式参数
                intent.putExtra("isReceiveMode", isReceiveMode);
                
                // 使用startActivityForResult代替startActivity
                if (context instanceof Activity) {
                    ((Activity) context).startActivityForResult(intent, REQUEST_DETAIL);
                } else {
                    // 如果context不是Activity，则使用普通方式启动
                    context.startActivity(intent);
                }
            }
        });
        
        // 设置编辑按钮点击事件
        holder.buttonEdit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (operationListener != null) {
                    operationListener.onEdit(batch, holder.getAdapterPosition());
                }
            }
        });
        
        // 设置删除按钮点击事件
        holder.buttonDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (operationListener != null) {
                    operationListener.onDelete(batch, holder.getAdapterPosition());
                }
            }
        });
        
        // 设置确定寄送/接收按钮点击事件
        holder.buttonConfirmShipment.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (operationListener != null) {
                    if (isReceiveMode) {
                        // 跳转到确认接收页面
                        Intent intent = new Intent(context, SampleFlowDetailActivity.class);
                        intent.putExtra("batchId", batch.getId());
                        intent.putExtra("isReceiveMode", true);
                        intent.putExtra("directReceive", true); // 直接进入接收确认流程
                        
                        // 使用startActivityForResult代替startActivity
                        if (context instanceof Activity) {
                            ((Activity) context).startActivityForResult(intent, REQUEST_DETAIL);
                        } else {
                            context.startActivity(intent);
                        }
                    } else {
                        // 原有的确认寄送逻辑
                        operationListener.onConfirmShipment(batch, holder.getAdapterPosition());
                    }
                }
            }
        });
    }
    
    @Override
    public int getItemCount() {
        int count = batchList != null ? batchList.size() : 0;
        Log.d(TAG, "getItemCount: " + count);
        return count;
    }
    
    /**
     * 根据批次类型值获取类型名称
     * @param batchType 批次类型值（0=县级, 1=市级, 2=省级）
     * @return 批次类型名称
     */
    private String getBatchTypeName(Integer batchType) {
        if (batchType == null) {
            return "未设置";
        }
        switch (batchType) {
            case 0:
                return "县级";
            case 1:
                return "市级";
            case 2:
                return "省级";
            default:
                return "未知";
        }
    }
    
    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView textViewBatchCode; // 批次编号
        TextView textViewBsm; // 批次类型
        TextView textViewBatchName; // 项目名称
        TextView textViewSenderName;
        TextView textViewSendOrg;
        TextView textViewDeliveryType;
        TextView textViewBatchState;
        TextView textViewCreateTime;
        Button buttonEdit;
        Button buttonDelete;
        Button buttonConfirmShipment;
        
        // 兼容旧版变量，不再使用
        TextView textViewSampleCount;
        
        ViewHolder(View itemView) {
            super(itemView);
            
            textViewBatchCode = itemView.findViewById(R.id.textViewBatchCode);
            textViewBsm = itemView.findViewById(R.id.textViewBsm);
            textViewBatchName = itemView.findViewById(R.id.textViewBatchName);
            textViewSenderName = itemView.findViewById(R.id.textViewSenderName);
            textViewSendOrg = itemView.findViewById(R.id.textViewSendOrg);
            textViewDeliveryType = itemView.findViewById(R.id.textViewDeliveryType);
            textViewBatchState = itemView.findViewById(R.id.textViewBatchState);
            textViewCreateTime = itemView.findViewById(R.id.textViewCreateTime);
            buttonEdit = itemView.findViewById(R.id.buttonEdit);
            buttonDelete = itemView.findViewById(R.id.buttonDelete);
            buttonConfirmShipment = itemView.findViewById(R.id.buttonConfirmShipment);
            
            // 保留旧版变量的引用，兼容性目的
            textViewSampleCount = itemView.findViewById(R.id.textViewSampleCount);
        }
    }
} 
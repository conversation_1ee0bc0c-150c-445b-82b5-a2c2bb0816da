//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.deltaphone.cameramodule.camera;

import android.annotation.TargetApi;
import android.app.Activity;
import android.app.Fragment;
import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.os.Build.VERSION;
import android.util.SparseArray;

import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.dcloud.android.annotation.NonNull;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

;

public class PermissionReq {
    private static AtomicInteger sRequestCode = new AtomicInteger(0);
    private static SparseArray<PermissionReq.Result> sResultArray = new SparseArray();
    private static Set<String> sManifestPermissionSet;
    private static Object mObject;
    private String[] mPermissions;
    private PermissionReq.Result mResult;

    private PermissionReq(Object object) {
        mObject = object;
    }

    public static PermissionReq with(@NonNull Context activity) {
        return new PermissionReq(activity);
    }

    public static PermissionReq with(@NonNull Fragment fragment) {
        return new PermissionReq(fragment);
    }

    public PermissionReq permissions(@NonNull String... permissions) {
        this.mPermissions = permissions;
        return this;
    }

    public PermissionReq result(@Nullable PermissionReq.Result result) {
        this.mResult = result;
        return this;
    }

    public void request() {
        Activity activity = getActivity(mObject);
        if (activity == null) {
            throw new IllegalArgumentException(mObject.getClass().getName() + " is not supported");
        } else {
            initManifestPermission(activity);
            String[] var2 = this.mPermissions;
            int requestCode = var2.length;

            for(int var4 = 0; var4 < requestCode; ++var4) {
                String permission = var2[var4];
                if (!sManifestPermissionSet.contains(permission)) {
                    if (this.mResult != null) {
                        this.mResult.onDenied(0);
                    }

                    return;
                }
            }

            if (VERSION.SDK_INT < 23) {
                if (this.mResult != null) {
                    this.mResult.onGranted();
                }

            } else {
                List<String> deniedPermissionList = getDeniedPermissions(activity, this.mPermissions);
                if (deniedPermissionList.isEmpty()) {
                    if (this.mResult != null) {
                        this.mResult.onGranted();
                    }

                } else {
                    requestCode = genRequestCode();
                    String[] deniedPermissions = (String[])deniedPermissionList.toArray(new String[deniedPermissionList.size()]);
                    requestPermissions(mObject, deniedPermissions, requestCode);
                    sResultArray.put(requestCode, this.mResult);
                }
            }
        }
    }

    public static void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        PermissionReq.Result result = (PermissionReq.Result)sResultArray.get(requestCode);
        if (result != null) {
            sResultArray.remove(requestCode);

            for(int i = 0; i < grantResults.length; ++i) {
                if (grantResults[i] != 0) {
                    if (!ActivityCompat.shouldShowRequestPermissionRationale(getActivity(mObject), permissions[i])) {
                        result.onDenied(1);
                    } else {
                        result.onDenied(0);
                    }

                    return;
                }
            }

            result.onGranted();
        }
    }

    @TargetApi(23)
    private static void requestPermissions(Object object, String[] permissions, int requestCode) {
        if (object instanceof Activity) {
            ((Activity)object).requestPermissions(permissions, requestCode);
        } else if (object instanceof Fragment) {
            ((Fragment)object).requestPermissions(permissions, requestCode);
        }

    }

    private static List<String> getDeniedPermissions(Context context, String[] permissions) {
        List<String> deniedPermissionList = new ArrayList();
        String[] var3 = permissions;
        int var4 = permissions.length;

        for(int var5 = 0; var5 < var4; ++var5) {
            String permission = var3[var5];
            if (ContextCompat.checkSelfPermission(context, permission) != 0) {
                deniedPermissionList.add(permission);
            }
        }

        return deniedPermissionList;
    }

    private static synchronized void initManifestPermission(Context context) {
        if (sManifestPermissionSet == null) {
            sManifestPermissionSet = new HashSet();

            try {
                PackageInfo packageInfo = context.getPackageManager().getPackageInfo(context.getPackageName(), PackageManager.GET_PERMISSIONS);
                String[] permissions = packageInfo.requestedPermissions;
                Collections.addAll(sManifestPermissionSet, permissions);
            } catch (NameNotFoundException var3) {
                var3.printStackTrace();
            }
        }

    }

    private static Activity getActivity(Object object) {
        if (object != null) {
            if (object instanceof Activity) {
                return (Activity)object;
            }

            if (object instanceof Fragment) {
                return ((Fragment)object).getActivity();
            }
        }

        return null;
    }

    private static int genRequestCode() {
        return sRequestCode.incrementAndGet();
    }

    public interface Result {
        void onGranted();

        void onDenied(int var1);
    }
}

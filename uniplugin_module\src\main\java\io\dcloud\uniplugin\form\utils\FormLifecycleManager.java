package io.dcloud.uniplugin.form.utils;

import android.app.Activity;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.dcloud.uniplugin.enums.SamplingPointStatus;
import io.dcloud.uniplugin.fileUpload.ImageAdapter;
import io.dcloud.uniplugin.form.SignatureView;
import io.dcloud.uniplugin.form.field.FieldFile;
import io.dcloud.uniplugin.form.media.MediaHandler;
import io.dcloud.uniplugin.form.processor.FormProcessor;
import io.dcloud.uniplugin.form.renderer.FormRenderer;
import io.dcloud.uniplugin.model.FormConfig;
import io.dcloud.uniplugin.model.FormFieldConfig;
import io.dcloud.uniplugin.model.FormFieldGroup;
import io.dcloud.uniplugin.model.PhotoInfo;
import io.dcloud.uniplugin.service.FormConfigDatabaseHelper;
import io.dcloud.uniplugin.service.FormService;

/**
 * 表单生命周期管理器，负责管理表单的加载、渲染和数据填充等生命周期流程
 */
public class FormLifecycleManager {
    private static final String TAG = "FormLifecycleManager";

    /**
     * 表单加载完成监听器
     */
    public interface OnFormLoadedListener {
        void onFormLoaded();
    }

    private Activity activity;
    private LinearLayout formContainer;
    private String pjdybh;
    private Double latitude;
    private Double longitude;
    private Long pjdyId;
//    private String xmmc;

    private FormConfig formConfig;
    private List<FormFieldConfig> allFields = new ArrayList<>();
    private Map<String, View> formViews;
    private Map<String, ImageAdapter> imageAdapters;
    private Map<String, List<FieldFile>> fieldFiles;

    private FormRenderer formRenderer;
    private FormProcessor formProcessor;
    private FormUIController uiController;
    private FormButtonManager buttonManager;
    private FormMediaPreviewManager mediaPreviewManager;
    private FormLocationManager locationManager;
    private FormOfflineManager offlineManager;
    private FormLocalStorageManager localStorageManager;
    private FormService formService;
    private MediaHandler mediaHandler;
    private TextView locationText;

    private OnFormLoadedListener onFormLoadedListener;
    private SamplingPointStatus status;

    /**
     * 构造函数
     *
     * @param activity      活动
     * @param formContainer 表单容器
     * @param pjdybh       采样点编号
     */
    public FormLifecycleManager(Activity activity, LinearLayout formContainer, String pjdybh) {
        this.activity = activity;
        this.formContainer = formContainer;
        this.pjdybh = pjdybh;

        // 初始化数据结构
        formViews = new HashMap<>();
        imageAdapters = new HashMap<>();
        fieldFiles = new HashMap<>();
    }

    /**
     * 设置经纬度信息
     *
     * @param latitude  纬度
     * @param longitude 经度
     */
    public void setCoordinates(Double latitude, Double longitude) {
        this.latitude = latitude;
        this.longitude = longitude;
    }

    /**
     * 设置项目点样ID
     *
     * @param pjdyId 项目点样ID
     */
    public void setPjdyId(Long pjdyId) {
        this.pjdyId = pjdyId;
        Log.d(TAG, "设置项目点样ID: " + pjdyId);
        
        // 如果FormRenderer已初始化，则传递pjdyId
        if (formRenderer != null) {
            formRenderer.setPjdyId(pjdyId);
        }
    }

    /**
     * 设置UI控制器
     *
     * @param uiController 表单UI控制器
     */
    public void setUIController(FormUIController uiController) {
        this.uiController = uiController;
        if (locationManager != null) {
            locationManager.setUIController(uiController);
        }
    }

    /**
     * 初始化服务
     *
     * @param mediaHandler 媒体处理器
     */
    public void initServices(MediaHandler mediaHandler) {
        initServices(this.uiController, mediaHandler);
    }

    /**
     * 初始化服务和管理器
     *
     * @param uiController UI控制器
     * @param mediaHandler 媒体处理器
     */
    public void initServices(FormUIController uiController, MediaHandler mediaHandler) {
        this.uiController = uiController;
        this.mediaHandler = mediaHandler;

        // 初始化服务
        formService = new FormService(activity);
        formRenderer = new FormRenderer(activity, formContainer);
        
        // 如果pjdyId不为空，设置给FormRenderer
        if (pjdyId != null) {
            formRenderer.setPjdyId(pjdyId);
        }
        
        localStorageManager = new FormLocalStorageManager(activity, pjdybh, pjdybh);
    }

    /**
     * 加载表单配置
     * @param samplingPointStatus
     * @param pjdybh
     * @param xmmc
     */
    public void loadFormConfig(SamplingPointStatus samplingPointStatus, String pjdybh, String xmmc) {
        this.status = samplingPointStatus;
        // 显示加载进度
        if (uiController != null) {
            uiController.showLoading(true);
        }
        try {
            // 位置管理器初始化移到DynamicFormActivity中进行控制
            // 从本地数据库加载default_form配置
            FormConfigDatabaseHelper configDbHelper = FormConfigDatabaseHelper.getInstance(activity);
            FormConfig formConfig = configDbHelper.getFormConfig("default_form");
            this.formConfig = formConfig;
            renderFormWithConfig(formConfig,pjdybh,xmmc);

            // 尝试从本地加载之前保存的表单数据
            formContainer.post(this::loadLocalFormData);

            // 如果是离线表单，需要同时加载离线表单数据
//            if (isOffline) {
//                if (offlineManager != null) {
//                    offlineManager.loadOfflineFormData(uiController, (config, formData) -> {
//                        renderFormWithConfig(config);
//                        offlineManager.fillFormWithOfflineData(formData);
//                        uiController.updateSubmitButtonState(formData.isComplete(), isOffline);
//                    });
//                }
//            } else {
//
//            }
            uiController.showLoading(false);
        } catch (Exception e) {
            Log.e(TAG, "加载表单配置失败: " + e.getMessage(), e);
            if (uiController != null) {
                uiController.showLoading(false);
//                uiController.showError("加载表单配置失败: " + e.getMessage());
            }
        }
    }

    /**
     * 渲染表单
     *
     * @param formConfig 表单配置
     * @param pjdybh
     * @param xmmc
     */
    public void renderFormWithConfig(FormConfig formConfig, String pjdybh, String xmmc) {
        try {
            // 获取所有字段
            if (formConfig.getFieldGroups() != null && !formConfig.getFieldGroups().isEmpty()) {
                // 从字段组中获取字段
                allFields = new ArrayList<>();
                for (FormFieldGroup group : formConfig.getFieldGroups()) {
                    if (group != null && group.getFields() != null) {
                        allFields.addAll(group.getFields());
                    }
                }
            } else if (formConfig.getFields() != null && !formConfig.getFields().isEmpty()) {
                // 直接使用字段列表
                allFields = formConfig.getFields();
            } else {
                // 没有字段
                allFields = new ArrayList<>();
            }

            // 设置表单渲染器的当前位置坐标（样点坐标，作为备选位置）
            if (latitude != null && longitude != null) {
                formRenderer.setCurrentLocation(latitude, longitude);
                Log.d(TAG, "向表单渲染器传递样点位置: 纬度=" + latitude + ", 经度=" + longitude);
            } else {
                Log.w(TAG, "没有有效的样点坐标，样点经纬度字段将使用默认值0");
                formRenderer.setCurrentLocation(0, 0);
            }
            
            // 设置位置管理器，以便获取实际定位
            if (locationManager != null) {
                setupFormRendererLocationListener();
            } else {
                Log.w(TAG, "位置管理器为空，无法获取实际定位，将使用样点坐标或默认值");
            }

            // 使用表单渲染器渲染表单
            formRenderer.renderForm(formConfig, pjdybh, xmmc);

            // 获取渲染后的表单数据结构
            formViews = formRenderer.getFormViews();
            imageAdapters = formRenderer.getImageAdapters();

            // 初始化表单处理器
            formProcessor = new FormProcessor(activity, formViews, imageAdapters,
                    FormDataAdapter.convertFieldFilesToStrings(fieldFiles), allFields);

            // 设置表单处理监听器
            if (activity instanceof FormProcessor.OnFormProcessListener) {
                formProcessor.setOnFormProcessListener((FormProcessor.OnFormProcessListener) activity);
            }

            // 初始化其他管理器
            mediaPreviewManager = new FormMediaPreviewManager(activity, imageAdapters, fieldFiles, formViews);
            mediaPreviewManager.setFormFields(allFields);
            mediaPreviewManager.initPreviewListeners();

            // 设置媒体预览管理器的文件变更监听器
            initFormProcessorUpdateListener();

            buttonManager = new FormButtonManager(activity, formViews, allFields, fieldFiles, mediaHandler);
            buttonManager.setupFormButtonListeners(pjdybh, xmmc);

            offlineManager = new FormOfflineManager(activity, formProcessor, formViews, imageAdapters,
                    fieldFiles, formService, formConfig, this.pjdybh);

            // 转换String列表为FieldFile列表
            Map<String, List<String>> stringFiles = formRenderer.getFieldFiles();
            for (Map.Entry<String, List<String>> entry : stringFiles.entrySet()) {
                String fieldId = entry.getKey();
                List<String> paths = entry.getValue();

                if (paths != null && !paths.isEmpty()) {
                    List<FieldFile> fieldFileList = new ArrayList<>();
                    for (String path : paths) {
                        fieldFileList.add(new FieldFile(path));
                    }
                    fieldFiles.put(fieldId, fieldFileList);
                }
            }

            // 确保所有按钮可点击
            uiController.ensureFormButtonsClickable();

            // 应用输入验证到所有表单字段
            applyEditTextValidators();

            // 表单渲染后更新位置文本视图
//            if (latitude != null && longitude != null && latitude != 0.0 && longitude != 0.0) {
//                Log.d(TAG, "表单渲染完成，更新位置文本视图: 纬度=" + latitude + ", 经度=" + longitude);
//
//                // 查找所有位置文本视图并更新
//                for (Map.Entry<String, View> entry : formViews.entrySet()) {
//                    View view = entry.getValue();
//                    TextView locationTextView = findLocationTextInView(view);
//                    if (locationTextView != null && ("等待获取GPS坐标...".equals(locationTextView.getText().toString()) ||
//                            locationTextView.getText().toString().isEmpty())) {
//                        // 更新位置文本
//                        String locationInfo = String.format("经度: %.6f\n纬度: %.6f",
//                                longitude, latitude);
//                        locationTextView.setText(locationInfo);
//                        Log.d(TAG, "在表单渲染后更新位置文本视图: " + locationInfo);
//                    }
//                }
//            }

            // 通知表单加载完成
            if (onFormLoadedListener != null) {
                onFormLoadedListener.onFormLoaded();
                Log.d(TAG, "通知表单加载完成");
            }
        } catch (Exception e) {
            Log.e(TAG, "渲染表单失败", e);
            UIUtils.showToast(activity, "渲染表单失败: " + e.getMessage());
        }
    }

//    /**
//     * 在视图中查找位置文本视图
//     *
//     * @param view 父视图
//     * @return 位置文本视图，如果没找到则返回null
//     */
//    private TextView findLocationTextInView(View view) {
//        if (view == null) return null;
//
//        // 尝试直接查找
//        if (view instanceof ViewGroup) {
//            // 尝试查找ID为locationText的文本视图
//            TextView textView = view.findViewById(uni.dcloud.io.uniplugin_module.R.id.locationText);
//            if (textView != null) {
//                return textView;
//            }
//        }
//
//        return null;
//    }

    /**
     * 初始化位置管理器
     */
    public void initLocationManager() {
        if (activity == null) {
            Log.e(TAG, "activity为空，无法初始化位置管理器");
            return;
        }

        try {
            // 创建位置管理器
            locationManager = new FormLocationManager(activity);

            // 查找位置文本视图
            locationText = findLocationTextView();
            locationManager.setLocationText(locationText);

            // 设置UI控制器
            if (uiController != null) {
                locationManager.setUIController(uiController);
            }

            // 如果设置了经纬度，则在地图上标记位置
            if (latitude != null && longitude != null && latitude != 0.0 && longitude != 0.0) {
                locationManager.setTargetCoordinates(latitude, longitude);
            }

            // 设置位置更新回调
            locationManager.setLocationUpdateCallback((location, isInGeofence) -> {
                // 如果表单UI控制器已初始化，更新表单可编辑状态
                if (uiController != null) {
                    if (latitude != null && longitude != null && latitude != 0.0 && longitude != 0.0) {
                        // 只有在设置了目标坐标的情况下才控制表单可编辑状态
                        // 检查是否允许在电子围栏外提交
                        boolean allowOutside = locationManager.getAllowSubmitOutsideGeofence();
                        if (!isInGeofence && allowOutside) {
                            // 如果不在围栏内但允许在围栏外提交，设置表单为只读但允许提交
                            uiController.setFormReadOnlyButSubmittable(false); // 不启用保存到本地按钮
                            Log.d(TAG, "位置回调：不在围栏内但允许提交，设置表单只读但仅提交按钮可用");
                        } else {
                        uiController.setFormEnabled(isInGeofence);
                            Log.d(TAG, "位置回调：根据围栏状态设置表单: " + (isInGeofence ? "启用" : "禁用"));
                        }
                    } else {
                        // 如果没有设置目标坐标，表单始终可编辑
                        uiController.setFormEnabled(true);
                        Log.d(TAG, "位置回调：未设置目标坐标，表单始终可编辑");
                    }
                }
            });
            
            // 如果formRenderer已初始化，设置位置监听
            if (formRenderer != null) {
                setupFormRendererLocationListener();
            }

            // 使用requestLocationAndWait方法替代requestSingleLocationUpdate
            new Thread(() -> {
                // 请求位置并等待结果，这将触发对话框显示
                locationManager.requestLocationAndWait();
            }).start();

        } catch (Exception e) {
            Log.e(TAG, "初始化位置管理器失败: " + e.getMessage(), e);
        }
    }

    /**
     * 设置表单渲染器的位置监听器，使其能够接收位置更新
     */
    private void setupFormRendererLocationListener() {
        if (locationManager != null && formRenderer != null) {
            // 设置位置管理器到表单渲染器
            formRenderer.setLocationManager(locationManager);
            
            // 设置表单渲染器位置监听器，当位置更新时更新表单中的经纬度字段
            locationManager.setFormRendererLocationListener((latitude, longitude) -> {
                // 在位置更新时设置表单渲染器的当前位置
                formRenderer.setCurrentLocation(latitude, longitude);
                Log.d(TAG, "位置更新时设置表单渲染器的当前位置: 纬度=" + latitude + ", 经度=" + longitude);
            });
            
            // 初始时设置一次当前位置（如果位置管理器已有位置）
            double lat = locationManager.getLatitude();
            double lng = locationManager.getLongitude();
            if (lat != 0.0 || lng != 0.0) {
                formRenderer.setCurrentLocation(lat, lng);
                Log.d(TAG, "初始化时设置表单渲染器的当前位置: 纬度=" + lat + ", 经度=" + lng);
            } else if (latitude != null && longitude != null) {
                // 如果位置管理器没有位置，使用Intent传入的坐标作为初始值
                formRenderer.setCurrentLocation(latitude, longitude);
                Log.d(TAG, "使用Intent传入的坐标作为初始位置: 纬度=" + latitude + ", 经度=" + longitude);
            }
        } else {
            Log.w(TAG, "未能设置表单渲染器位置监听器：" + 
                  (locationManager == null ? "位置管理器为空" : "表单渲染器为空"));
        }
    }

    /**
     * 查找位置文本视图
     *
     * @return 位置文本视图
     */
    private TextView findLocationTextView() {
        TextView textView = null;

        // 1. 首先尝试通过 tag 找到
        View rootView = activity.findViewById(android.R.id.content);
        if (rootView instanceof ViewGroup) {
            textView = findViewWithTagRecursive((ViewGroup) rootView, "location_text");
        }

        // 2. 如果没找到，尝试在表单视图中查找类型为位置的字段
        if (textView == null && formViews != null && allFields != null) {
            for (FormFieldConfig field : allFields) {
                if (FormFieldConfig.TYPE_LOCATION.equals(field.getFieldType())) {
                    View fieldView = formViews.get(field.getFieldId());
                    if (fieldView != null) {
                        // 尝试查找 TextView
                        textView = fieldView.findViewById(android.R.id.text1);
                        if (textView == null) {
                            // 尝试通过 tag 查找
                            textView = findViewWithTagRecursive(fieldView, "location_text");
                        }
                        if (textView != null) break;
                    }
                }
            }
        }

        // 3. 如果仍然找不到，创建一个临时的不可见文本视图
        if (textView == null) {
            textView = new TextView(activity);
            textView.setVisibility(View.GONE);

            // 添加到表单容器中
            if (formContainer != null) {
                formContainer.addView(textView);
            }
        }

        return textView;
    }

    /**
     * 递归查找带有指定标签的视图
     *
     * @param tag 标签
     * @return 找到的 TextView，如果没找到则返回 null
     */
    private TextView findViewWithTagRecursive(View view, Object tag) {
        if (view == null || tag == null) {
            return null;
        }

        // 检查当前视图是否有匹配的标签
        if (tag.equals(view.getTag()) && view instanceof TextView) {
            return (TextView) view;
        }

        // 如果是视图组，检查其子视图
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                TextView found = findViewWithTagRecursive(viewGroup.getChildAt(i), tag);
                if (found != null) {
                    return found;
                }
            }
        }

        return null;
    }

    /**
     * 加载本地表单数据
     */
    public void loadLocalFormData() {
        try {
            ensureFormComponents();

            // 确保本地存储管理器已初始化
            if (localStorageManager == null) {
                localStorageManager = new FormLocalStorageManager(activity, formConfig.getFormId());
            }

            JSONObject formData = null;
            
            // 根据状态决定从哪里加载数据
            if (status != null && (status == SamplingPointStatus.SUBMITTED || status == SamplingPointStatus.PENDING_RECTIFY)) {
                // 从sampling_points表获取数据
                if (pjdybh != null && !pjdybh.isEmpty()) {
                    io.dcloud.uniplugin.db.SamplingPointsManager samplingPointsManager = 
                            io.dcloud.uniplugin.db.SamplingPointsManager.getInstance(activity);
                    
                    // 获取表单数据JSON
                    formData = samplingPointsManager.getFormDataJsonObjectByBsm(pjdybh);
                    Log.d(TAG, "从sampling_points表加载表单数据: " + (formData != null ? "成功" : "失败"));
                }
            } else {
                // 从本地文件存储获取数据
                formData = localStorageManager.loadFormData();
                Log.d(TAG, "从本地文件存储加载表单数据: " + (formData != null ? "成功" : "失败"));
            }
            
            if (formData != null) {
                // 填充表单数据到UI
                fillFormWithData(formData);

                // 加载文件
                fieldFiles = localStorageManager.loadFormFiles(formData);

                // 更新媒体预览
                if (fieldFiles != null && !fieldFiles.isEmpty()) {
                    updateMediaPreviews(fieldFiles);

                    // 确保按钮监听器正常工作
                    if (buttonManager != null) {
                        buttonManager.setupFormButtonListeners(pjdybh, null);
                    }

                    // 更新FormProcessor的文件列表
                    updateFormProcessorFileListInternal();
                }
                
                // 根据状态配置表单UI
                configureFormByStatus();
            } else {
//                UIUtils.showToast(activity, "未找到本地保存的表单数据");
            }
        } catch (Exception e) {
            Log.e(TAG, "加载本地表单数据失败: " + e.getMessage());
            UIUtils.showToast(activity, "加载本地表单数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据状态配置表单UI
     */
    private void configureFormByStatus() {
        if (status != null && uiController != null) {
            Log.d(TAG, "根据状态配置表单UI: " + status.getDescription());
            
            switch (status) {
                case SUBMITTED:
                    // 已提交状态：禁用表单编辑
                    uiController.setFormEnabled(false);
                    uiController.setSubmitEnabled(false, false);
                    if (locationManager != null) {
                        locationManager.setIgnoreGeofenceCheck(true, false);
                        Log.d(TAG, "已提交状态：设置忽略地理围栏检查");
                    }
                    break;
                    
                case PENDING_RECTIFY:
                    // 待整改状态：启用表单编辑
                    uiController.setFormEnabled(true);
                    if (locationManager != null) {
                        locationManager.setIgnoreGeofenceCheck(false);
                        Log.d(TAG, "待整改状态：启用地理围栏检查");
                    }
                    break;
                    
                case PENDING_SUBMIT:
                    // 待提交状态：启用表单编辑
                    uiController.setFormEnabled(true);
                    if (locationManager != null) {
                        locationManager.setIgnoreGeofenceCheck(false);
                        Log.d(TAG, "待提交状态：启用地理围栏检查");
                    }
                    break;
                    
                default:
                    // 其他状态默认启用编辑
                    uiController.setFormEnabled(true);
                    break;
            }
        }
    }

    /**
     * 更新媒体预览
     *
     * @param fieldFiles 字段文件映射
     */
    public void updateMediaPreviews(Map<String, List<FieldFile>> fieldFiles) {
        if (fieldFiles == null || fieldFiles.isEmpty()) {
            return;
        }

        // 遍历所有字段文件
        for (Map.Entry<String, List<FieldFile>> entry : fieldFiles.entrySet()) {
            String fieldId = entry.getKey();
            List<FieldFile> files = entry.getValue();

            if (files == null || files.isEmpty()) {
                continue;
            }

            // 获取字段配置，判断字段类型
            FormFieldConfig fieldConfig = getFieldConfigById(fieldId);
            String fieldType = fieldConfig != null ? fieldConfig.getFieldType() : "";

            // 动态判断字段类型
            String fieldIdLower = fieldId.toLowerCase();
            boolean isVideoField = fieldIdLower.contains("video");
            boolean isPhotoField = fieldIdLower.contains("photo") || fieldIdLower.contains("image") ||
                    fieldIdLower.contains("pic") || fieldIdLower.endsWith("_photos");
            boolean isSignatureField = fieldIdLower.contains("signature") || fieldIdLower.contains("sign");

            // 设置文件类型
            for (FieldFile file : files) {
                if (file != null) {
                    if (isVideoField && !"video".equals(file.getType())) {
                        file.setType("video");
                    } else if (isPhotoField && !"image".equals(file.getType())) {
                        file.setType("image");
                    } else if (isSignatureField && !"signature".equals(file.getType())) {
                        file.setType("signature");
                    }
                }
            }

            if (FormFieldConfig.TYPE_SIGNATURE.equals(fieldType) || isSignatureField) {
                // 处理签名字段
                View fieldView = formViews.get(fieldId);
                if (fieldView == null) {
                    Log.e(TAG, "找不到签名字段的视图: " + fieldId);
                    continue;
                }

                Log.d(TAG, "处理签名字段: " + fieldId);

                // 获取签名相关视图
                ImageView signatureImage = fieldView.findViewById(uni.dcloud.io.uniplugin_module.R.id.signatureImage);
                SignatureView signatureView = fieldView.findViewById(uni.dcloud.io.uniplugin_module.R.id.signatureView);
                TextView savedSignatureLabel = fieldView.findViewById(uni.dcloud.io.uniplugin_module.R.id.savedSignatureLabel);

                // 获取面板和按钮布局
                ViewGroup signaturePanel = null;
                View buttonLayout = null;

                if (signatureView != null) {
                    ViewParent parent = signatureView.getParent();
                    if (parent instanceof ViewGroup) {
                        signaturePanel = (ViewGroup) parent;
                    }
                }

                // 查找按钮布局
                for (int i = 0; i < ((ViewGroup) fieldView).getChildCount(); i++) {
                    View child = ((ViewGroup) fieldView).getChildAt(i);
                    if (child instanceof ViewGroup &&
                            child.findViewById(uni.dcloud.io.uniplugin_module.R.id.btnClear) != null) {
                        buttonLayout = child;
                        break;
                    }
                }

                // 获取第一个签名文件
                if (files.isEmpty()) {
                    Log.e(TAG, "签名字段 " + fieldId + " 没有文件");
                    continue;
                }

                FieldFile signatureFile = files.get(0);
                if (signatureFile == null) {
                    Log.e(TAG, "签名字段 " + fieldId + " 的文件是null");
                    continue;
                }

                if (!signatureFile.exists()) {
                    Log.e(TAG, "签名字段 " + fieldId + " 的文件不存在: " + signatureFile.getPath());
                    continue;
                }

                String path = signatureFile.getPath();
                Log.d(TAG, "加载签名图片: " + path);

                // 加载签名图片
                Bitmap bitmap = android.graphics.BitmapFactory.decodeFile(path);
                if (bitmap != null && signatureImage != null) {
                    // 显示签名图片
                    signatureImage.setImageBitmap(bitmap);
                    signatureImage.setVisibility(View.VISIBLE);
                    // 设置标签以便我们以后可以找到这个文件路径
                    signatureImage.setTag(path);

                    // 隐藏签名面板
                    if (signaturePanel != null) {
                        signaturePanel.setVisibility(View.GONE);
                    }

                    // 隐藏按钮布局
                    if (buttonLayout != null) {
                        buttonLayout.setVisibility(View.GONE);
                    }

                    // 显示已保存标签
                    if (savedSignatureLabel != null) {
                        savedSignatureLabel.setVisibility(View.VISIBLE);
                    }

                    Log.d(TAG, "签名图片已成功显示");
                } else {
                    Log.e(TAG, "无法加载签名图片: " + (bitmap == null ? "bitmap为null" : "signatureImage为null"));
                }
            } else if ((FormFieldConfig.TYPE_PHOTO.equals(fieldType) || FormFieldConfig.TYPE_VIDEO.equals(fieldType) ||
                    isPhotoField || isVideoField) && imageAdapters != null) {

                // 获取图片适配器
                ImageAdapter adapter = imageAdapters.get(fieldId);
                if (adapter != null) {
                    // 清空适配器
                    adapter.clearItems();

                    // 遍历文件列表，添加到适配器
                    for (FieldFile file : files) {
                        if (file != null && file.exists()) {
                            boolean isVideo = file.getType() != null && file.getType().equals("video");
                            adapter.addItem(file);
                        }
                    }
                }
            }
        }
    }

    /**
     * 填充表单数据到UI
     *
     * @param formData 表单数据
     */
    public void fillFormWithData(JSONObject formData) {
        // 使用FormDataUtils填充表单数据
        FormDataUtils.fillFormWithData(activity, formViews, allFields, formData, fieldFiles, locationManager);
    }

    /**
     * 根据字段ID获取字段配置
     *
     * @param fieldId 字段ID
     * @return 字段配置，如果不存在则返回null
     */
    public FormFieldConfig getFieldConfigById(String fieldId) {
        if (TextUtils.isEmpty(fieldId) || allFields == null) {
            return null;
        }

        for (FormFieldConfig field : allFields) {
            if (field.getFieldId().equals(fieldId)) {
                return field;
            }
        }

        return null;
    }

    /**
     * 更新FormProcessor的文件列表
     * 公开方法，用于强制更新文件列表
     */
    public void updateFormProcessorFileList() {
        Log.d(TAG, "强制更新FormProcessor文件列表");
        updateFormProcessorFileListInternal();
    }

    /**
     * 内部方法，更新FormProcessor的文件列表
     */
    private void updateFormProcessorFileListInternal() {
        if (formProcessor == null) {
            Log.e(TAG, "FormProcessor为空，无法更新文件列表");
            return;
        }

        // 使用全新的映射表，确保不会保留已删除的文件
        Map<String, List<FieldFile>> newFieldFiles = new HashMap<>();

        // 记录当前的fieldFiles状态
        Log.d(TAG, "开始更新FormProcessor文件列表，当前fieldFiles包含 " +
                (fieldFiles != null ? fieldFiles.size() : 0) + " 个字段");

        // 确保fieldFiles已初始化
        if (fieldFiles == null) {
            fieldFiles = new HashMap<>();
        }

        // 从所有ImageAdapter同步文件到新的映射表
        if (imageAdapters != null) {
            // 处理所有媒体适配器
            for (Map.Entry<String, ImageAdapter> entry : imageAdapters.entrySet()) {
                String fieldId = entry.getKey();
                ImageAdapter adapter = entry.getValue();

                if (adapter != null) {
                    List<ImageAdapter.FileItem> fileItems = adapter.getFileItems();
                    if (fileItems != null && !fileItems.isEmpty()) {
                        // 为此字段创建FieldFile列表
                        List<FieldFile> files = new ArrayList<>();

                        // 添加所有有效的文件项
                        for (ImageAdapter.FileItem item : fileItems) {
                            String path = item.getFilePath();
                            if (path != null && !path.isEmpty() && new File(path).exists()) {
                                // 根据字段ID和文件类型确定文件类型
                                FormFieldConfig fieldConfig = getFieldConfigById(fieldId);
                                String fileType = FormDataUtils.inferFileTypeFromFieldIdAndPath(fieldId, path, fieldConfig);

                                // 查找原始FieldFile对象中是否有位置信息
                                FieldFile existingFile = findExistingFieldFile(fieldId, path);
                                if (existingFile != null &&
                                        (existingFile.getLatitude() != null ||
                                                existingFile.getLongitude() != null ||
                                                existingFile.getDirection() != null)) {
                                    // 如果原始FieldFile有位置信息，使用原始对象的位置信息
                                    files.add(existingFile);
                                    Log.d(TAG, "保留原始文件位置信息: " + path +
                                            " 纬度=" + existingFile.getLatitude() +
                                            " 经度=" + existingFile.getLongitude() +
                                            " 方位角=" + existingFile.getDirection());
                                } else {
                                    // 否则创建新的FieldFile对象
                                    FieldFile fieldFile = new FieldFile(path, fileType);
                                    files.add(fieldFile);
                                    Log.d(TAG, "从适配器同步文件: " + path + " (类型: " + fileType + ")");
                                }
                            }
                        }

                        if (!files.isEmpty()) {
                            newFieldFiles.put(fieldId, files);
                            Log.d(TAG, "更新字段[" + fieldId + "]的文件列表，包含 " + files.size() + " 个文件");
                        }
                    }
                }
            }
        }

        // 处理签名字段 - 统一使用相同的处理方式
        for (Map.Entry<String, View> entry : formViews.entrySet()) {
            String fieldId = entry.getKey();
            View rootView = entry.getValue();

            // 检查字段类型
            FormFieldConfig fieldConfig = getFieldConfigById(fieldId);
            boolean isSignatureField = fieldConfig != null &&
                    FormFieldConfig.TYPE_SIGNATURE.equals(fieldConfig.getFieldType());

            // 如果是签名字段，检查ImageView标签
            if (isSignatureField || fieldId.toLowerCase().contains("signature")) {
                // 查找签名图像视图
                ImageView signatureImage = null;
                if (rootView instanceof ViewGroup) {
                    signatureImage = rootView.findViewById(uni.dcloud.io.uniplugin_module.R.id.signatureImage);
                } else if (rootView instanceof ImageView) {
                    signatureImage = (ImageView) rootView;
                }

                if (signatureImage != null && signatureImage.getTag() instanceof String) {
                    String filePath = (String) signatureImage.getTag();
                    File file = new File(filePath);

                    if (file.exists()) {
                        // 创建签名字段的文件列表
                        List<FieldFile> files = new ArrayList<>();

                        // 添加签名文件
                        FieldFile signatureFile = new FieldFile(filePath, "signature");
                        files.add(signatureFile);
                        newFieldFiles.put(fieldId, files);
                        Log.d(TAG, "从ImageView添加签名文件: " + fieldId + " -> " + filePath);
                    } else {
                        Log.w(TAG, "签名文件不存在: " + filePath);
                    }
                }
            }
        }

        // 完全替换fieldFiles而不是合并
        fieldFiles.clear();
        fieldFiles.putAll(newFieldFiles);

        // 创建适用于FormProcessor的字符串路径映射表
        Map<String, List<String>> fileMap = new HashMap<>();

        // 创建文件元数据映射，用于存储位置信息
        Map<String, Map<String, Object>> metadataMap = new HashMap<>();

        // 从mediaPreviewManager获取照片信息
        Map<String, List<PhotoInfo>> photoInfoMap = null;
        if (mediaPreviewManager != null) {
            try {
                photoInfoMap = new HashMap<>();
                // 获取所有字段的照片信息
                for (String fieldId : fieldFiles.keySet()) {
                    List<PhotoInfo> photoInfos = mediaPreviewManager.getPhotoInfos(fieldId);
                    if (photoInfos != null && !photoInfos.isEmpty()) {
                        photoInfoMap.put(fieldId, photoInfos);
                        Log.d(TAG, "字段 " + fieldId + " 有 " + photoInfos.size() + " 个照片信息");
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "获取照片信息时出错: " + e.getMessage());
            }
        }

        // 遍历所有字段的文件
        for (Map.Entry<String, List<FieldFile>> entry : fieldFiles.entrySet()) {
            String fieldId = entry.getKey();
            List<FieldFile> files = entry.getValue();

            if (files != null && !files.isEmpty()) {
                List<String> paths = new ArrayList<>();
                Map<String, Object> fieldMetadata = new HashMap<>();

                // 记录是否找到位置信息，用于调试
                boolean foundLocationInfo = false;

                for (FieldFile file : files) {
                    if (file != null && file.exists()) {
                        String path = file.getPath();
                        paths.add(path);

                        // 收集位置信息
                        Map<String, Object> fileInfo = new HashMap<>();

                        // 优先从FieldFile对象获取位置信息
                        if (file.getLatitude() != null || file.getLongitude() != null || file.getDirection() != null) {
                            foundLocationInfo = true;

                            // 添加位置信息到元数据
                            if (file.getLatitude() != null) {
                                fileInfo.put("latitude", file.getLatitude());
                            }
                            if (file.getLongitude() != null) {
                                fileInfo.put("longitude", file.getLongitude());
                            }
                            if (file.getDirection() != null) {
                                fileInfo.put("direction", file.getDirection());
                            }

                            Log.d(TAG, "文件 " + path + " 包含位置信息: 纬度=" + file.getLatitude() +
                                    ", 经度=" + file.getLongitude() + ", 方位角=" + file.getDirection());
                        }

                        // 如果没有从FieldFile获取到位置信息，尝试从photoInfoMap获取
                        if (!foundLocationInfo && photoInfoMap != null && photoInfoMap.containsKey(fieldId)) {
                            List<PhotoInfo> photoInfos = photoInfoMap.get(fieldId);
                            for (PhotoInfo photoInfo : photoInfos) {
                                if (photoInfo.getFilePath() != null && photoInfo.getFilePath().equals(path)) {
                                    // 找到匹配的照片信息
                                    fileInfo.put("latitude", photoInfo.getLatitude());
                                    fileInfo.put("longitude", photoInfo.getLongitude());
                                    fileInfo.put("direction", photoInfo.getDirection());

                                    Log.d(TAG, "从photoInfoMap找到位置信息: 文件=" + path +
                                            ", 纬度=" + photoInfo.getLatitude() +
                                            ", 经度=" + photoInfo.getLongitude() +
                                            ", 方位角=" + photoInfo.getDirection());

                                    foundLocationInfo = true;
                                    break;
                                }
                            }
                        }

                        // 只有当有位置信息时才添加到元数据
                        if (!fileInfo.isEmpty()) {
                            fieldMetadata.put(path, fileInfo);
                        }
                    }
                }

                if (!foundLocationInfo && photoInfoMap != null && photoInfoMap.containsKey(fieldId)) {
                    // 如果通过文件路径匹配没有找到位置信息，记录日志
                    Log.d(TAG, "字段 " + fieldId + " 中的文件没有匹配到photoInfoMap中的位置信息");
                }

                if (!paths.isEmpty()) {
                    fileMap.put(fieldId, paths);

                    // 只有当有元数据时才添加
                    if (!fieldMetadata.isEmpty()) {
                        metadataMap.put(fieldId, fieldMetadata);
                    }
                }
            }
        }

        // 更新FormProcessor的文件列表和元数据
        try {
            formProcessor.updateFieldFiles(fileMap, metadataMap);
            Log.d(TAG, "已更新FormProcessor的文件列表和元数据，包含 " + fileMap.size() + " 个字段, " +
                    metadataMap.size() + " 个带元数据的字段");
        } catch (Exception e) {
            Log.e(TAG, "更新FormProcessor文件列表和元数据时出错: " + e.getMessage());
            // 如果调用新方法失败，回退到调用旧方法
            formProcessor.updateFieldFiles(fileMap);
            Log.d(TAG, "已回退到更新FormProcessor的文件列表（无元数据），包含 " + fileMap.size() + " 个字段");
        }
    }

    /**
     * 初始化字段文件
     */
    private void initFieldFiles() {
        // 如果fieldFiles还没初始化，初始化它
        if (fieldFiles == null) {
            fieldFiles = new HashMap<>();
        }
    }

    /**
     * 确保表单视图、适配器和字段文件已经初始化
     */
    private void ensureFormComponents() {
        // 确保formViews已初始化
        if (formViews == null) {
            formViews = new HashMap<>();
        }

        // 确保imageAdapters已初始化
        if (imageAdapters == null) {
            imageAdapters = new HashMap<>();
        }

        // 确保fieldFiles已初始化
        initFieldFiles();
    }

    /**
     * 处理位置权限结果
     *
     * @param requestCode  请求码
     * @param permissions  权限
     * @param grantResults 授权结果
     */
    public void handleLocationPermissionResult(int requestCode, String[] permissions, int[] grantResults) {
        if (locationManager != null) {
            locationManager.handlePermissionResult(requestCode, permissions, grantResults);
        }
    }

    /**
     * 更新表单按钮监听器
     */
    public void updateFormButtonListeners() {
        if (buttonManager != null) {
            buttonManager.setupFormButtonListeners(pjdybh, null);
        } else if (formViews != null && allFields != null && fieldFiles != null && mediaHandler != null) {
            // 尝试重新初始化按钮管理器
            buttonManager = new FormButtonManager(activity, formViews, allFields, fieldFiles, mediaHandler);
            buttonManager.setupFormButtonListeners(pjdybh, null);
        }
    }

    /**
     * 释放资源
     * 在Activity的onDestroy方法中调用
     */
    public void release() {
        try {
            // 释放位置管理器资源
            if (locationManager != null) {
                // 释放位置管理器资源
                locationManager.release();
            }

            // 清空视图引用
            if (formViews != null) {
                formViews.clear();
            }

            // 清空图片适配器引用
            if (imageAdapters != null) {
                imageAdapters.clear();
            }

            // 其他资源清理
            formConfig = null;
            formRenderer = null;
            formProcessor = null;
            uiController = null;
            buttonManager = null;
            mediaPreviewManager = null;
            locationManager = null;
            offlineManager = null;
            localStorageManager = null;
            formService = null;
            mediaHandler = null;
        } catch (Exception e) {
            Log.e(TAG, "释放资源失败: " + e.getMessage());
        }
    }

    public Map<String, View> getFormViews() {
        return formViews;
    }

    public Map<String, List<FieldFile>> getFieldFiles() {
        return fieldFiles;
    }

    /**
     * 获取图像适配器集合
     * @return 图像适配器映射
     */
    public Map<String, ImageAdapter> getImageAdapters() {
        return imageAdapters;
    }

    public FormProcessor getFormProcessor() {
        return formProcessor;
    }

    public FormMediaPreviewManager getMediaPreviewManager() {
        Log.d(TAG, "获取媒体预览管理器");

        if (mediaPreviewManager == null) {
            Log.w(TAG, "媒体预览管理器为null，尝试重新初始化");

            try {
                // 确保必要的集合已初始化
                if (imageAdapters == null) {
                    Log.w(TAG, "imageAdapters为null，正在初始化");
                    imageAdapters = new HashMap<>();
                }

                if (fieldFiles == null) {
                    Log.w(TAG, "fieldFiles为null，正在初始化");
                    fieldFiles = new HashMap<>();
                }

                if (formViews == null) {
                    Log.w(TAG, "formViews为null，正在初始化");
                    formViews = new HashMap<>();
                }

                // 创建新的媒体预览管理器实例
                mediaPreviewManager = new FormMediaPreviewManager(activity, imageAdapters, fieldFiles, formViews);

                // 设置表单字段配置
                if (allFields != null) {
                    mediaPreviewManager.setFormFields(allFields);
                } else {
                    Log.w(TAG, "allFields为null，无法设置表单字段配置");
                }

                // 初始化预览监听器
                try {
                    mediaPreviewManager.initPreviewListeners();
                    Log.d(TAG, "已初始化媒体预览监听器");
                } catch (Exception e) {
                    Log.e(TAG, "初始化媒体预览监听器失败: " + e.getMessage(), e);
                }

                // 设置表单处理器更新监听器
                initFormProcessorUpdateListener();

                Log.d(TAG, "媒体预览管理器初始化成功");
            } catch (Exception e) {
                Log.e(TAG, "初始化媒体预览管理器失败: " + e.getMessage(), e);

                // 确保在出错时仍然返回一个有效实例
                if (mediaPreviewManager == null) {
                    mediaPreviewManager = new FormMediaPreviewManager(activity,
                            imageAdapters != null ? imageAdapters : new HashMap<>(),
                            fieldFiles != null ? fieldFiles : new HashMap<>(),
                            formViews != null ? formViews : new HashMap<>());

                    Log.w(TAG, "创建了应急媒体预览管理器实例");
                }
            }
        }

        return mediaPreviewManager;
    }

    /**
     * 获取位置管理器
     * @return 位置管理器
     */
    public FormLocationManager getLocationManager() {
        return locationManager;
    }

    private void initFormProcessorUpdateListener() {
        if (mediaPreviewManager == null) {
            Log.e(TAG, "无法初始化FormProcessor更新监听器：mediaPreviewManager为null");
            return;
        }

        try {
            mediaPreviewManager.setOnFormProcessorUpdateListener(() -> {
                Log.d(TAG, "收到文件列表变更通知，更新FormProcessor");
                if (formProcessor != null) {
                    updateFormProcessorFileListInternal();
                } else {
                    Log.w(TAG, "formProcessor为空，无法更新文件列表");
                }
            });
            Log.d(TAG, "成功设置FormProcessor更新监听器");
        } catch (Exception e) {
            Log.e(TAG, "设置FormProcessor更新监听器失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取所有表单字段配置
     *
     * @return 表单字段配置列表
     */
    public List<FormFieldConfig> getAllFields() {
        return allFields;
    }

    /**
     * 在现有fieldFiles中查找指定路径的文件
     *
     * @param fieldId 字段ID
     * @param path    文件路径
     * @return 找到的FieldFile对象，如果没找到则返回null
     */
    private FieldFile findExistingFieldFile(String fieldId, String path) {
        if (fieldFiles == null || path == null) {
            return null;
        }

        List<FieldFile> files = fieldFiles.get(fieldId);
        if (files == null || files.isEmpty()) {
            return null;
        }

        for (FieldFile file : files) {
            if (file != null && path.equals(file.getPath())) {
                return file;
            }
        }

        return null;
    }

    /**
     * 设置表单加载完成监听器
     * @param listener 监听器
     */
    public void setOnFormLoadedListener(OnFormLoadedListener listener) {
        this.onFormLoadedListener = listener;
    }

    /**
     * 应用输入验证到所有文本输入框
     */
    private void applyEditTextValidators() {
        if (formViews == null || allFields == null || formViews.isEmpty() || allFields.isEmpty()) {
            Log.d(TAG, "表单视图或字段为空，跳过应用输入验证");
            return;
        }

        Log.d(TAG, "开始为表单字段应用输入验证...");
        
        int setupCount = 0;
        
        for (FormFieldConfig field : allFields) {
            if (field == null || TextUtils.isEmpty(field.getFieldId())) {
                continue;
            }
            
            String fieldId = field.getFieldId();
            String fieldType = field.getFieldType();
            String fieldName = field.getFieldName();
            Integer maxLength = field.getMaxLength();
            Boolean required = field.isRequired();
            
            Log.d(TAG, "处理字段: id=" + fieldId + ", name=" + fieldName + 
                      ", type=" + fieldType + ", maxLength=" + maxLength + 
                      ", required=" + required);
            
            // 跳过不需要输入验证的字段类型
            if (FormFieldConfig.TYPE_PHOTO.equals(fieldType) ||
                FormFieldConfig.TYPE_VIDEO.equals(fieldType) ||
                FormFieldConfig.TYPE_FILE.equals(fieldType) ||
                FormFieldConfig.TYPE_AUDIO.equals(fieldType) ||
                FormFieldConfig.TYPE_SIGNATURE.equals(fieldType)) {
                continue;
            }
            
            // 获取字段视图
            View view = formViews.get(fieldId);
            if (view == null) {
                Log.d(TAG, "找不到字段视图: " + fieldId);
                continue;
            }
            
            // 为EditText类型的视图应用验证
            if (view instanceof android.widget.EditText) {
                FormEditTextValidator.setupEditTextValidation(activity, (android.widget.EditText) view, field);
                setupCount++;
                Log.d(TAG, "已设置EditText验证: " + fieldId);
            }
        }
        
        Log.d(TAG, "完成表单验证设置，成功设置了 " + setupCount + " 个字段");
    }
}
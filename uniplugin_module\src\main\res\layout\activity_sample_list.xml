<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#f5f5f5">

    <!-- 样点信息区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@android:color/white"
        android:elevation="2dp">

        <!-- 评价单元标识码 -->
        <TextView
            android:id="@+id/textViewSamplingPointInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="评价单元标识码: "
            android:textSize="16sp"
            android:textColor="#333333"
            android:textStyle="bold"
            android:layout_marginBottom="12dp" />

        <!-- 按钮容器 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:weightSum="2">

            <!-- 新增样品按钮 -->
            <Button
                android:id="@+id/btnAddSample"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="+ 新增样品"
                android:textColor="@android:color/white"
                android:background="#4CAF50"
                android:textSize="16sp"
                android:textStyle="bold"
                android:drawableStart="@android:drawable/ic_menu_add"
                android:drawablePadding="8dp"
                android:gravity="center" />

            <!-- 连接打印机按钮 -->
            <Button
                android:id="@+id/btnConnectPrinter"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="连接打印机"
                android:textColor="@android:color/white"
                android:background="#2196F3"
                android:textSize="14sp"
                android:textStyle="bold"
                android:drawableStart="@android:drawable/stat_sys_data_bluetooth"
                android:drawablePadding="6dp"
                android:gravity="center"
                android:ellipsize="end"
                android:maxLines="1" />

        </LinearLayout>

    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#e0e0e0" />

    <!-- 样品列表区域 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <!-- 样品列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewSamples"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="8dp"
            android:clipToPadding="false" />

        <!-- 空状态提示 -->
        <LinearLayout
            android:id="@+id/textViewEmpty"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone">

            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@android:drawable/ic_menu_gallery"
                android:alpha="0.3"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="暂无样品数据"
                android:textSize="16sp"
                android:textColor="#999999"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="点击上方新增样品按钮添加样品"
                android:textSize="14sp"
                android:textColor="#cccccc" />

        </LinearLayout>

    </FrameLayout>

    <!-- 进度条布局 -->
    <FrameLayout
        android:id="@+id/loadingLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80000000"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="48dp"
            android:text="正在加载..."
            android:textColor="#FFFFFF"
            android:textSize="16sp" />
    </FrameLayout>

</LinearLayout> 
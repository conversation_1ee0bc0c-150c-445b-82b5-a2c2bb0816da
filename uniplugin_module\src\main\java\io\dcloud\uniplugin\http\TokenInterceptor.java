package io.dcloud.uniplugin.http;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.text.TextUtils;

import java.io.IOException;

import io.dcloud.uniplugin.LoginActivity;
import io.dcloud.uniplugin.enums.SharedPreferencesEnum;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

/**
 * Token拦截器
 * 用于在请求头中添加Token
 */
public class TokenInterceptor implements Interceptor {
    
    private final android.app.Activity activity;
    
    public TokenInterceptor(android.app.Activity activity) {
        this.activity = activity;
    }
    
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request original = chain.request();
        
        // 获取请求URL
        String url = original.url().toString();
        
        // 判断是否是登录相关接口（不需要token的接口）
        boolean isLoginRelated = url.contains("system/auth/login") || 
                                url.contains("system/auth/publickey")
                || url.contains("appsc/app/version")
                ;
        
        // 如果是登录相关接口，直接发送请求
        if (isLoginRelated) {
            return chain.proceed(original);
        }

        // 获取Token
        SharedPreferences userPrefs = activity.getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, Context.MODE_PRIVATE);
        String token = userPrefs.getString(SharedPreferencesEnum.ACCESS_TOKEN.value, "");
        
        // 如果Token为空，重定向到登录页面
        if (TextUtils.isEmpty(token)) {
            // 使用Handler在主线程中执行UI操作
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    new android.app.AlertDialog.Builder(activity)
                        .setTitle("提示")
                        .setMessage("登录已过期，是否重新登录？")
                        .setPositiveButton("确定", (dialog, which) -> {
                            // 跳转到登录页面
                            Intent intent = new Intent(activity, LoginActivity.class);
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                            activity.startActivity(intent);
                        })
                        .setNegativeButton("取消", null)
                        .show();
                }
            });
            
            // 抛出异常，中断请求
            throw new IOException("登录已过期，请重新登录");
        }
        
        // 添加Token到请求头
        Request request = original.newBuilder()
                .header("Authorization", "Bearer " + token)
                .method(original.method(), original.body())
                .build();
        
        return chain.proceed(request);
    }
}
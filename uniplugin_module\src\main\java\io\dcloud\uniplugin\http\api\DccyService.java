package io.dcloud.uniplugin.http.api;

import java.util.List;

import io.dcloud.uniplugin.model.ApiResponse;
import io.dcloud.uniplugin.model.BcgdSampleYplzqd;
import io.dcloud.uniplugin.model.DccyDdcVO;
import io.dcloud.uniplugin.model.FormConfigResponse;
import io.dcloud.uniplugin.model.ProjectVo;
import io.dcloud.uniplugin.model.SoilBagDetail;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * 调查采样API服务接口
 */
public interface DccyService {

    /**
     * 获取调查人员待调查列表
     */
    @GET("pjdy/xfjl/list/dcr")
    Call<ApiResponse<List<DccyDdcVO>>> getDcrList(@Query("dccyZt") Integer dccyZt);
    
    /**
     * 获取已调查样点（list/dcr接口）
     */
    @GET("pjdy/dccy/list/dcr")
    Call<ApiResponse<List<FormConfigResponse>>> getDccyList();

    /**
     * 根据采土袋编号获取采土袋详情
     */
    @GET("pjdy/dccy/ctd/detail/ctdbh")
    Call<ApiResponse<SoilBagDetail>> getSoilBagDetail(@Query("ctdbh") String ctdbh);
    
    /**
     * 创建样品流转清单
     */
    @POST("bcgd/sample/yplzqd/create")
    Call<ApiResponse<Long>> createSample(@Body BcgdSampleYplzqd sample);

    /**
     * 获取项目列表
     */
    @GET("bcgd/project/list")
    Call<ApiResponse<List<ProjectVo>>> getProjectList();
}

<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:id="@+id/message_layout"
    android:layout_height="60dp"
    android:orientation="vertical"
    android:background="@color/toolbar_background">

 <ImageButton
     android:contentDescription="@string/polylineButtonDescription"
     style="@style/measurement_btn"
     android:id="@+id/polylineButton"
     android:src="@drawable/ic_action_polyline"
     app:layout_constraintEnd_toStartOf="@+id/polygonButton"
     tools:ignore="MissingConstraints" />
 <ImageButton
     android:contentDescription="@string/polygonButtonDescription"
     style="@style/measurement_btn"
     android:id="@+id/polygonButton"
     android:layout_toEndOf="@+id/polylineButton"
     android:layout_toRightOf="@+id/polylineButton"
     android:src="@drawable/ic_action_polygon"
     app:layout_constraintStart_toEndOf="@+id/polylineButton"
     tools:ignore="MissingConstraints" />
 <ImageButton
     style="@style/measurement_btn"
     android:id="@+id/undo"
     android:src="@drawable/ic_menu_undo"
     android:contentDescription="@string/undoButtonDescription"
     android:layout_toEndOf="@+id/polygonButton"
     android:layout_toRightOf="@+id/polygonButton"
     app:layout_constraintStart_toEndOf="@+id/polygonButton"
     tools:ignore="MissingConstraints" />
 <ImageButton
     style="@style/measurement_btn"
     android:id="@+id/redo"
     android:src="@drawable/ic_menu_redo"
     android:contentDescription="@string/redoButtonDescription"
     android:layout_toEndOf="@+id/undo"
     android:layout_toRightOf="@+id/undo"
     app:layout_constraintStart_toEndOf="@+id/undo"
     tools:ignore="MissingConstraints" />
 <!--<"@style/measurement_btn"
     android:id="@+id/stop"
     android:src="@drawable/ic_menu_stop"
     android:contentDescription="@string/stopButtonDescription"
     android:layout_toEndOf="@+id/redo"
     android:layout_toRightOf="@+id/redo"
     app:layout_constraintStart_toEndOf="@+id/redo"
     tools:ignore="MissingConstraints" />-->
 <ImageButton
     style="@style/measurement_btn"
     android:id="@+id/cancle_btn"
     android:src="@drawable/clear"
     android:contentDescription="@string/stopButtonDescription"
     android:layout_toEndOf="@+id/redo"
     android:layout_toRightOf="@+id/redo"
     app:layout_constraintStart_toEndOf="@+id/redo"
     tools:ignore="MissingConstraints" />
 <TextView
     android:layout_width="180dp"
     android:layout_height="30dp"
     android:background="@color/white"
     android:textColor="@color/black"
     android:id="@+id/result_text"
     app:layout_constraintBottom_toBottomOf="@id/message_layout"
     android:layout_marginTop="30dp"
     android:text="请开始绘制！"
     tools:ignore="MissingConstraints" />


 <androidx.constraintlayout.widget.Group
     android:id="@+id/group"
     android:layout_width="wrap_content"
     android:layout_height="wrap_content"
     android:visibility="gone"

     app:constraint_referenced_ids="polylineButton,polygonButton,undo,redo,cancle_btn,result_text"/>


</androidx.constraintlayout.widget.ConstraintLayout>

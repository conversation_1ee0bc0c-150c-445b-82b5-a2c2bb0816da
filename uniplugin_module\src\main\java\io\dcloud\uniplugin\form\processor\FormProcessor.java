package io.dcloud.uniplugin.form.processor;

import android.content.Context;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.dcloud.uniplugin.fileUpload.ImageAdapter;
import io.dcloud.uniplugin.form.SignatureView;
import io.dcloud.uniplugin.form.utils.FormUtils;
import io.dcloud.uniplugin.model.FormFieldConfig;
import io.dcloud.uniplugin.form.utils.FormValidationUtils;
import io.dcloud.uniplugin.form.processor.DropdownFieldProcessor;

/**
 * 表单处理器，负责表单数据的收集、验证和提交
 */
public class FormProcessor {
    private static final String TAG = "FormProcessor";

    private Context context;
    private Map<String, View> formViews;
    private Map<String, ImageAdapter> imageAdapters;
    private Map<String, List<String>> fieldFiles;
    private Map<String, Map<String, Object>> fileMetadata;
    private List<FormFieldConfig> fields;
    private OnFormProcessListener listener;

    /**
     * 表单处理监听器接口
     */
    public interface OnFormProcessListener {
        /**
         * 表单验证失败时回调
         * @param fieldId 字段ID
         * @param errorMessage 错误信息
         */
        void onValidationFailed(String fieldId, String errorMessage);

        /**
         * 表单提交成功时回调
         * @param formData 表单数据
         */
        void onFormSubmitted(JSONObject formData);

        /**
         * 表单处理过程中发生错误时回调
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 构造函数
     * @param context 上下文
     * @param formViews 表单视图映射
     * @param imageAdapters 图片适配器映射
     * @param fieldFiles 字段文件映射
     * @param fields 字段列表
     */
    public FormProcessor(Context context, Map<String, View> formViews,
                        Map<String, ImageAdapter> imageAdapters,
                        Map<String, List<String>> fieldFiles,
                        List<FormFieldConfig> fields) {
        this.context = context;
        this.formViews = formViews;
        this.imageAdapters = imageAdapters;
        this.fieldFiles = fieldFiles;
        this.fields = fields;
        this.fileMetadata = new HashMap<>();
    }

    /**
     * 设置表单处理监听器
     * @param listener 监听器
     */
    public void setOnFormProcessListener(OnFormProcessListener listener) {
        this.listener = listener;
    }

    /**
     * 处理表单，验证并收集表单数据
     *
     * @return 表单数据，如果验证失败则返回null
     */
    public JSONObject processForm() {
        Log.d(TAG, "开始处理表单");
        
        // 收集表单数据
        JSONObject formData = collectFormData();
        
        // 验证所有字段
        boolean validationPassed = true;
        
        for (FormFieldConfig field : fields) {
            if (field == null) continue;
            
            String fieldId = field.getFieldId();
            String fieldType = field.getFieldType();
            
            // 检查是否为媒体文件类型字段
            boolean isMediaField = FormFieldConfig.TYPE_PHOTO.equals(fieldType) || 
                                   FormFieldConfig.TYPE_FILE.equals(fieldType) ||
                                   FormFieldConfig.TYPE_VIDEO.equals(fieldType) ||
                                   FormFieldConfig.TYPE_SIGNATURE.equals(fieldType);
            
            String value = null;
            
            if (isMediaField) {
                // 对于媒体字段，从 fieldFiles 中获取值
                List<String> files = fieldFiles.get(fieldId);
                if (files != null && !files.isEmpty()) {
                    try {
                        // 检查是否是签名字段
                        if (FormFieldConfig.TYPE_SIGNATURE.equals(fieldType)) {
                            // 签名字段通常只有一个文件，直接返回路径
                            value = files.get(0);
                            Log.d(TAG, "获取签名字段 " + fieldId + " 的文件路径: " + value);
                        } else {
                            // 将文件列表转换为JSON数组字符串
                            JSONArray fileArray = new JSONArray();
                            for (String file : files) {
                                fileArray.put(file);
                            }
                            value = fileArray.toString();
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "构建文件列表JSON时出错: " + e.getMessage());
                    }
                } else {
                    // 特别记录签名字段缺少文件的情况
                    if (FormFieldConfig.TYPE_SIGNATURE.equals(fieldType)) {
                        Log.w(TAG, "签名字段 " + fieldId + " 没有关联的文件");
                    }
                }
            } else {
                // 对于非媒体字段，从 formData 中获取值
                if (formData.has(fieldId)) {
                    try {
                        value = formData.getString(fieldId);
                    } catch (JSONException e) {
                        Log.e(TAG, "获取字段值时出错: " + e.getMessage());
                    }
                }
            }
            
            // 使用验证方法验证字段
            if (!validateField(field, fieldId, value)) {
                validationPassed = false;
                // 继续验证其他字段，以便一次性显示所有错误
            }
        }
        
        // 如果验证未通过，返回 null
        if (!validationPassed) {
            Log.d(TAG, "表单验证未通过");
            return null;
        }
        
        // 所有验证都通过，触发表单提交回调
        if (listener != null) {
            listener.onFormSubmitted(formData);
        }
        
        Log.d(TAG, "表单处理完成，数据: " + formData.toString());
        return formData;
    }

    /**
     * 验证表单
     *
     * @return 验证结果
     */
    private boolean validateForm() {
        // 验证所有必填字段
        for (FormFieldConfig field : fields) {
            String fieldId = field.getFieldId();
            
            // 检查必填字段
            if (Boolean.TRUE.equals(field.isRequired())) {
                View fieldView = formViews.get(fieldId);
                if (fieldView == null) {
                    continue; // 找不到字段视图，跳过验证
                }
                
                String fieldValue = getFieldValue(fieldId);
                
                // 检查字段值是否为空
                if (TextUtils.isEmpty(fieldValue)) {
                    // 字段为空，触发验证失败回调
                    if (listener != null) {
                        String errorMessage = field.getLabel() + "为必填项";
                        listener.onValidationFailed(fieldId, errorMessage);
                    }
                    return false;
                }
            }
        }
        
        return true; // 所有验证都通过
    }

    /**
     * 收集表单数据
     * @return 表单数据JSON对象
     */
    @NonNull
    public JSONObject collectFormData() {
        JSONObject formData = new JSONObject();

        try {
            for (FormFieldConfig field : fields) {
                if (field == null) {
                    continue;
                }

                String fieldId = field.getFieldId();
                
                // 跳过没有ID的字段
                if (TextUtils.isEmpty(fieldId)) {
                    continue;
                }

                // 获取字段视图
                View fieldView = formViews.get(fieldId);
                if (fieldView == null) {
                    // 找不到视图，跳过
                    continue;
                }

                // 获取字段值
                Object value = getFieldValue(field, fieldView);
                
                // 添加到表单数据（对于媒体文件字段，不再添加到根级别）
                if (value != null) {
                    String fieldType = field.getFieldType();
                    boolean isMediaField = FormFieldConfig.TYPE_PHOTO.equals(fieldType) || 
                                          FormFieldConfig.TYPE_FILE.equals(fieldType) ||
                                          FormFieldConfig.TYPE_VIDEO.equals(fieldType) ||
                                          FormFieldConfig.TYPE_SIGNATURE.equals(fieldType);
                    
                    // 不将媒体文件路径添加到根级别，而是依赖__files对象
                    if (!isMediaField) {
                        formData.put(fieldId, value);
                    } else {
                        Log.d(TAG, "跳过将媒体字段[" + fieldId + "]添加到表单数据根级别，将使用__files对象");
                    }
                }
            }
        } catch (JSONException e) {
            Log.e(TAG, "收集表单数据出错: " + e.getMessage(), e);
            return new JSONObject();
        }

        return formData;
    }

    /**
     * 获取字段值
     * @param field 字段配置
     * @param fieldView 字段视图
     * @return 字段值（注意：虽然媒体字段值不会添加到表单数据根级别，但仍然需要返回值用于验证等其他目的）
     */
    private Object getFieldValue(FormFieldConfig field, View fieldView) {
        String fieldType = field.getFieldType();
        String fieldId = field.getFieldId();

        try {
            if (FormFieldConfig.TYPE_TEXT.equals(fieldType) || 
                FormFieldConfig.TYPE_TEXTAREA.equals(fieldType) || 
                FormFieldConfig.TYPE_DATE.equals(fieldType)) {
                
                // 文本类型输入
                if (fieldView instanceof EditText) {
                    EditText editText = (EditText) fieldView;
                    return editText.getText().toString().trim();
                }
            } else if (FormFieldConfig.TYPE_NUMBER.equals(fieldType)) {
                // 数字类型输入
                if (fieldView instanceof EditText) {
                    EditText editText = (EditText) fieldView;
                    String text = editText.getText().toString().trim();
                    if (!TextUtils.isEmpty(text)) {
                        try {
                            return Double.parseDouble(text);
                        } catch (NumberFormatException e) {
                            return text;
                        }
                    }
                }
            } else if (FormFieldConfig.TYPE_DROPDOWN.equals(fieldType)) {
                // 下拉框选择
                if (fieldView instanceof Spinner || fieldView instanceof LinearLayout) {
                    // 使用DropdownFieldProcessor获取值，它会处理"其他"选项
                    return DropdownFieldProcessor.getSelectedValue(fieldView, field);
                }
            } else if (FormFieldConfig.TYPE_PHOTO.equals(fieldType) || 
                    FormFieldConfig.TYPE_FILE.equals(fieldType)) {
                // 文件上传类型
                List<String> files = fieldFiles.get(fieldId);
                if (files != null && !files.isEmpty()) {
                    // 单文件返回字符串，多文件返回数组
                    if (files.size() == 1) {
                        return files.get(0);
                    } else {
                        JSONArray fileArray = new JSONArray();
                        for (String file : files) {
                            fileArray.put(file);
                        }
                        return fileArray;
                    }
                }
            } else if (FormFieldConfig.TYPE_LOCATION.equals(fieldType)) {
                // 位置类型
                TextView textView = fieldView.findViewById(uni.dcloud.io.uniplugin_module.R.id.locationText);
                if (textView != null) {
                    return textView.getText().toString().trim();
                }
            } else if (FormFieldConfig.TYPE_SIGNATURE.equals(fieldType)) {
                // 签名类型
                // 首先检查 fieldFiles 中是否已有签名文件
                List<String> files = fieldFiles.get(fieldId);
                if (files != null && !files.isEmpty()) {
                    Log.d(TAG, "签名字段 " + fieldId + " 已有保存的文件，返回现有文件路径");
                    return files.get(0);
                }
                
                // 如果没有现有文件，尝试从签名视图获取新签名
                SignatureView signatureView = fieldView.findViewById(uni.dcloud.io.uniplugin_module.R.id.signatureView);
                if (signatureView != null && signatureView.isSigned()) {
                    Bitmap bitmap = signatureView.getSignatureBitmap();
                    if (bitmap != null) {
                        // 保存签名图片到文件
                        String filePath = FormUtils.saveSignatureImage(context, bitmap, fieldId);
                        if (!TextUtils.isEmpty(filePath)) {
                            Log.d(TAG, "签名字段 " + fieldId + " 创建了新的签名文件: " + filePath);
                            return filePath;
                        }
                    }
                } else {
                    Log.d(TAG, "签名字段 " + fieldId + " 没有签名或签名视图为空");
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "获取字段值出错: " + fieldId + ", " + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 更新字段文件列表
     * @param updatedFieldFiles 更新的字段文件列表
     */
    public void updateFieldFiles(Map<String, List<String>> updatedFieldFiles) {
        if (updatedFieldFiles == null) {
            Log.w(TAG, "传入的文件列表为空，保持原有文件列表");
            return;
        }
        
        Log.d(TAG, "更新文件列表: 从 " + (fieldFiles != null ? fieldFiles.size() : 0) + " 个字段 到 " + updatedFieldFiles.size() + " 个字段");
        
        // 完全替换文件列表而不是合并
        fieldFiles = new HashMap<>();
        fieldFiles.putAll(updatedFieldFiles);
        
        // 打印每个字段的文件数量，便于调试
        StringBuilder logBuilder = new StringBuilder("更新后的文件映射:\n");
        for (Map.Entry<String, List<String>> entry : fieldFiles.entrySet()) {
            String fieldId = entry.getKey();
            List<String> files = entry.getValue();
            
            logBuilder.append("  字段[").append(fieldId).append("]: ")
                     .append(files == null ? 0 : files.size()).append("个文件\n");
            
            if (files != null && !files.isEmpty()) {
                for (int i = 0; i < files.size(); i++) {
                    logBuilder.append("    - 文件").append(i + 1).append(": ")
                             .append(files.get(i)).append("\n");
                }
            }
        }
        
        Log.d(TAG, logBuilder.toString());
    }

    /**
     * 更新字段文件列表及其元数据（包括位置信息）
     * @param updatedFieldFiles 更新的字段文件列表
     * @param metadata 文件元数据，包含位置信息等
     */
    public void updateFieldFiles(Map<String, List<String>> updatedFieldFiles, Map<String, Map<String, Object>> metadata) {
        // 先调用基本的updateFieldFiles方法
        updateFieldFiles(updatedFieldFiles);
        
        // 如果有元数据，更新元数据映射
        if (metadata != null) {
            // 清除旧的元数据并添加新的
            fileMetadata.clear();
            fileMetadata.putAll(metadata);
            
            Log.d(TAG, "更新文件元数据: " + fileMetadata.size() + " 个字段");
            
            // 打印元数据信息
            for (Map.Entry<String, Map<String, Object>> entry : fileMetadata.entrySet()) {
                String fieldId = entry.getKey();
                Map<String, Object> meta = entry.getValue();
                
                Log.d(TAG, "字段 " + fieldId + " 的元数据: " + meta);
            }
        }
    }

    /**
     * 获取文件元数据
     * @param fieldId 字段ID
     * @param filePath 文件路径
     * @return 元数据映射
     */
    public Map<String, Object> getFileMetadata(String fieldId, String filePath) {
        if (fileMetadata != null && fileMetadata.containsKey(fieldId)) {
            Map<String, Object> fieldMeta = fileMetadata.get(fieldId);
            if (fieldMeta != null && fieldMeta.containsKey(filePath)) {
                return (Map<String, Object>) fieldMeta.get(filePath);
            }
        }
        return null;
    }

    /**
     * 验证字段值
     *
     * @param field    字段配置
     * @param fieldId  字段ID
     * @param value    字段值
     * @return 验证结果，true为通过验证，false为未通过验证
     */
    private boolean validateField(FormFieldConfig field, String fieldId, String value) {
        if (field == null) {
            return true; // 如果字段配置为空，默认验证通过
        }

        // 使用FormValidationUtils进行校验
        String errorMessage = FormValidationUtils.validateField(field, value);
        
        if (errorMessage != null) {
            // 校验失败，触发回调
            if (listener != null) {
                listener.onValidationFailed(fieldId, errorMessage);
            }
            return false;
        }
        
        return true; // 验证通过
    }

    /**
     * 获取字段值（通过字段ID）
     * @param fieldId 字段ID
     * @return 字段值字符串
     */
    public String getFieldValue(String fieldId) {
        // 查找对应的字段配置
        FormFieldConfig field = findFieldById(fieldId);
        if (field == null) {
            return "";
        }
        
        // 获取字段视图
        View fieldView = formViews.get(fieldId);
        if (fieldView == null) {
            return "";
        }
        
        // 调用多参数版本的getFieldValue方法
        Object value = getFieldValue(field, fieldView);
        
        // 将值转换为字符串
        return value != null ? value.toString() : "";
    }
    
    /**
     * 根据ID查找字段配置
     * @param fieldId 字段ID
     * @return 字段配置，如果找不到则返回null
     */
    private FormFieldConfig findFieldById(String fieldId) {
        if (fields == null || TextUtils.isEmpty(fieldId)) {
            return null;
        }
        
        for (FormFieldConfig field : fields) {
            if (field != null && fieldId.equals(field.getFieldId())) {
                return field;
            }
        }
        
        return null;
    }
} 
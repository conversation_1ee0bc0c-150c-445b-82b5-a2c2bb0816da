package io.dcloud.uniplugin.sample;

import android.app.AlertDialog;
import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import io.dcloud.uniplugin.http.RetrofitManager;
import io.dcloud.uniplugin.model.ApiResponse;
import io.dcloud.uniplugin.model.Sample;
import io.dcloud.uniplugin.utils.PrinterHelper;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import uni.dcloud.io.uniplugin_module.R;

/**
 * 采土袋管理
 */
public class SampleListActivity extends AppCompatActivity implements SampleListAdapter.OnSampleActionListener, PrinterHelper.PrintCallback {

    private static final String TAG = "SampleListActivity";
    public static final String EXTRA_PJDY_BSM = "pjdybh";
    public static final String EXTRA_PJDY_ID = "pjdyId";
    public static final String EXTRA_XFJL_ID = "xfjlId";


    private String pjdybh;
    private Long pjdyId;
    private Long xfjlId;
    private TextView textViewSamplingPointInfo;
    private Button btnAddSample;
    private Button btnConnectPrinter;
    private RecyclerView recyclerViewSamples;
    private LinearLayout textViewEmpty;
    private FrameLayout loadingLayout;

    private SampleListAdapter adapter;
    private List<Sample> sampleList = new ArrayList<>();
    private PrinterHelper printerHelper;
    
    // 网络请求相关变量
    private boolean isRequestingData = false; // 避免重复请求

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sample_list);

        // 设置ActionBar的返回按钮
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setTitle("采土袋管理");
        }

        // 获取传递的评价单元ID和标识码
        pjdyId = getIntent().getLongExtra(EXTRA_PJDY_ID, 0L);
        pjdyId = getIntent().getLongExtra(EXTRA_PJDY_ID, 0L);
        xfjlId = getIntent().getLongExtra(EXTRA_XFJL_ID, 0L);
//        Log.i(TAG, "onCreate:下发记录ID:"+ xfjlId);
        pjdybh = getIntent().getStringExtra(EXTRA_PJDY_BSM);
        
        // 检查评价单元ID是否有效
        if (pjdyId == 0L) {
            Toast.makeText(this, "评价单元ID无效", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }
        
        // 如果标识码为空，设置一个默认值用于显示
        if (pjdybh == null || pjdybh.isEmpty()) {
            pjdybh = "ID: " + pjdyId;
        }

        // 初始化视图
        initViews();

        // 初始化打印机工具
        initPrinterHelper();

        // 设置RecyclerView
        setupRecyclerView();

        // 加载数据
        loadSamples();

        Log.d(TAG, "采土袋管理页面启动，样点BSM: " + pjdybh);
        
        // 添加ActionBar菜单项
        ActionBar ab = getSupportActionBar();
        if (ab != null) {
            ab.setDisplayHomeAsUpEnabled(true);
            ab.setDisplayShowTitleEnabled(true);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 释放打印机资源
        if (printerHelper != null) {
            printerHelper.destroy();
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        } else if (item.getItemId() == 1) {
            // 显示搜索对话框
            showSearchDialog();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public boolean onCreateOptionsMenu(android.view.Menu menu) {
        // 添加搜索菜单
        menu.add(0, 1, 0, "搜索采土袋")
            .setIcon(android.R.drawable.ic_menu_search)
            .setShowAsAction(android.view.MenuItem.SHOW_AS_ACTION_ALWAYS);
        return true;
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        textViewSamplingPointInfo = findViewById(R.id.textViewSamplingPointInfo);
        btnAddSample = findViewById(R.id.btnAddSample);
        btnConnectPrinter = findViewById(R.id.btnConnectPrinter);
        recyclerViewSamples = findViewById(R.id.recyclerViewSamples);
        textViewEmpty = findViewById(R.id.textViewEmpty);
        loadingLayout = findViewById(R.id.loadingLayout);

        // 设置评价单元标识码信息
        textViewSamplingPointInfo.setText("评价单元标识码: " + pjdybh);

        // 设置新增按钮点击事件
        btnAddSample.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showAddSampleDialog();
            }
        });

        // 设置连接打印机按钮点击事件
        btnConnectPrinter.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                printerHelper.showPrinterSelectionDialog();
            }
        });
    }

    /**
     * 初始化打印机辅助工具
     */
    private void initPrinterHelper() {
        printerHelper = new PrinterHelper(this);
        printerHelper.setPrintCallback(this);
        // 不进行任何自动打印机检查，仅在需要打印时检查
    }

    /**
     * 更新打印机状态显示
     */
    private void updatePrinterStatus() {
        if (btnConnectPrinter != null) {
            // 检查打印机是否已连接
            if (printerHelper.isPrinterConnected()) {
                String printerInfo = printerHelper.getPrinterInfo();
                btnConnectPrinter.setText(printerInfo);
            } else {
                btnConnectPrinter.setText("连接打印机");
            }
        }
    }

    /**
     * 设置RecyclerView
     */
    private void setupRecyclerView() {
        adapter = new SampleListAdapter(this, sampleList);
        adapter.setOnSampleActionListener(this);
        recyclerViewSamples.setLayoutManager(new LinearLayoutManager(this));
        recyclerViewSamples.setAdapter(adapter);
    }

    /**
     * 加载样品数据
     */
    private void loadSamples() {
        if (isRequestingData) {
            return; // 避免重复请求
        }
        
        // 显示加载提示
        showLoading(true);
        isRequestingData = true;
        
        Log.d(TAG, "开始加载采土袋数据，评价单元ID: " + pjdyId);
        
        // 调用API获取数据，使用评价单元ID而非标识码
        RetrofitManager.getInstance(this)
            .getSampleService()
            .getSamplesByPjdyId(String.valueOf(pjdyId))
            .enqueue(new Callback<ApiResponse<List<Sample>>>() {
                @Override
                public void onResponse(Call<ApiResponse<List<Sample>>> call, Response<ApiResponse<List<Sample>>> response) {
                    isRequestingData = false;
                    showLoading(false);
                    
                    if (response.isSuccessful() && response.body() != null) {
                        ApiResponse<List<Sample>> result = response.body();
                        Log.d(TAG, "API响应: code=" + result.getCode() + ", msg=" + result.getMsg());
                        
                        if (result.isSuccess() && result.getData() != null) {
                            // 数据获取成功
                            List<Sample> samples = result.getData();
                            Log.d(TAG, "API返回样品数量: " + samples.size());
                            
                            // 输出原始样品数据
                            for (int i = 0; i < samples.size(); i++) {
                                Sample sample = samples.get(i);
                                Log.d(TAG, "原始样品[" + i + "]: id=" + sample.getId() + 
                                          ", pjdybh=" + sample.getpjdybh() + 
                                          ", sampleNumber/ctdbh=" + sample.getSampleNumber() + 
                                          ", sampleType/yplx=" + sample.getSampleType() + 
                                          ", sampleTypeName=" + sample.getSampleTypeName() + 
                                          ", sampleWeight/ypzl=" + sample.getSampleWeight() + 
                                          ", createTime=" + sample.getCreateTime());
                            }
                            
                            // 处理样品数据，确保样品类型名称正确
                            for (Sample sample : samples) {
                                // 如果样品类型名称为空，根据样品类型代码设置
                                if (sample.getSampleTypeName() == null || sample.getSampleTypeName().isEmpty()) {
                                    Sample.SampleType sampleType = Sample.SampleType.fromCode(sample.getSampleType());
                                    if (sampleType != null) {
                                        sample.setSampleTypeName(sampleType.getName());
                                    } else {
                                        sample.setSampleTypeName("未知类型");
                                    }
                                }
                                
                                // 如果样品编号为空但ctdbh有值，确保设置
                                if ((sample.getSampleNumber() == null || sample.getSampleNumber().isEmpty()) && 
                                    sample.getSampleCode() != null && !sample.getSampleCode().isEmpty()) {
                                    sample.setSampleNumber(sample.getSampleCode());
                                }
                                
                                // 日志输出样品信息，用于调试
                                Log.d(TAG, "处理后的样品: ID=" + sample.getId() + 
                                          ", 编号=" + sample.getSampleNumber() + 
                                          ", 类型=" + sample.getSampleType() + 
                                          ", 类型名称=" + sample.getSampleTypeName() + 
                                          ", 重量=" + sample.getSampleWeight());
                            }
                            
                            // 更新界面
                            runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    sampleList.clear();
                                    sampleList.addAll(samples);
                                    adapter.updateData(sampleList);
                                    updateUI();
                                    
                                    Log.d(TAG, "从API加载样品数据完成，共 " + samples.size() + " 条");
                                }
                            });
                        } else {
                            // 服务器返回错误
                            String errorMsg = result.getMsg();
                            if (errorMsg == null || errorMsg.isEmpty()) {
                                errorMsg = "获取样品数据失败";
                            }
                            showError(errorMsg);
                            Log.e(TAG, "API错误: " + errorMsg);
                        }
                    } else {
                        // HTTP请求失败
                        String errorMsg = "服务器请求失败，请检查网络连接";
                        showError(errorMsg);
                        Log.e(TAG, "HTTP请求失败: " + response.code() + ", " + response.message());
                    }
                }

                @Override
                public void onFailure(Call<ApiResponse<List<Sample>>> call, Throwable t) {
                    isRequestingData = false;
                    showLoading(false);
                    
                    // 网络请求失败
                    Log.e(TAG, "API请求失败: " + t.getMessage(), t);
                    showError("网络连接失败: " + t.getMessage());
                }
            });
    }

    /**
     * 显示/隐藏加载提示
     */
    private void showLoading(boolean isLoading) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (loadingLayout != null) {
                    loadingLayout.setVisibility(isLoading ? View.VISIBLE : View.GONE);
                }
            }
        });
    }
    
    /**
     * 显示错误提示
     */
    private void showError(String errorMessage) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                Toast.makeText(SampleListActivity.this, errorMessage, Toast.LENGTH_SHORT).show();
            }
        });
    }

    /**
     * 更新UI显示
     */
    private void updateUI() {
        if (sampleList.isEmpty()) {
            recyclerViewSamples.setVisibility(View.GONE);
            textViewEmpty.setVisibility(View.VISIBLE);
        } else {
            recyclerViewSamples.setVisibility(View.VISIBLE);
            textViewEmpty.setVisibility(View.GONE);
        }
    }

    /**
     * 显示新增样品对话框
     */
    private void showAddSampleDialog() {
        View dialogView = getLayoutInflater().inflate(R.layout.dialog_add_sample, null);

        Spinner spinnerSampleType = dialogView.findViewById(R.id.spinnerSampleType);
        EditText editTextSampleWeight = dialogView.findViewById(R.id.editTextSampleWeight);
        EditText editTextBsm = dialogView.findViewById(R.id.editTextBsm);
        EditText editTextSampleCode = dialogView.findViewById(R.id.editTextSampleCode);
        Button btnCancel = dialogView.findViewById(R.id.btnCancel);
        Button btnConfirm = dialogView.findViewById(R.id.btnConfirm);

        // 自动设置评价单元标识码并设为不可编辑
        editTextBsm.setText(pjdybh);
        editTextBsm.setEnabled(false);

        // 设置样品类型选择器
        List<String> sampleTypeNames = new ArrayList<>();
        List<Sample.SampleType> availableTypes = new ArrayList<>();

        for (Sample.SampleType type : Sample.SampleType.values()) {
            // 检查该类型是否已经存在于当前列表
            boolean exists = false;
            for (Sample sample : sampleList) {
                if (sample.getSampleType() == type.getCode()) {
                    exists = true;
                    break;
                }
            }
            
            if (!exists) {
                sampleTypeNames.add(type.getName());
                availableTypes.add(type);
            }
        }

        if (availableTypes.isEmpty()) {
            Toast.makeText(this, "所有样品类型都已添加", Toast.LENGTH_SHORT).show();
            return;
        }

        ArrayAdapter<String> typeAdapter = new ArrayAdapter<>(this, 
                android.R.layout.simple_spinner_item, sampleTypeNames);
        typeAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerSampleType.setAdapter(typeAdapter);

        // 监听样品类型选择变化，更新样品编号并设置为不可编辑
        spinnerSampleType.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position >= 0 && position < availableTypes.size()) {
                    Sample.SampleType selectedType = availableTypes.get(position);
                    String sampleNumber = Sample.generateSampleNumber(pjdybh, selectedType);
                    editTextSampleCode.setText(sampleNumber);
                    editTextSampleCode.setEnabled(false); // 设置样品编号为不可编辑
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                editTextSampleCode.setText("");
            }
        });

        // 创建对话框
        AlertDialog dialog = new AlertDialog.Builder(this)
                .setView(dialogView)
                .create();

        // 取消按钮
        btnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });

        // 确定按钮
        btnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                int selectedPosition = spinnerSampleType.getSelectedItemPosition();
                String weightStr = editTextSampleWeight.getText().toString().trim();
                String sampleCode = editTextBsm.getText().toString().trim();
                String sampleNumber = editTextSampleCode.getText().toString().trim();

                // 验证输入
                if (selectedPosition < 0) {
                    Toast.makeText(SampleListActivity.this, "请选择样品类型", Toast.LENGTH_SHORT).show();
                    return;
                }

                if (weightStr.isEmpty()) {
                    Toast.makeText(SampleListActivity.this, "请输入样品重量", Toast.LENGTH_SHORT).show();
                    return;
                }

                if (sampleNumber.isEmpty()) {
                    Toast.makeText(SampleListActivity.this, "请输入样品编号", Toast.LENGTH_SHORT).show();
                    return;
                }

                try {
                    double weight = Double.parseDouble(weightStr);
                    if (weight <= 0) {
                        Toast.makeText(SampleListActivity.this, "样品重量必须大于0", Toast.LENGTH_SHORT).show();
                        return;
                    }

                    Sample.SampleType selectedType = availableTypes.get(selectedPosition);
                    Sample sample = new Sample(pjdybh, selectedType, weight, sampleCode);
                    // 设置评价单元ID
                    sample.setPjdyId(pjdyId);
                    sample.setSampleNumber(sampleNumber); // 设置手动输入的样品编号

                    // 保存样品到服务器
                    addSampleToServer(sample, dialog);

                } catch (NumberFormatException e) {
                    Toast.makeText(SampleListActivity.this, "请输入有效的重量数值", Toast.LENGTH_SHORT).show();
                }
            }
        });

        dialog.show();
    }

    /**
     * 添加样品到服务器
     */
    private void addSampleToServer(Sample sample, AlertDialog dialog) {
        showLoading(true);

        // 准备请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("pjdyId", pjdyId);           // 评价单元ID
        params.put("xfjlId", xfjlId);           //下发记录ID
        params.put("pjdybh", sample.getpjdybh());   // 评价单元标识码
        params.put("ctdbh", sample.getSampleNumber());  // 采土袋编号
        params.put("yplx", sample.getSampleType());  // 样品类型
        params.put("ypzl", sample.getSampleWeight()); // 样品重量

        // 调用API添加样品
        RetrofitManager.getInstance(this)
            .getSampleService()
            .addSample(params)
            .enqueue(new Callback<ApiResponse<Boolean>>() {
                @Override
                public void onResponse(Call<ApiResponse<Boolean>> call, Response<ApiResponse<Boolean>> response) {
                    showLoading(false);

                    if (response.isSuccessful() && response.body() != null) {
                        ApiResponse<Boolean> result = response.body();
                        if (result.getCode() == 0) { // 只检查code为0判断成功，不检查data字段
                            // 成功添加样品
                            runOnUiThread(() -> {
                                if (dialog != null) {
                                    dialog.dismiss();
                                }
                                
                                // 重新加载数据
                                loadSamples();

                                Toast.makeText(SampleListActivity.this, "样品添加成功", Toast.LENGTH_SHORT).show();
                                Log.d(TAG, "样品添加成功: " + sample.getSampleNumber());
                            });
                        } else {
                            // 服务器返回错误
                            String errorMsg = result.getMsg();
                            if (errorMsg == null || errorMsg.isEmpty()) {
                                errorMsg = "添加样品失败";
                            }
                            final String finalErrorMsg = errorMsg;
                            runOnUiThread(() -> {
                                showError(finalErrorMsg);
                            });
                        }
                    } else {
                        // HTTP请求失败
                        runOnUiThread(() -> {
                            showError("服务器请求失败，请检查网络连接");
                        });
                    }
                }

                @Override
                public void onFailure(Call<ApiResponse<Boolean>> call, Throwable t) {
                    showLoading(false);
                    Log.e(TAG, "添加样品API请求失败: " + t.getMessage(), t);
                    runOnUiThread(() -> {
                        showError("网络连接失败: " + t.getMessage());
                    });
                }
            });
    }

    /**
     * 更新样品到服务器
     */
    private void updateSampleToServer(Sample sample, AlertDialog dialog) {
        showLoading(true);

        // 准备请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("id", sample.getId());     // 样品ID
        params.put("pjdyId", pjdyId);         // 评价单元ID
        params.put("pjdybh", sample.getpjdybh()); // 评价单元标识码
        params.put("ctdbh", sample.getSampleNumber());  // 采土袋编号
        params.put("yplx", sample.getSampleType());  // 样品类型
        params.put("ypzl", sample.getSampleWeight()); // 样品重量

        // 调用API更新样品
        RetrofitManager.getInstance(this)
            .getSampleService()
            .updateSample(params)
            .enqueue(new Callback<ApiResponse<Void>>() {
                @Override
                public void onResponse(Call<ApiResponse<Void>> call, Response<ApiResponse<Void>> response) {
                    showLoading(false);

                    if (response.isSuccessful() && response.body() != null) {
                        ApiResponse<Void> responseBody = response.body();
                        if (responseBody.getCode() == 0) { // 只检查code为0判断成功，不检查data字段
                            // 成功更新样品
                            runOnUiThread(() -> {
                                if (dialog != null) {
                                    dialog.dismiss();
                                }
                                
                                // 重新加载数据
                                loadSamples();

                                Toast.makeText(SampleListActivity.this, "样品更新成功", Toast.LENGTH_SHORT).show();
                                Log.d(TAG, "样品更新成功: " + sample.getSampleNumber());
                            });
                        } else {
                            // 服务器返回错误
                            String errorMsg = responseBody.getMsg();
                            if (errorMsg == null || errorMsg.isEmpty()) {
                                errorMsg = "更新样品失败";
                            }
                            final String finalErrorMsg = errorMsg;
                            runOnUiThread(() -> {
                                showError(finalErrorMsg);
                            });
                        }
                    } else {
                        // HTTP请求失败
                        runOnUiThread(() -> {
                            showError("服务器请求失败，请检查网络连接");
                        });
                    }
                }

                @Override
                public void onFailure(Call<ApiResponse<Void>> call, Throwable t) {
                    showLoading(false);
                    Log.e(TAG, "更新样品API请求失败: " + t.getMessage(), t);
                    runOnUiThread(() -> {
                        showError("网络连接失败: " + t.getMessage());
                    });
                }
            });
    }

    /**
     * 删除样品
     */
    private void deleteSampleFromServer(Sample sample) {
        showLoading(true);

        // 调用API删除样品
        RetrofitManager.getInstance(this)
            .getSampleService()
            .deleteSample(sample.getId())
            .enqueue(new Callback<ApiResponse<Void>>() {
                @Override
                public void onResponse(Call<ApiResponse<Void>> call, Response<ApiResponse<Void>> response) {
                    showLoading(false);

                    if (response.isSuccessful() && response.body() != null) {
                        ApiResponse<Void> result = response.body();
                        if (result.getCode() == 0) { // 只检查code为0判断成功
                            // 成功删除样品
                            runOnUiThread(() -> {
                                // 重新加载数据
                                loadSamples();

                                Toast.makeText(SampleListActivity.this, "样品删除成功", Toast.LENGTH_SHORT).show();
                                Log.d(TAG, "样品删除成功: " + sample.getSampleNumber());
                            });
                        } else {
                            // 服务器返回错误
                            String errorMsg = result.getMsg();
                            if (errorMsg == null || errorMsg.isEmpty()) {
                                errorMsg = "删除样品失败";
                            }
                            final String finalErrorMsg = errorMsg;
                            runOnUiThread(() -> {
                                showError(finalErrorMsg);
                            });
                        }
                    } else {
                        // HTTP请求失败
                        runOnUiThread(() -> {
                            showError("服务器请求失败，请检查网络连接");
                        });
                    }
                }

                @Override
                public void onFailure(Call<ApiResponse<Void>> call, Throwable t) {
                    showLoading(false);
                    Log.e(TAG, "删除样品API请求失败: " + t.getMessage(), t);
                    runOnUiThread(() -> {
                        showError("网络连接失败: " + t.getMessage());
                    });
                }
            });
    }

    /**
     * 根据采土袋编号获取采土袋信息
     */
    private void getSampleDetail(String ctdbh) {
        showLoading(true);

        // 调用API获取样品详情
        RetrofitManager.getInstance(this)
            .getSampleService()
            .getSampleDetail(ctdbh)
            .enqueue(new Callback<ApiResponse<Sample>>() {
                @Override
                public void onResponse(Call<ApiResponse<Sample>> call, Response<ApiResponse<Sample>> response) {
                    showLoading(false);

                    if (response.isSuccessful() && response.body() != null) {
                        ApiResponse<Sample> result = response.body();
                        if (result.isSuccess() && result.getData() != null) {
                            // 成功获取样品详情
                            Sample sample = result.getData();

                            // 显示样品详情对话框
                            runOnUiThread(() -> {
                                showSampleDetailDialog(sample);
                            });
                        } else {
                            // 服务器返回错误
                            String errorMsg = result.getMsg();
                            if (errorMsg == null || errorMsg.isEmpty()) {
                                errorMsg = "获取样品详情失败";
                            }
                            final String finalErrorMsg = errorMsg;
                            runOnUiThread(() -> {
                                showError(finalErrorMsg);
                            });
                        }
                    } else {
                        // HTTP请求失败
                        runOnUiThread(() -> {
                            showError("服务器请求失败，请检查网络连接");
                        });
                    }
                }

                @Override
                public void onFailure(Call<ApiResponse<Sample>> call, Throwable t) {
                    showLoading(false);
                    Log.e(TAG, "获取样品详情API请求失败: " + t.getMessage(), t);
                    runOnUiThread(() -> {
                        showError("网络连接失败: " + t.getMessage());
                    });
                }
            });
    }

    /**
     * 显示样品详情对话框
     */
    private void showSampleDetailDialog(Sample sample) {
        if (sample == null) {
            return;
        }

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("样品详情");

        // 构建详情信息
        StringBuilder detailInfo = new StringBuilder();
        detailInfo.append("样品编号: ").append(sample.getSampleNumber()).append("\n\n");
        detailInfo.append("样品类型: ").append(sample.getSampleTypeName()).append("\n\n");
        detailInfo.append("样品重量: ").append(sample.getSampleWeight()).append("g\n\n");
        detailInfo.append("评价单元标识码: ").append(sample.getpjdybh()).append("\n\n");
        if (sample.getCreateTime() != null && !sample.getCreateTime().isEmpty()) {
            detailInfo.append("创建时间: ").append(sample.getCreateTime()).append("\n\n");
        }

        builder.setMessage(detailInfo.toString());
        builder.setPositiveButton("确定", null);
        builder.show();
    }

    @Override
    public void onPrintSample(Sample sample) {
        // 打印样品标签功能 - 修改为直接打印
        Log.d(TAG, "打印样品标签: " + sample.getSampleNumber());

        // 检查打印机连接状态
        if (!printerHelper.isPrinterConnected()) {
            // 显示连接打印机的提示
            AlertDialog.Builder builder = new AlertDialog.Builder(this);
            builder.setTitle("提示");
            builder.setMessage("请先连接打印机再进行打印操作");
            builder.setPositiveButton("连接打印机", (dialog, which) -> {
                printerHelper.showPrinterSelectionDialog();
            });
            builder.setNegativeButton("取消", null);
            builder.show();
            return;
        }

        // 显示打印确认对话框
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("打印样品标签");
        builder.setMessage("样品编号: " + sample.getSampleNumber() + "\n" +
                         "样品类型: " + sample.getSampleTypeName() + "\n" +
                         "重量: " + (sample.getSampleWeight() != null ?
                         String.format(Locale.getDefault(), "%.2fg", sample.getSampleWeight()) : "未知") + "\n" +
                         "评价单元标识码: " + sample.getpjdybh() + "\n\n" +
                         "确定要打印此标签吗？");

        builder.setPositiveButton("确定", (dialog, which) -> {
            // 执行打印操作
            printerHelper.printSampleLabel(sample);
        });

        builder.setNegativeButton("取消", null);
        builder.show();
    }

    @Override
    public void onEditSample(Sample sample) {
        // 修改样品功能
        Log.d(TAG, "修改样品: " + sample.getSampleNumber());
        showEditSampleDialog(sample);
    }

    @Override
    public void onDeleteSample(Sample sample) {
        // 删除样品功能
        Log.d(TAG, "删除样品: " + sample.getSampleNumber());

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("删除样品");
        builder.setMessage("确定要删除样品 \"" + sample.getSampleNumber() + "\" 吗？\n\n" +
                         "此操作不可撤销！");

        builder.setPositiveButton("确定", (dialog, which) -> {
            deleteSampleFromServer(sample);
        });

        builder.setNegativeButton("取消", null);
        builder.show();
    }

    /**
     * 显示修改样品对话框
     */
    private void showEditSampleDialog(Sample sample) {
        View dialogView = getLayoutInflater().inflate(R.layout.dialog_add_sample, null);

        Spinner spinnerSampleType = dialogView.findViewById(R.id.spinnerSampleType);
        EditText editTextSampleWeight = dialogView.findViewById(R.id.editTextSampleWeight);
        EditText editTextBsm = dialogView.findViewById(R.id.editTextBsm);
        EditText editTextSampleCode = dialogView.findViewById(R.id.editTextSampleCode);
        Button btnCancel = dialogView.findViewById(R.id.btnCancel);
        Button btnConfirm = dialogView.findViewById(R.id.btnConfirm);

        // 设置当前值，并设置标识码和样品编号为不可编辑
        editTextBsm.setText(sample.getpjdybh());
        editTextBsm.setEnabled(false);
        editTextSampleWeight.setText(String.valueOf(sample.getSampleWeight()));
        editTextSampleCode.setText(sample.getSampleNumber());
        editTextSampleCode.setEnabled(false);

        // 设置样品类型选择器
        List<String> sampleTypeNames = new ArrayList<>();
        List<Sample.SampleType> availableTypes = new ArrayList<>();
        int selectedTypeIndex = -1;

        for (Sample.SampleType type : Sample.SampleType.values()) {
            // 检查该类型是否已经存在于当前列表（排除当前样品）
            boolean exists = false;
            for (Sample s : sampleList) {
                if (s.getId() != sample.getId() && s.getSampleType() == type.getCode()) {
                    exists = true;
                    break;
                }
            }
            
            if (!exists || type.getCode() == sample.getSampleType()) {
                sampleTypeNames.add(type.getName());
                availableTypes.add(type);
                
                // 如果是当前样品的类型，记录索引
                if (type.getCode() == sample.getSampleType()) {
                    selectedTypeIndex = availableTypes.size() - 1;
                }
            }
        }

        ArrayAdapter<String> typeAdapter = new ArrayAdapter<>(this, 
                android.R.layout.simple_spinner_item, sampleTypeNames);
        typeAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerSampleType.setAdapter(typeAdapter);

        // 设置当前选中的类型
        if (selectedTypeIndex >= 0) {
            spinnerSampleType.setSelection(selectedTypeIndex);
        }

        // 监听样品类型选择变化，但不再更新样品编号（因为已设为不可编辑）
        spinnerSampleType.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                // 不再自动修改样品编号，保持原编号不变
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                // 不做任何操作
            }
        });

        // 创建对话框
        AlertDialog dialog = new AlertDialog.Builder(this)
                .setTitle("修改样品")
                .setView(dialogView)
                .create();

        // 取消按钮
        btnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });

        // 确定按钮
        btnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                int selectedPosition = spinnerSampleType.getSelectedItemPosition();
                String weightStr = editTextSampleWeight.getText().toString().trim();

                // 验证输入
                if (selectedPosition < 0) {
                    Toast.makeText(SampleListActivity.this, "请选择样品类型", Toast.LENGTH_SHORT).show();
                    return;
                }

                if (weightStr.isEmpty()) {
                    Toast.makeText(SampleListActivity.this, "请输入样品重量", Toast.LENGTH_SHORT).show();
                    return;
                }

                try {
                    double weight = Double.parseDouble(weightStr);
                    if (weight <= 0) {
                        Toast.makeText(SampleListActivity.this, "样品重量必须大于0", Toast.LENGTH_SHORT).show();
                        return;
                    }

                    Sample.SampleType selectedType = availableTypes.get(selectedPosition);
                    
                    // 更新样品信息（保持编号不变）
                    sample.setSampleType(selectedType.getCode());
                    sample.setSampleTypeName(selectedType.getName());
                    sample.setSampleWeight(weight);
                    // 样品编号保持不变

                    // 保存修改到服务器
                    updateSampleToServer(sample, dialog);

                } catch (NumberFormatException e) {
                    Toast.makeText(SampleListActivity.this, "请输入有效的重量数值", Toast.LENGTH_SHORT).show();
                }
            }
        });

        dialog.show();
    }

    // ===========================================
    // PrinterHelper.PrintCallback 接口实现
    // ===========================================
    
    @Override
    public void onPrintStart() {
        Log.d(TAG, "开始打印");
        // 可以在此处显示打印进度或禁用相关按钮
    }

    @Override
    public void onPrintSuccess() {
        Log.d(TAG, "打印成功");
        // 打印成功后的处理
    }

    @Override
    public void onPrintFailed(String error) {
        Log.e(TAG, "打印失败: " + error);
        // 打印失败后的处理
    }

    @Override
    public void onPrinterConnected() {
        Log.d(TAG, "打印机连接成功");
        updatePrinterStatus();
    }

    @Override
    public void onPrinterDisconnected() {
        Log.d(TAG, "打印机连接断开");
        updatePrinterStatus();
    }

    /**
     * 显示搜索对话框
     */
    private void showSearchDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("搜索采土袋");
        
        // 创建输入框
        final EditText input = new EditText(this);
        input.setHint("请输入采土袋编号");
        input.setInputType(android.text.InputType.TYPE_CLASS_TEXT);
        builder.setView(input);
        
        // 设置按钮
        builder.setPositiveButton("搜索", (dialog, which) -> {
            String ctdbh = input.getText().toString().trim();
            if (!ctdbh.isEmpty()) {
                getSampleDetail(ctdbh);
            } else {
                Toast.makeText(this, "请输入采土袋编号", Toast.LENGTH_SHORT).show();
            }
        });
        
        builder.setNegativeButton("取消", (dialog, which) -> dialog.cancel());
        
        builder.show();
    }
} 
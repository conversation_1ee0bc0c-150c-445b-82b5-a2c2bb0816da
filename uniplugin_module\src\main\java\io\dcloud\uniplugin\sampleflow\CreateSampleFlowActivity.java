package io.dcloud.uniplugin.sampleflow;

import android.app.ProgressDialog;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import io.dcloud.uniplugin.http.RetrofitManager;
import io.dcloud.uniplugin.model.ApiResponse;
import io.dcloud.uniplugin.model.YPLZ;
import io.dcloud.uniplugin.model.YplzBatch;
import io.dcloud.uniplugin.model.YplzqdCreateRequest;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import uni.dcloud.io.uniplugin_module.R;

/**
 * 新增样品流转批次页面
 */
public class CreateSampleFlowActivity extends AppCompatActivity {
    
    private static final String TAG = "CreateSampleFlowActivity";
    private static final int REQUEST_QR_SCAN = 1001;
    private static final int REQUEST_SELECT_BATCH = 1002;
    
    private EditText editTextBatchName;
    private EditText editTextRemark;
    private Button buttonAddSample;
    private Button buttonSelectBatch;
    private RecyclerView recyclerViewSamples;
    private LinearLayout layoutEmptySamples;
    private Button buttonCancel;
    private Button buttonSubmit;
    
    private CreateSampleAdapter adapter;
    private List<YPLZ> sampleList = new ArrayList<>();
    private YplzBatch selectedBatch = null;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_create_sample_flow);
        
        // 设置ActionBar标题和返回按钮
        setTitle("新增样品流转批次");
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        
        initViews();
        setupRecyclerView();
        setupListeners();
        updateSampleListDisplay();
        
        // 检查是否从批次管理页面传入了批次ID
        if (getIntent() != null && getIntent().hasExtra("batchId")) {
            Long batchId = getIntent().getLongExtra("batchId", -1);
            if (batchId != -1) {
                loadBatchInfo(batchId);
            }
        }
    }
    
    private void initViews() {
        editTextBatchName = findViewById(R.id.editTextBatchName);
        editTextRemark = findViewById(R.id.editTextRemark);
        buttonAddSample = findViewById(R.id.buttonAddSample);
        buttonSelectBatch = findViewById(R.id.buttonSelectBatch);
        recyclerViewSamples = findViewById(R.id.recyclerViewSamples);
        layoutEmptySamples = findViewById(R.id.layoutEmptySamples);
        buttonCancel = findViewById(R.id.buttonCancel);
        buttonSubmit = findViewById(R.id.buttonSubmit);
    }
    
    private void setupRecyclerView() {
        adapter = new CreateSampleAdapter(this, sampleList);
        recyclerViewSamples.setLayoutManager(new LinearLayoutManager(this));
        recyclerViewSamples.setAdapter(adapter);
        
        // 设置删除监听器
        adapter.setOnSampleDeleteListener(position -> {
            showDeleteConfirmDialog(position);
        });
    }
    
    private void setupListeners() {
        // 扫码添加样品
        buttonAddSample.setOnClickListener(v -> startQRCodeScan());
        
        // 选择批次按钮
        buttonSelectBatch.setOnClickListener(v -> {
            Intent intent = new Intent(this, SampleBatchListActivity.class);
            intent.putExtra("isSelection", true);
            startActivityForResult(intent, REQUEST_SELECT_BATCH);
        });
        
        // 取消按钮
        buttonCancel.setOnClickListener(v -> finish());
        
        // 提交按钮
        buttonSubmit.setOnClickListener(v -> submitSampleFlow());
    }
    
    private void startQRCodeScan() {
        Intent intent = new Intent(this, QRCodeScanActivity.class);
        startActivityForResult(intent, REQUEST_QR_SCAN);
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == REQUEST_QR_SCAN && resultCode == RESULT_OK) {
            if (data != null) {
                String scanResult = data.getStringExtra("scan_result");
                // 添加日志调试
                Log.d(TAG, "扫描返回结果: " + (scanResult != null ? scanResult : "null"));
                
                if (!TextUtils.isEmpty(scanResult)) {
                    addSampleWithCode(scanResult);
                } else {
                    Toast.makeText(this, "扫描结果为空", Toast.LENGTH_SHORT).show();
                }
            } else {
                Log.d(TAG, "扫描返回的Intent为null");
                Toast.makeText(this, "未接收到扫描数据", Toast.LENGTH_SHORT).show();
            }
        } else if (requestCode == REQUEST_QR_SCAN) {
            // 记录其他结果状态
            Log.d(TAG, "扫描返回结果状态: " + resultCode);
        } else if (requestCode == REQUEST_SELECT_BATCH && resultCode == RESULT_OK) {
            // 处理批次选择结果
            if (data != null && data.hasExtra("batchId")) {
                Long batchId = data.getLongExtra("batchId", -1);
                if (batchId != -1) {
                    loadBatchInfo(batchId);
                }
            }
        }
    }
    
    private void loadBatchInfo(Long batchId) {
        ProgressDialog progressDialog = new ProgressDialog(this);
        progressDialog.setMessage("正在加载批次信息...");
        progressDialog.setCancelable(false);
        progressDialog.show();
        
        RetrofitManager.getInstance(this)
                .getSampleBatchServiceApi()
                .getBatchDetails(batchId)
                .enqueue(new Callback<ApiResponse<YplzBatch>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<YplzBatch>> call, Response<ApiResponse<YplzBatch>> response) {
                        progressDialog.dismiss();
                        
                        if (response.isSuccessful() && response.body() != null && response.body().getData() != null) {
                            selectedBatch = response.body().getData();
                            if (selectedBatch != null) {
                                updateUIWithBatch(selectedBatch);
                            }
                        } else {
                            Toast.makeText(CreateSampleFlowActivity.this, 
                                    "获取批次详情失败", Toast.LENGTH_SHORT).show();
                        }
                    }
                    
                    @Override
                    public void onFailure(Call<ApiResponse<YplzBatch>> call, Throwable t) {
                        progressDialog.dismiss();
                        Toast.makeText(CreateSampleFlowActivity.this, 
                                "网络请求失败: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                    }
                });
    }
    
    private void updateUIWithBatch(YplzBatch batch) {
        // 更新UI界面显示批次信息
        if (batch != null) {
            editTextBatchName.setText(batch.getBatchName());
            editTextRemark.setText("测试");
            
            // 修改标题显示
            setTitle("添加样品到批次");
            
            // 禁用批次名称编辑
            editTextBatchName.setEnabled(false);
            
            // 更新提交按钮文本
            buttonSubmit.setText("添加样品到批次");
            
            Toast.makeText(this, "已选择批次: " + batch.getBatchCode(), Toast.LENGTH_SHORT).show();
        }
    }
    
    private void addSampleWithCode(String sampleCode) {
        // 检查是否已存在相同编码的样品
        for (YPLZ sample : sampleList) {
            if (sampleCode.equals(sample.getSampleCode())) {
                Toast.makeText(this, "样品编码 " + sampleCode + " 已存在", Toast.LENGTH_SHORT).show();
                return;
            }
        }
        
        // 创建新样品
        YPLZ newSample = new YPLZ();
        newSample.setSampleCode(sampleCode);
        newSample.setStatus(0L); // 默认状态为待流转
        newSample.setWeightUnit("g"); // 默认重量单位为克
        
        // 如果已选择批次，则关联批次ID
        if (selectedBatch != null) {
            newSample.setBatchId(selectedBatch.getId());
        }
        
        sampleList.add(newSample);
        adapter.notifyItemInserted(sampleList.size() - 1);
        updateSampleListDisplay();
        
        Toast.makeText(this, "已添加样品：" + sampleCode, Toast.LENGTH_SHORT).show();
    }
    
    private void showDeleteConfirmDialog(int position) {
        YPLZ sample = sampleList.get(position);
        
        new AlertDialog.Builder(this)
                .setTitle("删除样品")
                .setMessage("确定要删除样品 " + sample.getSampleCode() + " 吗？")
                .setPositiveButton("删除", (dialog, which) -> {
                    sampleList.remove(position);
                    adapter.notifyItemRemoved(position);
                    adapter.notifyItemRangeChanged(position, sampleList.size());
                    updateSampleListDisplay();
                    Toast.makeText(this, "已删除样品", Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton("取消", null)
                .show();
    }
    
    private void updateSampleListDisplay() {
        if (sampleList.isEmpty()) {
            layoutEmptySamples.setVisibility(View.VISIBLE);
            recyclerViewSamples.setVisibility(View.GONE);
        } else {
            layoutEmptySamples.setVisibility(View.GONE);
            recyclerViewSamples.setVisibility(View.VISIBLE);
        }
    }
    
    private void submitSampleFlow() {
        // 验证输入
        String batchName = editTextBatchName.getText().toString().trim();
        if (TextUtils.isEmpty(batchName)) {
            Toast.makeText(this, "请输入批次名称", Toast.LENGTH_SHORT).show();
            editTextBatchName.requestFocus();
            return;
        }
        
        if (sampleList.isEmpty()) {
            Toast.makeText(this, "请至少添加一个样品", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 验证样品信息完整性
        for (int i = 0; i < sampleList.size(); i++) {
            YPLZ sample = sampleList.get(i);
            if (TextUtils.isEmpty(sample.getSampleName())) {
                Toast.makeText(this, "第 " + (i + 1) + " 个样品的名称不能为空", Toast.LENGTH_SHORT).show();
                return;
            }
        }
        
        // 根据是否选择了现有批次决定API请求
        if (selectedBatch != null) {
            // 添加样品到现有批次
            addSamplesToBatch();
        } else {
            // 创建新批次
            createNewBatch();
        }
    }
    
    private void addSamplesToBatch() {
        if (selectedBatch == null) return;
        
        // 显示进度对话框
        ProgressDialog progressDialog = new ProgressDialog(this);
        progressDialog.setMessage("正在添加样品到批次...");
        progressDialog.setCancelable(false);
        progressDialog.show();
        
        RetrofitManager.getInstance(this)
                .getSampleBatchServiceApi()
                .addSamplesToBatch(selectedBatch.getId(), sampleList)
                .enqueue(new Callback<ApiResponse<Boolean>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<Boolean>> call, Response<ApiResponse<Boolean>> response) {
                        progressDialog.dismiss();
                        
                        if (response.isSuccessful() && response.body() != null && response.body().getData() != null) {
                            Toast.makeText(CreateSampleFlowActivity.this, 
                                    "样品添加成功", Toast.LENGTH_SHORT).show();
                            
                            // 返回成功结果
                            setResult(RESULT_OK);
                            finish();
                        } else {
                            String errorMsg = "添加失败";
                            Toast.makeText(CreateSampleFlowActivity.this, 
                                    errorMsg, Toast.LENGTH_SHORT).show();
                        }
                    }
                    
                    @Override
                    public void onFailure(Call<ApiResponse<Boolean>> call, Throwable t) {
                        progressDialog.dismiss();
                        Toast.makeText(CreateSampleFlowActivity.this, 
                                "网络请求失败: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                        Log.e(TAG, "添加样品到批次失败", t);
                    }
                });
    }
    
    private void createNewBatch() {
        // 构建请求数据
        YplzqdCreateRequest request = new YplzqdCreateRequest();
        request.setBatchName(editTextBatchName.getText().toString().trim());
        request.setRemark(editTextRemark.getText().toString().trim());
        request.setSamples(sampleList);
        
        // 显示进度对话框
        ProgressDialog progressDialog = new ProgressDialog(this);
        progressDialog.setMessage("正在提交流转信息...");
        progressDialog.setCancelable(false);
        progressDialog.show();
        
        // 发起网络请求
        RetrofitManager.getInstance(this)
                .getSampleFlowService()
                .createSampleFlow(request)
                .enqueue(new Callback<ApiResponse<Long>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<Long>> call, Response<ApiResponse<Long>> response) {
                        progressDialog.dismiss();
                        
                        if (response.isSuccessful() && response.body() != null && response.body().getData() != null) {
                            Toast.makeText(CreateSampleFlowActivity.this, "流转批次创建成功", Toast.LENGTH_SHORT).show();
                            
                            // 返回成功结果
                            setResult(RESULT_OK);
                            finish();
                        } else {
                            String errorMsg = "创建失败";
                            Toast.makeText(CreateSampleFlowActivity.this, errorMsg, Toast.LENGTH_SHORT).show();
                        }
                    }
                    
                    @Override
                    public void onFailure(Call<ApiResponse<Long>> call, Throwable t) {
                        progressDialog.dismiss();
                        Toast.makeText(CreateSampleFlowActivity.this, 
                                "网络请求失败: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                        Log.e(TAG, "创建样品流转批次失败", t);
                    }
                });
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
} 
package io.dcloud.uniplugin.model;

import java.io.Serializable;

/**
 * 照片信息类，用于存储照片的文件名、经纬度、方位角和文件路径
 */
public class PhotoInfo implements Serializable {
    private String fileName;    // 文件名
    private double longitude;   // 经度
    private double latitude;    // 纬度
    private float direction;    // 方位角
    private String filePath;    // 文件路径
    private String fileTime;

    /**
     * 默认构造函数
     */
    public PhotoInfo() {
    }

    /**
     * 带所有参数的构造函数
     * @param fileName 文件名
     * @param longitude 经度
     * @param latitude 纬度
     * @param direction 方位角
     * @param filePath 文件路径
     */
    public PhotoInfo(String fileName, double longitude, double latitude, float direction, String filePath,String fileTime) {
        this.fileName = fileName;
        this.longitude = longitude;
        this.latitude = latitude;
        this.direction = direction;
        this.filePath = filePath;
        this.fileTime = fileTime;
    }

    /**
     * 获取文件名
     * @return 文件名
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * 设置文件名
     * @param fileName 文件名
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * 获取经度
     * @return 经度
     */
    public double getLongitude() {
        return longitude;
    }

    /**
     * 设置经度
     * @param longitude 经度
     */
    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }

    /**
     * 获取纬度
     * @return 纬度
     */
    public double getLatitude() {
        return latitude;
    }

    /**
     * 设置纬度
     * @param latitude 纬度
     */
    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }

    /**
     * 获取方位角
     * @return 方位角
     */
    public float getDirection() {
        return direction;
    }

    /**
     * 设置方位角
     * @param direction 方位角
     */
    public void setDirection(float direction) {
        this.direction = direction;
    }

    /**
     * 获取文件路径
     * @return 文件路径
     */
    public String getFilePath() {
        return filePath;
    }

    /**
     * 设置文件路径
     * @param filePath 文件路径
     */
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    /**
     * 获取文件时间
     * @return 文件时间
     */
    public String getFileTime() {
        return fileTime;
    }

    /**
     * 设置文件时间
     * @param fileTime 文件时间
     */
    public void setFileTime(String fileTime) {
        this.fileTime = fileTime;
    }

    /**
     * 将对象转为字符串
     * @return 对象的字符串表示
     */
    @Override
    public String toString() {
        return "PhotoInfo{" +
                "fileName='" + fileName + '\'' +
                ", longitude=" + longitude +
                ", latitude=" + latitude +
                ", direction=" + direction +
                ", filePath='" + filePath + '\'' +
                ", fileTime='" + fileTime + '\'' +
                '}';
    }
} 
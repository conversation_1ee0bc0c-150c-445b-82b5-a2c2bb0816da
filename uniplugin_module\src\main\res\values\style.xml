<?xml version="1.0" encoding="utf-8"?>
<resources>
<!--    地图上的按钮-->
    <style name="map_btn">
<!--
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:padding="5dp"
        android:scaleType="fitCenter"
        android:radius="5dp"
        android:background="@color/white"-->
        <item name="layout_constraintLeft_toLeftOf">parent</item>
        <item name="layout_constraintTop_toTopOf">parent</item>
        <item name="android:layout_width">40dp</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:padding">5dp</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:radius">10dp</item>
        <item name="android:background">@color/white</item>
    </style>

    <style name="map_btn2">
        <!--
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:padding="5dp"
                android:scaleType="fitCenter"
                android:radius="5dp"
                android:background="@color/white"-->
        <item name="android:layout_width">40dp</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:padding">5dp</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:radius">40dp</item>
        <item name="android:background">@color/white</item>
    </style>
    <style name="color_btn">
        <!--
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:padding="5dp"
                android:scaleType="fitCenter"
                android:radius="5dp"
                android:background="@color/white"-->
        <item name="layout_constraintLeft_toLeftOf">parent</item>
        <item name="layout_constraintTop_toTopOf">parent</item>
        <item name="android:layout_width">40dp</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:padding">5dp</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:radius">10dp</item>
        <item name="android:background">@color/white</item>
    </style>
<!--    量测计算中的按钮-->
    <style name="measurement_btn">
<!--        android:background="@drawable/button_bar_background"-->
<!--        android:layout_alignParentTop="true"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_width="wrap_content"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintHorizontal_bias="0.5"-->
        <item name="android:background">@drawable/button_bar_background</item>
        <item name="android:layout_alignParentTop">true</item>
        <item name="android:layout_height">30dp</item>
        <item name="android:layout_width">30dp</item>
        <item name="android:padding">5dp</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="layout_constraintBottom_toBottomOf">parent</item>
        <item name="layout_constraintHorizontal_bias">0.5</item>
    </style>
<!--    图层管理中的按钮-->
    <style name="layers_move">
        <item name="android:layout_width">25dp</item>
        <item name="android:layout_height">25dp</item>
        <item name="android:padding">5dp</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:background">@color/white</item>
    </style>
    <style name="layers_close">
        <item name="android:layout_width">40dp</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:padding">5dp</item>
        <item name="android:scaleType">fitEnd</item>
        <item name="android:background">@color/white</item>
    </style>
</resources>
package io.dcloud.uniplugin.model;

import java.util.List;

/**
 * 样品流转清单创建请求
 */
public class YplzqdCreateRequest {
    /**
     * 批次名称
     */
    private String batchName;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 样品列表
     */
    private List<YPLZ> samples;

    public String getBatchName() {
        return batchName;
    }

    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<YPLZ> getSamples() {
        return samples;
    }

    public void setSamples(List<YPLZ> samples) {
        this.samples = samples;
    }
} 
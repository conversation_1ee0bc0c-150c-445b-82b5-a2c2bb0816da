package io.dcloud.uniplugin.form.utils;

import android.content.Context;
import android.content.pm.PackageManager;
import android.location.Location;
import android.location.LocationManager;

import androidx.core.app.ActivityCompat;

public class LocationUtils {
    private static final String TAG = "LocationUtils";
    private static final long MAX_LOCATION_AGE_MS = 5 * 60 * 1000; // 5分钟
    private static final float MIN_ACCURACY = 50.0f; // 最小精度要求（米）
    private static final float MIN_DISTANCE = 10.0f; // 最小更新距离（米）
    private static final long MIN_TIME = 1000; // 最小更新间隔（毫秒）

    /**
     * 获取最后已知位置
     */
    public static Location getLastKnownLocation(Context context, LocationManager locationManager) {
        Location bestLocation = null;
        long bestTime = 0;

        if (ActivityCompat.checkSelfPermission(context, android.Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
            Location gpsLocation = locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER);
            Location networkLocation = locationManager.getLastKnownLocation(LocationManager.NETWORK_PROVIDER);

            if (gpsLocation != null && gpsLocation.getTime() > bestTime) {
                bestLocation = gpsLocation;
                bestTime = gpsLocation.getTime();
            }

            if (networkLocation != null && networkLocation.getTime() > bestTime) {
                bestLocation = networkLocation;
                bestTime = networkLocation.getTime();
            }
        }

        return bestLocation;
    }

    /**
     * 检查位置是否足够新
     */
    public static boolean isLocationRecent(Location location) {
        return location != null && System.currentTimeMillis() - location.getTime() < MAX_LOCATION_AGE_MS;
    }

//    /**
//     * 检查位置精度是否满足要求
//     */
//    public static boolean isLocationAccurate(Location location) {
//        return location != null && location.getAccuracy() <= MIN_ACCURACY;
//    }
//
//    /**
//     * 比较两个位置，返回较好的那个
//     */
//    public static Location getBetterLocation(Location location1, Location location2) {
//        if (location1 == null) {
//            return location2;
//        }
//        if (location2 == null) {
//            return location1;
//        }
//
//        // 检查时间差
//        long timeDelta = location1.getTime() - location2.getTime();
//
//        // 如果时间差大于1分钟，选择较新的位置
//        if (Math.abs(timeDelta) > 60000) {
//            return timeDelta > 0 ? location1 : location2;
//        }
//
//        // 检查精度，选择更精确的位置
//        if (location1.getAccuracy() < location2.getAccuracy()) {
//            return location1;
//        } else if (location2.getAccuracy() < location1.getAccuracy()) {
//            return location2;
//        }
//
//        // 时间和精度相近，优先选择GPS提供的位置
//        if (LocationManager.GPS_PROVIDER.equals(location1.getProvider())) {
//            return location1;
//        } else if (LocationManager.GPS_PROVIDER.equals(location2.getProvider())) {
//            return location2;
//        }
//
//        // 默认返回较新的位置
//        return timeDelta > 0 ? location1 : location2;
//    }
//
//    /**
//     * 格式化位置信息为显示文本
//     */
//    public static String formatLocation(Location location) {
//        if (location == null) return "";
//        return String.format("经度: %.6f\n纬度: %.6f\n精度: %.1f米",
//                location.getLongitude(), location.getLatitude(), location.getAccuracy());
//    }

    /**
     * 计算两个位置之间的距离
     * @param location1 第一个位置
     * @param location2 第二个位置
     * @return 距离，单位米
     */
    public static float calculateDistance(Location location1, Location location2) {
        if (location1 == null || location2 == null) {
            return 0;
        }
        
        float[] results = new float[1];
        Location.distanceBetween(
            location1.getLatitude(), 
            location1.getLongitude(), 
            location2.getLatitude(), 
            location2.getLongitude(), 
            results
        );
        
        return results[0];
    }

    /**
     * 计算位置与目标坐标之间的距离
     * @param location 当前位置
     * @param targetLatitude 目标纬度
     * @param targetLongitude 目标经度
     * @return 距离，单位米
     */
    public static float calculateDistance(Location location, double targetLatitude, double targetLongitude) {
        if (location == null) {
            return 0;
        }
        
        float[] results = new float[1];
        Location.distanceBetween(
            location.getLatitude(), 
            location.getLongitude(), 
            targetLatitude, 
            targetLongitude, 
            results
        );
        
        return results[0];
    }

    /**
     * 获取位置更新参数
     */
    public static class LocationUpdateParams {
        public final long minTime;
        public final float minDistance;

        public LocationUpdateParams() {
            this.minTime = MIN_TIME;
            this.minDistance = MIN_DISTANCE;
        }

        public LocationUpdateParams(long minTime, float minDistance) {
            this.minTime = minTime;
            this.minDistance = minDistance;
        }
    }
} 
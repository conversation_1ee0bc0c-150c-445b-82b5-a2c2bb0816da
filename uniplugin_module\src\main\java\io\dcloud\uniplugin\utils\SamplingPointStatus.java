package io.dcloud.uniplugin.utils;

/**
 * 采样点状态枚举类
 */
public enum SamplingPointStatus {
    
    PENDING_SURVEY(1, "待调查"),
    PENDING_SUBMIT(2, "待提交"),
    SUBMITTED(3, "已提交"), 
    PENDING_RECTIFY(4, "待整改");
    
    private final int code;
    private final String description;
    
    SamplingPointStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据状态码获取状态枚举
     * @param code 状态码
     * @return 状态枚举
     */
    public static SamplingPointStatus getByCode(int code) {
        for (SamplingPointStatus status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }
} 
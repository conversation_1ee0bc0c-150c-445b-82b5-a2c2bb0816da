package io.dcloud.uniplugin.form;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.ListView;
import android.widget.TextView;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import io.dcloud.uniplugin.form.utils.FormLocalStorageManager;
import io.dcloud.uniplugin.form.utils.UIUtils;
import uni.dcloud.io.uniplugin_module.R;

/**
 * 已保存表单列表活动，显示所有本地保存的表单
 */
public class SavedFormsActivity extends AppCompatActivity {
    private static final String TAG = "SavedFormsActivity";
    
    private ListView listViewForms;
    private TextView textViewEmpty;
    private List<Map<String, Object>> savedForms;
    private List<String> formDisplayNames;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_saved_forms);
        
        // 设置ActionBar
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("本地保存的表单");
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        
        // 初始化视图
        listViewForms = findViewById(R.id.listViewForms);
        textViewEmpty = findViewById(R.id.textViewEmpty);
        
        // 加载保存的表单列表
        loadSavedForms();
        
        // 设置列表点击事件
        listViewForms.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                if (position >= 0 && position < savedForms.size()) {
                    Map<String, Object> formInfo = savedForms.get(position);
                    showFormOptionsDialog(formInfo, position);
                }
            }
        });
    }
    
    /**
     * 加载保存的表单列表
     */
    private void loadSavedForms() {
        savedForms = FormLocalStorageManager.getSavedForms(this);
        formDisplayNames = new ArrayList<>();
        
        for (Map<String, Object> formInfo : savedForms) {
            String formName = (String) formInfo.get("formName");
            String saveTimeStr = (String) formInfo.get("saveTimeStr");
            
            if (formName == null) {
                formName = "未命名表单";
            }
            
            String displayName = formName + " (" + saveTimeStr + ")";
            formDisplayNames.add(displayName);
        }
        
        // 更新UI
        if (formDisplayNames.isEmpty()) {
            listViewForms.setVisibility(View.GONE);
            textViewEmpty.setVisibility(View.VISIBLE);
        } else {
            listViewForms.setVisibility(View.VISIBLE);
            textViewEmpty.setVisibility(View.GONE);
            
            ArrayAdapter<String> adapter = new ArrayAdapter<>(this,
                    android.R.layout.simple_list_item_1, formDisplayNames);
            listViewForms.setAdapter(adapter);
        }
    }
    
    /**
     * 显示表单操作选项对话框
     * @param formInfo 表单信息
     * @param position 列表位置
     */
    private void showFormOptionsDialog(final Map<String, Object> formInfo, final int position) {
        String[] options = {"查看表单", "删除表单"};
        
        new AlertDialog.Builder(this)
                .setTitle("表单操作")
                .setItems(options, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        switch (which) {
                            case 0: // 查看表单
                                openSavedForm(formInfo);
                                break;
                            case 1: // 删除表单
                                confirmDeleteForm(formInfo, position);
                                break;
                        }
                    }
                })
                .show();
    }
    
    /**
     * 打开保存的表单
     * @param formInfo 表单信息
     */
    private void openSavedForm(Map<String, Object> formInfo) {
        String formId = (String) formInfo.get("formId");
        Log.d(TAG, "打开保存的表单: " + formId);
        
        // 创建打开动态表单的意图
        Intent intent = new Intent(this, DynamicFormActivity.class);
        intent.putExtra("pjdybh", formId);
        intent.putExtra("loadLocalForm", true);
        startActivity(intent);
    }
    
    /**
     * 确认删除表单
     * @param formInfo 表单信息
     * @param position 列表位置
     */
    private void confirmDeleteForm(final Map<String, Object> formInfo, final int position) {
        String formName = (String) formInfo.get("formName");
        
        new AlertDialog.Builder(this)
                .setTitle("确认删除")
                .setMessage("确定要删除表单" + formName + "吗？此操作不能撤销。")
                .setPositiveButton("删除", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        deleteForm(formInfo, position);
                    }
                })
                .setNegativeButton("取消", null)
                .show();
    }
    
    /**
     * 删除表单
     * @param formInfo 表单信息
     * @param position 列表位置
     */
    private void deleteForm(Map<String, Object> formInfo, int position) {
        String formId = (String) formInfo.get("formId");
        int id = (Integer) formInfo.get("id");
        
        boolean success = FormLocalStorageManager.deleteFormDataById(this, id);
        
        if (success) {
            Log.d(TAG, "表单已成功删除: " + formId + "，ID: " + id);
            UIUtils.showToast(this, "表单已删除");
            
            // 更新列表
            savedForms.remove(position);
            formDisplayNames.remove(position);
            
            ArrayAdapter<String> adapter = (ArrayAdapter<String>) listViewForms.getAdapter();
            adapter.notifyDataSetChanged();
            
            // 如果没有表单了，显示空视图
            if (savedForms.isEmpty()) {
                listViewForms.setVisibility(View.GONE);
                textViewEmpty.setVisibility(View.VISIBLE);
            }
        } else {
            Log.e(TAG, "删除表单失败: " + formId + "，ID: " + id);
            UIUtils.showToast(this, "删除表单失败，请重试");
        }
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
} 
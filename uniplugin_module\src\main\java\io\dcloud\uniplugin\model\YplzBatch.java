package io.dcloud.uniplugin.model;

/**
 * 样品流转批次模型
 */
public class YplzBatch {
    /**
     * 批次编号
     */
    private String batchCode;
    /**
     * 批次名称
     */
    private String batchName;
    /**
     * 批次状态（0送样、1完成、2退回）
     */
    private Long batchState;
    /**
     * 批次类型（县0/市1/省2）
     */
    private Integer batchType;
    /**
     * 创建人ID
     */
    private Long createrId;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 运送信息
     */
    private String deliveryMessage;
    /**
     * 运送方式
     */
    private String deliveryType;
    private Long id;
    /**
     * 是否确认录入完成
     */
    private Long isSure;
    /**
     * 接样单位
     */
    private String receiveOrganization;
    /**
     * 接样单位ID（检测机构ID）
     */
    private Long receiveOrganizationId;
    /**
     * 接样人ID
     */
    private Long receiverId;
    /**
     * 接样人姓名
     */
    private String receiverName;
    /**
     * 接样人联系方式
     */
    private String receiverPhone;
    /**
     * 接样人签名
     */
    private String receiverSignature;
    /**
     * 样品数量
     */
    private Long sampleNumber;
    /**
     * 送样人ID
     */
    private Long senderId;
    /**
     * 送样人姓名
     */
    private String senderName;
    /**
     * 送样人联系方式
     */
    private String senderPhone;
    /**
     * 送样人签名
     */
    private String senderSignature;
    /**
     * 送样单位
     */
    private String sendOrganization;

    public String getBatchCode() { return batchCode; }
    public void setBatchCode(String value) { this.batchCode = value; }

    public String getBatchName() { return batchName; }
    public void setBatchName(String value) { this.batchName = value; }

    public Long getBatchState() { return batchState; }
    public void setBatchState(Long value) { this.batchState = value; }

    public Integer getBatchType() { return batchType; }
    public void setBatchType(Integer value) { this.batchType = value; }

    public Long getCreaterId() { return createrId; }
    public void setCreaterId(Long value) { this.createrId = value; }

    public Long getCreateTime() { return createTime; }
    public void setCreateTime(Long value) { this.createTime = value; }

    public String getDeliveryMessage() { return deliveryMessage; }
    public void setDeliveryMessage(String value) { this.deliveryMessage = value; }

    public String getDeliveryType() { return deliveryType; }
    public void setDeliveryType(String value) { this.deliveryType = value; }

    public Long getId() { return id; }
    public void setId(Long value) { this.id = value; }

    public Long getIsSure() { return isSure; }
    public void setIsSure(Long value) { this.isSure = value; }

    public String getReceiveOrganization() { return receiveOrganization; }
    public void setReceiveOrganization(String value) { this.receiveOrganization = value; }

    public Long getReceiveOrganizationId() { return receiveOrganizationId; }
    public void setReceiveOrganizationId(Long value) { this.receiveOrganizationId = value; }

    public Long getReceiverId() { return receiverId; }
    public void setReceiverId(Long value) { this.receiverId = value; }

    public String getReceiverName() { return receiverName; }
    public void setReceiverName(String value) { this.receiverName = value; }

    public String getReceiverPhone() { return receiverPhone; }
    public void setReceiverPhone(String value) { this.receiverPhone = value; }

    public String getReceiverSignature() { return receiverSignature; }
    public void setReceiverSignature(String value) { this.receiverSignature = value; }

    public Long getSampleNumber() { return sampleNumber; }
    public void setSampleNumber(Long value) { this.sampleNumber = value; }

    public Long getSenderId() { return senderId; }
    public void setSenderId(Long value) { this.senderId = value; }

    public String getSenderName() { return senderName; }
    public void setSenderName(String value) { this.senderName = value; }

    public String getSenderPhone() { return senderPhone; }
    public void setSenderPhone(String value) { this.senderPhone = value; }

    public String getSenderSignature() { return senderSignature; }
    public void setSenderSignature(String value) { this.senderSignature = value; }

    public String getSendOrganization() { return sendOrganization; }
    public void setSendOrganization(String value) { this.sendOrganization = value; }
}
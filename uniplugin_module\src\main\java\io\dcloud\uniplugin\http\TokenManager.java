package io.dcloud.uniplugin.http;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Toast;

import io.dcloud.uniplugin.LoginActivity;
import io.dcloud.uniplugin.enums.SharedPreferencesEnum;

/**
 * Token管理工具类
 */
public class TokenManager {
    private static final String TAG = "TokenManager";
    
    /**
     * 保存令牌
     * @param context 上下文
     * @param token 访问令牌
     * @param expiryTimeInMillis 过期时间（毫秒）
     */
    public static void saveToken(Context context, String token, long expiryTimeInMillis) {
        if (context == null) {
            Log.e(TAG, "Context为空，无法保存令牌");
            return;
        }
        
        SharedPreferences prefs = context.getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(SharedPreferencesEnum.ACCESS_TOKEN.value, token);
        editor.putLong(SharedPreferencesEnum.EXPIRE_TIME.value, expiryTimeInMillis);
        editor.apply();
        
        Log.d(TAG, "令牌已保存");
    }
    
    /**
     * 获取令牌
     * @param context 上下文
     * @return 访问令牌，如果不存在或已过期则返回null
     */
    public static String getToken(Context context) {
        if (context == null) {
            Log.e(TAG, "Context为空，无法获取令牌");
            return null;
        }
        
        SharedPreferences prefs = context.getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, Context.MODE_PRIVATE);
        String token = prefs.getString(SharedPreferencesEnum.ACCESS_TOKEN.value, null);
        long expiryTime = prefs.getLong(SharedPreferencesEnum.EXPIRE_TIME.value, 0);
        
        // 检查令牌是否存在且未过期
        if (token != null && expiryTime > System.currentTimeMillis()) {
            return token;
        } else if (token != null) {
            // 令牌已过期，弹框提示并跳转到登录页
            Log.d(TAG, "令牌已过期");
            redirectToLogin(context, "登录已过期，请重新登录");
            return null;
        } else {
            Log.d(TAG, "令牌不存在");
            return null;
        }
    }
    
    /**
     * 清除令牌
     * @param context 上下文
     */
    public static void clearToken(Context context) {
        if (context == null) {
            Log.e(TAG, "Context为空，无法清除令牌");
            return;
        }
        
        SharedPreferences prefs = context.getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.remove(SharedPreferencesEnum.ACCESS_TOKEN.toString());
        editor.remove(SharedPreferencesEnum.EXPIRE_TIME.toString());
        editor.apply();
        
        Log.d(TAG, "令牌已清除");
    }
    
    /**
     * 检查令牌是否存在且有效
     * @param context 上下文
     * @return 令牌是否有效
     */
    public static boolean isTokenValid(Context context) {
        return getToken(context) != null;
    }
    
    /**
     * 重定向到登录页面
     * @param context 上下文
     * @param message 提示消息
     */
    public static void redirectToLogin(Context context, String message) {
        if (context == null) {
            return;
        }
        Log.e("Token管理器", "重新登录提熊" );
        
        // 使用Handler在主线程中执行UI操作
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                // 显示提示
                Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
                
                // 跳转到登录页面
                Intent intent = new Intent(context, LoginActivity.class);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                context.startActivity(intent);
            }
        });
    }
}
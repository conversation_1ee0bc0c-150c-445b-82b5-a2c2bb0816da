package io.dcloud.uniplugin.utils;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.widget.Toast;

import com.dothantech.lpapi.LPAPI;
import com.dothantech.printer.IDzPrinter;
import com.dothantech.printer.IDzPrinter.PrintParamName;
import com.dothantech.printer.IDzPrinter.PrintProgress;
import com.dothantech.printer.IDzPrinter.PrinterAddress;
import com.dothantech.printer.IDzPrinter.PrinterState;
import com.dothantech.printer.IDzPrinter.ProgressInfo;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import io.dcloud.uniplugin.model.Sample;

/**
 * 打印机辅助工具类
 */
public class PrinterHelper {
    
    private static final String TAG = "PrinterHelper";
    
    // SharedPreferences键名
    private static final String KEY_LAST_PRINTER_MAC = "LastPrinterMac";
    private static final String KEY_LAST_PRINTER_NAME = "LastPrinterName";
    private static final String KEY_LAST_PRINTER_TYPE = "LastPrinterType";
    private static final String KEY_PRINT_DENSITY = "PrintDensity";
    private static final String KEY_PRINT_SPEED = "PrintSpeed";
    private static final String KEY_GAP_TYPE = "GapType";
    
    private Context context;
    private LPAPI api;
    private Handler handler;
    private PrinterAddress printerAddress;
    private AlertDialog stateDialog;
    private PrintCallback printCallback;
    
    // 打印参数
    private int printDensity = -1;
    private int printSpeed = -1;
    private int gapType = -1;
    
    // 连接超时设置（15秒）
    private static final int CONNECTION_TIMEOUT = 15000; // 15秒
    private Handler timeoutHandler;
    private Runnable timeoutRunnable;
    
    /**
     * 打印回调接口
     */
    public interface PrintCallback {
        void onPrintStart();
        void onPrintSuccess();
        void onPrintFailed(String error);
        void onPrinterConnected();
        void onPrinterDisconnected();
    }
    
    /**
     * LPAPI回调
     */
    private final LPAPI.Callback apiCallback = new LPAPI.Callback() {
        @Override
        public void onStateChange(PrinterAddress printer, PrinterState state) {
            switch (state) {
                case Connected:
                case Connected2:
                    handler.post(() -> onPrinterConnected(printer));
                    break;
                case Disconnected:
                    handler.post(() -> onPrinterDisconnected());
                    break;
                default:
                    break;
            }
        }
        
        @Override
        public void onPrintProgress(PrinterAddress address, IDzPrinter.PrintData printData, 
                                   PrintProgress progress, Object addiInfo) {
            switch (progress) {
                case Success:
                    handler.post(() -> onPrintSuccess());
                    break;
                case Failed:
                    handler.post(() -> onPrintFailed("打印失败"));
                    break;
                default:
                    break;
            }
        }
        
        @Override
        public void onPrinterDiscovery(PrinterAddress printerAddress, Object o) {
            // 打印机发现回调
        }
        
        @Override
        public void onProgressInfo(ProgressInfo progressInfo, Object o) {
            // 进度信息回调
        }
    };
    
    public PrinterHelper(Context context) {
        this.context = context;
        this.handler = new Handler();
        this.timeoutHandler = new Handler();
        this.api = LPAPI.Factory.createInstance(apiCallback);
        loadSettings();
    }
    
    /**
     * 设置打印回调
     */
    public void setPrintCallback(PrintCallback callback) {
        this.printCallback = callback;
    }
    
    /**
     * 加载设置
     */
    private void loadSettings() {
        SharedPreferences sp = context.getSharedPreferences("PrinterSettings", Context.MODE_PRIVATE);
        String lastPrinterMac = sp.getString(KEY_LAST_PRINTER_MAC, null);
        String lastPrinterName = sp.getString(KEY_LAST_PRINTER_NAME, null);
        String lastPrinterType = sp.getString(KEY_LAST_PRINTER_TYPE, null);
        
        IDzPrinter.AddressType lastAddressType = TextUtils.isEmpty(lastPrinterType) ? 
                null : IDzPrinter.AddressType.valueOf(lastPrinterType);
        
        if (lastPrinterMac != null && lastPrinterName != null && lastAddressType != null) {
            printerAddress = new PrinterAddress(lastPrinterName, lastPrinterMac, lastAddressType);
        }
        
        printDensity = sp.getInt(KEY_PRINT_DENSITY, -1);
        printSpeed = sp.getInt(KEY_PRINT_SPEED, -1);
        gapType = sp.getInt(KEY_GAP_TYPE, -1);
    }
    
    /**
     * 保存设置
     */
    private void saveSettings() {
        SharedPreferences.Editor editor = context.getSharedPreferences("PrinterSettings", Context.MODE_PRIVATE).edit();
        
        if (printerAddress != null) {
            editor.putString(KEY_LAST_PRINTER_MAC, printerAddress.macAddress);
            editor.putString(KEY_LAST_PRINTER_NAME, printerAddress.shownName);
            editor.putString(KEY_LAST_PRINTER_TYPE, printerAddress.addressType.toString());
        }
        
        editor.putInt(KEY_PRINT_DENSITY, printDensity);
        editor.putInt(KEY_PRINT_SPEED, printSpeed);
        editor.putInt(KEY_GAP_TYPE, gapType);
        editor.apply();
    }
    
    /**
     * 初始化（尝试连接上次使用的打印机）
     */
    public void initialize() {
        if (printerAddress != null) {
            connectPrinter(printerAddress);
        }
    }
    
    /**
     * 检查蓝牙和权限
     */
    public boolean checkBluetoothAndPermissions() {
        BluetoothAdapter btAdapter = BluetoothAdapter.getDefaultAdapter();
        if (btAdapter == null) {
            Toast.makeText(context, "设备不支持蓝牙", Toast.LENGTH_SHORT).show();
            return false;
        }
        
        if (!btAdapter.isEnabled()) {
            Toast.makeText(context, "请先开启蓝牙", Toast.LENGTH_SHORT).show();
            return false;
        }
        
        // 检查蓝牙权限
        if (Build.VERSION.SDK_INT >= 31) { // Android 12+ (API 31)
            // Android 12+ 需要新的蓝牙权限
            if (context.checkSelfPermission("android.permission.BLUETOOTH_SCAN") !=
                    android.content.pm.PackageManager.PERMISSION_GRANTED ||
                context.checkSelfPermission("android.permission.BLUETOOTH_CONNECT") != 
                    android.content.pm.PackageManager.PERMISSION_GRANTED) {
                
                if (context instanceof Activity) {
                    ((Activity) context).requestPermissions(new String[]{
                        "android.permission.BLUETOOTH_SCAN",
                        "android.permission.BLUETOOTH_CONNECT",
                        Manifest.permission.ACCESS_FINE_LOCATION,
                        Manifest.permission.ACCESS_COARSE_LOCATION
                    }, 1001);
                }
                Toast.makeText(context, "请授予蓝牙权限", Toast.LENGTH_SHORT).show();
                return false;
            }
        } else {
            // Android 12之前的权限检查
            if (context.checkSelfPermission(Manifest.permission.BLUETOOTH) != 
                    android.content.pm.PackageManager.PERMISSION_GRANTED ||
                context.checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) != 
                    android.content.pm.PackageManager.PERMISSION_GRANTED) {
                
                if (context instanceof Activity) {
                    ((Activity) context).requestPermissions(new String[]{
                        Manifest.permission.BLUETOOTH,
                        Manifest.permission.BLUETOOTH_ADMIN,
                        Manifest.permission.ACCESS_FINE_LOCATION,
                        Manifest.permission.ACCESS_COARSE_LOCATION
                    }, 1001);
                }
                Toast.makeText(context, "请授予蓝牙权限", Toast.LENGTH_SHORT).show();
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 显示打印机选择对话框
     */
    public void showPrinterSelectionDialog() {
        if (!checkBluetoothAndPermissions()) {
            return;
        }
        
        List<PrinterAddress> printers = api.getAllPrinterAddresses(null);
        if (printers.isEmpty()) {
            Toast.makeText(context, "未找到已配对的打印机", Toast.LENGTH_SHORT).show();
            return;
        }
        
        String[] printerNames = new String[printers.size()];
        for (int i = 0; i < printers.size(); i++) {
            PrinterAddress printer = printers.get(i);
            printerNames[i] = printer.shownName + "\n" + printer.macAddress;
        }
        
        new AlertDialog.Builder(context)
                .setTitle("选择打印机")
                .setItems(printerNames, (dialog, which) -> {
                    PrinterAddress selectedPrinter = printers.get(which);
                    connectPrinter(selectedPrinter);
                })
                .show();
    }
    
    /**
     * 连接打印机
     */
    private void connectPrinter(PrinterAddress printer) {
        if (api.openPrinterByAddress(printer)) {
            showProgressDialog("正在连接打印机...");
            this.printerAddress = printer;
            saveSettings();
            
            // 设置连接超时
            cancelTimeoutTimer(); // 取消之前可能存在的计时器
            timeoutRunnable = new Runnable() {
                @Override
                public void run() {
                    // 连接超时处理
                    if (api.getPrinterState() == PrinterState.Connecting) {
                        // 如果15秒后还在连接中，则认为连接超时
                        api.quit(); // 关闭连接
                        handler.post(() -> {
                            dismissProgressDialog();
                            Toast.makeText(context, "打印机连接超时，请重试", Toast.LENGTH_SHORT).show();
                            if (printCallback != null) {
                                printCallback.onPrinterDisconnected();
                            }
                        });
                    }
                }
            };
            timeoutHandler.postDelayed(timeoutRunnable, CONNECTION_TIMEOUT);
        } else {
            onPrinterDisconnected();
        }
    }
    
    /**
     * 检查打印机是否已连接
     */
    public boolean isPrinterConnected() {
        PrinterState state = api.getPrinterState();
        
        if (state == null || state.equals(PrinterState.Disconnected)) {
            Toast.makeText(context, "请先连接打印机", Toast.LENGTH_SHORT).show();
            return false;
        }
        
        if (state.equals(PrinterState.Connecting)) {
            Toast.makeText(context, "打印机正在连接中，请稍候", Toast.LENGTH_SHORT).show();
            return false;
        }
        
        return true;
    }
    
    /**
     * 打印样品标签
     */
    public void printSampleLabel(Sample sample) {
        if (!isPrinterConnected()) {
            return;
        }
        
        Bundle printParams = createPrintParams();
        
        if (printSample2DBarcode(sample, printParams)) {
            if (printCallback != null) {
                printCallback.onPrintStart();
            }
            showProgressDialog("正在打印...");
        } else {
            onPrintFailed("打印请求失败");
        }
    }
    
    /**
     * 创建打印参数
     */
    private Bundle createPrintParams() {
        Bundle params = new Bundle();
        
        if (printDensity >= 0) {
            params.putInt(PrintParamName.PRINT_DENSITY, printDensity);
        }
        
        if (printSpeed >= 0) {
            params.putInt(PrintParamName.PRINT_SPEED, printSpeed);
        }
        
        if (gapType >= 0) {
            params.putInt(PrintParamName.GAP_TYPE, gapType);
        }
        
        return params;
    }
    
    /**
     * 打印样品二维码标签
     */
    private boolean printSample2DBarcode(Sample sample, Bundle params) {
        double width = 70.0;
        double height = 50.0;
        
        // 开始绘图任务，传入参数(页面宽度, 页面高度, 旋转角度)
        api.startJob(width, height, 90);
        
        // 获取样品信息
        String sampleNumber = sample.getSampleNumber() != null ? sample.getSampleNumber() : "无编号";
        String sampleType = sample.getSampleTypeName() != null ? sample.getSampleTypeName() : "未知类型";
        String sampleWeight = sample.getSampleWeight() != null ? 
                String.format(Locale.getDefault(), "%.2fg", sample.getSampleWeight()) : "未知";
        String sampleCode = sample.getSampleCode() != null ? sample.getSampleCode() : "无";
        
        // 格式化创建时间
        String createTime = "暂无";
        if (sample.getCreateTime() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
            createTime = sdf.format(new Date(sample.getCreateTime()));
        }
        
        // 绘制文本信息
        api.drawText("样品编号：" + sampleNumber, 2, 5, 45, 4, 4);
        api.drawText("样品类型：" + sampleType, 2, 15, 45, 4, 4);
        api.drawText("样品重量：" + sampleWeight, 2, 25, 45, 4, 4);
        api.drawText("评价单元：" + sampleCode, 2, 35, 45, 4, 4);
        api.drawText("创建时间：" + createTime, 2, 45, 45, 4, 4);
        
        // 绘制二维码（使用样品编号作为二维码内容）
        double qrcodeWidth = 25;
        double qrcodeX = 40;
        double qrcodeY = 15;
        api.draw2DQRCode(sampleNumber, qrcodeX, qrcodeY, qrcodeWidth);
        
        // 结束绘图任务提交打印
        return api.commitJob();
    }
    
    /**
     * 显示进度对话框
     */
    private void showProgressDialog(String message) {
        dismissProgressDialog();
        stateDialog = new AlertDialog.Builder(context)
                .setCancelable(false)
                .setTitle(message)
                .show();
    }
    
    /**
     * 关闭进度对话框
     */
    private void dismissProgressDialog() {
        if (stateDialog != null && stateDialog.isShowing()) {
            stateDialog.dismiss();
        }
        stateDialog = null;
    }
    
    /**
     * 取消超时计时器
     */
    private void cancelTimeoutTimer() {
        if (timeoutRunnable != null) {
            timeoutHandler.removeCallbacks(timeoutRunnable);
            timeoutRunnable = null;
        }
    }
    
    /**
     * 打印机连接成功回调
     */
    private void onPrinterConnected(PrinterAddress printer) {
        // 取消超时计时器
        cancelTimeoutTimer();
        
        dismissProgressDialog();
        Toast.makeText(context, "打印机连接成功", Toast.LENGTH_SHORT).show();
        this.printerAddress = printer;
        saveSettings();
        
        if (printCallback != null) {
            printCallback.onPrinterConnected();
        }
    }
    
    /**
     * 打印机断开连接回调
     */
    private void onPrinterDisconnected() {
        // 取消超时计时器
        cancelTimeoutTimer();
        
        dismissProgressDialog();
        Toast.makeText(context, "打印机连接失败或已断开", Toast.LENGTH_SHORT).show();
        
        if (printCallback != null) {
            printCallback.onPrinterDisconnected();
        }
    }
    
    /**
     * 打印成功回调
     */
    private void onPrintSuccess() {
        dismissProgressDialog();
        Toast.makeText(context, "打印成功", Toast.LENGTH_SHORT).show();
        
        if (printCallback != null) {
            printCallback.onPrintSuccess();
        }
    }
    
    /**
     * 打印失败回调
     */
    private void onPrintFailed(String error) {
        dismissProgressDialog();
        Toast.makeText(context, error, Toast.LENGTH_SHORT).show();
        
        if (printCallback != null) {
            printCallback.onPrintFailed(error);
        }
    }
    
    /**
     * 获取当前连接的打印机信息
     */
    public String getPrinterInfo() {
        if (!isPrinterConnected()) {
            return "未连接打印机";
        }
        
        IDzPrinter.PrinterInfo info = api.getPrinterInfo();
        if (info != null && info.deviceName != null) {
            // 简化显示信息，只显示设备名称，如果太长则截断
            String deviceName = info.deviceName;
            if (deviceName.length() > 12) {
                return deviceName.substring(0, 12) + "...";
            }
            return deviceName;
        }
        
        return "已连接";
    }
    
    /**
     * 获取详细的打印机信息
     */
    public String getDetailedPrinterInfo() {
        if (!isPrinterConnected()) {
            return "未连接打印机";
        }
        
        IDzPrinter.PrinterInfo info = api.getPrinterInfo();
        if (info != null) {
            return info.deviceName + "\n" + info.deviceAddress;
        }
        
        return "获取打印机信息失败";
    }
    
    /**
     * 释放资源
     */
    public void destroy() {
        // 取消超时计时器
        cancelTimeoutTimer();
        
        dismissProgressDialog();
        
        if (api != null) {
            api.quit();
            api = null;
        }
    }
} 
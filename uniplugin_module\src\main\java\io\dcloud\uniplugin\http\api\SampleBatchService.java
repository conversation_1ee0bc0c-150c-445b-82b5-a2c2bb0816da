package io.dcloud.uniplugin.http.api;

import java.util.List;
import java.util.Map;

import io.dcloud.uniplugin.model.ApiResponse;
import io.dcloud.uniplugin.model.YPLZ;
import io.dcloud.uniplugin.model.YplzBatch;
import io.dcloud.uniplugin.model.YplzCreateRequest;
import io.dcloud.uniplugin.model.YplzPageResponse;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Path;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;

/**
 * 样品批次API服务接口
 */
public interface SampleBatchService {

    /**
     * 向批次中添加样品
     */
    @POST("bcgd/sample/batch/{batchId}/addSamples")
    Call<ApiResponse<Boolean>> addSamplesToBatch(
        @Path("batchId") Long batchId,
        @Body List<YPLZ> samples
    );

    /**
     * 创建样品批次
     * @param request 批次创建请求
     * @return 创建的批次ID
     */
    @POST("bcgd/sample/yplz/create")
    Call<ApiResponse<Long>> createSampleBatch(@Body YplzCreateRequest request);


    /**
     * 获取样品批次分页数据
     * @param queryParams 查询参数 包括 page, size, 以及其他过滤条件
     * @return 分页数据
     */
    @GET("bcgd/sample/yplz/page")
    Call<ApiResponse<YplzPageResponse<YplzBatch>>> getSampleBatchPage(@QueryMap Map<String, Object> queryParams);

    /**
     * 获取单个样品批次详情
     */
    @GET("bcgd/sample/yplz/get")
    Call<ApiResponse<YplzBatch>> getSampleBatchDetail(@Query("id") Long id);


    /**
     * 获取批次详情
     */
    @GET("bcgd/sample/batch/detail/{id}")
    Call<ApiResponse<YplzBatch>> getBatchDetails(@Path("id") Long batchId);

    /**
     * 更新样品批次
     */
    @POST("bcgd/sample/yplz/update")
    Call<ApiResponse<Boolean>> updateSampleBatch(@Body YplzBatch batch);

    /**
     * 更新样品批次状态
     */
    @GET("bcgd/sample/yplz/updateStatus")
    Call<ApiResponse<Boolean>> updateSampleBatchStatus(@Query("id") Long id, @Query("status") Long status);

    /**
     * 删除样品批次
     */
    @GET("bcgd/sample/yplz/delete")
    Call<ApiResponse<Boolean>> deleteSampleBatch(@Query("id") Long id);

    /**
     * 接样
     */
    @POST("bcgd/sample/yplz/receive")
    Call<ApiResponse<Integer>> receiveSampleBatch(@Body YplzBatch batch);
} 
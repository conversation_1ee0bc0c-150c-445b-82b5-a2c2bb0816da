<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <TextView
            android:id="@+id/textFormId"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="表单ID: FORM_001"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/textSamplingPointId"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="采样点: SP001"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/textSubmissionDate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="提交时间: 2023-01-01 12:00:00"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/textStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="状态: 完整"
            android:textSize="14sp"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btnSync"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="4dp"
                android:layout_weight="1"
                android:backgroundTint="#1971AC"
                android:text="同步"
                android:textSize="12sp" />

            <Button
                android:id="@+id/btnDelete"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_weight="1"
                android:backgroundTint="#D32F2F"
                android:text="删除"
                android:textSize="12sp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView> 
<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <SurfaceView
        android:id="@+id/surface_view_camera2_activity"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <LinearLayout
        android:id="@+id/head_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top"
        android:background="@color/top_bar_bg"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingVertical="8dp">

        <ImageView
            android:id="@+id/back_uniapp"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:onClick="onVideoGo"
            android:src="@drawable/ic_back"
            android:layout_marginLeft="10dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_weight="9"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal|center_vertical"
            android:text="00:00"
            android:id="@+id/videoCountdown"
            android:visibility="invisible"
            android:textColor="@color/option_icon_tint"
            android:textSize="18sp"/>
        <TextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"/>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/zoom_layout"
        android:layout_width="25dp"
        android:layout_height="wrap_content"
        android:background="@drawable/ic_camera_zoom_bg"
        android:gravity="center_vertical|end"
        android:layout_gravity="center_vertical|end">

        <TextView
            android:id="@+id/zoom_factor"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="1.0x"
            android:textSize="12sp"
            android:textColor="#FFFFFF"
            android:gravity="center"
            android:layout_gravity="center_vertical"/>
    </LinearLayout>


    <RelativeLayout
        android:id="@+id/bottom_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/top_bar_bg">
        <LinearLayout
            android:id="@+id/sv_change_camera"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:layout_marginStart="32dp"
            android:background="@drawable/ic_camera_change_bg"
            android:gravity="center"
            android:layout_marginLeft="32dp"
            android:layout_centerVertical="true">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_camera_change" />
        </LinearLayout>

        <ImageView
            android:id="@+id/sv_capture"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_capture"
            android:layout_centerInParent="true"
            android:layout_centerVertical="true"
            />
    </RelativeLayout>

    <!--    拍摄完成之后-->
    <ImageView
        android:id="@+id/iv_show_camera_activity"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitCenter"
        android:visibility="gone"/>
    <VideoView
        android:id="@+id/vv_show_camera_activity"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitCenter"
        android:visibility="gone"/>
    <LinearLayout
        android:id="@+id/iv_show_chose_fail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|start"
        android:layout_marginBottom="60dp"
        android:layout_marginStart="32dp"
        android:background="@drawable/ic_chose_fail_bg"
        android:gravity="center"
        android:layout_marginLeft="32dp"
        android:visibility="gone">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_chose_fail" />
    </LinearLayout>
    <LinearLayout
        android:id="@+id/iv_show_chose_success"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginBottom="60dp"
        android:layout_marginEnd="32dp"
        android:background="@drawable/ic_chose_success_bg"
        android:gravity="center"
        android:layout_marginRight="32dp"
        android:visibility="gone">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_chose_success" />
    </LinearLayout>
</FrameLayout>

package io.dcloud.uniplugin.utils;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.ContentValues;
import android.content.Context;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.ProgressBar;
import android.widget.Toast;

import androidx.annotation.NonNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

import io.dcloud.uniplugin.db.DatabaseConstants;
import io.dcloud.uniplugin.db.DccyDdcDBHelper;
import io.dcloud.uniplugin.db.SamplingPointsManager;
import io.dcloud.uniplugin.enums.DccyZt;
import io.dcloud.uniplugin.enums.SharedPreferencesEnum;
import io.dcloud.uniplugin.form.utils.FormJsonUtils;
import io.dcloud.uniplugin.form.utils.FormLocalStorageManager;
import io.dcloud.uniplugin.http.RetrofitManager;
import io.dcloud.uniplugin.model.ApiResponse;
import io.dcloud.uniplugin.model.DccyDdcVO;
import io.dcloud.uniplugin.model.DccyVO;
import io.dcloud.uniplugin.model.FormConfig;
import io.dcloud.uniplugin.model.FormConfigResponse;
import io.dcloud.uniplugin.service.FormConfigDatabaseHelper;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 样点数据同步管理器
 * 用于处理待调查点信息和已调查样点数据的下载和同步
 */
public class SamplingPointSyncManager {
    private static final String TAG = "SamplingPointSyncManager";
    
    // 单例模式
    private static SamplingPointSyncManager instance;
    
    private SamplingPointSyncManager() {
        // 私有构造方法
    }
    
    /**
     * 获取实例（单例模式）
     */
    public static synchronized SamplingPointSyncManager getInstance() {
        if (instance == null) {
            instance = new SamplingPointSyncManager();
        }
        return instance;
    }
    
    /**
     * 同步接口回调
     */
    public interface SyncCallback {
        void onSuccess();
        void onFailure(String errorMessage);
    }
    
    /**
     * 同步下载数据（阻塞方式）
     * 此方法在主线程中显示对话框并等待异步操作完成
     * 
     * @param activity Activity上下文，必须在主线程中调用
     * @param callback 同步完成后的回调接口，可选
     */
    public void downloadDataSynchronously(Activity activity, SyncCallback callback) {
        // 确保在主线程中执行
        if (Looper.myLooper() != Looper.getMainLooper()) {
            // 如果不在主线程，则切换到主线程执行
            activity.runOnUiThread(() -> downloadDataSynchronously(activity, callback));
            return;
        }
        
        // 创建并显示不可取消的对话框
        AlertDialog.Builder builder = new AlertDialog.Builder(activity);
        builder.setTitle("正在同步数据");
        builder.setMessage("正在同步样点数据，请稍候...");
        builder.setCancelable(false); // 设置对话框不可取消
        
        // 添加进度条
        ProgressBar progressBar = new ProgressBar(activity);
        builder.setView(progressBar);
        
        AlertDialog dialog = builder.create();
        dialog.show();
        
        // 定义用于跟踪完成状态的AtomicBoolean
        final AtomicBoolean isCompleted = new AtomicBoolean(false);
        
        // 创建一个关闭对话框并显示结果的辅助方法
        Runnable completeWithResult = new Runnable() {
            @Override
            public void run() {
                // 只有当这是第一次完成时才执行（防止重复执行）
                if (isCompleted.compareAndSet(false, true)) {
                    Log.d(TAG, "数据同步过程结束，关闭对话框");
                    // 确保在主线程中关闭对话框
                    if (Looper.myLooper() == Looper.getMainLooper()) {
                        if (dialog.isShowing()) {
                            try {
                                dialog.dismiss();
//                                Toast.makeText(activity, "数据同步完成", Toast.LENGTH_SHORT).show();
                            } catch (Exception e) {
                                Log.e(TAG, "关闭对话框出错: " + e.getMessage());
                            }
                        }
                        // 调用回调接口
                        if (callback != null) {
                            callback.onSuccess();
                        }
                    } else {
                        activity.runOnUiThread(() -> {
                            if (dialog.isShowing()) {
                                try {
                                    dialog.dismiss();
//                                    Toast.makeText(activity, "数据同步完成", Toast.LENGTH_SHORT).show();
                                } catch (Exception e) {
                                    Log.e(TAG, "关闭对话框出错: " + e.getMessage());
                                }
                            }
                            // 调用回调接口
                            if (callback != null) {
                                callback.onSuccess();
                            }
                        });
                    }
                }
            }
        };
        
        // 使用异步方法，但通过对话框阻止用户交互
        downloadDcydList(activity, false, new SyncCallback() {
            @Override
            public void onSuccess() {
                Log.d(TAG, "数据同步成功回调");
                completeWithResult.run();
            }
            
            @Override
            public void onFailure(String errorMessage) {
                // 在主线程中关闭对话框并显示错误
                Log.d(TAG, "数据同步失败回调: " + errorMessage);
                if (isCompleted.compareAndSet(false, true)) {
                    activity.runOnUiThread(() -> {
                        if (dialog.isShowing()) {
                            try {
                                dialog.dismiss();
//                                Toast.makeText(activity, "同步失败: " + errorMessage, Toast.LENGTH_SHORT).show();
                            } catch (Exception e) {
                                Log.e(TAG, "关闭对话框出错: " + e.getMessage());
                            }
                        }
                        // 调用失败回调
                        if (callback != null) {
                            callback.onFailure(errorMessage);
                        }
                    });
                }
            }
        });
        
        // 添加安全措施：如果30秒后对话框仍然显示，则强制关闭它
        // 使用主线程的Handler
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.postDelayed(() -> {
            if (!isCompleted.get() && dialog.isShowing()) {
                Log.w(TAG, "检测到数据同步超时，强制关闭对话框");
                isCompleted.set(true);
                try {
                    dialog.dismiss();
                    Toast.makeText(activity, "数据同步超时，请稍后重试", Toast.LENGTH_SHORT).show();
                } catch (Exception e) {
                    Log.e(TAG, "关闭对话框出错: " + e.getMessage());
                }
                // 调用超时失败回调
                if (callback != null) {
                    callback.onFailure("数据同步超时");
                }
            }
        }, 30000); // 30秒超时
    }
    
    /**
     * 同步下载数据（阻塞方式）- 兼容性方法
     * 此方法在主线程中显示对话框并等待异步操作完成
     * 
     * @param activity Activity上下文，必须在主线程中调用
     */
    public void downloadDataSynchronously(Activity activity) {
        downloadDataSynchronously(activity, null);
    }
    
    /**
     * 下载待调查点列表
     * 
     * @param activity Activity上下文
     * @param showProgressDialog 是否显示进度对话框
     * @param callback 回调接口
     */
    public void downloadDcydList(Activity activity, boolean showProgressDialog, SyncCallback callback) {
        if (activity == null) {
            Log.e(TAG, "Activity为空，无法下载数据");
            if (callback != null) {
                callback.onFailure("Activity为空，无法下载数据");
            }
            return;
        }
        
        //判断是否在线登录
        SharedPreferences sp = activity.getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, Context.MODE_PRIVATE);
        boolean isOfflineLogin = sp.getBoolean(SharedPreferencesEnum.IS_OFFLINE_LOGIN.value, false);
        
        // 获取当前用户ID
        Long userId = getUserId(activity);
        
        if (!isOfflineLogin) {
            // 创建并显示加载对话框
            AlertDialog progressDialog = null;
            if (showProgressDialog) {
                AlertDialog.Builder builder = new AlertDialog.Builder(activity);
                builder.setTitle("正在同步");
                builder.setMessage("正在同步调查数据，请稍候...");
                builder.setCancelable(false);
                progressDialog = builder.create();
                progressDialog.show();
            }
            
            // 保存对话框的最终引用
            final AlertDialog finalProgressDialog = progressDialog;
            
            // 使用getDcrList接口获取数据
            RetrofitManager.getInstance(activity)
                    .getDccyService()
                    .getDcrList(DccyZt.DCCY_ZT_DDC.getValue())
                    .enqueue(new Callback<ApiResponse<List<DccyDdcVO>>>() {
                        @Override
                        public void onResponse(Call<ApiResponse<List<DccyDdcVO>>> call, Response<ApiResponse<List<DccyDdcVO>>> response) {
                            // 关闭加载对话框
                            if (finalProgressDialog != null) {
                                finalProgressDialog.dismiss();
                            }

                            if (response.isSuccessful() && response.body() != null) {
                                ApiResponse<List<DccyDdcVO>> apiResponse = response.body();
                                if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                                    List<DccyDdcVO> dcrList = apiResponse.getData();
                                    
                                    // 不管列表是否为空，都保存到数据库（清空旧数据）
                                    saveDccyDdcListToDatabase(activity, dcrList, userId);
                                    downloadUsingRegularMethod(activity, userId, callback);
                                    if (dcrList.isEmpty()) {
                                        Log.e(TAG, "没有有效的待调查点数据");
//                                        Toast.makeText(activity, "没有待调查点数据，已清空本地缓存", Toast.LENGTH_SHORT).show();
                                        if (callback != null) {
                                            callback.onSuccess(); // 返回成功，因为清空表是预期行为
                                        }
                                    }
                                } else {
                                    String errorMsg = apiResponse != null ? apiResponse.getMsg() : "API响应失败";
                                    Log.e(TAG, "API响应失败: " + errorMsg);
                                    Toast.makeText(activity, "API响应失败: " + errorMsg, Toast.LENGTH_SHORT).show();
                                    if (callback != null) {
                                        callback.onFailure(errorMsg);
                                    }
                                }
                            } else {
                                Log.e(TAG, "请求失败: " + (response.errorBody() != null ? "错误响应" : "响应为空"));
                                Toast.makeText(activity, "请求失败", Toast.LENGTH_SHORT).show();
                                if (callback != null) {
                                    callback.onFailure("请求失败");
                                }
                            }
                        }

                        @Override
                        public void onFailure(Call<ApiResponse<List<DccyDdcVO>>> call, Throwable t) {
                            // 关闭加载对话框
                            if (finalProgressDialog != null) {
                                finalProgressDialog.dismiss();
                            }

                            Log.e(TAG, "请求失败: " + t.getMessage(), t);
                            Toast.makeText(activity, "调查数据信息同步失败", Toast.LENGTH_SHORT).show();

                            // 获取本地缓存的数据
                            showLocalDcrData(activity, userId);
                            
                            if (callback != null) {
                                callback.onFailure(t.getMessage());
                            }
                        }
                    });
        } else {
            // 离线登录时，显示本地缓存的数据
            showLocalDcrData(activity, userId);
            if (callback != null) {
                callback.onSuccess();
            }
        }
    }
    
    /**
     * 下载已调查的数据
     */
    public void downloadUsingRegularMethod(Activity activity, Long userId, SyncCallback callback) {
        if (activity == null) {
            Log.e(TAG, "Activity为空，无法下载数据");
            if (callback != null) {
                callback.onFailure("Activity为空，无法下载数据");
            }
            return;
        }
        
        RetrofitManager.getInstance(activity)
                .getDccyService()
                .getDccyList()
                .enqueue(new Callback<ApiResponse<List<FormConfigResponse>>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<List<FormConfigResponse>>> call, Response<ApiResponse<List<FormConfigResponse>>> response) {
                        if (response.isSuccessful() && response.body() != null) {
                            try {
                                // 获取响应中的data数组
                                List<FormConfigResponse> data = response.body().getData();
                                // 解析数据，存到数据库
                                // 转换响应数据为DccyVO列表
                                List<DccyVO> dccyList = FormJsonUtils.extractDccyVOFromJson(data);
                                if (dccyList != null && !dccyList.isEmpty()) {
                                    // 保存样点数据到数据库 (传递 userId)
                                    saveDccyListToDatabase(activity, dccyList, data, userId);
                                    Log.d(TAG, "成功下载并转换" + dccyList.size() + "个样点信息");
//                                    Toast.makeText(activity, "样点信息同步成功", Toast.LENGTH_SHORT).show();
                                    
                                    // 使用MediaDownloadManager下载媒体文件
                                    MediaDownloadManager downloadManager = new MediaDownloadManager(activity);
                                    downloadManager.setDownloadCompletionListener(new MediaDownloadManager.DownloadCompletionListener() {
                                        @Override
                                        public void onDownloadCompleted(int successCount, int failureCount) {
                                            Log.d(TAG, "媒体文件下载完成，成功: " + successCount + ", 失败: " + failureCount);
                                            
                                            // 显示下载结果
                                            if (activity != null && !activity.isFinishing()) {
                                                String message;
                                                if (failureCount > 0) {
                                                    message = "同步完成，但有 " + failureCount + " 个媒体文件下载失败";
                                                } else {
                                                    message = "同步完成";
                                                }
                                                Toast.makeText(activity, message, Toast.LENGTH_SHORT).show();
                                            }
                                            
                                            if (callback != null) {
                                                if (failureCount > 0) {
                                                    callback.onFailure("同步完成，但有 " + failureCount + " 个媒体文件下载失败");
                                                } else {
                                                    callback.onSuccess();
                                                }
                                            }
                                        }
                                    });
                                    downloadManager.downloadMediaFilesWithProgress(dccyList);
                                } else {
                                    Log.e(TAG, "没有有效的样点数据");
//                                    Toast.makeText(activity, "没有已调查信息", Toast.LENGTH_SHORT).show();
                                    if (callback != null) {
                                        callback.onFailure("没有下发数据");
                                    }
                                }

                            } catch (Exception e) {
                                Log.e(TAG, "处理响应数据时出错: " + e.getMessage(), e);
                                Toast.makeText(activity, "处理数据时出错: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                                if (callback != null) {
                                    callback.onFailure("处理数据时出错: " + e.getMessage());
                                }
                            }
                        } else {
                            Log.e(TAG, "请求失败: " + (response.errorBody() != null ? "错误响应" : "响应为空"));
                            Toast.makeText(activity, "请求失败", Toast.LENGTH_SHORT).show();
                            if (callback != null) {
                                callback.onFailure("请求失败");
                            }
                        }
                    }

                    @Override
                    public void onFailure(Call<ApiResponse<List<FormConfigResponse>>> call, Throwable t) {
                        Log.e(TAG, "请求失败: " + t.getMessage(), t);
                        Toast.makeText(activity, "样点信息同步失败", Toast.LENGTH_SHORT).show();
                        if (callback != null) {
                            callback.onFailure("样点信息同步失败: " + t.getMessage());
                        }
                    }
                });
    }
    
    /**
     * 保存待调查点列表到数据库
     */
    private void saveDccyDdcListToDatabase(Context context, List<DccyDdcVO> data, Long userId) {
        if (data == null) {
            data = new ArrayList<>(); // 如果传入的数据为null则创建一个空列表
        }
        
        if (userId == null) {
             Log.e(TAG, "User ID is null, cannot save DDC points with user association.");
             if (context != null) {
                 Toast.makeText(context, "无法获取用户ID，保存待调查点失败", Toast.LENGTH_SHORT).show();
             }
             return;
        }
        
        // 获取本地离线缓存的样点数据BSM列表
        List<String> localSavedBsmList = new ArrayList<>();
        
        // 使用FormLocalStorageManager获取所有本地保存的表单
        try {
            List<Map<String, Object>> savedForms = FormLocalStorageManager.getSavedForms(context);
            for (Map<String, Object> form : savedForms) {
                String bsm = (String) form.get("bsm");
                if (bsm != null && !bsm.isEmpty()) {
                    localSavedBsmList.add(bsm);
                    Log.d(TAG, "本地已保存的表单BSM: " + bsm);
                }
            }
            Log.d(TAG, "从本地存储获取到 " + localSavedBsmList.size() + " 个已保存的BSM");
        } catch (Exception e) {
            Log.e(TAG, "获取本地表单BSM列表失败: " + e.getMessage(), e);
        }

        // 获取表单配置数据库中的数据
        FormConfigDatabaseHelper configDbHelper = FormConfigDatabaseHelper.getInstance(context);
        List<FormConfig> allFormConfigs = configDbHelper.getAllFormConfigs();
        for (FormConfig formConfig : allFormConfigs) {
            String formId = formConfig.getFormId();
            if (formId != null && !formId.isEmpty() && !localSavedBsmList.contains(formId)) {
                localSavedBsmList.add(formId);
                Log.d(TAG, "从表单配置中获取的BSM: " + formId);
            }
        }

        // 获取DccyDdcDBHelper实例
        DccyDdcDBHelper dbHelper = DccyDdcDBHelper.getInstance(context);
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        int successCount = 0;
        
        try {
            // 验证表是否存在
            Cursor tableCheck = db.rawQuery(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name=?", 
                    new String[]{DatabaseConstants.TABLE_DDC_POINTS});
            boolean tableExists = tableCheck.moveToFirst();
            tableCheck.close();
            
            if (!tableExists) {
                Log.w(TAG, "表不存在，尝试创建表: " + DatabaseConstants.TABLE_DDC_POINTS);
                // 尝试创建表
                db.execSQL(DatabaseConstants.CREATE_TABLE_DDC_POINTS);
            }
            
            db.beginTransaction();

            // 清空待调查数据表 - 无论传入数据是否为空都执行此操作
            int deletedRows = db.delete(DatabaseConstants.TABLE_DDC_POINTS, null, null);
            Log.d(TAG, "已清空表: " + DatabaseConstants.TABLE_DDC_POINTS + ", 删除了 " + deletedRows + " 行");
            
            // 仅在数据非空的情况下插入
            if (!data.isEmpty()) {
                // 插入数据
                for (DccyDdcVO dccyDdcVO : data) {
                    ContentValues values = new ContentValues();
                    
                    // 设置基本信息
                    if (dccyDdcVO.getId() != null) values.put(DatabaseConstants.COLUMN_DDC_ID, dccyDdcVO.getId());
                    if (dccyDdcVO.getPjdyId() != null) values.put(DatabaseConstants.COLUMN_DDC_PJDY_ID, dccyDdcVO.getPjdyId());
                    if (dccyDdcVO.getpjdybh() != null) values.put(DatabaseConstants.COLUMN_DDC_PJDY_BSM, dccyDdcVO.getpjdybh());
                    if (dccyDdcVO.getDcdwId() != null) values.put(DatabaseConstants.COLUMN_DDC_DCDW_ID, dccyDdcVO.getDcdwId());
                    if (dccyDdcVO.getDcdw() != null) values.put(DatabaseConstants.COLUMN_DDC_DCDW_NAME, dccyDdcVO.getDcdw());
                    if (dccyDdcVO.getDcrId() != null) values.put(DatabaseConstants.COLUMN_DDC_DCR_ID, dccyDdcVO.getDcrId());
                    if (dccyDdcVO.getDcr() != null) values.put(DatabaseConstants.COLUMN_DDC_DCR_NAME, dccyDdcVO.getDcr());
                    if (dccyDdcVO.getXfrId() != null) values.put(DatabaseConstants.COLUMN_DDC_XFR_ID, dccyDdcVO.getXfrId());
                    if (dccyDdcVO.getXfr() != null) values.put(DatabaseConstants.COLUMN_DDC_XFR, dccyDdcVO.getXfr());
                    if (dccyDdcVO.getDcTime() != null) values.put(DatabaseConstants.COLUMN_DDC_DC_TIME, dccyDdcVO.getDcTime());
                    if (dccyDdcVO.getXfsj() != null) values.put(DatabaseConstants.COLUMN_DDC_XFSJ, dccyDdcVO.getXfsj());
                    if (dccyDdcVO.getZt() != null) values.put(DatabaseConstants.COLUMN_DDC_ZT, dccyDdcVO.getZt());
                    if (dccyDdcVO.getChrId() != null) values.put(DatabaseConstants.COLUMN_DDC_CHR_ID, dccyDdcVO.getChrId());
                    if (dccyDdcVO.getChr() != null) values.put(DatabaseConstants.COLUMN_DDC_CHR, dccyDdcVO.getChr());
                    if (dccyDdcVO.getChsj() != null) values.put(DatabaseConstants.COLUMN_DDC_CHSJ, dccyDdcVO.getChsj());
                    if (dccyDdcVO.getDwjd() != null) values.put(DatabaseConstants.COLUMN_DDC_DWJD, dccyDdcVO.getDwjd());
                    if (dccyDdcVO.getDwwd() != null) values.put(DatabaseConstants.COLUMN_DDC_DWWD, dccyDdcVO.getDwwd());
                    if (dccyDdcVO.getXmmc() != null) values.put(DatabaseConstants.COLUMN_XMMC, dccyDdcVO.getXmmc());

                    // 设置 user_id
                    values.put(DatabaseConstants.COLUMN_USER_ID, userId);

                    // 检查是否存在本地数据
                    int hasLocalData = (dccyDdcVO.getpjdybh() != null && localSavedBsmList.contains(dccyDdcVO.getpjdybh())) ? 1 : 0;
                    values.put(DatabaseConstants.COLUMN_HAS_LOCAL_DATA, hasLocalData);
                    
                    // 验证values是否为空
                    if (values.size() == 0) {
                        Log.e(TAG, "ContentValues为空，跳过插入");
                        continue;
                    }
                    
                    // 插入数据
                    long rowId = db.insert(DatabaseConstants.TABLE_DDC_POINTS, null, values);
                    if (rowId == -1) {
                        Log.e(TAG, "插入数据失败: ID=" + dccyDdcVO.getId() + ", BSM=" + dccyDdcVO.getpjdybh());
                    } else {
                        successCount++;
                        Log.d(TAG, "成功插入数据: ID=" + dccyDdcVO.getId() + ", rowId=" + rowId);
                    }
                }
                
                // 验证插入数据数量
                Cursor cursor = db.rawQuery("SELECT COUNT(*) FROM " + DatabaseConstants.TABLE_DDC_POINTS, null);
                int count = 0;
                if (cursor.moveToFirst()) {
                    count = cursor.getInt(0);
                }
                cursor.close();
                Log.d(TAG, "事务内表中实际记录数: " + count + ", 应插入: " + data.size());
                
                Log.d(TAG, "成功保存 " + successCount + "/" + data.size() + " 条调查数据记录到数据库");
            } else {
                Log.d(TAG, "数据列表为空，仅清空了数据表");
            }
            
            db.setTransactionSuccessful();
        } catch (Exception e) {
            Log.e(TAG, "保存调查数据数据到数据库失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (db != null) {
                if (db.inTransaction()) {
                    db.endTransaction();
                }
                db.close(); // 确保关闭数据库连接
            }
        }
    }
    
    /**
     * 将样点列表保存到数据库
     */
    private void saveDccyListToDatabase(Context context, List<DccyVO> dccyList, List<FormConfigResponse> data, Long userId) {
        if (dccyList == null || dccyList.isEmpty()) {
            return;
        }
        if (userId == null) {
            Log.e(TAG, "User ID is null, cannot save sampling points with user association.");
            if (context != null) {
                Toast.makeText(context, "无法获取用户ID，保存样点信息失败", Toast.LENGTH_SHORT).show();
            }
            return;
        }

        // SamplingPointsManager.saveSamplingPoints 需要修改以接收 userId
        int successCount = SamplingPointsManager.getInstance(context)
                .saveSamplingPoints(dccyList, data, userId); // 传递 userId
        
        Log.d(TAG, "成功保存样点数据 for user " + userId + ", 共 " + successCount + "/" + dccyList.size() + " 条记录");
    }
    
    /**
     * 显示本地缓存的调查数据数据
     */
    private void showLocalDcrData(Context context, Long userId) {
        DccyDdcDBHelper dbHelper = DccyDdcDBHelper.getInstance(context);
        List<DccyDdcVO> dcrList = dbHelper.getAllDcrList(userId);

        if (dcrList != null && !dcrList.isEmpty()) {
            Log.d(TAG, "从本地数据库加载了 " + dcrList.size() + " 条调查数据信息 for user " + userId);
            if (context != null) {
                Toast.makeText(context, "已加载本地缓存的待调查点信息", Toast.LENGTH_SHORT).show();
            }
        } else {
            Log.d(TAG, "本地数据库中没有调查数据信息 for user " + userId);
            if (context != null) {
                Toast.makeText(context, "未找到本地缓存的待调查点信息", Toast.LENGTH_SHORT).show();
            }
        }
    }
    
    /**
     * 获取当前用户ID
     */
    @NonNull
    private Long getUserId(Context context) {
        if (context == null) {
            Log.e(TAG, "Context为空，无法获取用户ID");
            return -1L;
        }
        
        SharedPreferences sp = context.getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, Context.MODE_PRIVATE);
        Long userId = sp.getLong(SharedPreferencesEnum.USER_ID.value, -1L);
        if (userId == -1L) {
            Log.e(TAG, "Failed to retrieve user ID from SharedPreferences.");
            return -1L;
        }
        Log.d(TAG, "Current User ID: " + userId);
        return userId;
    }
} 
package io.dcloud.uniplugin.http.api;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.UUID;

import io.dcloud.uniplugin.model.ApiResponse;
import io.dcloud.uniplugin.http.RetrofitManager;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.Part;

/**
 * 文件上传服务
 */
public class FileUploadService {
    
    private static final String TAG = "FileUploadService";
    
    /**
     * 文件上传接口
     */
    public interface FileUploadApi {
        @Multipart
        @POST("infra/file/upload")
        Call<ApiResponse<String>> uploadFile(
                @Part MultipartBody.Part file
        );
    }
    
    /**
     * 文件上传回调
     */
    public interface FileUploadCallback {
        void onSuccess(String fileUrl);
        void onFailure(String errorMsg);
    }
    
    private Activity activity;
    
    public FileUploadService(Activity activity) {
        this.activity = activity;
    }
    
    /**
     * 上传签名图片
     * @param signatureBitmap 签名图片
     * @param callback 回调
     */
    public void uploadSignature(Bitmap signatureBitmap, FileUploadCallback callback) {
        if (signatureBitmap == null) {
            if (callback != null) {
                callback.onFailure("签名图片为空");
            }
            return;
        }
        
        // 将Bitmap保存为临时文件
        File signatureFile = saveBitmapToFile(activity, signatureBitmap);
        if (signatureFile == null) {
            if (callback != null) {
                callback.onFailure("保存签名图片失败");
            }
            return;
        }
        
        // 创建MultipartBody.Part用于上传
        RequestBody requestFile = RequestBody.create(MediaType.parse("image/png"), signatureFile);
        MultipartBody.Part body = MultipartBody.Part.createFormData("file", signatureFile.getName(), requestFile);
        
        // 获取API服务
        FileUploadApi fileUploadApi = RetrofitManager.getInstance(activity).getRetrofit().create(FileUploadApi.class);
        
        // 发起上传请求
        fileUploadApi.uploadFile(body).enqueue(new Callback<ApiResponse<String>>() {
            @Override
            public void onResponse(Call<ApiResponse<String>> call, Response<ApiResponse<String>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<String> apiResponse = response.body();
                    if (apiResponse.getData() != null) {
                        if (callback != null) {
                            callback.onSuccess(apiResponse.getData());
                        }
                    } else {
                        if (callback != null) {
                            callback.onFailure(apiResponse.getMsg() != null ? apiResponse.getMsg() : "上传失败");
                        }
                    }
                } else {
                    if (callback != null) {
                        callback.onFailure("上传失败，服务器响应异常");
                    }
                }
                
                // 删除临时文件
                if (signatureFile.exists()) {
                    signatureFile.delete();
                }
            }
            
            @Override
            public void onFailure(Call<ApiResponse<String>> call, Throwable t) {
                Log.e(TAG, "上传签名失败", t);
                if (callback != null) {
                    callback.onFailure("上传失败：" + t.getMessage());
                }
                
                // 删除临时文件
                if (signatureFile.exists()) {
                    signatureFile.delete();
                }
            }
        });
    }
    
    /**
     * 将Bitmap保存为临时文件
     * @param context 上下文
     * @param bitmap 位图
     * @return 临时文件
     */
    private File saveBitmapToFile(Context context, Bitmap bitmap) {
        try {
            // 创建临时文件名
            String fileName = "signature_" + UUID.randomUUID().toString() + ".png";
            File file = new File(context.getCacheDir(), fileName);
            
            // 保存Bitmap到文件
            FileOutputStream fos = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, fos);
            fos.flush();
            fos.close();
            
            return file;
        } catch (IOException e) {
            Log.e(TAG, "保存Bitmap到文件失败", e);
            return null;
        }
    }
} 
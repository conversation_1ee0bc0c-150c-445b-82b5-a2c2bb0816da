package io.dcloud.uniplugin;

import android.app.Activity;
import android.content.Context;
import android.media.Image;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.esri.arcgisruntime.layers.GroupLayer;
import com.esri.arcgisruntime.layers.GroupVisibilityMode;
import com.esri.arcgisruntime.layers.Layer;
import com.esri.arcgisruntime.loadable.LoadStatus;
import com.esri.arcgisruntime.mapping.view.MapView;
import com.google.android.material.bottomsheet.BottomSheetBehavior;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import uni.dcloud.io.uniplugin_module.R;

/**
 * An adapter that displays {@link Layer}s and their children if they are an instance of {@link GroupLayer}
 */
public class LayersAdapter extends RecyclerView.Adapter<LayersAdapter.ViewHolder> implements OnLayerCheckedChangedListener {


    private Context contxt2;
    private MainActivity activity2;

    private static final int VIEW_TYPE_PARENT = 0;
    private static final int VIEW_TYPE_LAYER = 1;
    //理论上说mlayers的长度应该是4
    private final List<Layer> mLayers = new ArrayList<>();
    private final OnLayerCheckedChangedListener mOnLayerCheckedChangedListener;

    public LayersAdapter(OnLayerCheckedChangedListener onLayerCheckedChangedListener,Context context,MainActivity mainActivity) {
        mOnLayerCheckedChangedListener = onLayerCheckedChangedListener;
        this.setActivity(mainActivity);
        this.setContxt(context);
    }


    public void setContxt(Context contxt) {
        this.contxt2 = (MainActivity) contxt;
    }
    public void setActivity(Activity activity) {
        this.activity2 = (MainActivity) activity;
    }



    @NonNull @Override public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        // inflate the layout for a GroupLayer
        if (getItemViewType(i) == VIEW_TYPE_PARENT) {
            return new ParentViewHolder(
                    LayoutInflater.from(viewGroup.getContext())
                            .inflate(R.layout.adapter_item_group_layer_parent, viewGroup, false),
                    this);
        } else {
            // inflate the layout for a Layer
            return new LayerViewHolder(
                    LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.adapter_item_group_layer, viewGroup, false),
                    this);
        }
    }

    @Override public void onBindViewHolder(@NonNull ViewHolder viewHolder, int i) {
        viewHolder.bind(mLayers.get(i), mLayers.get(i).isVisible());
    }

    @Override public int getItemViewType(int position) {
        if (mLayers.get(position) instanceof GroupLayer) {
            return VIEW_TYPE_PARENT;
        } else {
            return VIEW_TYPE_LAYER;
        }
    }

    @Override public int getItemCount() {
        return mLayers.size();
    }

    /**
     * Add a {@link Layer} to the adapter
     *
     * @param layer
     */
    void addLayer(Layer layer) {
        if (!mLayers.contains(layer)) {
            mLayers.add(layer);
            notifyItemInserted(mLayers.size() - 1);
        }
    }

    /**
     * Called when a checkbox on a layer in the list is checked or unchecked
     *
     * @param layer   that has been checked or unchecked
     * @param checked whether the checkbox has been checked or unchecked
     */
    @Override public void layerCheckedChanged(Layer layer, boolean checked) {
        if (mOnLayerCheckedChangedListener != null) {
            mOnLayerCheckedChangedListener.layerCheckedChanged(layer, checked);
        }
        notifyDataSetChanged();
    }

    /**
     * Subclass of {@link ViewHolder} to display {@link View}s related to {@link GroupLayer}s and their children {@link Layer}s
     */
    class ParentViewHolder extends ViewHolder {

        private final CheckBox mParentCheckbox;
        private final TextView mParentTextView;
        private final ViewGroup mChildLayout;
        private RadioGroup mRadioGroup;
        private ImageView ivElmentSub;
        private ImageView ivElmentAdd;
        private EditText etElementCount;
        private ImageView ivMoveUp;
        private ImageView ivMoveDown;
        private ImageView exitButton;


        //父勾选框
        ParentViewHolder(@NonNull View itemView, OnLayerCheckedChangedListener onLayerCheckedChangedListener) {
            super(itemView, onLayerCheckedChangedListener);
            mParentCheckbox = itemView.findViewById(R.id.layerCheckbox);
            mParentTextView = itemView.findViewById(R.id.layerNameTextView);
            mChildLayout = itemView.findViewById(R.id.childLayout);
            ivElmentSub = itemView.findViewById(R.id.iv_element_sub);
            ivElmentAdd = itemView.findViewById(R.id.iv_element_add);
            etElementCount = itemView.findViewById(R.id.et_element_count);
            ivMoveUp = itemView.findViewById(R.id.move_up_btn);
            ivMoveDown = itemView.findViewById(R.id.down_btn);
            //退出按钮仅在父容器中可以存在
            exitButton = itemView.findViewById(R.id.exitButton);
            exitButton.setVisibility(View.VISIBLE);
        }

        @Override void bind(Layer layer, boolean isVisible) {
            LinearLayout linearLayout = (LinearLayout) itemView.findViewById(R.id.ll_element_count);
            if (isVisible){
                linearLayout.setVisibility(View.VISIBLE);
                initView(ivElmentSub,ivElmentAdd,etElementCount,ivMoveUp,ivMoveDown,exitButton,layer);
            }else {
                linearLayout.setVisibility(View.INVISIBLE);
            }
            //父勾选框的按钮反映处。
            mParentCheckbox.setOnCheckedChangeListener(null);
            mParentCheckbox.setChecked(isVisible);
            mParentTextView.setText(layer.getName());
            Log.i("Parentname",(String) layer.getName());

            mParentCheckbox.setOnCheckedChangeListener(
                    (buttonView, isChecked) -> mOnLayerCheckedChangedListener.layerCheckedChanged(layer, isChecked));

            // if children can be shown in legend
            if (((GroupLayer) layer).isShowChildrenInLegend()) {

                boolean isExclusive = ((GroupLayer) layer).getVisibilityMode() == GroupVisibilityMode.EXCLUSIVE;
                if (isExclusive) {
                    mRadioGroup = new RadioGroup(mParentCheckbox.getContext());
                    mChildLayout.addView(mRadioGroup);
                }

                for (Layer childLayer : ((GroupLayer) layer).getLayers()) {
                    // if the layer has not been loaded
                    if (childLayer.getLoadStatus() != LoadStatus.LOADED) {
                        // add a listener to run when the layer has been loaded
                        childLayer.addDoneLoadingListener(() -> addChildLayer(childLayer));
                        // load layer
                        childLayer.loadAsync();

                    } else {
                        addChildLayer(childLayer);
                    }
                }
            }
        }

        /**
         * Add a {@link View} showing the child {@link Layer} of the {@link GroupLayer}
         *
         * @param childLayer layer to display
         */

        private void addChildLayer(Layer childLayer) {
            View view;

            // try to reuse the view if possible
            if (mChildLayout.findViewWithTag(childLayer) == null) {
                view = LayoutInflater.from(itemView.getContext())
                        .inflate(R.layout.adapter_item_group_layer, mChildLayout, false);
                view.setTag(childLayer);
            } else {
                view = mChildLayout.findViewWithTag(childLayer);
            }

            ((LinearLayout.LayoutParams) view.getLayoutParams()).setMarginStart(
                    itemView.getResources().getDimensionPixelSize(R.dimen.adapter_item_child_margin_start));

            CheckBox checkBox = view.findViewById(R.id.layerCheckbox);

            // if this is an exclusive layer it will have a non-null mRadioGroup
            if (mRadioGroup != null) {
                // hide the checkbox that exists
                checkBox.setVisibility(View.GONE);
                // create a radio button or reuse an old one
                RadioButton radioButton = view.findViewWithTag("radioButton");
                if (radioButton == null) {
                    radioButton = new RadioButton(itemView.getContext());
                    radioButton.setTag("radioButton");
                    ((ViewGroup) view).addView(radioButton, 0);
                }
                //TODO:这里还有个问题，单选型的groupLayer，parent已经取消勾选了，图不见了，但是单选框并没有取消勾选，而且数字框也没有隐藏
                radioButton.setOnCheckedChangeListener(null);
                radioButton.setChecked(childLayer.isVisible());
                radioButton.setOnCheckedChangeListener(
                        ((buttonView, isChecked) -> mOnLayerCheckedChangedListener.layerCheckedChanged(childLayer, isChecked)));
                TextView textView = view.findViewById(R.id.layerNameTextView);
                textView.setText(childLayer.getName());
                //判断textView，当名称为"业务图层"时，删除后面的按钮


                //对数字框的点击事件进行初始化
                ImageView ivElmentSub = view.findViewById(R.id.iv_element_sub);
                ImageView ivElmentAdd = view.findViewById(R.id.iv_element_add);
                EditText etElementCount = view.findViewById(R.id.et_element_count);
                ImageView ivMoveUP = view.findViewById(R.id.move_up_btn);
                ImageView ivMoveDown = view.findViewById(R.id.down_btn);
                ImageView exitButton = view.findViewById(R.id.exitButton);

                LinearLayout linearLayout = (LinearLayout) view.findViewById(R.id.ll_element_count);
                if (childLayer.isVisible()){
                    linearLayout.setVisibility(View.VISIBLE);
                    initView(ivElmentSub,ivElmentAdd,etElementCount,ivMoveUP,ivMoveDown,exitButton,childLayer);
                }else {
                    linearLayout.setVisibility(View.INVISIBLE);
                }

                if (mRadioGroup.findViewWithTag(childLayer) == null) {
                    // remove the view from the existing parent and add it to the radio group
                    ViewGroup parent = (ViewGroup) view.getParent();
                    if (parent != null) {
                        parent.removeView(view);
                    }
                    mRadioGroup.addView(view);
                }
            } else {
                checkBox.setOnCheckedChangeListener(null);
                checkBox.setChecked(childLayer.isVisible());
                checkBox.setOnCheckedChangeListener(
                        (buttonView, isChecked) -> mOnLayerCheckedChangedListener.layerCheckedChanged(childLayer, isChecked));
                TextView textView = view.findViewById(R.id.layerNameTextView);
                textView.setText(childLayer.getName());
                //对数字框的点击事件进行初始化
                ImageView ivElmentSub = view.findViewById(R.id.iv_element_sub);
                ImageView ivElmentAdd = view.findViewById(R.id.iv_element_add);
                EditText etElementCount = view.findViewById(R.id.et_element_count);
                ImageView ivMoveUp = view.findViewById(R.id.move_up_btn);
                ImageView ivMoveDown = view.findViewById(R.id.down_btn);
                ImageButton exitButton = view.findViewById(R.id.exitButton);
                LinearLayout linearLayout = (LinearLayout) view.findViewById(R.id.ll_element_count);
                if (childLayer.isVisible()){
                    linearLayout.setVisibility(View.VISIBLE);
                    initView(ivElmentSub,ivElmentAdd,etElementCount,ivMoveUp,ivMoveDown,exitButton,childLayer);
                }else {
                    linearLayout.setVisibility(View.INVISIBLE);
                }
                if (mChildLayout.findViewWithTag(childLayer) == null) {
                    mChildLayout.addView(view);
                }
            }
        }
    }

    /**
     * Subclass of {@link ViewHolder} to display {@link View}s related to a {@link Layer}
     */
    class LayerViewHolder extends ViewHolder {

        private final CheckBox mCheckbox;
        private final TextView mTextView;
        private final ImageView ivElmentSub;
        private final ImageView ivElmentAdd;
        private final EditText etElementCount;
        private final ImageView ivMoveUp;
        private final ImageView ivMoveDown;
        private final ImageButton exitButton;

        LayerViewHolder(@NonNull View itemView, OnLayerCheckedChangedListener onLayerCheckedChangedListener) {
            super(itemView, onLayerCheckedChangedListener);
            mCheckbox = itemView.findViewById(R.id.layerCheckbox);
            mTextView = itemView.findViewById(R.id.layerNameTextView);
            ivElmentSub = itemView.findViewById(R.id.iv_element_sub);
            ivElmentAdd = itemView.findViewById(R.id.iv_element_add);
            etElementCount = itemView.findViewById(R.id.et_element_count);
            ivMoveUp = itemView.findViewById(R.id.move_up_btn);
            ivMoveDown = itemView.findViewById(R.id.down_btn);
            exitButton = itemView.findViewById(R.id.exitButton);
        }

        @Override void bind(Layer layer, boolean isVisible) {
            LinearLayout linearLayout = (LinearLayout) itemView.findViewById(R.id.ll_element_count);
            if (isVisible){
                linearLayout.setVisibility(View.VISIBLE);
                initView(ivElmentSub,ivElmentAdd,etElementCount,ivMoveUp,ivMoveDown,exitButton,layer);
            }else {
                linearLayout.setVisibility(View.INVISIBLE);
            }
            mCheckbox.setOnCheckedChangeListener(null);
            mCheckbox.setChecked(isVisible);
            mTextView.setText(layer.getName());
            Log.d("name", (String) mTextView.getText());

            mCheckbox.setOnCheckedChangeListener(
                    (buttonView, isChecked) -> mOnLayerCheckedChangedListener.layerCheckedChanged(layer, isChecked));
        }
    }

    //一开始即使看不见也进行数据框的初始化函数
    private void initView(ImageView ivElementSub, ImageView ivElementAdd, EditText etElementCount,ImageView ivMoveUp, ImageView ivMoveDown ,ImageView exitButton,Layer layer) {
        ivElementSub.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                String numStr = etElementCount.getText().toString().trim();
                if (numStr.isEmpty()) {
                    numStr = "1.0";
                }
                float num = Float.parseFloat(numStr);
                num = (float) (num - 0.1);
                if (num < 0.1) {
                    num = 0.0F;
                }
                etElementCount.setText(Float.toString(num));
            }
        });

        ivElementAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                String numStr = etElementCount.getText().toString().trim();
                if (numStr.isEmpty()) {
                    numStr = "1.0";
                }
                float num = Float.parseFloat(numStr);
                num = (float) (num + 0.1);
                if (num >= 1.0) {
                    num = 1.0F;
                }
                etElementCount.setText(Float.toString(num));
            }
        });

        etElementCount.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                String countStr = editable.toString().trim();
                if (countStr.isEmpty()) {
                    countStr = "1.0";
                }
                float elementCount = Float.parseFloat(countStr);
                if (elementCount < 0.1 ) {
                    ivElementSub.setImageResource(R.drawable.un_minus);
                    ivElementAdd.setImageResource(R.drawable.addbold);
                } else if (elementCount > 0.9) {
                    ivElementSub.setImageResource(R.drawable.minus);
                    ivElementAdd.setImageResource(R.drawable.un_addbold);
                } else {
                    ivElementSub.setImageResource(R.drawable.minus);
                    ivElementAdd.setImageResource(R.drawable.addbold);
                }
                //设置透明度
                layer.setOpacity(elementCount);

            }
        });

        //声明实时更新layerName
        List upDown_layer_name = MainActivity.LayerName;
        //声明图层总数的下标
        int layer_total_index = MainActivity.LayerName.size();
        //图层上移,已完成
        ivMoveUp.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                //声明判断图层是否为最高层级的参数,0代表否，1代表是
                int is_highest_layer = 0;
                //获取选中的图层的名称
                Log.d("点击的itemView的名字", layer.getName());
                //判断是否为业务图层
                if(layer.getName().equals("业务图层")){
                    Toast.makeText(activity2, "该图层已是最顶层，无法移动！", Toast.LENGTH_LONG).show();
                }
                else{
                    //根据图层名称获取图层的层级（即加载顺序）
                    String MoveLayerName = layer.getName();
                    //获取该图层的下标
                    int layerIndex = 9999;
                    int layerIndexNext = 9999;
                    int LayerNameLength = layer_total_index;
                    System.out.println(LayerNameLength);
                    for (int i = 0; i < layer_total_index; i++) {
                        //判断图层名与其中是否存在相同，相同则记录数组下标
                        if (Objects.equals(upDown_layer_name.get(i), MoveLayerName)) {
                            if (i < layer_total_index - 1) {
                                layerIndex = i;
                                layerIndexNext = i + 1;
                                is_highest_layer = 0;
                            }
                            else
                            {
                                is_highest_layer = 1;
                                Toast.makeText(activity2, "该图层的层级已经是最低级别了！", Toast.LENGTH_LONG).show();
                            }
                        }
                    }
                    System.out.println(layerIndex);
                    System.out.println(layerIndexNext);

                    if (is_highest_layer == 0)
                    {
                        //这里对其进行修改
                        MapView mapView = activity2.findViewById(R.id.mapView);
                        GroupLayer groupLayer = (GroupLayer)mapView.getMap().getOperationalLayers().get(0);
                        //删除选中的图层
                        groupLayer.getLayers().remove(layerIndex);
                        //重新添加该图层，并使之上移
                        groupLayer.getLayers().add(layerIndexNext,layer);

                        upDown_layer_name.clear();
                        System.out.println("图层名称集合总数：" + MainActivity.LayerName.size());
                        System.out.println(upDown_layer_name.size());
                        System.out.println(layer_total_index);

                        //清空数组的同时，也将原始的数组的同时清除了
                        for (int i = 0;i <layer_total_index;i++)
                        {
                            String layer_name = groupLayer.getLayers().get(i).getName();
                            System.out.println(layer_name);
                            upDown_layer_name.add(layer_name);
                        }
                        System.out.println("图层名称集合总数：" + MainActivity.LayerName.size());
                    }
                }
            }
        });
        //图层下移
        ivMoveDown.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                //声明判断图层是否为最低层级的参数,0代表否，1代表是
                int is_lowest_layer = 0;
                //获取选中的图层的名称
                Log.d("点击的itemView的名字", layer.getName());
                if (layer.getName().equals("业务图层"))
                {
                    Toast.makeText(activity2, "该图层已是最顶层，无法移动！", Toast.LENGTH_LONG).show();
                }
                else{
                    //根据图层名称获取图层的层级（即加载顺序）
                    String MoveLayerName = layer.getName();
                    //获取该图层的下标
                    int layerIndex = 9999;
                    int layerIndexNext = 9999;
                    int LayerNameLength = layer_total_index;
                    System.out.println(LayerNameLength);
                    for (int i = 0; i < layer_total_index; i++) {
                        //判断图层名与其中是否存在相同，相同则记录数组下标
                        if (Objects.equals(upDown_layer_name.get(i), MoveLayerName)) {
                            if (i > 0) {
                                layerIndex = i;
                                layerIndexNext = i - 1;
                                is_lowest_layer = 0;
                            }
                            else
                            {
                                is_lowest_layer = 1;
                                Toast.makeText(activity2, "该图层的层级已经是最高级别了！", Toast.LENGTH_LONG).show();
                            }
                        }
                    }

                    System.out.println(layerIndex);
                    System.out.println(layerIndexNext);

                    if (is_lowest_layer == 0)
                    {
                        //这里对其进行修改
                        MapView mapView = activity2.findViewById(R.id.mapView);
                        GroupLayer groupLayer = (GroupLayer)mapView.getMap().getOperationalLayers().get(0);
                        //删除选中的图层
                        groupLayer.getLayers().remove(layerIndex);
                        //重新添加该图层，并使之下移
                        groupLayer.getLayers().add(layerIndexNext,layer);

                        upDown_layer_name.clear();
                        System.out.println("图层名称集合总数：" + MainActivity.LayerName.size());
                        System.out.println(upDown_layer_name.size());
                        System.out.println(layer_total_index);

                        //清空数组的同时，也将原始的数组的同时清除了
                        for (int i = 0;i <layer_total_index;i++)
                        {
                            String layer_name = groupLayer.getLayers().get(i).getName();
                            System.out.println(layer_name);
                            upDown_layer_name.add(layer_name);
                        }
                        System.out.println("图层名称集合总数：" + MainActivity.LayerName.size());
                    }
                }
            }
        });

        exitButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 不应该关闭整个Activity，而是隐藏BottomSheet
                if (activity2 != null) {
                    // 调用MainActivity中的方法来隐藏BottomSheet
                    activity2.closeLayerAdapter();
                }
            }
        });
    }




    abstract class ViewHolder extends RecyclerView.ViewHolder {
        OnLayerCheckedChangedListener mOnLayerCheckedChangedListener;
        ViewHolder(@NonNull View itemView, OnLayerCheckedChangedListener onLayerCheckedChangedListener) {
            super(itemView);
            mOnLayerCheckedChangedListener = onLayerCheckedChangedListener;
        }
        abstract void bind(Layer layer, boolean isVisible);
    }
}

interface OnLayerCheckedChangedListener {
    void layerCheckedChanged(Layer layer, boolean checked);
}
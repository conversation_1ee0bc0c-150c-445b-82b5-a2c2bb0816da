package io.dcloud.uniplugin;


import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.SavedStateViewModelFactory;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.savedstate.SavedStateRegistry;

import com.esri.arcgisruntime.mapping.view.MapView;

import java.io.File;
import java.io.IOException;


public class MyViewModel extends ViewModel  {
    private static final String KEY_MODEL_DATA = "key_model_data";
    private SavedStateHandle mSavedStateHandle;
    private MutableLiveData<Double> mData = new MutableLiveData<>();


    public MyViewModel(SavedStateHandle savedStateHandle) {
        this.mSavedStateHandle = savedStateHandle;
    }

    public MutableLiveData<Double> getModelData(){
        return mSavedStateHandle.getLiveData(KEY_MODEL_DATA);
    }
    public void setData(Double data) {
        mData.setValue(data);
    }
    public Double getData() {
        return  mData.getValue();
    }
}

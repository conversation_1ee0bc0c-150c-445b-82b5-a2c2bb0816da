<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res"/><source path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\build\generated\res\rs\release"/><source path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res"><file name="addbold" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\drawable\addbold.png" qualifiers="" type="drawable"/><file name="bg_go" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\drawable\bg_go.xml" qualifiers="" type="drawable"/><file name="button_bar_background" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\drawable\button_bar_background.xml" qualifiers="" type="drawable"/><file name="button_pressed" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\drawable\button_pressed.xml" qualifiers="" type="drawable"/><file name="clear" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\drawable\clear.png" qualifiers="" type="drawable"/><file name="current_lo" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\drawable\current_lo.png" qualifiers="" type="drawable"/><file name="fullpic" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\drawable\fullpic.png" qualifiers="" type="drawable"/><file name="green" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\drawable\green.png" qualifiers="" type="drawable"/><file name="ic_action_polygon" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\drawable\ic_action_polygon.png" qualifiers="" type="drawable"/><file name="ic_action_polyline" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\drawable\ic_action_polyline.png" qualifiers="" type="drawable"/><file name="ic_menu_redo" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\drawable\ic_menu_redo.png" qualifiers="" type="drawable"/><file name="ic_menu_undo" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\drawable\ic_menu_undo.png" qualifiers="" type="drawable"/><file name="layers" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\drawable\layers.png" qualifiers="" type="drawable"/><file name="minus" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\drawable\minus.png" qualifiers="" type="drawable"/><file name="north" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\drawable\north.png" qualifiers="" type="drawable"/><file name="red" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\drawable\red.png" qualifiers="" type="drawable"/><file name="ruler" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\drawable\ruler.png" qualifiers="" type="drawable"/><file name="yellow" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\drawable\yellow.png" qualifiers="" type="drawable"/><file name="activity_main_swdc" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\layout\activity_main_swdc.xml" qualifiers="" type="layout"/><file name="legend" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\layout\legend.xml" qualifiers="" type="layout"/><file name="measurement_toolbar" path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\layout\measurement_toolbar.xml" qualifiers="" type="layout"/><file path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\values\colors.xml" qualifiers=""><color name="background_gradient_start">#000000</color><color name="background_gradient_end">#DDDDDD</color><color name="fastlane_background">#0096a6</color><color name="search_opaque">#ffaa3f</color><color name="selected_background">#ffaa3f</color><color name="default_background">#3d3d3d</color><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="white">#FFFFFFFF</color><color name="black">#FF000000</color><color name="colorPrimary">#FF018786</color><color name="toolbar_background">#FF018786</color></file><file path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\values\strings.xml" qualifiers=""><string name="image_2023_service">http://***********:6080/geoscene/rest/services/Image2023/MapServer</string><string name="polylineButtonDescription">绘制线</string><string name="polygonButtonDescription">绘制面</string><string name="undoButtonDescription">撤回</string><string name="redoButtonDescription">恢复</string><string name="stopButtonDescription">完成</string></file><file path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\main\res\values\style.xml" qualifiers=""><style name="map_btn">
        <item name="layout_constraintLeft_toLeftOf">parent</item>
        <item name="layout_constraintTop_toTopOf">parent</item>
        <item name="android:layout_width">40dp</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:padding">5dp</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:radius">10dp</item>
        <item name="android:background">@color/white</item>
    </style><style name="measurement_btn">
        <item name="android:background">@drawable/button_bar_background</item>
        <item name="android:layout_alignParentTop">true</item>
        <item name="android:layout_height">30dp</item>
        <item name="android:layout_width">30dp</item>
        <item name="android:padding">5dp</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="layout_constraintBottom_toBottomOf">parent</item>
        <item name="layout_constraintHorizontal_bias">0.5</item>
    </style><style name="color_btn">
        <item name="layout_constraintLeft_toLeftOf">parent</item>
        <item name="layout_constraintTop_toTopOf">parent</item>
        <item name="android:layout_width">40dp</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:padding">5dp</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:radius">10dp</item>
        <item name="android:background">@color/white</item>
    </style></file></source><source path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\build\generated\res\rs\release"/><source path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\uniappandandroid\UniPlugin-Hello-AS\uniplugin_swdc\src\release\res"/></dataSet><mergedItems/></merger>
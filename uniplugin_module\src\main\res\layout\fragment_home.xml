<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 欢迎标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:text="广东省补充耕地质量验收APP"
            android:textColor="#1E88E5"
            android:textSize="24sp"
            android:textStyle="bold" />

        <!-- 功能按钮网格 -->
        <GridLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:columnCount="2"
            android:rowCount="2">

            <!-- 作业地图 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/btnWorkMap"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@android:drawable/ic_dialog_map"
                        android:tint="#1E88E5" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="作业地图"
                        android:textColor="#333333"
                        android:textSize="16sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 样点列表 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/btnSampleList"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@android:drawable/ic_menu_sort_by_size"
                        android:tint="#1E88E5" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="样点列表"
                        android:textColor="#333333"
                        android:textSize="16sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

        </GridLayout>
    </LinearLayout>
</ScrollView> 
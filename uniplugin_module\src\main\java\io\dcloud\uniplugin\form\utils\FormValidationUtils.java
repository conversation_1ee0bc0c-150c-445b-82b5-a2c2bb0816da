package io.dcloud.uniplugin.form.utils;

import android.text.TextUtils;
import android.util.Log;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Pattern;

import io.dcloud.uniplugin.form.field.FieldFile;
import io.dcloud.uniplugin.model.FormFieldConfig;

/**
 * 表单校验工具类，提供各种表单字段校验规则
 */
public class FormValidationUtils {
    private static final String TAG = "FormValidationUtils";

    /**
     * --- 新增：执行完整的表单提交校验 ---
     * 校验所有字段，包括必填项、文件数量和格式。
     *
     * @param fields      字段配置列表
     * @param fieldFiles  字段对应的文件列表 Map<FieldId, List<FieldFile>>
     * @param fieldValues 字段值 Map<FieldId, ValueString>
     * @return 如果校验通过返回null，否则返回包含所有错误信息的字符串
     */
    public static String validateFormForSubmission(List<FormFieldConfig> fields,
                                                    Map<String, List<FieldFile>> fieldFiles,
                                                    Map<String, String> fieldValues) {
        Log.d(TAG, "开始执行完整的表单提交校验...");
        StringBuilder errorMessages = new StringBuilder();

        try {
            if (fields == null || fields.isEmpty()) {
                Log.w(TAG, "没有字段配置，跳过校验");
                return null;
            }

            for (FormFieldConfig field : fields) {
                if (field == null || field.getFieldId() == null) continue;

                String fieldId = field.getFieldId();
                String label = field.getLabel();
                boolean isRequired = Boolean.TRUE.equals(field.isRequired());
                String fieldType = field.getFieldType();
                String fieldValue = fieldValues != null ? fieldValues.get(fieldId) : null;
                int fileCount = (fieldFiles != null && fieldFiles.containsKey(fieldId)) ? fieldFiles.get(fieldId).size() : 0;

                // 1. 检查必填项 (Required)
                boolean isEmpty = false;
                if (FormFieldConfig.TYPE_PHOTO.equals(fieldType) ||
                    FormFieldConfig.TYPE_VIDEO.equals(fieldType) ||
                    FormFieldConfig.TYPE_FILE.equals(fieldType) ||
                    FormFieldConfig.TYPE_AUDIO.equals(fieldType) ||
                    FormFieldConfig.TYPE_SIGNATURE.equals(fieldType)) {
                    isEmpty = (fileCount == 0);
                } else {
                    isEmpty = TextUtils.isEmpty(fieldValue) || "null".equals(fieldValue);
                }

                if (isRequired && isEmpty) {
                    String error = label + " 为必填项";
                    errorMessages.append("\n• ").append(error);
                    continue;
                }

                // 2. 检查文件类型字段的数量 (minFiles / maxFiles)
                if (FormFieldConfig.TYPE_PHOTO.equals(fieldType) ||
                    FormFieldConfig.TYPE_VIDEO.equals(fieldType) ||
                    FormFieldConfig.TYPE_FILE.equals(fieldType) ||
                    FormFieldConfig.TYPE_AUDIO.equals(fieldType)) {

                    Integer minFiles = field.getMinFiles();
                    Integer maxFiles = field.getMaxFiles();

                    if (minFiles != null && minFiles > 0 && fileCount < minFiles) {
                        String error = label + " 至少需要上传 " + minFiles + " 个文件 (当前: " + fileCount + ")";
                        errorMessages.append("\n• ").append(error);
                    }

                    if (maxFiles != null && maxFiles > 0 && fileCount > maxFiles) {
                        String error = label + " 最多只能上传 " + maxFiles + " 个文件 (当前: " + fileCount + ")";
                        errorMessages.append("\n• ").append(error);
                    }
                }

                // 3. 检查已填写字段的格式 (使用本类中的具体校验方法)
                if (!isEmpty && !(FormFieldConfig.TYPE_PHOTO.equals(fieldType) ||
                                FormFieldConfig.TYPE_VIDEO.equals(fieldType) ||
                                FormFieldConfig.TYPE_FILE.equals(fieldType) ||
                                FormFieldConfig.TYPE_AUDIO.equals(fieldType) ||
                                FormFieldConfig.TYPE_SIGNATURE.equals(fieldType))) {

                    String formatError = null;
                    switch (fieldType) {
                        case FormFieldConfig.TYPE_TEXT:
                        case FormFieldConfig.TYPE_TEXTAREA:
                            formatError = validateText(field, fieldValue);
                            break;
                        case FormFieldConfig.TYPE_NUMBER:
                            formatError = validateNumber(field, fieldValue);
                            break;
                        case FormFieldConfig.TYPE_DATE:
                            formatError = validateDate(field, fieldValue);
                            break;
                        case FormFieldConfig.TYPE_DROPDOWN:
                             formatError = validateDropdown(field, fieldValue);
                             break;
                        case FormFieldConfig.TYPE_LOCATION:
                             formatError = validateLocation(field, fieldValue);
                             break;
                    }

                    if (formatError != null) {
                        errorMessages.append("\n• ").append(formatError);
                    }
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "执行表单校验时出错: " + e.getMessage(), e);
            errorMessages.append("\n• 校验过程中发生内部错误: ").append(e.getMessage());
        }

        if (errorMessages.length() > 0) {
            Log.e(TAG, "表单提交校验失败: " + errorMessages.toString().trim());
            return errorMessages.toString().trim();
        } else {
            Log.d(TAG, "表单提交校验通过");
            return null;
        }
    }

    /**
     * 验证字段值是否符合配置的校验规则
     *
     * @param field 字段配置
     * @param value 字段值
     * @return 校验结果，如果通过返回null，否则返回错误消息
     */
    public static String validateField(FormFieldConfig field, String value) {
        if (field == null) {
            return "字段配置为空";
        }

        // 如果值为空，则跳过后续格式校验
        if (value == null || value.trim().isEmpty()) {
            return null;
        }

        // 根据字段类型进行不同的校验
        String fieldType = field.getFieldType();
        if (fieldType == null) {
            return null; // 如果字段类型为空，跳过校验
        }

        switch (fieldType) {
            case FormFieldConfig.TYPE_TEXT:
                return validateText(field, value);

            case FormFieldConfig.TYPE_NUMBER:
                return validateNumber(field, value);

            case FormFieldConfig.TYPE_DATE:
                return validateDate(field, value);

            case FormFieldConfig.TYPE_TEXTAREA:
                return validateTextArea(field, value);

            case FormFieldConfig.TYPE_DROPDOWN:
                return validateDropdown(field, value);

            case FormFieldConfig.TYPE_LOCATION:
                return validateLocation(field, value);

            default:
                return null;
        }
    }

    /**
     * 验证文本字段
     */
    public static String validateText(FormFieldConfig field, String value) {
        // 检查最小长度
        Integer minLength = field.getMinLength();
        if (minLength != null && minLength > 0 && value.length() < minLength) {
            return field.getLabel() + "长度不能小于" + minLength + "个字符";
        }

        // 检查最大长度 - 只使用maxLength属性
        Integer maxLength = field.getMaxLength();
        if (maxLength != null && maxLength > 0 && value.length() > maxLength) {
            return field.getLabel() + "长度不能超过" + maxLength + "个字符";
        }

        // 检查正则表达式模式
        if (!TextUtils.isEmpty(field.getPattern())) {
            try {
                Pattern pattern = Pattern.compile(field.getPattern());
                if (!pattern.matcher(value).matches()) {
                    return field.getLabel() + "格式不正确" +
                            (TextUtils.isEmpty(field.getPatternError()) ? "" : "：" + field.getPatternError());
                }
            } catch (Exception e) {
                Log.e(TAG, "正则表达式校验失败: " + e.getMessage());
            }
        }

        // 检查特定字段类型的格式
        String fieldId = field.getFieldId().toLowerCase();

        // 手机号码校验
        if (fieldId.contains("phone") || fieldId.contains("mobile")) {
            if (!isValidPhoneNumber(value)) {
                return "请输入有效的手机号码";
            }
        }

        // 邮箱校验
        if (fieldId.contains("email")) {
            if (!isValidEmail(value)) {
                return "请输入有效的电子邮箱";
            }
        }

        // 身份证号校验
        if (fieldId.contains("idcard") || fieldId.contains("id_card")) {
            if (!isValidIdCard(value)) {
                return "请输入有效的身份证号码";
            }
        }

        return null;
    }

    /**
     * 验证数字字段
     */
    public static String validateNumber(FormFieldConfig field, String value) {
        try {
            double numValue = Double.parseDouble(value);
            String fieldId = field.getFieldId();
            
            // 检查最小值
            Double min = field.getMin();
            if (min != null && numValue < min) {
                return field.getLabel() + "不能小于" + min;
            }
            
            // 检查最大值
            Double max = field.getMax();
            if (max != null && numValue > max) {
                return field.getLabel() + "不能大于" + max;
            }
            
            // 从minValue和maxValue属性也获取检查范围
            Double minValue = field.getMinValue();
            if (minValue != null && numValue < minValue) {
                return field.getLabel() + "不能小于" + minValue;
            }
            
            Double maxValue = field.getMaxValue();
            if (maxValue != null && numValue > maxValue) {
                return field.getLabel() + "不能大于" + maxValue;
            }
            
            // 检查是否为整数
            Boolean isInteger = field.isInteger();
            if (Boolean.TRUE.equals(isInteger) && numValue != Math.floor(numValue)) {
                return field.getLabel() + "必须是整数";
            }
            
        } catch (NumberFormatException e) {
            return field.getLabel() + "必须是有效的数字";
        }
        
        return null; // 验证通过
    }

    /**
     * 验证日期字段
     */
    public static String validateDate(FormFieldConfig field, String value) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        format.setLenient(false);
        
        try {
            Date date = format.parse(value);
            
            // 检查最小日期
            if (!TextUtils.isEmpty(field.getMinDate())) {
                try {
                    Date minDate = format.parse(field.getMinDate());
                    if (date != null && date.before(minDate)) {
                        return field.getLabel() + "不能早于" + field.getMinDate();
                    }
                } catch (ParseException e) {
                    Log.e(TAG, "解析最小日期失败: " + e.getMessage());
                }
            }
            
            // 检查最大日期
            if (!TextUtils.isEmpty(field.getMaxDate())) {
                try {
                    Date maxDate = format.parse(field.getMaxDate());
                    if (date != null && date.after(maxDate)) {
                        return field.getLabel() + "不能晚于" + field.getMaxDate();
                    }
                } catch (ParseException e) {
                    Log.e(TAG, "解析最大日期失败: " + e.getMessage());
                }
            }
        } catch (ParseException e) {
            return field.getLabel() + "必须是有效的日期格式 (YYYY-MM-DD)";
        }
        
        return null; // 验证通过
    }

    /**
     * 验证文本区域字段
     */
    private static String validateTextArea(FormFieldConfig field, String value) {
        // 检查最小长度
        Integer minLength = field.getMinLength();
        if (minLength != null && minLength > 0 && value.length() < minLength) {
            return field.getLabel() + "长度不能小于" + minLength + "个字符";
        }

        // 检查最大长度 - 只使用maxLength属性
        Integer maxLength = field.getMaxLength();
        if (maxLength != null && maxLength > 0 && value.length() > maxLength) {
            return field.getLabel() + "长度不能超过" + maxLength + "个字符";
        }

        return null;
    }

    /**
     * 验证下拉框字段
     */
    private static String validateDropdown(FormFieldConfig field, String value) {
        // 如果值为空，则直接返回null（在前面的必填检查中会处理）
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        
        // 检查选项是否在预定义列表中
        if (field.getOptions() != null && !field.getOptions().isEmpty()) {
            boolean found = false;
            boolean hasOtherOption = false;
            
            // 首先检查是否有"其他"选项
            for (FormFieldConfig.OptionItem option : field.getOptions()) {
                String label = option.getLabel();
                if (label != null && (label.equals("其他") || label.equals("其它") || label.equalsIgnoreCase("other"))) {
                    hasOtherOption = true;
                    break;
                }
            }
            
            // 检查值是否在选项中
            for (FormFieldConfig.OptionItem option : field.getOptions()) {
                if (option.getValue().equals(value) || option.getLabel().equals(value)) {
                    found = true;
                    break;
                }
            }

            // 如果值不在选项列表中，但有"其他"选项，则认为是用户输入的自定义值，验证通过
            if (!found && hasOtherOption) {
                Log.d(TAG, "下拉框选择了'其他'选项，用户输入: " + value);
                found = true;
            }

            if (!found) {
                return field.getLabel() + "选项无效";
            }
        }

        return null;
    }

    /**
     * 验证位置字段
     */
    private static String validateLocation(FormFieldConfig field, String value) {
        // 检查位置字符串是否包含经纬度信息
        if (!value.contains("经度:") || !value.contains("纬度:")) {
            return "位置信息不完整";
        }

        // 可以添加更复杂的位置校验逻辑，如检查经纬度是否在合理范围内

        return null;
    }

    /**
     * 检查是否是有效的照片文件
     */
    public static boolean isValidPhotoFile(String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return false;
        }
        
        String lowerPath = filePath.toLowerCase();
        return lowerPath.endsWith(".jpg") || 
               lowerPath.endsWith(".jpeg") || 
               lowerPath.endsWith(".png") || 
               lowerPath.endsWith(".gif") || 
               lowerPath.endsWith(".webp");
    }

    /**
     * 检查是否是有效的视频文件
     */
    public static boolean isValidVideoFile(String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return false;
        }
        
        String lowerPath = filePath.toLowerCase();
        return lowerPath.endsWith(".mp4") || 
               lowerPath.endsWith(".3gp") || 
               lowerPath.endsWith(".mov") || 
               lowerPath.endsWith(".avi") || 
               lowerPath.endsWith(".wmv") ||
               lowerPath.endsWith(".mkv");
    }

    /**
     * 验证手机号码格式
     */
    private static boolean isValidPhoneNumber(String phone) {
        if (TextUtils.isEmpty(phone)) {
            return false;
        }

        // 中国大陆手机号码格式校验
        return phone.matches("^1[3-9]\\d{9}$");
    }

    /**
     * 验证电子邮箱格式
     */
    private static boolean isValidEmail(String email) {
        if (TextUtils.isEmpty(email)) {
            return false;
        }

        // 简单的邮箱格式校验
        return email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    }

    /**
     * 验证身份证号格式
     */
    private static boolean isValidIdCard(String idCard) {
        if (TextUtils.isEmpty(idCard)) {
            return false;
        }

        // 简单的身份证号码格式校验（18位）
        return idCard.matches("^[1-9]\\d{5}(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]$");
    }
} 
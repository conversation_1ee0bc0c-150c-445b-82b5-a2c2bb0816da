package com.deltaphone.cameramodule;
import android.Manifest;
import android.app.ProgressDialog;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.deltaphone.cameramodule.FileChange.GlideEngine;
import com.deltaphone.cameramodule.camera.FileUtils;
import com.hw.videoprocessor.VideoProcessor;
import com.luck.picture.lib.app.PictureAppMaster;
import com.luck.picture.lib.basic.IBridgePictureBehavior;
import com.luck.picture.lib.basic.PictureCommonFragment;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureMimeType;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.config.SelectModeConfig;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.entity.MediaExtraInfo;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.luck.picture.lib.utils.MediaUtils;

import java.util.ArrayList;



public class fileChangeActivity extends AppCompatActivity implements IBridgePictureBehavior{
    private static final int REQUEST_PERMISSION = 1;
    private ProgressDialog progressDialog;
    boolean success;//用来判断是否压缩完成

    private final static String TAG = "PictureSelectorTag";



    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Intent mIntent = getIntent();
        String model = mIntent.getStringExtra("model");
        setContentView(R.layout.activity_inject_fragment);
        // 获取顶部导航栏的高度
        int statusBarHeight = getStatusBarHeight();

        // 设置占位框的高度
        View placeholderView = findViewById(R.id.placeholderView);
        ViewGroup.LayoutParams params = placeholderView.getLayoutParams();
        params.height = statusBarHeight;
        placeholderView.setLayoutParams(params);

        // 检查权限并请求
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.READ_EXTERNAL_STORAGE}, REQUEST_PERMISSION);
        } else {
            loadFiles(model);
        }

        //去除标题栏
        if (getSupportActionBar() != null){
            getSupportActionBar().hide();
        }

        progressDialog = new ProgressDialog(this);
        progressDialog.setTitle(null);
        progressDialog.setCancelable(false);
        progressDialog.setMessage("视频压缩中，请稍等......");

        success = false;

    }

    // 获取顶部导航栏的高度
    private int getStatusBarHeight() {
        int height = 0;
        int resourceId = getResources().getIdentifier("status_bar_height", "dimen", "android");
        if (resourceId > 0) {
            height = getResources().getDimensionPixelSize(resourceId);
        }
        return height;
    }


    private void loadFiles(String model) {
        int chooseMode = SelectMimeType.ofImage();
        if (model.equals("0")) {
            chooseMode = SelectMimeType.ofImage();
        }else if(model.equals("1")){
            chooseMode = SelectMimeType.ofVideo();
        }
        PictureSelector.create(this)
                .openGallery(chooseMode)
                .setImageEngine(GlideEngine.createGlideEngine())
                .isDisplayCamera(false)
                .setSelectionMode(SelectModeConfig.SINGLE)
                .setFilterVideoMaxSecond(120)//过滤最大时长
                .setSelectMaxDurationSecond(120)//选择最大时长
                .buildLaunch(R.id.fragment_container, new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        analyticalSelectResults(result);
                    }

                    @Override
                    public void onCancel() {
                        Log.i(TAG, "PictureSelector Cancel");
                        finish();
                    }
                });
    }

    @Override
    public void onSelectFinish(PictureCommonFragment.SelectorResult result) {
        if (result == null) {
            return;
        }
        if (result.mResultCode == RESULT_OK) {
            ArrayList<LocalMedia> selectorResult = PictureSelector.obtainSelectorList(result.mResultData);
            analyticalSelectResults(selectorResult);
        } else if (result.mResultCode == RESULT_CANCELED) {
            Log.i(TAG, "onSelectFinish PictureSelector Cancel");
            finish();
        }
    }

    /**
     * 处理选择结果
     *
     * @param result
     */
    private void analyticalSelectResults(ArrayList<LocalMedia> result) {
        StringBuilder builder = new StringBuilder();
        builder.append("Result").append("\n");
        String imagePath = "";
        int moduel = 0;
        for (LocalMedia media : result) {
            if (media.getWidth() == 0 || media.getHeight() == 0) {
                if (PictureMimeType.isHasImage(media.getMimeType())) {
                    MediaExtraInfo imageExtraInfo = MediaUtils.getImageSize(this,media.getPath());
                    media.setWidth(imageExtraInfo.getWidth());
                    media.setHeight(imageExtraInfo.getHeight());
                } else if (PictureMimeType.isHasVideo(media.getMimeType())) {
                    MediaExtraInfo videoExtraInfo = MediaUtils.getVideoSize(PictureAppMaster.getInstance().getAppContext(), media.getPath());
                    media.setWidth(videoExtraInfo.getWidth());
                    media.setHeight(videoExtraInfo.getHeight());
                }
            }
            builder.append(media.getAvailablePath()).append("\n");
            Log.i(TAG, "文件名: " + media.getFileName());
            Log.i(TAG, "是否压缩:" + media.isCompressed());
            Log.i(TAG, "压缩:" + media.getCompressPath());
            Log.i(TAG, "原图:" + media.getPath());
            Log.i(TAG, "绝对路径:" + media.getRealPath());
            Log.i(TAG, "是否裁剪:" + media.isCut());
            Log.i(TAG, "裁剪:" + media.getCutPath());
            Log.i(TAG, "是否开启原图:" + media.isOriginal());
            Log.i(TAG, "原图路径:" + media.getOriginalPath());
            Log.i(TAG, "沙盒路径:" + media.getSandboxPath());
            Log.i(TAG, "原始宽高: " + media.getWidth() + "x" + media.getHeight());
            Log.i(TAG, "裁剪宽高: " + media.getCropImageWidth() + "x" + media.getCropImageHeight());
            Log.i(TAG, "文件大小: " + media.getSize());
            imagePath = media.getRealPath();
            if (PictureMimeType.isHasVideo(media.getMimeType())){
                String outputPath = FileUtils.getSDPath(fileChangeActivity.this)+"/TRSPGISData/TRSPCameraData/"+FileUtils.getTimeStampFileName(1);
                //如果是视频的话就还需要进行压缩
                executeCompressVideo(media.getRealPath(),outputPath);
                moduel = 1;
            }else if (PictureMimeType.isHasImage(media.getMimeType())){
                success = true;
                moduel = 0;
            }
        }
        if(success && moduel==0){
            Intent intent = new Intent();
            intent.putExtra("imagePath",imagePath);
            setResult(1086, intent);
            finish();
        }

    }


    private void executeCompressVideo(String videoPath, String outputPath) {
        progressDialog.show();

        new Thread(new Runnable() {
            @Override
            public void run() {
                success = true;
                try {
                    MediaMetadataRetriever retriever = new MediaMetadataRetriever();
                    retriever.setDataSource(fileChangeActivity.this, Uri.parse(videoPath));

                    int bitrate = Integer.parseInt(retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_BITRATE));

                    VideoProcessor.processor(getApplicationContext())
                            .input(videoPath)
                            .output(outputPath)
                            .bitrate(bitrate / 2)
                            .frameRate(30)
                            .process();
                } catch (Exception e) {
                    success = false;
                    e.printStackTrace();
                    postError();
                }
                if(success){
                    Log.d(TAG, "run: 压缩成功");
//                    startPreviewActivity(filePath);
                    Intent intent = new Intent();
                    intent.putExtra("imagePath",outputPath);
                    setResult(1086, intent);
                    finish();
                }
                progressDialog.dismiss();
            }
        }).start();
    }
    
    private void postError() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                Toast.makeText(getApplicationContext(), "压缩失败!", Toast.LENGTH_SHORT).show();
                finish();
            }
        });
    }


    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (requestCode == REQUEST_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                loadFiles("0");
            } else {
                // 处理权限被拒绝的情况
            }
        }
    }
}

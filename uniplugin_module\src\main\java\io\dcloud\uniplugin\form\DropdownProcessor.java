package io.dcloud.uniplugin.form;

import android.util.Log;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

import io.dcloud.uniplugin.model.FormFieldConfig;

/**
 * 下拉选择框处理器
 * 用于处理动态表单中的下拉框选项
 */
public class DropdownProcessor {
    private static final String TAG = "DropdownProcessor";

    /**
     * 将字符串形式的选项解析成OptionItem列表
     * 支持三种格式:
     * 1. JSON数组格式：[{"label":"选项1","value":"value1"}, {"label":"选项2","value":"value2"}]
     * 2. 逗号分隔格式：选项1,选项2,选项3
     * 3. 标签值对格式：标签1:值1,标签2:值2
     *
     * @param optionsStr 选项字符串
     * @return 解析后的选项列表
     */
    public static List<FormFieldConfig.OptionItem> parseOptions(String optionsStr) {
        List<FormFieldConfig.OptionItem> options = new ArrayList<>();
        
        if (optionsStr == null || optionsStr.trim().isEmpty()) {
            // 如果选项字符串为空，返回空列表
            Log.w(TAG, "选项字符串为空");
            return options;
        }
        
        // 尝试解析为JSON格式
        if (optionsStr.trim().startsWith("[") && optionsStr.trim().endsWith("]")) {
            return parseJsonOptions(optionsStr);
        } else {
            // 尝试解析为逗号分隔格式
            return parseCommaOptions(optionsStr);
        }
    }
    
    /**
     * 解析JSON格式的选项字符串
     * 
     * @param jsonOptionsStr JSON格式的选项字符串
     * @return 解析后的选项列表
     */
    private static List<FormFieldConfig.OptionItem> parseJsonOptions(String jsonOptionsStr) {
        List<FormFieldConfig.OptionItem> options = new ArrayList<>();
        
        try {
            JSONArray jsonArray = new JSONArray(jsonOptionsStr);
            
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject optionJson = jsonArray.getJSONObject(i);
                FormFieldConfig.OptionItem option = new FormFieldConfig.OptionItem();
                
                // 获取标签和值
                String label = optionJson.optString("label", "");
                String value = optionJson.optString("value", "");
                
                if (label.isEmpty() && value.isEmpty()) {
                    // 跳过空选项
                    continue;
                }
                
                // 如果标签为空但值不为空，使用值作为标签
                if (label.isEmpty()) {
                    label = value;
                }
                
                // 如果值为空但标签不为空，使用标签作为值
                if (value.isEmpty()) {
                    value = label;
                }
                
                option.setLabel(label);
                option.setValue(value);
                options.add(option);
            }
            
            Log.d(TAG, "成功解析JSON格式的选项，共" + options.size() + "个选项");
        } catch (JSONException e) {
            Log.e(TAG, "解析JSON格式选项失败: " + e.getMessage(), e);
        }
        
        return options;
    }
    
    /**
     * 解析逗号分隔格式的选项字符串
     * 
     * @param commaOptionsStr 逗号分隔的选项字符串
     * @return 解析后的选项列表
     */
    private static List<FormFieldConfig.OptionItem> parseCommaOptions(String commaOptionsStr) {
        List<FormFieldConfig.OptionItem> options = new ArrayList<>();
        
        String[] optionParts = commaOptionsStr.split(",");
        for (String part : optionParts) {
            String trimmed = part.trim();
            if (!trimmed.isEmpty()) {
                FormFieldConfig.OptionItem option = new FormFieldConfig.OptionItem();
                
                // 处理可能存在的label:value格式
                if (trimmed.contains(":")) {
                    String[] labelValue = trimmed.split(":", 2);
                    option.setLabel(labelValue[0].trim());
                    option.setValue(labelValue[1].trim());
                } else {
                    // 如果没有冒号，则标签和值相同
                    option.setLabel(trimmed);
                    option.setValue(trimmed);
                }
                
                options.add(option);
            }
        }
        
        Log.d(TAG, "成功解析逗号分隔格式的选项，共" + options.size() + "个选项");
        return options;
    }
} 
package io.dcloud.uniplugin.form.utils;

import android.app.TimePickerDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.util.Log;
import android.view.View;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.widget.EditText;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;

/**
 * UI工具类，提供常用的UI操作方法
 */
public class UIUtils {
    private static final String TAG = "UIUtils";

    // 用于跟踪所有活动的对话框
    private static final List<AlertDialog> activeDialogs = new ArrayList<>();
    private static AlertDialog loadingDialog;

    /**
     * 显示Toast消息
     */
    public static void showToast(Context context, String message) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
    }

    /**
     * 显示确认对话框
     * @return 创建的AlertDialog对象
     */
    public static AlertDialog showConfirmDialog(Context context, String title, String message, 
                                        String positiveText, String negativeText,
                                        DialogInterface.OnClickListener listener) {
        AlertDialog dialog = new AlertDialog.Builder(context)
                .setTitle(title)
                .setMessage(message)
                .setPositiveButton(positiveText, listener)
                .setNegativeButton(negativeText, null)
                .create();
        dialog.show();
        activeDialogs.add(dialog);
        return dialog;
    }

    /**
     * 显示确认对话框（带有两个按钮监听器）
     * @return 创建的AlertDialog对象
     */
    public static AlertDialog showConfirmDialog(Context context, String title, String message, 
                                        String positiveText, String negativeText,
                                        DialogInterface.OnClickListener positiveListener,
                                        DialogInterface.OnClickListener negativeListener) {
        AlertDialog dialog = new AlertDialog.Builder(context)
                .setTitle(title)
                .setMessage(message)
                .setPositiveButton(positiveText, positiveListener)
                .setNegativeButton(negativeText, negativeListener)
                .create();
        dialog.show();
        activeDialogs.add(dialog);
        return dialog;
    }

    /**
     * 显示时间选择器
     * @param context 上下文
     * @param editText 文本框
     * @param calendar 日历对象
     * @param dateFormat 日期格式
     */
    private static void showTimePicker(Context context, final EditText editText,
                                      final Calendar calendar, final String dateFormat) {
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        
        // 创建时间选择器
        TimePickerDialog timePickerDialog = new TimePickerDialog(context,
                (view, selectedHour, selectedMinute) -> {
                    // 设置选择的时间
                    calendar.set(Calendar.HOUR_OF_DAY, selectedHour);
                    calendar.set(Calendar.MINUTE, selectedMinute);
                    calendar.set(Calendar.SECOND, 0);
                    
                    // 格式化日期时间并设置
                    SimpleDateFormat sdf = new SimpleDateFormat(dateFormat, Locale.getDefault());
                    String formattedDateTime = sdf.format(calendar.getTime());
                    editText.setText(formattedDateTime);
                }, hour, minute, true);
        
        timePickerDialog.show();
    }


    /**
     * 设置EditText错误提示
     */
    public static void setEditTextError(EditText editText, String error) {
        editText.setError(error);
        editText.requestFocus();
    }

    /**
     * 应用高亮动画
     */
    public static void applyHighlightAnimation(Context context, View view) {
        AlphaAnimation animation = new AlphaAnimation(0.3f, 1.0f);
        animation.setDuration(500);
        animation.setRepeatMode(Animation.REVERSE);
        animation.setRepeatCount(3);
        view.startAnimation(animation);
    }

    /**
     * 显示加载对话框
     */
    public static void showLoadingDialog(Context context, String message) {
        if (context == null) {
            return;
        }
        
        // 如果已有加载对话框，先关闭它
        dismissLoadingDialog();
        
        try {
            AlertDialog.Builder builder = new AlertDialog.Builder(context);
            builder.setMessage(message)
                    .setCancelable(false);
            
            loadingDialog = builder.create();
            activeDialogs.add(loadingDialog);
            loadingDialog.show();
        } catch (Exception e) {
            Log.e(TAG, "显示加载对话框时出错: " + e.getMessage());
        }
    }

    /**
     * 关闭加载对话框
     */
    public static void dismissLoadingDialog() {
        if (loadingDialog != null && loadingDialog.isShowing()) {
            try {
                loadingDialog.dismiss();
            } catch (Exception e) {
                Log.e(TAG, "关闭加载对话框时出错: " + e.getMessage());
            }
            activeDialogs.remove(loadingDialog);
            loadingDialog = null;
        }
    }

    /**
     * 关闭所有活动的对话框
     */
    public static void dismissAllDialogs() {
        // 关闭加载对话框
        dismissLoadingDialog();
        
        // 关闭其他所有对话框
        for (AlertDialog dialog : activeDialogs) {
            if (dialog != null && dialog.isShowing()) {
                try {
                    dialog.dismiss();
                } catch (Exception e) {
                    Log.e(TAG, "关闭对话框时出错: " + e.getMessage());
                }
            }
        }
        activeDialogs.clear();
    }

} 
//plugins {
//    id 'com.android.library'
//}

apply plugin:'com.android.library'

android {
//    namespace 'com.deltaphone.cameramodule'
    compileSdkVersion 29
    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }

    defaultConfig {
        minSdkVersion 23
        targetSdkVersion 28
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
//    buildFeatures {
//        viewBinding true
//    }
}

dependencies {
    //implementation 'androidx.appcompat:appcompat:1.4.1‘
    implementation 'androidx.appcompat:appcompat:1.2.0'
    //implementation 'com.google.android.material:material:1.5.0'
    implementation'com.google.android.material:material:1.1.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.3'
    //implementation 'androidx.navigation:navigation-fragment:2.4.1'
    //implementation 'androidx.navigation:navigation-ui:2.4.1'
    implementation'androidx.navigation:navigation-fragment:2.2.2'
    implementation'androidx.navigation:navigation-ui:2.2.2'

    implementation 'com.getbase:floatingactionbutton:1.10.1'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.2.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.2.0'
    implementation 'androidx.gridlayout:gridlayout:1.0.0'
    implementation 'com.google.android.gms:play-services-maps:18.0.2'
    testImplementation 'junit:junit:4.13.2'
    //androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    //androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'

    implementation 'androidx.leanback:leanback:1.0.0'
    implementation 'com.github.bumptech.glide:glide:4.11.0'
    //implementation 'com.google.android.material:material:1.4.+'
    //implementation 'com.google.android.material:material:1.5.0'

    //implementation 'androidx.constraintlayout:constraintlayout:2.1.3'

    //从这里开始是uniappmodule必须要的
    compileOnly fileTree(dir: 'libs', include: ['*.jar','*.aar'])

    compileOnly fileTree(dir: '../app/libs', include: ['uniapp-v8-release.aar'])

    compileOnly 'androidx.recyclerview:recyclerview:1.0.0'
    compileOnly 'androidx.legacy:legacy-support-v4:1.0.0'
    compileOnly 'androidx.appcompat:appcompat:1.0.0'
    implementation 'com.alibaba:fastjson:1.1.46.android'
    implementation 'com.facebook.fresco:fresco:1.13.0'

    implementation 'com.vividsolutions:jts-core:1.14.0'

    // PictureSelector 基础 (必须)
    implementation 'io.github.lucksiege:pictureselector:v3.11.1'

    //视频压缩
    implementation 'com.github.yellowcath:VideoProcessor:2.4.2'
}
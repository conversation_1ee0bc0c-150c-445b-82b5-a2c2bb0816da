<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="4dp"
    app:cardElevation="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textViewSampleInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:textColor="#212121"
                android:text="样品名称 - 样品类型 (×1)" />

            <TextView
                android:id="@+id/textViewRemark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textSize="14sp"
                android:textColor="#757575"
                android:text="备注: 样品备注信息" />

        </LinearLayout>

        <Button
            android:id="@+id/buttonDelete"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:background="@android:drawable/ic_menu_delete"
            android:backgroundTint="#F44336" />

    </LinearLayout>

</androidx.cardview.widget.CardView> 
package io.dcloud.uniplugin.service;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.os.Environment;
import android.util.Log;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import io.dcloud.uniplugin.db.DatabaseConstants;
import io.dcloud.uniplugin.form.utils.FormJsonUtils;
import io.dcloud.uniplugin.model.FormConfig;
import io.dcloud.uniplugin.model.FormConfigResponse;

/**
 * 表单配置数据库帮助类
 * 管理表单配置的存储、检索和更新
 */
public class FormConfigDatabaseHelper extends SQLiteOpenHelper {
    private static final String TAG = "FormConfigDBHelper";
    
    // 数据库名和版本 - 与主数据库保持一致
    private static final String DATABASE_NAME = DatabaseConstants.DATABASE_NAME;
    private static final int DATABASE_VERSION = DatabaseConstants.DATABASE_VERSION;
    
    // 表名
    private static final String TABLE_FORM_CONFIG = "form_config";
    
    // 字段名
    private static final String COLUMN_ID = "id";
    private static final String COLUMN_FORM_ID = "form_id";
    private static final String COLUMN_FORM_NAME = "form_name";
    private static final String COLUMN_DESCRIPTION = "description";
    private static final String COLUMN_VERSION = "version";
    private static final String COLUMN_SUBMIT_URL = "submit_url";
    private static final String COLUMN_IS_OFFLINE_SUPPORTED = "is_offline_supported";
    private static final String COLUMN_CONFIG_JSON = "config_json";
    private static final String COLUMN_CREATE_TIME = "create_time";
    private static final String COLUMN_UPDATE_TIME = "update_time";
    
    // 创建表SQL
    private static final String SQL_CREATE_TABLE = 
            "CREATE TABLE IF NOT EXISTS " + TABLE_FORM_CONFIG + " (" +
            COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
            COLUMN_FORM_ID + " TEXT NOT NULL, " +
            COLUMN_FORM_NAME + " TEXT NOT NULL, " +
            COLUMN_DESCRIPTION + " TEXT, " +
            COLUMN_VERSION + " TEXT, " +
            COLUMN_SUBMIT_URL + " TEXT, " +
            COLUMN_IS_OFFLINE_SUPPORTED + " INTEGER DEFAULT 0, " +
            COLUMN_CONFIG_JSON + " TEXT NOT NULL, " +
            COLUMN_CREATE_TIME + " LONG DEFAULT (strftime('%s','now') * 1000), " +
            COLUMN_UPDATE_TIME + " LONG DEFAULT (strftime('%s','now') * 1000)" +
            ")";
    
    private static FormConfigDatabaseHelper instance;
    private final String mDatabasePath;
    
    /**
     * 获取单例实例
     */
    public static synchronized FormConfigDatabaseHelper getInstance(Context context) {
        if (instance == null) {
            instance = new FormConfigDatabaseHelper(context.getApplicationContext());
        }
        return instance;
    }
    
    /**
     * 构造方法
     */
    private FormConfigDatabaseHelper(Context context) {
        super(context, getExternalDatabasePath(context), null, DATABASE_VERSION);
        this.mDatabasePath = getExternalDatabasePath(context);
        
        // 确保数据库目录存在
        File dbDir = new File(mDatabasePath).getParentFile();
        if (dbDir != null && !dbDir.exists()) {
            dbDir.mkdirs();
        }
    }
    
    /**
     * 获取外部存储中数据库的完整路径
     */
    private static String getExternalDatabasePath(Context context) {
        // 获取外部存储中的BCGDGISData/BCGDSqliteData目录
        File externalDir = new File(Environment.getExternalStorageDirectory(), "BCGDGISData/BCGDSqliteData");
        if (!externalDir.exists()) {
            boolean success = externalDir.mkdirs();
            if (!success) {
                Log.e(TAG, "创建外部存储数据库目录失败，将使用内部存储");
                return DatabaseConstants.DATABASE_NAME;
            }
        }
        
        return new File(externalDir, DATABASE_NAME).getAbsolutePath();
    }
    
    @Override
    public void onCreate(SQLiteDatabase db) {
        createFormConfigTable(db);
    }
    
    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.d(TAG, "数据库升级: " + oldVersion + " -> " + newVersion);
        
        // 检查表是否存在，不存在则创建
        if (!isTableExists(db, TABLE_FORM_CONFIG)) {
            createFormConfigTable(db);
        }
    }
    
    /**
     * 检查表是否存在
     */
    private boolean isTableExists(SQLiteDatabase db, String tableName) {
        Cursor cursor = db.rawQuery(
                "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                new String[]{tableName}
        );
        
        boolean tableExists = false;
        if (cursor != null) {
            tableExists = cursor.getCount() > 0;
            cursor.close();
        }
        
        return tableExists;
    }
    
    /**
     * 创建表单配置表
     */
    private void createFormConfigTable(SQLiteDatabase db) {
        db.execSQL(SQL_CREATE_TABLE);
        Log.d(TAG, "form_config表创建成功");
    }
    
    /**
     * 确保表存在
     */
    private void ensureTableExists(SQLiteDatabase db) {
        if (!isTableExists(db, TABLE_FORM_CONFIG)) {
            createFormConfigTable(db);
        }
    }
    
    /**
     * 保存表单配置
     */
    public long saveFormConfig(FormConfig formConfig, FormConfigResponse configJson) {
        // 验证参数
        if (formConfig == null) {
            Log.e(TAG, "保存表单配置失败：formConfig 为 null");
            return -1;
        }
        
        SQLiteDatabase db = this.getWritableDatabase();
        ensureTableExists(db);
        
        // 处理JSON
        String configJsonStr = FormJsonUtils.normalizeFormConfigJson(configJson, formConfig);
        
        // 准备数据
        ContentValues values = prepareContentValues(formConfig, configJsonStr);
        
        // 检查是否已存在相同 form_id 的记录
        long id = updateOrInsertFormConfig(db, values, formConfig.getFormId());
        
        db.close();
        return id;
    }
    /**
     * 准备要存储的ContentValues
     */
    private ContentValues prepareContentValues(FormConfig formConfig, String configJson) {
        ContentValues values = new ContentValues();
        
        // 确保必需字段有值
        String formId = formConfig.getFormId();
        if (formId == null || formId.trim().isEmpty()) {
            formId = "form_" + System.currentTimeMillis();
            Log.w(TAG, "表单 ID 为空，生成临时 ID: " + formId);
            formConfig.setFormId(formId);
        }
        
        String formName = formConfig.getFormName();
        if (formName == null || formName.trim().isEmpty()) {
            formName = "未命名表单";
            Log.w(TAG, "表单名称为空，使用默认名称: " + formName);
            formConfig.setFormName(formName);
        }
        
        values.put(COLUMN_FORM_ID, formId);
        values.put(COLUMN_FORM_NAME, formName);
        values.put(COLUMN_DESCRIPTION, formConfig.getDescription());
        values.put(COLUMN_VERSION, formConfig.getVersion());
        values.put(COLUMN_SUBMIT_URL, formConfig.getSubmitUrl());
        values.put(COLUMN_IS_OFFLINE_SUPPORTED, formConfig.isOfflineSupported() ? 1 : 0);
        values.put(COLUMN_CONFIG_JSON, configJson);
        values.put(COLUMN_UPDATE_TIME, System.currentTimeMillis());
        
        return values;
    }
    
    /**
     * 更新或插入表单配置
     */
    private long updateOrInsertFormConfig(SQLiteDatabase db, ContentValues values, String formId) {
        Cursor cursor = db.query(
                TABLE_FORM_CONFIG,
                new String[]{COLUMN_ID},
                COLUMN_FORM_ID + " = ?",
                new String[]{formId},
                null, null, null
        );
        
        long id;
        if (cursor != null && cursor.moveToFirst()) {
            // 更新现有记录
            id = cursor.getLong(cursor.getColumnIndexOrThrow(COLUMN_ID));
            db.update(TABLE_FORM_CONFIG, values, COLUMN_ID + " = ?", new String[]{String.valueOf(id)});
            cursor.close();
            Log.d(TAG, "更新表单配置，ID: " + id);
        } else {
            // 插入新记录
            id = db.insert(TABLE_FORM_CONFIG, null, values);
            if (cursor != null) {
                cursor.close();
            }
            Log.d(TAG, "插入新表单配置，ID: " + id);
        }
        
        return id;
    }
    
    /**
     * 根据表单ID获取表单配置
     */
    public FormConfig getFormConfig(String formId) {
        SQLiteDatabase db = this.getReadableDatabase();
        ensureTableExists(db);
        
        Cursor cursor = db.query(
                TABLE_FORM_CONFIG,
                null,
                COLUMN_FORM_ID + " = ?",
                new String[]{formId},
                null, null, null
        );
        
        FormConfig formConfig = null;
        if (cursor != null && cursor.moveToFirst()) {
            try {
                String configJson = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_CONFIG_JSON));

                formConfig = FormJsonUtils.parseFormConfigJson(configJson, formId);
                if (formConfig == null) {
                    formConfig = FormJsonUtils.createDefaultFormConfig(formId);
                }
            } catch (Exception e) {
                Log.e(TAG, "处理数据库查询结果时出错: " + e.getMessage(), e);
                formConfig = FormJsonUtils.createDefaultFormConfig(formId);
            } finally {
                cursor.close();
            }
        } else {
            if (cursor != null) {
                cursor.close();
            }
            Log.w(TAG, "未找到ID为 " + formId + " 的表单配置");
        }
        
        db.close();
        return formConfig;
    }
    
    /**
     * 获取所有表单配置
     */
    public List<FormConfig> getAllFormConfigs() {
        List<FormConfig> formConfigs = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();
        ensureTableExists(db);
        
        Cursor cursor = db.query(
                TABLE_FORM_CONFIG,
                null,
                null,
                null,
                null,
                null,
                COLUMN_UPDATE_TIME + " DESC"
        );
        
        if (cursor != null && cursor.moveToFirst()) {
            do {
                try {
                    String configJson = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_CONFIG_JSON));
                    String formId = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_FORM_ID));
                    
                    FormConfig formConfig = FormJsonUtils.parseFormConfigJson(configJson, formId);
                    if (formConfig != null) {
                        formConfigs.add(formConfig);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "解析表单配置失败: " + e.getMessage(), e);
                }
            } while (cursor.moveToNext());
            
            cursor.close();
        }
        
        db.close();
        return formConfigs;
    }
} 
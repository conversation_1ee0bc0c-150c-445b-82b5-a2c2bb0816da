package com.abdu.qrcode;

import android.app.Activity;
import android.content.Intent;
import android.util.Log;

import com.alibaba.fastjson.JSONObject;

import java.util.Date;

import io.dcloud.feature.uniapp.annotation.UniJSMethod;
import io.dcloud.feature.uniapp.bridge.UniJSCallback;
import io.dcloud.feature.uniapp.common.UniModule;

public class QrcodeMoudle extends UniModule{
    private static int REQUEST_CODE = 200;
    private UniJSCallback qrcodeCallback;

    public QrcodeMoudle() {

    }
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == QrcodeMoudle.REQUEST_CODE) {
            String scanResult = data.getStringExtra("result");
            System.out.println("返回的结果:"+scanResult);
            // 检查requestCode和resultCode是否符合预期
             // 从Intent中获取扫描结果
            // 处理扫描结果，例如调用回调函数等
            this.qrcodeCallback.invoke(scanResult);
        }else{
            this.qrcodeCallback.invoke(null);
        }
    }

    @UniJSMethod(
            uiThread = true
    )
    public void testprint(JSONObject options, UniJSCallback callback) {
        System.out.println("进来了");
        this.qrcodeCallback = callback;
        Intent intent = new Intent(this.mUniSDKInstance.getContext(), ScanActivity.class);
        intent.putExtra("model","test");
        ((Activity)this.mUniSDKInstance.getContext()).startActivityForResult(intent, REQUEST_CODE);
    }
}

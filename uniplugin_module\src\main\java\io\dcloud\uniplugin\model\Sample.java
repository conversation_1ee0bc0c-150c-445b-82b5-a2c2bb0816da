package io.dcloud.uniplugin.model;

import com.google.gson.annotations.SerializedName;

/**
 * 样品数据模型
 */
public class Sample {
    
    /**
     * 样品类型枚举
     */
    public enum SampleType {
        SURFACE_SAMPLE(1, "表层样品"),
        SOIL_BULK_DENSITY(2, "土壤容重样品");
//        WATER_STABLE_AGGREGATE(3, "水稳性大团聚体样品");
        
        private final int code;
        private final String name;
        
        SampleType(int code, String name) {
            this.code = code;
            this.name = name;
        }
        
        public int getCode() {
            return code;
        }
        
        public String getName() {
            return name;
        }
        
        public static SampleType fromCode(int code) {
            for (SampleType type : values()) {
                if (type.code == code) {
                    return type;
                }
            }
            return null;
        }
    }
    
    private Long id;
    private Long pjdyId;
    private String pjdybh;
    
    @SerializedName(value = "sampleType", alternate = {"yplx"})
    private Integer sampleType;
    
    private String sampleTypeName;
    
    @SerializedName(value = "sampleWeight", alternate = {"ypzl"})
    private Double sampleWeight;
    
    private String sampleCode;
    
    @SerializedName(value = "sampleNumber", alternate = {"ctdbh"})
    private String sampleNumber;
    
    private String createTime;
    private String updateTime;
    private Long userId;
    
    public Sample() {
    }
    
    public Sample(String pjdybh, SampleType type, Double weight, String code) {
        this.pjdybh = pjdybh;
        this.sampleType = type.getCode();
        this.sampleTypeName = type.getName();
        this.sampleWeight = weight;
        this.sampleCode = code;
        this.sampleNumber = generateSampleNumber(pjdybh, type);
    }
    
    /**
     * 生成样品编号：bsm + 样品类型代码 + 0
     */
    public static String generateSampleNumber(String pjdybh, SampleType type) {
        return pjdybh + type.getCode() + "0";
    }

    public Long getPjdyId() {
        return pjdyId;
    }

    public void setPjdyId(Long pjdyId) {
        this.pjdyId = pjdyId;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getpjdybh() {
        return pjdybh;
    }
    
    public void setpjdybh(String pjdybh) {
        this.pjdybh = pjdybh;
    }
    
    public Integer getSampleType() {
        return sampleType;
    }
    
    public void setSampleType(Integer sampleType) {
        this.sampleType = sampleType;
    }
    
    /**
     * 获取样品类型枚举对象
     * @return SampleType枚举对象，如果无效则返回null
     */
    public SampleType getSampleTypeEnum() {
        if (sampleType == null) {
            return null;
        }
        return SampleType.fromCode(sampleType);
    }
    
    /**
     * 设置样品类型（使用枚举）
     * @param sampleType 样品类型枚举
     */
    public void setSampleType(SampleType sampleType) {
        if (sampleType != null) {
            this.sampleType = sampleType.getCode();
            this.sampleTypeName = sampleType.getName();
        }
    }
    
    public String getSampleTypeName() {
        return sampleTypeName;
    }
    
    public void setSampleTypeName(String sampleTypeName) {
        this.sampleTypeName = sampleTypeName;
    }
    
    public Double getSampleWeight() {
        return sampleWeight;
    }
    
    public void setSampleWeight(Double sampleWeight) {
        this.sampleWeight = sampleWeight;
    }
    
    public String getSampleCode() {
        return sampleCode;
    }
    
    public void setSampleCode(String sampleCode) {
        this.sampleCode = sampleCode;
    }
    
    public String getSampleNumber() {
        return sampleNumber;
    }
    
    public void setSampleNumber(String sampleNumber) {
        this.sampleNumber = sampleNumber;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    @Override
    public String toString() {
        return "Sample{" +
                "id=" + id +
                ", pjdybh='" + pjdybh + '\'' +
                ", sampleType=" + sampleType +
                ", sampleTypeName='" + sampleTypeName + '\'' +
                ", sampleWeight=" + sampleWeight +
                ", sampleCode='" + sampleCode + '\'' +
                ", sampleNumber='" + sampleNumber + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", userId=" + userId +
                '}';
    }
} 
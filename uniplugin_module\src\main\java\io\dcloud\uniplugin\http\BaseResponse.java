package io.dcloud.uniplugin.http;

public class BaseResponse<T> {
    private int code;
    private String msg;
    private T data;

    public static final int CODE_SUCCESS = 0;
    public static final int CODE_UNAUTHORIZED = 401;
    public static final int CODE_SYSTEM_ERROR = 500;

    public boolean isSuccess() {
        return code == CODE_SUCCESS;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
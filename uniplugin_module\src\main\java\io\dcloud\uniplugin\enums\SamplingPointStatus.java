package io.dcloud.uniplugin.enums;

/**
 * 样点状态枚举
 */
public enum SamplingPointStatus {
    
    PENDING_SURVEY(1, "待调查"),
    PENDING_SUBMIT(2, "待提交"),
    SUBMITTED(3, "已提交"),
    PENDING_RECTIFY(4, "待整改");
    
    private final int code;
    private final String description;
    
    SamplingPointStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取状态枚举
     * @param code 状态代码
     * @return 对应的状态枚举，如果没有找到则返回null
     */
    public static SamplingPointStatus fromCode(int code) {
        for (SamplingPointStatus status : SamplingPointStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }
} 
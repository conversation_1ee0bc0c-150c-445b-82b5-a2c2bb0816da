package io.dcloud.uniplugin.http;

import android.app.Activity;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.util.concurrent.TimeUnit;

import io.dcloud.uniplugin.http.api.DccyService;
import io.dcloud.uniplugin.http.api.FormService;
import io.dcloud.uniplugin.http.api.LabService;
import io.dcloud.uniplugin.http.api.SampleBatchService;
import io.dcloud.uniplugin.http.api.SampleFlowService;
import io.dcloud.uniplugin.http.api.SampleService;
import io.dcloud.uniplugin.http.api.UserService;
import io.dcloud.uniplugin.model.NumberBooleanAdapter;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * Retrofit管理类
 */
public class RetrofitManager {
    private static final String TAG = "RetrofitManager";
    
    // 服务器地址，需要根据实际情况修改
    private static final String BASE_URL = "http://*************:48082/admin-api/";
    
    // 超时时间
    private static final long CONNECT_TIMEOUT = 60;
    private static final long READ_TIMEOUT = 60;
    private static final long WRITE_TIMEOUT = 60;
    
    private static RetrofitManager instance;
    private final Retrofit retrofit;
    private final Activity activity;
    
    /**
     * 获取单例
     */
    public static synchronized RetrofitManager getInstance(Activity activity) {
        if (instance == null) {
            instance = new RetrofitManager(activity);
        }
        return instance;
    }
    
    /**
     * 私有构造方法
     */
    private RetrofitManager(Activity activity) {
        this.activity = activity;
        
        // 创建OkHttpClient
        OkHttpClient.Builder httpClientBuilder = new OkHttpClient.Builder()
                .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS);
        
        // 添加日志拦截器
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor();
        loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        httpClientBuilder.addInterceptor(loggingInterceptor);
        
        // 添加Token拦截器
        httpClientBuilder.addInterceptor(new TokenInterceptor(activity));
        
        // 创建自定义Gson，注册TypeAdapter处理数字布尔值
        Gson gson = new GsonBuilder()
                .registerTypeAdapter(Boolean.class, new NumberBooleanAdapter())
                .registerTypeAdapter(boolean.class, new NumberBooleanAdapter())
                .create();
        
        // 创建Retrofit，添加Gson转换器
        retrofit = new Retrofit.Builder()
                .baseUrl(BASE_URL)
                .client(httpClientBuilder.build())
                .addConverterFactory(GsonConverterFactory.create(gson)) // 使用自定义Gson配置
                .addConverterFactory(new CustomConverterFactory(activity))
                .build();
    }
    
    /**
     * 获取用户API服务
     */
    public UserService getApiService() {
        return retrofit.create(UserService.class);
    }

    /**
     * 获取调查采样API服务
     */
    public DccyService getDccyService() {
        return retrofit.create(DccyService.class);
    }

    /**
     * 获取采土袋API服务
     */
    public SampleService getSampleService() {
        return retrofit.create(SampleService.class);
    }

    /**
     * 获取动态表单API服务
     */
    public FormService getFormService() {
        return retrofit.create(FormService.class);
    }

    /**
     * 获取样品流转API服务
     */
    public SampleFlowService getSampleFlowService() {
        return retrofit.create(SampleFlowService.class);
    }
    
    /**
     * 获取样品批次API服务（api包下的实现，支持addSamplesToBatch方法）
     */
    public SampleBatchService getSampleBatchServiceApi() {
        return retrofit.create(SampleBatchService.class);
    }
    
    /**
     * 获取检测实验室API服务
     */
    public LabService getLabService() {
        return retrofit.create(LabService.class);
    }
    
    /**
     * 获取Retrofit实例，供FileUploadService等使用
     */
    public Retrofit getRetrofit() {
        return retrofit;
    }
}
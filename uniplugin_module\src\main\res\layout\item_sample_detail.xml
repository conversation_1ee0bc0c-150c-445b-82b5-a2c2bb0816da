<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="4dp"
    app:cardElevation="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- 样品编号和状态 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/textViewSampleCode"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="样品编号: SMP001"
                android:textColor="#333333"
                android:textSize="14sp"
                android:textStyle="bold" />

<!--            <TextView-->
<!--                android:id="@+id/textViewStatus"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:background="@drawable/tag_blue"-->
<!--                android:paddingLeft="8dp"-->
<!--                android:paddingTop="2dp"-->
<!--                android:paddingRight="8dp"-->
<!--                android:paddingBottom="2dp"-->
<!--                android:text="已入库"-->
<!--                android:textColor="#FFFFFF"-->
<!--                android:textSize="12sp" />-->
        </LinearLayout>

        <!-- 样品信息区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            
            <!-- 左侧信息 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">
                
                <!-- 样品名称 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="4dp">
                    
                    <TextView
                        android:layout_width="70dp"
                        android:layout_height="wrap_content"
                        android:text="样品名称:"
                        android:textColor="#666666"
                        android:textSize="12sp" />
                    
                    <TextView
                        android:id="@+id/textViewSampleName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#333333"
                        android:textSize="12sp" />
                </LinearLayout>
                
                <!-- 样品类型 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="4dp">
                    
                    <TextView
                        android:layout_width="70dp"
                        android:layout_height="wrap_content"
                        android:text="样品类型:"
                        android:textColor="#666666"
                        android:textSize="12sp" />
                    
                    <TextView
                        android:id="@+id/textViewSampleType"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#333333"
                        android:textSize="12sp" />
                </LinearLayout>
                
                <!-- 样品重量 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="4dp">
                    
                    <TextView
                        android:layout_width="70dp"
                        android:layout_height="wrap_content"
                        android:text="样品重量:"
                        android:textColor="#666666"
                        android:textSize="12sp" />
                    
                    <TextView
                        android:id="@+id/textViewSampleWeight"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#333333"
                        android:textSize="12sp" />
                </LinearLayout>
                
                <!-- 采样点 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    
                    <TextView
                        android:layout_width="70dp"
                        android:layout_height="wrap_content"
                        android:text="采样点:"
                        android:textColor="#666666"
                        android:textSize="12sp" />
                    
                    <TextView
                        android:id="@+id/textViewSamplingPoint"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#333333"
                        android:textSize="12sp" />
                </LinearLayout>
            </LinearLayout>
            
            <!-- 右侧操作按钮 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="end"
                android:layout_marginStart="8dp">
                
                <Button
                    android:id="@+id/buttonEdit"
                    android:layout_width="60dp"
                    android:layout_height="32dp"
                    android:text="修改"
                    android:textSize="12sp"
                    android:textColor="#FFFFFF"
                    android:background="@drawable/button_primary"
                    android:layout_marginBottom="8dp"
                    android:visibility="visible" />
                
                <Button
                    android:id="@+id/buttonDelete"
                    android:layout_width="60dp"
                    android:layout_height="32dp"
                    android:text="删除"
                    android:textSize="12sp"
                    android:textColor="#FFFFFF"
                    android:background="@drawable/button_danger"
                    android:visibility="visible" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView> 
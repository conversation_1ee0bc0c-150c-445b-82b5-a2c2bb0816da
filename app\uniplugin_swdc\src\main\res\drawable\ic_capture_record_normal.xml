<?xml version="1.0" encoding="utf-8"?>

<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="oval"
    android:useLevel="false">
    <stroke android:color="#000000"
        android:width="1dp"></stroke>

<!--    <padding-->
<!--        android:bottom="6dp"-->
<!--        android:left="6dp"-->
<!--        android:right="6dp"-->
<!--        android:top="6dp"/>-->

    <gradient
        android:gradientRadius="4dp"
        android:angle="180"
        android:startColor="@color/record_normal"
        android:endColor="@color/record_normal"/>
    <size
        android:width="@dimen/icon_capture_size"
        android:height="@dimen/icon_capture_size"></size>

</shape>
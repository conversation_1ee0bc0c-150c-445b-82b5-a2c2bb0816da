package io.dcloud.uniplugin.form.utils;

import android.app.Activity;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.Spinner;

import androidx.appcompat.app.AlertDialog;

import org.json.JSONObject;

import java.util.List;
import java.util.Map;

import io.dcloud.uniplugin.form.field.FieldFile;
import io.dcloud.uniplugin.form.processor.FormProcessor;
import io.dcloud.uniplugin.fileUpload.ImageAdapter;
import io.dcloud.uniplugin.model.FormConfig;
import io.dcloud.uniplugin.model.FormData;
import io.dcloud.uniplugin.service.FormService;

/**
 * 表单离线管理器，负责处理离线表单的保存和加载
 */
public class FormOfflineManager {
    private static final String TAG = "FormOfflineManager";
    
    private Activity activity;
    private FormProcessor formProcessor;
    private Map<String, View> formViews;
    private Map<String, ImageAdapter> imageAdapters;
    private Map<String, List<FieldFile>> fieldFiles;
    private FormService formService;
    private FormConfig formConfig;
    private String pjdybh;
    private AlertDialog loadingDialog;
    
    /**
     * 表单服务回调接口
     */
    public interface FormServiceCallback {
        void onSuccess(JSONObject result);
        void onError(String errorMessage);
    }
    
    /**
     * 构造函数
     * @param activity 活动
     * @param formProcessor 表单处理器
     * @param formViews 表单视图
     * @param imageAdapters 图片适配器
     * @param fieldFiles 字段文件
     * @param formService 表单服务
     * @param formConfig 表单配置
     * @param pjdybh 采样点ID
     */
    public FormOfflineManager(Activity activity, FormProcessor formProcessor, 
                            Map<String, View> formViews, 
                            Map<String, ImageAdapter> imageAdapters, 
                            Map<String, List<FieldFile>> fieldFiles,
                            FormService formService,
                            FormConfig formConfig,
                            String pjdybh) {
        this.activity = activity;
        this.formProcessor = formProcessor;
        this.formViews = formViews;
        this.imageAdapters = imageAdapters;
        this.fieldFiles = fieldFiles;
        this.formService = formService;
        this.formConfig = formConfig;
        this.pjdybh = pjdybh;
    }

    /**
     * 获取离线表单数据
     * @param pjdybh 样点ID
     * @return 表单数据
     */
    public FormData getOfflineFormData(String pjdybh) {
        // 临时实现：返回临时数据
        FormData formData = new FormData();
        formData.setSamplingPointId(pjdybh);
        return formData;
    }
    
    /**
     * 从FormData中获取表单配置
     * @param formData 表单数据
     * @return 表单配置
     */
    public FormConfig getFormConfig(FormData formData) {
        // 临时实现：返回空配置
        return FormDataAdapter.getEmptyFormConfig();
    }
    
    /**
     * 使用离线表单数据填充表单字段
     * @param formData 离线表单数据
     */
    public void fillFormWithOfflineData(FormData formData) {
        try {
            // 填充表单字段
            Map<String, String> formFields = formData.getFormFields();
            
            // 转换文件列表为FieldFile列表
            Map<String, List<FieldFile>> files = FormDataAdapter.convertFilesToFieldFiles(formData);

            if (formFields == null || formFields.isEmpty()) {
                Log.w(TAG, "离线表单字段为空");
                return;
            }

            // 填充文本字段
            for (Map.Entry<String, String> entry : formFields.entrySet()) {
                String fieldId = entry.getKey();
                String value = entry.getValue();

                View view = formViews.get(fieldId);
                if (view == null) continue;

                try {
                    // 根据字段类型设置值
                    if (view instanceof EditText) {
                        ((EditText) view).setText(value);
                    } else if (view instanceof Spinner) {
                        Spinner spinner = (Spinner) view;
                        // 需要找到正确的选项索引
                        for (int i = 0; i < spinner.getAdapter().getCount(); i++) {
                            Object item = spinner.getItemAtPosition(i);
                            if (item != null && item.toString().equals(value)) {
                                spinner.setSelection(i);
                                break;
                            }
                        }
                    }
                    // 其他类型的控件可以在这里添加...
                } catch (Exception e) {
                    Log.e(TAG, "设置字段值失败: " + fieldId, e);
                }
            }

            // 加载文件字段
            for (Map.Entry<String, List<FieldFile>> entry : files.entrySet()) {
                String fieldId = entry.getKey();
                List<FieldFile> fieldFileList = entry.getValue();

                if (fieldFileList == null || fieldFileList.isEmpty()) continue;

                ImageAdapter adapter = imageAdapters.get(fieldId);
                if (adapter != null) {
                    // 清空原有项
                    adapter.clearItems();

                    // 添加离线保存的图片
                    for (FieldFile fieldFile : fieldFileList) {
                        adapter.addItem(fieldFile);
                    }

                    adapter.notifyDataSetChanged();
                }

                // 保存文件路径到当前字段
                this.fieldFiles.put(fieldId, fieldFileList);
            }

            Log.d(TAG, "成功填充表单数据");
        } catch (Exception e) {
            Log.e(TAG, "填充离线表单数据失败", e);
            UIUtils.showToast(activity, "加载表单数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 加载离线表单数据
     */
    public void loadOfflineFormData(FormUIController uiController, FormDataLoadedCallback callback) {
        uiController.showLoading(true);
        try {
            FormData formData = getOfflineFormData(pjdybh);
            if (formData != null) {
                // 获取表单配置
                FormConfig config = getFormConfig(formData);
                if (config != null) {
                    this.formConfig = config;
                    
                    if (callback != null) {
                        callback.onFormDataLoaded(config, formData);
                    }
                } else {
                    UIUtils.showToast(activity, "离线表单配置为空");
                    Log.e(TAG, "离线表单配置为空");
                }
            } else {
                UIUtils.showToast(activity, "未找到离线表单数据");
                Log.e(TAG, "未找到离线表单数据");
            }
        } catch (Exception e) {
            UIUtils.showToast(activity, "加载离线表单失败: " + e.getMessage());
            Log.e(TAG, "加载离线表单失败", e);
        } finally {
            uiController.showLoading(false);
        }
    }
    
    /**
     * 表单数据加载回调
     */
    public interface FormDataLoadedCallback {
        void onFormDataLoaded(FormConfig config, FormData formData);
    }
} 
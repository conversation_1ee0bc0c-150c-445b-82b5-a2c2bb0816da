package io.dcloud.uniplugin.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.TextView;

import io.dcloud.uniplugin.view.SignatureView;
import uni.dcloud.io.uniplugin_module.R;

public class SignatureDialog extends Dialog {
    
    private SignatureView signatureView;
    private TextView titleTextView;
    private Button clearButton;
    private Button cancelButton;
    private Button confirmButton;
    private SignatureListener listener;
    private String title;
    
    public interface SignatureListener {
        void onSignatureConfirmed(String signatureBase64);
        void onSignatureCancelled();
    }
    
    public SignatureDialog(Context context, String title, SignatureListener listener) {
        super(context);
        this.listener = listener;
        this.title = title;
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setCancelable(false);
    }
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_signature);
        
        signatureView = findViewById(R.id.signatureView);
        titleTextView = findViewById(R.id.textViewTitle);
        clearButton = findViewById(R.id.buttonClear);
        cancelButton = findViewById(R.id.buttonCancel);
        confirmButton = findViewById(R.id.buttonConfirm);
        
        titleTextView.setText(title);
        
        clearButton.setOnClickListener(v -> signatureView.clear());
        
        cancelButton.setOnClickListener(v -> {
            if (listener != null) {
                listener.onSignatureCancelled();
            }
            dismiss();
        });
        
        confirmButton.setOnClickListener(v -> {
            if (signatureView.isEmpty()) {
                // 签名为空
                return;
            }
            
            if (listener != null) {
                String signatureBase64 = signatureView.getSignatureAsBase64();
                listener.onSignatureConfirmed(signatureBase64);
            }
            dismiss();
        });
    }
} 
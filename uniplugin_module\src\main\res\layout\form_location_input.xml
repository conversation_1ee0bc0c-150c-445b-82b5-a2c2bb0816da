<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp">

    <TextView
        android:id="@+id/locationText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="80dp"
        android:background="@android:color/white"
        android:padding="8dp"
        android:textSize="14sp"
        android:gravity="center_vertical"
        android:text="等待获取GPS坐标..."
        android:layout_marginBottom="8dp"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btnRefreshLocation"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="重新定位"
            android:layout_marginEnd="4dp"/>

        <Button
            android:id="@+id/btnSelectLocation"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="手动选点"
            android:layout_marginStart="4dp"/>

    </LinearLayout>

</LinearLayout> 
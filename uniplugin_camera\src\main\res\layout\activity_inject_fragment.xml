<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 顶部透明占位框 -->
    <View
        android:id="@+id/placeholderView"
        android:layout_width="match_parent"
        android:layout_height="?android:attr/actionBarSize"
        android:background="@android:color/transparent" />

    <!-- 填充剩余高度的FrameLayout -->
    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/placeholderView"
        android:background="@android:color/white" />

</RelativeLayout>

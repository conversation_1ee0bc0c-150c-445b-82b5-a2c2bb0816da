package com.chy.map;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.widget.ImageButton;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.esri.arcgisruntime.ArcGISRuntimeEnvironment;
import com.esri.arcgisruntime.arcgisservices.LevelOfDetail;
import com.esri.arcgisruntime.arcgisservices.TileInfo;
import com.esri.arcgisruntime.geometry.Envelope;
import com.esri.arcgisruntime.geometry.Point;
import com.esri.arcgisruntime.geometry.SpatialReference;
import com.esri.arcgisruntime.layers.ArcGISTiledLayer;
import com.esri.arcgisruntime.layers.WebTiledLayer;
import com.esri.arcgisruntime.mapping.ArcGISMap;
import com.esri.arcgisruntime.mapping.Basemap;
import com.esri.arcgisruntime.mapping.Viewpoint;
import com.esri.arcgisruntime.mapping.view.Graphic;
import com.esri.arcgisruntime.mapping.view.GraphicsOverlay;
import com.esri.arcgisruntime.mapping.view.MapView;
import com.esri.arcgisruntime.symbology.SimpleMarkerSymbol;
import com.esri.arcgisruntime.toolkit.scalebar.Scalebar;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;

public class MainActivity extends AppCompatActivity {
    private MapView mMapView;
    private String[] toolNames = {"compassButton", "zoomInButton", "zoomOutButton", "fullPicButton", "measurementButton", "currentLoButton","layersButton"};

    //pointList是一个JSONArray
    private JSONArray pointList;
    //单个跳转样点的信息
    private GraphicsOverlay pointGraphicsOverlay;
    @SuppressLint("MissingInflatedId")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_main_swdc);
        ArcGISRuntimeEnvironment.setLicense("runtimelite,1000,rud7416273699,none,4N5X0H4AH7AH6XCFK036");
        mMapView = findViewById(R.id.mapView);
        ArcGISMap map = new ArcGISMap();
        mMapView.setMap(map);
        mMapView.setViewpointCenterAsync(new Point(113.5, 23), 9234299.78);
        ArrayList<LevelOfDetail> levels = new ArrayList<LevelOfDetail>();
        levels.add(new LevelOfDetail(1, 0.703125, 295497593.05875003));
        levels.add(new LevelOfDetail(2, 0.3515625, 147748796.52937502));
        levels.add(new LevelOfDetail(3, 0.17578125, 73874398.264687508));
        levels.add(new LevelOfDetail(4, 0.087890625, 36937199.132343754));
        levels.add(new LevelOfDetail(5, 0.**********, 18468599.566171877));
        levels.add(new LevelOfDetail(6, 0.02197265625, 9234299.**********));
        levels.add(new LevelOfDetail(7, 0.010986328125, 4617149.**********));
        levels.add(new LevelOfDetail(8, 0.0054931640625, 2308574.**********));
        levels.add(new LevelOfDetail(8, 0.00274658203125, 1154287.4728857423));
        levels.add(new LevelOfDetail(10, 0.001373291015625, 577143.73644287116));
        levels.add(new LevelOfDetail(11, 0.0006866455078125, 288571.86822143558));
        levels.add(new LevelOfDetail(12, 0.00034332275390625, 144285.93411071779));
        levels.add(new LevelOfDetail(13, 0.000171661376953125, 72142.967055358895));
        levels.add(new LevelOfDetail(14, 8.58306884765625e-005, 36071.483527679447));
        levels.add(new LevelOfDetail(15, 4.291534423828125e-005, 18035.741763839724));
        levels.add(new LevelOfDetail(16, 2.1457672119140625e-005, 9017.8708819198619));
        levels.add(new LevelOfDetail(17, 1.0728836059570313e-005, 4508.9354409599309));
        levels.add(new LevelOfDetail(18, 5.3644180297851563e-006, 2254.4677204799655));
        levels.add(new LevelOfDetail(19, 2.6822090148925781e-006, 1127.2338602399827));
        levels.add(new LevelOfDetail(20, 1.3411045074462891e-006, 563.61693011999137));

        int iDPI = 96;
        int iTileWidth = 256;
        int iTileHeight = 256;

        TileInfo tileInfo = new TileInfo(iDPI, TileInfo.ImageFormat.JPG, levels, new Point(-180, 90, SpatialReference.create(4490)), SpatialReference.create(4490), iTileHeight, iTileWidth);

        Envelope fullExtent = new Envelope(109.63682542226111,20.203900597875062,117.32443682984213,25.535408282985312,SpatialReference.create(4490));
        String templateUri = "http://19.25.36.95/gmap/proxyHandler?paasId=C90-44003156&paasToken=c5dc6ec82ca647799983870fe25ac3b4&serviceCode=YZT1677228322068&url=https://ztn-data.gdgov.cn:8581/GatewayMsg/http/api/proxy/invoke?TOKEN=4CEC6D37BF430740C1C6E500A2338A18&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=DOM_2000_2020_2M_JCB&STYLE=default&TILEMATRIXSET=default028mm&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}";
        String templateUri2="http://19.25.36.95/gmap/proxyHandler?paasId=C90-44003664&paasToken=a5447d62994d4b1a98181cb86c21f948&serviceCode=YZT1694159107667&url=https://ztn-data.gdgov.cn:8581/GatewayMsg/http/api/proxy/invoke?token=1VlOfRHby7YiWBjdp41JtZCg6XFXyzo6&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=dzdttyb2022&STYLE=tyb&TILEMATRIXSET=default028mm&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&FORMAT=image/png";
        String templateUri1="http://19.25.36.95/gmap/proxyHandler?paasId=C90-44003664&paasToken=a5447d62994d4b1a98181cb86c21f948&serviceCode=YZT1694159105477&url=https://ztn-data.gdgov.cn:8581/GatewayMsg/http/api/proxy/invoke?token=oeBftz1QdHJ3kauQSAhaF9SVw0FQK0Wf&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=dzdttyb2022&STYLE=tyb&TILEMATRIXSET=default028mm&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&FORMAT=image/png";
        WebTiledLayer webTiledLayer =new WebTiledLayer(templateUri,tileInfo,fullExtent);
        WebTiledLayer webTiledLayerone =new WebTiledLayer(templateUri1,tileInfo,fullExtent);
        WebTiledLayer webTiledLayertwo =new WebTiledLayer(templateUri2,tileInfo,fullExtent);
        webTiledLayerone.setVisible(false);
        map.getBasemap().getBaseLayers().add(webTiledLayer);
        map.getBasemap().getBaseLayers().add(webTiledLayerone);
        map.getBasemap().getBaseLayers().add(webTiledLayertwo);
        mMapView.setViewpointCenterAsync(new Point(113.5,23),4617149.**********);//



        List<HashMap<String, ImageButton>> list = getImageButton();
        BaseTool baseTool = new BaseTool(mMapView, (MainActivity) this, this);
        baseTool.initTools(getImageButton());
        Intent intent = getIntent();
        String points = intent.getStringExtra("points");
        String point = intent.getStringExtra("point");
        this.pointList = JSONArray.parseArray(points);
        String type = intent.getStringExtra("type");
        SamplePointsTool samplePointsTool = new SamplePointsTool(mMapView, this, (MainActivity) this,type);
        GraphicsOverlay  mGraphicsOverlay = new GraphicsOverlay();
        mMapView.getGraphicsOverlays().add(mGraphicsOverlay);
        if (type.equals("one")){
            samplePointsTool.addOnePoints(pointList,point);
        }else{
            samplePointsTool.addPoints(pointList);
        }
        //导航栏添加返回按钮
        //这里在Activity的onCreat()方法里写
        ActionBar actionBar = getSupportActionBar();
        actionBar.setHomeButtonEnabled(true);
        actionBar.setDisplayHomeAsUpEnabled(true);  //添加返回的图标

        mMapView.setAttributionTextVisible(false);//去掉底部水印
        Scalebar mScalebar = (Scalebar) findViewById(R.id.scalebar);
        mScalebar.setLineColor(Color.BLACK);
        mScalebar.bindTo(mMapView);
    }

    @Override
    public void onBackPressed() {


        setResult(SwdcModule.REQUEST_CODE_PAGE, null);

        finish(); // 关闭当前页面


    }


    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                onBackPressed();
                finish(); // 关闭当前页面
                return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private List<HashMap<String, ImageButton>> getImageButton() {
        List<HashMap<String, ImageButton>> list = new ArrayList<HashMap<String, ImageButton>>();
        for (int i = 0; i < toolNames.length; i++) {
            int resID = getResources().getIdentifier(toolNames[i], "id", getPackageName());
            ImageButton imageButton = (ImageButton) findViewById(resID);
            HashMap<String, ImageButton> btn = new HashMap<String, ImageButton>();
            btn.put(toolNames[i], imageButton);
            list.add(btn);
        }
        return list;
    }
    @Override
    protected void onDestroy() {
        super.onDestroy();
        mMapView.dispose();
        System.gc();
    }
}
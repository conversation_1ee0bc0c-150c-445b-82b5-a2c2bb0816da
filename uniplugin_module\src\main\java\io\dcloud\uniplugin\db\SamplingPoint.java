package io.dcloud.uniplugin.db;

/**
 * 采样点数据模型
 */
public class SamplingPoint {
    private String id;
    private String ydbh;
    private String ydlb;
    private String zldwdm;
    private String zldwmc;
    private double cyjd;
    private double cywd;
    private String cylx;
    private String tdlylx;
    private String tdlylxMc;
    private String sjdm;
    private String shjdm;
    private String xjdm;
    private String zjdm;
    private int cydId;
    private String cydMc;
    private String ts;
    private String tz;
    private String tl;
    private String yl;
    private String sfsty;
    private double hbgd;
    private int yhId;
    private String yhMc;
    private long dcsj;
    private String dcdw;
    private String dcz;
    private long tjsj;
    private String sfyjd;
    private String bz;
    private String zt;
    private int cygc;
    private int sfsjptzg;
    private String cygczt;
    private Long cygcTjsj;
    private int sfgjptzg;
    private int sftsncp;
    private String ncpmc;
    private String gsdmc;
    private boolean isCached; // 是否已缓存到本地

    public SamplingPoint() {
        this.isCached = true;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getYdbh() {
        return ydbh;
    }

    public void setYdbh(String ydbh) {
        this.ydbh = ydbh;
    }

    public String getYdlb() {
        return ydlb;
    }

    public void setYdlb(String ydlb) {
        this.ydlb = ydlb;
    }

    public String getZldwdm() {
        return zldwdm;
    }

    public void setZldwdm(String zldwdm) {
        this.zldwdm = zldwdm;
    }

    public String getZldwmc() {
        return zldwmc;
    }

    public void setZldwmc(String zldwmc) {
        this.zldwmc = zldwmc;
    }

    public double getCyjd() {
        return cyjd;
    }

    public void setCyjd(double cyjd) {
        this.cyjd = cyjd;
    }

    public double getCywd() {
        return cywd;
    }

    public void setCywd(double cywd) {
        this.cywd = cywd;
    }

    public String getCylx() {
        return cylx;
    }

    public void setCylx(String cylx) {
        this.cylx = cylx;
    }

    public String getTdlylx() {
        return tdlylx;
    }

    public void setTdlylx(String tdlylx) {
        this.tdlylx = tdlylx;
    }

    public String getTdlylxMc() {
        return tdlylxMc;
    }

    public void setTdlylxMc(String tdlylxMc) {
        this.tdlylxMc = tdlylxMc;
    }

    public String getSjdm() {
        return sjdm;
    }

    public void setSjdm(String sjdm) {
        this.sjdm = sjdm;
    }

    public String getShjdm() {
        return shjdm;
    }

    public void setShjdm(String shjdm) {
        this.shjdm = shjdm;
    }

    public String getXjdm() {
        return xjdm;
    }

    public void setXjdm(String xjdm) {
        this.xjdm = xjdm;
    }

    public String getZjdm() {
        return zjdm;
    }

    public void setZjdm(String zjdm) {
        this.zjdm = zjdm;
    }

    public int getCydId() {
        return cydId;
    }

    public void setCydId(int cydId) {
        this.cydId = cydId;
    }

    public String getCydMc() {
        return cydMc;
    }

    public void setCydMc(String cydMc) {
        this.cydMc = cydMc;
    }

    public String getTs() {
        return ts;
    }

    public void setTs(String ts) {
        this.ts = ts;
    }

    public String getTz() {
        return tz;
    }

    public void setTz(String tz) {
        this.tz = tz;
    }

    public String getTl() {
        return tl;
    }

    public void setTl(String tl) {
        this.tl = tl;
    }

    public String getYl() {
        return yl;
    }

    public void setYl(String yl) {
        this.yl = yl;
    }

    public String getSfsty() {
        return sfsty;
    }

    public void setSfsty(String sfsty) {
        this.sfsty = sfsty;
    }

    public double getHbgd() {
        return hbgd;
    }

    public void setHbgd(double hbgd) {
        this.hbgd = hbgd;
    }

    public int getYhId() {
        return yhId;
    }

    public void setYhId(int yhId) {
        this.yhId = yhId;
    }

    public String getYhMc() {
        return yhMc;
    }

    public void setYhMc(String yhMc) {
        this.yhMc = yhMc;
    }

    public long getDcsj() {
        return dcsj;
    }

    public void setDcsj(long dcsj) {
        this.dcsj = dcsj;
    }

    public String getDcdw() {
        return dcdw;
    }

    public void setDcdw(String dcdw) {
        this.dcdw = dcdw;
    }

    public String getDcz() {
        return dcz;
    }

    public void setDcz(String dcz) {
        this.dcz = dcz;
    }

    public long getTjsj() {
        return tjsj;
    }

    public void setTjsj(long tjsj) {
        this.tjsj = tjsj;
    }

    public String getSfyjd() {
        return sfyjd;
    }

    public void setSfyjd(String sfyjd) {
        this.sfyjd = sfyjd;
    }

    public String getBz() {
        return bz;
    }

    public void setBz(String bz) {
        this.bz = bz;
    }

    public String getZt() {
        return zt;
    }

    public void setZt(String zt) {
        this.zt = zt;
    }

    public int getCygc() {
        return cygc;
    }

    public void setCygc(int cygc) {
        this.cygc = cygc;
    }

    public int getSfsjptzg() {
        return sfsjptzg;
    }

    public void setSfsjptzg(int sfsjptzg) {
        this.sfsjptzg = sfsjptzg;
    }

    public String getCygczt() {
        return cygczt;
    }

    public void setCygczt(String cygczt) {
        this.cygczt = cygczt;
    }

    public Long getCygcTjsj() {
        return cygcTjsj;
    }

    public void setCygcTjsj(Long cygcTjsj) {
        this.cygcTjsj = cygcTjsj;
    }

    public int getSfgjptzg() {
        return sfgjptzg;
    }

    public void setSfgjptzg(int sfgjptzg) {
        this.sfgjptzg = sfgjptzg;
    }

    public int getSftsncp() {
        return sftsncp;
    }

    public void setSftsncp(int sftsncp) {
        this.sftsncp = sftsncp;
    }

    public String getNcpmc() {
        return ncpmc;
    }

    public void setNcpmc(String ncpmc) {
        this.ncpmc = ncpmc;
    }

    public String getGsdmc() {
        return gsdmc;
    }

    public void setGsdmc(String gsdmc) {
        this.gsdmc = gsdmc;
    }

    public boolean isCached() {
        return isCached;
    }

    public void setCached(boolean cached) {
        isCached = cached;
    }

    @Override
    public String toString() {
        return "SamplingPoint{" +
                "id='" + id + '\'' +
                ", ydbh='" + ydbh + '\'' +
                ", zldwmc='" + zldwmc + '\'' +
                ", cydMc='" + cydMc + '\'' +
                '}';
    }
} 
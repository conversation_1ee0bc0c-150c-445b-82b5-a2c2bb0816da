<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5"
    tools:context="io.dcloud.uniplugin.LabHomeActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 用户信息卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="欢迎使用检测实验室管理系统"
                    android:textColor="#333333"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="用户名："
                        android:textColor="#666666" />

                    <TextView
                        android:id="@+id/textViewUsername"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="未知用户"
                        android:textColor="#333333" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="角色："
                        android:textColor="#666666" />

                    <TextView
                        android:id="@+id/textViewRole"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="未知角色"
                        android:textColor="#333333" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 功能卡片网格 -->
        <GridLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:columnCount="2"
            android:rowCount="1">

            <!-- 样品流转接收卡片 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardViewSampleReceive"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_rowWeight="1"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@android:drawable/ic_menu_share"
                        android:tint="#2196F3" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="样品流转接收"
                        android:textColor="#333333"
                        android:textSize="16sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 个人信息卡片 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardViewProfile"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_rowWeight="1"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@android:drawable/ic_menu_myplaces"
                        android:tint="#9C27B0" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="个人信息"
                        android:textColor="#333333"
                        android:textSize="16sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </GridLayout>
    </LinearLayout>
</ScrollView> 
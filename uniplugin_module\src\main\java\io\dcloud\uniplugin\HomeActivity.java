package io.dcloud.uniplugin;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;

import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import io.dcloud.uniplugin.enums.SharedPreferencesEnum;
import io.dcloud.uniplugin.http.RetrofitManager;
import io.dcloud.uniplugin.model.ApiResponse;
import io.dcloud.uniplugin.model.AuthPermissionInfoRespVO;
import io.dcloud.uniplugin.model.FormConfig;
import io.dcloud.uniplugin.model.FormConfigResponse;
import io.dcloud.uniplugin.model.FormFieldConfig;
import io.dcloud.uniplugin.sampleflow.SampleFlowListActivity;
import io.dcloud.uniplugin.samplingPoint.SamplingPointsActivity;
import io.dcloud.uniplugin.service.FormConfigDatabaseHelper;
import io.dcloud.uniplugin.utils.SamplingPointSyncManager;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import uni.dcloud.io.uniplugin_module.R;

public class HomeActivity extends AppCompatActivity {

    private static final String TAG = "HomeActivity";
    private TextView textViewUsername;
    private TextView textViewRole;
//    private CardView cardViewMap;
    private CardView cardViewProfile;
    private CardView cardViewSamplingPoints;
    private CardView cardViewSampleFlow;
    private CardView cardViewDownloadData;
    private Long currentUserId = null;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_home);

        // 初始化视图
        textViewUsername = findViewById(R.id.textViewUsername);
        textViewRole = findViewById(R.id.textViewRole);
//        cardViewMap = findViewById(R.id.cardViewMap);
        cardViewProfile = findViewById(R.id.cardViewProfile);
        cardViewSamplingPoints = findViewById(R.id.cardViewSamplingPoints);
        cardViewSampleFlow = findViewById(R.id.cardViewSampleFlow);

        // 隐藏下载数据按钮（改为在样点管理页面实现）
        cardViewDownloadData = findViewById(R.id.cardViewDownloadData);
        cardViewDownloadData.setVisibility(View.GONE);

        // 样品流转按钮恢复可见
        // cardViewSampleFlow.setVisibility(View.GONE);

        // 加载用户信息（同时获取用户ID）
        loadUserInfoAndId();

        // 保留登录自动同步功能
        syncSamplingPointsData();

        // 预加载表单配置数据
        preloadFormConfigs();

//        // 设置点击事件
//        cardViewMap.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                // 跳转到地图页面
//                Intent intent = new Intent(HomeActivity.this, MainActivity.class);
//                startActivity(intent);
//            }
//        });

        cardViewProfile.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 显示个人信息页面
                Intent intent = new Intent(HomeActivity.this, ProfileActivity.class);
                startActivity(intent);
            }
        });

        cardViewSamplingPoints.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 跳转到样点管理页面
                Intent intent = new Intent(HomeActivity.this, SamplingPointsActivity.class);
                startActivity(intent);
            }
        });

        // 样品流转按钮点击事件（虽然按钮已隐藏，但保留事件代码以便将来可能恢复使用）
        cardViewSampleFlow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 跳转到样品流转页面
                Intent intent = new Intent(HomeActivity.this, SampleFlowListActivity.class);
                startActivity(intent);
            }
        });

        // 修改下载数据按钮点击事件为跳转到样点管理页面
        cardViewDownloadData.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 跳转到样点管理页面
                Intent intent = new Intent(HomeActivity.this, SamplingPointsActivity.class);
                startActivity(intent);
            }
        });
    }

    /**
     * 同步样点数据
     * 使用SamplingPointSyncManager替代原有的下载方法
     */
    private void syncSamplingPointsData() {
        // 使用同步方式下载数据，传递Activity实例
        SamplingPointSyncManager.getInstance().downloadDataSynchronously(this);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        // 检查是否从表单提交成功返回，需要重新加载样点数据
        if (resultCode == RESULT_OK && data != null && data.getBooleanExtra("RELOAD_SAMPLING_POINTS", false)) {
            Log.d(TAG, "收到重新加载样点数据的请求");
            // 调用同步方法
            syncSamplingPointsData();
        }
    }

    private void loadUserInfoAndId() {
        SharedPreferences sp = getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, MODE_PRIVATE);
        
        // 获取用户ID，注意处理默认值和可能的异常
        currentUserId = sp.getLong(SharedPreferencesEnum.USER_ID.value, -1L); // 使用 -1L 作为无效标记
        if (currentUserId == -1L) {
             currentUserId = null; // 设置为 null 表示无效
             // 可以考虑增加错误提示或处理逻辑
        }
        
        String permissionInfoStr = sp.getString(SharedPreferencesEnum.PERMISSION_INFO.value, "无法获取");
        if ("无法获取".equals(permissionInfoStr)) {
             textViewUsername.setText("未知用户");
             textViewRole.setText("未知角色");
             return;
        }
        
        Gson gson = new Gson();
        try {
             AuthPermissionInfoRespVO permissionInfoRespVO = gson.fromJson(permissionInfoStr, AuthPermissionInfoRespVO.class);
             if (permissionInfoRespVO != null && permissionInfoRespVO.getUser() != null) {
                 String nickname = permissionInfoRespVO.getUser().getNickname();
                 Set<String> roles = permissionInfoRespVO.getRoles();
                 StringBuilder role = new StringBuilder();
                 if (roles != null && !roles.isEmpty()) {
                     for (String r : roles) {
                         role.append(r).append(",");
                     }
                     role.deleteCharAt(role.length() - 1); 
                 }
                 textViewUsername.setText(nickname != null ? nickname : "N/A");
                 textViewRole.setText(role.length() > 0 ? role.toString() : "N/A");
             } else {
                  textViewUsername.setText("解析失败");
                  textViewRole.setText("解析失败");
             }
        } catch (Exception e) {
            textViewUsername.setText("解析错误");
            textViewRole.setText("解析错误");
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_home, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == R.id.action_logout) {
            logout();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private void logout() {
        // 显示退出中提示
        Toast.makeText(this, "正在退出登录...", Toast.LENGTH_SHORT).show();

        // 调用退出登录API
        RetrofitManager.getInstance(this)
                .getApiService()
                .logout()
                .enqueue(new Callback<ApiResponse<Boolean>>() {
                    @Override
                    public void onResponse(Call<ApiResponse<Boolean>> call, Response<ApiResponse<Boolean>> response) {
                        // 无论成功与否，都清除本地用户信息并跳转到登录页面
                        clearUserDataAndRedirect();
                    }

                    @Override
                    public void onFailure(Call<ApiResponse<Boolean>> call, Throwable t) {
                        Log.e(TAG, "退出登录请求失败: " + t.getMessage());
                        // 即使请求失败，也清除本地用户信息并跳转到登录页面
                        clearUserDataAndRedirect();
                    }
                });
    }

    private void clearUserDataAndRedirect() {
        // 清除SharedPreferences中的用户信息
        SharedPreferences sp = getSharedPreferences(SharedPreferencesEnum.USER_INFO_PREF.value, MODE_PRIVATE);
        sp.edit().clear().apply();

        // 显示退出成功提示
        Toast.makeText(this, "已退出登录", Toast.LENGTH_SHORT).show();

        // 跳转到登录页面
        Intent intent = new Intent(HomeActivity.this, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    /**
     * 预加载表单配置
     * 该方法在HomeActivity创建时调用，确保所有表单配置在进入样点管理前已加载到数据库
     */
    private void preloadFormConfigs() {
        Log.d(TAG, "开始预加载表单配置");

        // 创建进度对话框
        final AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("正在同步");
        builder.setMessage("正在加载表单配置，请稍候...");
        builder.setCancelable(false); // 设置对话框不可取消
        final androidx.appcompat.app.AlertDialog progressDialog = builder.create();
        progressDialog.show();

        // 创建新线程执行预加载，避免阻塞UI线程
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    // 预加载默认表单配置
                    loadAndSaveFormConfig("default_form");

                    // 加载完成，更新最后加载时间
                    saveLastPreloadTime();

                    // 关闭进度对话框
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            progressDialog.dismiss();
                            Toast.makeText(HomeActivity.this, "表单配置加载完成", Toast.LENGTH_SHORT).show();
                        }
                    });
                } catch (Exception e) {
                    // 发生错误时，关闭进度对话框并显示错误提示
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            progressDialog.dismiss();
                            Toast.makeText(HomeActivity.this, "表单配置预加载失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                        }
                    });
                }
            }
        }).start();
    }

    /**
     * 加载并保存单个表单配置
     *
     * @param formId 表单ID或样点ID
     */
    private void loadAndSaveFormConfig(String formId) {
        try {
            // 检查数据库中是否已存在该配置
            FormConfigDatabaseHelper configDbHelper =
                    FormConfigDatabaseHelper.getInstance(this);

            // 使用FormMockDataProvider获取模拟表单配置，始终使用default_form
//            String jsonStr = io.dcloud.uniplugin.form.MockDataProvider.getMockFormConfig("default_form");
            RetrofitManager.getInstance(this)
                    .getFormService()
                    .getForm()
                    .enqueue(new Callback<ApiResponse<FormConfigResponse>>() {
                        @Override
                        public void onResponse(Call<ApiResponse<FormConfigResponse>> call, Response<ApiResponse<FormConfigResponse>> response) {
                            if (response.isSuccessful() && response.body() != null && response.body().getData() != null) {
                                FormConfigResponse data = response.body().getData();
                                // 创建FormConfig对象
                                FormConfig formConfig = new FormConfig();
                                formConfig.setFormId("default_form");
                                formConfig.setFormName("默认表单");
                                formConfig.setDescription("");
                                formConfig.setVersion("1.0");
                                formConfig.setOfflineSupported(true);
                                List<FormFieldConfig> fieldConfigList = new ArrayList<>();
                                // 设置表单字段
                                formConfig.setFields(fieldConfigList);
                                // 保存到数据库
                                long configId = configDbHelper.saveFormConfig(formConfig, data);
                            }
                        }

                        @Override
                        public void onFailure(Call<ApiResponse<FormConfigResponse>> call, Throwable t) {
                            Log.e(TAG, "加载并保存表单配置 " + formId + " 失败: " + t.getMessage(), t);
                        }
                    });
        } catch (Exception e) {
            Log.e(TAG, "加载并保存表单配置 " + formId + " 失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存最后预加载时间
     */
    private void saveLastPreloadTime() {
        SharedPreferences sp = getSharedPreferences("form_config_prefs", MODE_PRIVATE);
        SharedPreferences.Editor editor = sp.edit();
        editor.putLong("last_preloadTime", System.currentTimeMillis());
        editor.apply();
    }
}
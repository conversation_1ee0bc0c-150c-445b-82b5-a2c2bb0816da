package io.dcloud.uniplugin.model;

import com.google.gson.annotations.SerializedName;

/**
 * 采土袋详情模型
 */
public class SoilBagDetail {
    
    @SerializedName("id")
    private Long id;
    
    @SerializedName("pjdyId")
    private Long pjdyId;
    
    @SerializedName("pjdybh")
    private String pjdybh;
    
    @SerializedName("ctdbh")
    private String ctdbh; // 采土袋编号
    
    @SerializedName("ydlb")
    private Integer ydlb; // 用地类别
    
    @SerializedName("yplx")
    private Integer yplx; // 样品类型
    
    @SerializedName("ypzl")
    private Double ypzl; // 样品重量
    
    @SerializedName("zt")
    private Integer zt; // 状态
    
    @SerializedName("creator")
    private Long creator;
    
    @SerializedName("createTime")
    private String createTime;
    
    @SerializedName("updateTime")
    private String updateTime;
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getPjdyId() {
        return pjdyId;
    }
    
    public void setPjdyId(Long pjdyId) {
        this.pjdyId = pjdyId;
    }
    
    public String getpjdybh() {
        return pjdybh;
    }
    
    public void setpjdybh(String pjdybh) {
        this.pjdybh = pjdybh;
    }
    
    public String getCtdbh() {
        return ctdbh;
    }
    
    public void setCtdbh(String ctdbh) {
        this.ctdbh = ctdbh;
    }
    
    public Integer getYdlb() {
        return ydlb;
    }
    
    public void setYdlb(Integer ydlb) {
        this.ydlb = ydlb;
    }
    
    public Integer getYplx() {
        return yplx;
    }
    
    public void setYplx(Integer yplx) {
        this.yplx = yplx;
    }
    
    public Double getYpzl() {
        return ypzl;
    }
    
    public void setYpzl(Double ypzl) {
        this.ypzl = ypzl;
    }
    
    public Integer getZt() {
        return zt;
    }
    
    public void setZt(Integer zt) {
        this.zt = zt;
    }
    
    public Long getCreator() {
        return creator;
    }
    
    public void setCreator(Long creator) {
        this.creator = creator;
    }
    
    public String getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
    
    public String getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
} 
<?xml version="1.0" encoding="utf-8"?>
<TableLayout
    android:id="@+id/point_info_dialog"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
 >
    <TableRow
        android:layout_marginStart="35px"
        app:layout_constraintStart_toStartOf="parent">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/pointCode"
            android:textColor="@color/black"
            android:textSize="8pt"/>
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/pointCode"
            android:textSize="8pt"/>
    </TableRow>
    <TableRow
        android:layout_marginStart="35px"
        app:layout_constraintStart_toStartOf="parent">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="@string/pointSamplingType"
            android:textSize="8pt"/>
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/pointSamplingType"
            android:textSize="8pt"/>
    </TableRow>
    <TableRow
        android:layout_marginStart="35px"
        app:layout_constraintStart_toStartOf="parent">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="@string/pointSampleType"
            android:textSize="8pt"/>
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/pointSampleType"
            android:textSize="8pt"/>
    </TableRow>
    <TableRow
        android:layout_marginStart="35px"
        app:layout_constraintStart_toStartOf="parent">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="@string/pointLocation"
            android:textSize="8pt"/>
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/pointLocation"
            android:textSize="8pt"/>
    </TableRow>
</TableLayout>
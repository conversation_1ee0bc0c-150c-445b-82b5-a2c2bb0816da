# 全局异常处理系统使用说明

## 概述

本项目已集成全局异常处理系统，当应用中发生未捕获的异常时，系统会自动将错误信息记录到日志文件中，保存在外部存储的`/BCGDGISData/ERROR`文件夹下。

## ⚠️ 重要说明

**全局异常处理器的主要作用是捕获所有未被try-catch处理的异常！**

### 两种异常记录方式

1. **自动记录（主要功能）**：
   - ✅ **不需要try-catch**
   - ✅ 自动捕获空指针异常、数组越界、类型转换异常等
   - ✅ 应用崩溃时自动记录
   - ✅ 这是全局异常处理器的核心功能

2. **手动记录（辅助功能）**：
   - 在try-catch块中主动调用`GlobalExceptionHandler.logException()`
   - 用于记录异常但不希望应用崩溃的场景

## 系统架构

- **初始化位置**: 在主app的`NewPandoraEntry.onCreate()`方法中初始化
- **异常处理器**: `io.dcloud.uniplugin.GlobalExceptionHandler`
- **日志存储**: `/BCGDGISData/ERROR/` 目录

## 功能特性

1. **自动捕获未捕获异常**：应用崩溃时自动记录异常信息
2. **手动记录异常**：在try-catch块中主动记录异常
3. **按日期分类**：日志文件按日期命名，格式为`error_yyyyMMdd.log`
4. **详细异常信息**：包含时间戳、线程名称、完整堆栈信息等

## 文件结构

```
/BCGDGISData/ERROR/
├── error_20240101.log    # 2024年1月1日的异常日志
├── error_20240102.log    # 2024年1月2日的异常日志
└── ...
```

## 日志格式

### 自动捕获的异常
```
========================================
异常时间: 2024-01-01 15:30:45
线程名称: main
========================================
java.lang.ArithmeticException: divide by zero
	at io.dcloud.uniplugin.TestModule.testExceptionHandling(TestModule.java:215)
	at java.lang.reflect.Method.invoke(Native Method)
	...

```

### 手动记录的异常
```
========================================
异常时间: 2024-01-01 15:30:45
异常类型: 手动记录
额外信息: testExceptionHandling方法中发生异常，参数：{"test":"data"}
========================================
java.lang.ArithmeticException: divide by zero
	at io.dcloud.uniplugin.TestModule.testExceptionHandling(TestModule.java:215)
	...

```

## 使用方法

### 1. 系统初始化（已自动配置）

系统已在`NewPandoraEntry.onCreate()`中自动初始化，无需额外配置：

```java
// 在NewPandoraEntry.java中已自动执行
GlobalExceptionHandler.init(this);
```

### 2. 自动异常捕获

当应用发生未捕获异常（如崩溃）时，会自动记录到日志文件，无需任何代码。

### 3. 手动记录异常

在try-catch块中手动记录异常：

```java
try {
    // 可能发生异常的代码
    String result = someRiskyOperation();
} catch (Exception e) {
    // 手动记录异常到日志文件
    GlobalExceptionHandler.logException(this, e, "someRiskyOperation方法执行失败");
    
    // 继续其他错误处理逻辑
    Log.e(TAG, "操作失败", e);
}
```

### 4. 在UniApp插件中使用

```java
@UniJSMethod(uiThread = true)
public void yourMethod(JSONObject options, UniJSCallback callback) {
    try {
        // 业务逻辑代码
        String result = processData(options);
        
        // 成功回调
        JSONObject successResult = new JSONObject();
        successResult.put("success", true);
        successResult.put("data", result);
        callback.invoke(successResult);
        
    } catch (Exception e) {
        // 记录异常
        GlobalExceptionHandler.logException(mUniSDKInstance.getContext(), e, 
            "yourMethod执行失败，参数：" + options.toString());
        
        // 错误回调
        JSONObject errorResult = new JSONObject();
        errorResult.put("success", false);
        errorResult.put("message", "操作失败：" + e.getMessage());
        callback.invoke(errorResult);
    }
}
```

## API 说明

### GlobalExceptionHandler.init(Context)

初始化全局异常处理器（已在NewPandoraEntry中自动调用）

**参数：**
- `context`: 应用上下文

### GlobalExceptionHandler.logException(Context, Throwable, String)

手动记录异常到日志文件

**参数：**
- `context`: 上下文
- `throwable`: 异常对象
- `extraInfo`: 额外信息（可选，传入null或空字符串）

## 测试方法

如需测试异常处理功能，可以在代码中添加以下测试代码：

### 测试自动异常捕获
```java
// 在后台线程中触发未捕获异常（仅用于测试）
new Thread(new Runnable() {
    @Override
    public void run() {
        String nullStr = null;
        int length = nullStr.length(); // 空指针异常，会被全局异常处理器自动捕获
    }
}).start();
```

### 测试手动异常记录
```java
try {
    // 可能出错的代码
    int result = 1 / 0;
} catch (Exception e) {
    // 手动记录异常
    GlobalExceptionHandler.logException(this, e, "测试手动异常记录");
}
```

### 验证结果
异常会被记录到：`/BCGDGISData/ERROR/error_yyyyMMdd.log`

## 项目集成

### 文件结构
```
app/
├── src/main/java/com/android/UniPlugin/
│   └── NewPandoraEntry.java  # 主入口，已添加异常处理器初始化

uniplugin_module/
├── src/main/java/io/dcloud/uniplugin/
│   ├── GlobalExceptionHandler.java     # 核心异常处理类
│   ├── LoginActivity.java              # 登录界面，已添加异常处理器初始化
│   └── MainActivity.java               # 主界面，已添加备份初始化
```

### 依赖关系
- 主app依赖uniplugin_module
- NewPandoraEntry、LoginActivity、MainActivity都引用GlobalExceptionHandler
- 无额外外部依赖

## 注意事项

1. **存储权限**：需要确保应用有外部存储写入权限
2. **文件路径**：日志文件保存在`/BCGDGISData/ERROR/`目录下
3. **文件大小**：建议定期清理过期的日志文件
4. **线程安全**：异常处理是线程安全的，可在任意线程中调用
5. **Application冲突**：已解决与uni-app框架的Application类冲突

## 故障排除

### 1. 日志文件未生成

- 检查应用是否有外部存储写入权限
- 确认`/BCGDGISData/ERROR/`目录是否成功创建
- 查看Android系统日志中的相关错误信息

### 2. 权限问题

Android 6.0及以上版本需要运行时权限，确保已获取以下权限：
- `WRITE_EXTERNAL_STORAGE`
- `READ_EXTERNAL_STORAGE`

### 3. 初始化问题

检查LogCat中是否有以下日志：
```
I/LoginActivity: 全局异常处理器已在LoginActivity中初始化
```

如果没有，检查相关Activity是否正确导入了GlobalExceptionHandler。

### 4. 路径问题

在Android 10及以上版本，可能需要适配分区存储，当前实现使用了传统的外部存储访问方式。

## 更新日志

- **v1.2.0**: 移除测试代码，保留纯净的异常处理功能
- **v1.1.0**: 解决Application类冲突，改为在多个入口点初始化
- **v1.0.0**: 初始版本，支持全局异常捕获和手动异常记录

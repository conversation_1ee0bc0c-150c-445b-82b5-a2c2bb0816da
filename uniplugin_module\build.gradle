apply plugin: 'com.android.library'

android {
    compileSdkVersion 29
    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }
    defaultConfig {

        minSdkVersion 23
        targetSdkVersion 28
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

}


dependencies {
    //implementation 'androidx.appcompat:appcompat:1.4.1'
    implementation 'androidx.appcompat:appcompat:1.2.0'
    //implementation 'com.google.android.material:material:1.5.0'
    implementation'com.google.android.material:material:1.1.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.3'
    //implementation 'androidx.navigation:navigation-fragment:2.4.1'
    //implementation 'androidx.navigation:navigation-ui:2.4.1'
    implementation'androidx.navigation:navigation-fragment:2.2.2'
    implementation'androidx.navigation:navigation-ui:2.2.2'

    implementation 'com.getbase:floatingactionbutton:1.10.1'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.2.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.2.0'
    implementation 'androidx.gridlayout:gridlayout:1.0.0'
    implementation 'com.google.android.gms:play-services-maps:18.0.2'
    implementation 'com.inthecheesefactory.thecheeselibrary:stated-fragment:0.9.2'
    implementation 'androidx.fragment:fragment:1.4.1'
    implementation project(path: ':uniplugin_camera')
    testImplementation 'junit:junit:4.13.2'
    //androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    //androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'

    implementation 'androidx.leanback:leanback:1.0.0'
    implementation 'com.github.bumptech.glide:glide:4.11.0'
    //implementation 'com.google.android.material:material:1.4.+'
    //implementation 'com.google.android.material:material:1.5.0'

    //implementation 'androidx.constraintlayout:constraintlayout:2.1.3'
    compileOnly fileTree(dir: 'libs', include: ['*.jar','*.aar'])

    compileOnly fileTree(dir: '../app/libs', include: ['uniapp-v8-release.aar'])

    compileOnly 'androidx.recyclerview:recyclerview:1.0.0'
    compileOnly 'androidx.legacy:legacy-support-v4:1.0.0'
    compileOnly 'androidx.appcompat:appcompat:1.0.0'
    implementation 'com.alibaba:fastjson:1.1.46.android'
    implementation 'com.facebook.fresco:fresco:1.13.0'

    implementation 'com.esri.arcgisruntime:arcgis-android:100.15.0'
    implementation 'com.esri.arcgisruntime:arcgis-android-toolkit:100.13.0'
    implementation 'com.vividsolutions:jts-core:1.14.0'
    api 'org.greenrobot:eventbus:3.0.0'

    // HTTP请求相关依赖
    implementation 'com.squareup.okhttp3:okhttp:4.9.3'

    // 添加PhotoView依赖，用于支持图片缩放
    implementation 'com.github.chrisbanes:PhotoView:2.3.0'

    /*implementation 'com.android.support:appcompat-v7:28.0.0'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'*/

    // Retrofit 依赖
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.9.3'

    // Gson 依赖
    implementation 'com.google.code.gson:gson:2.8.9'

    implementation 'androidx.recyclerview:recyclerview:1.2.1'

    // Google Play Services Location
    implementation 'com.google.android.gms:play-services-location:21.0.1'

    // 打印机相关依赖
    implementation files('libs/LPAPI-2024-10-21-R.jar')

    // ZXing库依赖 - 用于二维码扫描
    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'
    implementation 'com.google.zxing:core:3.5.1'

    compileOnly 'org.projectlombok:lombok:1.18.24'
    annotationProcessor 'org.projectlombok:lombok:1.18.24'

    // 网络请求库
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:okhttp:4.10.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.10.0'
    implementation 'com.google.code.gson:gson:2.9.0'
}

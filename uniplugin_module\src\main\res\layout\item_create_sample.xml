<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 样品编码和删除按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <TextView
                android:id="@+id/textViewSampleCode"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="样品编码：YP001"
                android:textColor="#333333"
                android:textSize="16sp"
                android:textStyle="bold" />

            <ImageButton
                android:id="@+id/buttonDelete"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_delete"
                android:tint="#FF5722"
                android:contentDescription="删除样品" />

        </LinearLayout>

        <!-- 样品基本信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 第一行：样品名称、类型 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp">

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginRight="8dp"
                    android:hint="样品名称 *"
                    app:boxStrokeColor="@color/colorPrimary"
                    app:hintTextColor="@color/colorPrimary">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/editTextSampleName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginLeft="8dp"
                    android:hint="样品类型"
                    app:boxStrokeColor="@color/colorPrimary"
                    app:hintTextColor="@color/colorPrimary">

                    <Spinner
                        android:id="@+id/spinnerSampleType"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:minHeight="48dp" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

            <!-- 第二行：重量、颜色 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginRight="8dp"
                    android:orientation="horizontal">

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginRight="4dp"
                        android:hint="重量"
                        app:boxStrokeColor="@color/colorPrimary"
                        app:hintTextColor="@color/colorPrimary">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/editTextWeight"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="numberDecimal"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <Spinner
                        android:id="@+id/spinnerWeightUnit"
                        android:layout_width="60dp"
                        android:layout_height="56dp"
                        android:layout_marginLeft="4dp" />

                </LinearLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginLeft="8dp"
                    android:hint="颜色"
                    app:boxStrokeColor="@color/colorPrimary"
                    app:hintTextColor="@color/colorPrimary">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/editTextColor"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

            <!-- 第三行：外观状态、质地 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp">

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginRight="8dp"
                    android:hint="外观状态"
                    app:boxStrokeColor="@color/colorPrimary"
                    app:hintTextColor="@color/colorPrimary">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/editTextExteriorState"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginLeft="8dp"
                    android:hint="质地"
                    app:boxStrokeColor="@color/colorPrimary"
                    app:hintTextColor="@color/colorPrimary">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/editTextTexture"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

            <!-- 第四行：保存条件、包装材料 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp">

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginRight="8dp"
                    android:hint="保存条件"
                    app:boxStrokeColor="@color/colorPrimary"
                    app:hintTextColor="@color/colorPrimary">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/editTextPreservationConditions"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginLeft="8dp"
                    android:hint="包装材料"
                    app:boxStrokeColor="@color/colorPrimary"
                    app:hintTextColor="@color/colorPrimary">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/editTextPackagingMaterial"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

            <!-- 备注 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="备注"
                app:boxStrokeColor="@color/colorPrimary"
                app:hintTextColor="@color/colorPrimary">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextRemarks"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textMultiLine"
                    android:maxLines="2"
                    android:minLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView> 
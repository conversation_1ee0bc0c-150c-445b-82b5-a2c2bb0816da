package io.dcloud.uniplugin.service;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.dcloud.uniplugin.model.FormData;

/**
 * 离线表单数据库帮助类
 */
public class OfflineFormDatabaseHelper extends SQLiteOpenHelper {
    private static final String TAG = "OfflineFormDBHelper";
    
    // 数据库名和版本
    private static final String DATABASE_NAME = "offline_forms.db";
    private static final int DATABASE_VERSION = 2;  // 升级数据库版本
    
    // 表名
    private static final String TABLE_FORMS = "offline_forms";
    
    // 字段名
    private static final String COLUMN_ID = "id";
    private static final String COLUMN_FORM_ID = "form_id";
    private static final String COLUMN_SAMPLING_POINT_ID = "sampling_point_id";
    private static final String COLUMN_FORM_VERSION = "form_version";
    private static final String COLUMN_SUBMISSION_DATE = "submission_date";
    private static final String COLUMN_FORM_DATA = "form_data";
    private static final String COLUMN_FILES_DATA = "files_data";
    private static final String COLUMN_SYNC_STATUS = "sync_status";
    private static final String COLUMN_IS_COMPLETE = "is_complete";  // 新增字段：表单是否完整
    private static final String COLUMN_CREATE_TIME = "create_time";
    
    // 创建表SQL
    private static final String SQL_CREATE_TABLE = 
            "CREATE TABLE " + TABLE_FORMS + " (" +
            COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
            COLUMN_FORM_ID + " TEXT, " +
            COLUMN_SAMPLING_POINT_ID + " TEXT, " +
            COLUMN_FORM_VERSION + " TEXT, " +
            COLUMN_SUBMISSION_DATE + " LONG, " +
            COLUMN_FORM_DATA + " TEXT, " +
            COLUMN_FILES_DATA + " TEXT, " +
            COLUMN_SYNC_STATUS + " INTEGER DEFAULT 0, " +
            COLUMN_IS_COMPLETE + " INTEGER DEFAULT 0, " +  // 新增字段
            COLUMN_CREATE_TIME + " LONG DEFAULT (strftime('%s','now') * 1000)" +
            ")";
    
    private static OfflineFormDatabaseHelper instance;
    
    /**
     * 获取单例实例
     */
    public static synchronized OfflineFormDatabaseHelper getInstance(Context context) {
        if (instance == null) {
            instance = new OfflineFormDatabaseHelper(context.getApplicationContext());
        }
        return instance;
    }
    
    /**
     * 构造方法
     */
    private OfflineFormDatabaseHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }
    
    @Override
    public void onCreate(SQLiteDatabase db) {
        db.execSQL(SQL_CREATE_TABLE);
    }
    
    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        if (oldVersion < 2) {
            // 添加is_complete字段
            db.execSQL("ALTER TABLE " + TABLE_FORMS + " ADD COLUMN " + COLUMN_IS_COMPLETE + " INTEGER DEFAULT 0");
        }
    }
    
    /**
     * 保存表单数据到离线数据库
     * @param formData 表单数据
     * @param isComplete 表单是否完整填写
     */
    public long saveFormData(FormData formData, boolean isComplete) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        // 序列化表单字段数据和文件数据
        Gson gson = new Gson();
        String formDataJson = gson.toJson(formData.getFormFields());
        String filesDataJson = gson.toJson(formData.getFiles());
        
        // 设置ContentValues
        values.put(COLUMN_FORM_ID, formData.getFormId());
        values.put(COLUMN_SAMPLING_POINT_ID, formData.getSamplingPointId());
        values.put(COLUMN_FORM_VERSION, formData.getFormVersion());
        values.put(COLUMN_SUBMISSION_DATE, formData.getSubmissionDate());
        values.put(COLUMN_FORM_DATA, formDataJson);
        values.put(COLUMN_FILES_DATA, filesDataJson);
        values.put(COLUMN_IS_COMPLETE, isComplete ? 1 : 0);
        
        // 插入数据
        long id = db.insert(TABLE_FORMS, null, values);
        Log.d(TAG, "Saved form data offline with ID: " + id + ", isComplete: " + isComplete);
        
        return id;
    }
    
    /**
     * 保存表单数据到离线数据库 (兼容旧方法)
     */
    public long saveFormData(FormData formData) {
        return saveFormData(formData, true);  // 默认为完整状态，保持向后兼容
    }
    
    /**
     * 获取所有未同步的离线表单数据
     */
    public List<FormData> getUnsyncedForms() {
        List<FormData> forms = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();
        
        String selection = COLUMN_SYNC_STATUS + " = ?";
        String[] selectionArgs = {"0"};
        
        Cursor cursor = db.query(
                TABLE_FORMS,
                null,
                selection,
                selectionArgs,
                null,
                null,
                COLUMN_SUBMISSION_DATE + " DESC"
        );
        
        if (cursor != null) {
            try {
                if (cursor.moveToFirst()) {
                    do {
                        FormData formData = cursorToFormData(cursor);
                        forms.add(formData);
                    } while (cursor.moveToNext());
                }
            } finally {
                cursor.close();
            }
        }
        
        return forms;
    }
    
    /**
     * 获取指定采样点的所有离线表单数据
     */
    public List<FormData> getFormsBySamplingPointId(String samplingPointId) {
        List<FormData> forms = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();
        
        String selection = COLUMN_SAMPLING_POINT_ID + " = ?";
        String[] selectionArgs = {samplingPointId};
        
        Cursor cursor = db.query(
                TABLE_FORMS,
                null,
                selection,
                selectionArgs,
                null,
                null,
                COLUMN_SUBMISSION_DATE + " DESC"
        );
        
        if (cursor != null) {
            try {
                if (cursor.moveToFirst()) {
                    do {
                        FormData formData = cursorToFormData(cursor);
                        forms.add(formData);
                    } while (cursor.moveToNext());
                }
            } finally {
                cursor.close();
            }
        }
        
        return forms;
    }
    
    /**
     * 获取指定采样点的最新表单数据
     * @param samplingPointId 采样点ID
     * @return 最新的表单数据，如果没有找到则返回null
     */
    public FormData getLatestFormBySamplingPointId(String samplingPointId) {
        SQLiteDatabase db = this.getReadableDatabase();
        
        String selection = COLUMN_SAMPLING_POINT_ID + " = ?";
        String[] selectionArgs = {samplingPointId};
        
        Cursor cursor = db.query(
                TABLE_FORMS,
                null,
                selection,
                selectionArgs,
                null,
                null,
                COLUMN_SUBMISSION_DATE + " DESC",  // 按提交时间倒序，获取最新的
                "1"  // 只获取第一条
        );
        
        FormData formData = null;
        if (cursor != null) {
            try {
                if (cursor.moveToFirst()) {
                    formData = cursorToFormData(cursor);
                }
            } finally {
                cursor.close();
            }
        }
        
        return formData;
    }
    
    /**
     * 更新表单同步状态
     */
    public int updateFormSyncStatus(long id, boolean synced) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        values.put(COLUMN_SYNC_STATUS, synced ? 1 : 0);
        
        String selection = COLUMN_ID + " = ?";
        String[] selectionArgs = {String.valueOf(id)};
        
        return db.update(TABLE_FORMS, values, selection, selectionArgs);
    }
    
    /**
     * 删除已同步的表单数据
     */
    public int deleteSyncedForms() {
        SQLiteDatabase db = this.getWritableDatabase();
        String selection = COLUMN_SYNC_STATUS + " = ?";
        String[] selectionArgs = {"1"};
        
        return db.delete(TABLE_FORMS, selection, selectionArgs);
    }
    
    /**
     * 检查指定表单是否完整
     * @param formId 表单ID
     * @param samplingPointId 采样点ID
     * @return 表单是否完整
     */
    public boolean isFormComplete(String formId, String samplingPointId) {
        SQLiteDatabase db = this.getReadableDatabase();
        
        String selection = COLUMN_FORM_ID + " = ? AND " + COLUMN_SAMPLING_POINT_ID + " = ?";
        String[] selectionArgs = {formId, samplingPointId};
        
        Cursor cursor = db.query(
                TABLE_FORMS,
                new String[]{COLUMN_IS_COMPLETE},
                selection,
                selectionArgs,
                null,
                null,
                COLUMN_SUBMISSION_DATE + " DESC",
                "1"  // 只获取最新的一条记录
        );
        
        boolean isComplete = false;
        if (cursor != null) {
            try {
                if (cursor.moveToFirst()) {
                    int isCompleteValue = cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_IS_COMPLETE));
                    isComplete = (isCompleteValue == 1);
                }
            } finally {
                cursor.close();
            }
        }
        
        return isComplete;
    }
    
    /**
     * 根据采样点ID检查最新的表单是否完整
     * @param samplingPointId 采样点ID
     * @return 表单是否完整
     */
    public boolean isFormComplete(String samplingPointId) {
        SQLiteDatabase db = this.getReadableDatabase();
        
        String selection = COLUMN_SAMPLING_POINT_ID + " = ?";
        String[] selectionArgs = {samplingPointId};
        
        Cursor cursor = db.query(
                TABLE_FORMS,
                new String[]{COLUMN_IS_COMPLETE},
                selection,
                selectionArgs,
                null,
                null,
                COLUMN_SUBMISSION_DATE + " DESC",
                "1"  // 只获取最新的一条记录
        );
        
        boolean isComplete = false;
        if (cursor != null) {
            try {
                if (cursor.moveToFirst()) {
                    int isCompleteValue = cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_IS_COMPLETE));
                    isComplete = (isCompleteValue == 1);
                }
            } finally {
                cursor.close();
            }
        }
        
        return isComplete;
    }
    
    /**
     * 从Cursor提取FormData对象
     */
    private FormData cursorToFormData(Cursor cursor) {
        FormData formData = new FormData();
        
        long id = cursor.getLong(cursor.getColumnIndexOrThrow(COLUMN_ID));
        String formId = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_FORM_ID));
        String samplingPointId = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_SAMPLING_POINT_ID));
        String formVersion = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_FORM_VERSION));
        long submissionDate = cursor.getLong(cursor.getColumnIndexOrThrow(COLUMN_SUBMISSION_DATE));
        String formDataJson = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_FORM_DATA));
        String filesDataJson = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_FILES_DATA));
        int isComplete = cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_IS_COMPLETE));
        
        formData.setId(id);
        formData.setFormId(formId);
        formData.setSamplingPointId(samplingPointId);
        formData.setFormVersion(formVersion);
        formData.setSubmissionDate(submissionDate);
        formData.setComplete(isComplete == 1);
        
        // 反序列化表单字段数据
        Gson gson = new Gson();
        Type mapType = new TypeToken<Map<String, String>>(){}.getType();
        Map<String, String> formFields = gson.fromJson(formDataJson, mapType);
        for (Map.Entry<String, String> entry : formFields.entrySet()) {
            formData.addFormField(entry.getKey(), entry.getValue());
        }
        
        // 反序列化文件数据
        Type filesMapType = new TypeToken<Map<String, List<String>>>(){}.getType();
        Map<String, List<String>> files = gson.fromJson(filesDataJson, filesMapType);
        for (Map.Entry<String, List<String>> entry : files.entrySet()) {
            for (String filePath : entry.getValue()) {
                formData.addFile(entry.getKey(), filePath);
            }
        }
        
        return formData;
    }
} 
package io.dcloud.uniplugin;

import static androidx.constraintlayout.helper.widget.MotionEffect.TAG;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.ContentValues;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.database.sqlite.SQLiteDatabase;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.text.method.ScrollingMovementMethod;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.RotateAnimation;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.esri.arcgisruntime.ArcGISRuntimeEnvironment;
import com.esri.arcgisruntime.concurrent.ListenableFuture;
import com.esri.arcgisruntime.data.Feature;
import com.esri.arcgisruntime.data.FeatureQueryResult;
import com.esri.arcgisruntime.data.FeatureTable;
import com.esri.arcgisruntime.data.QueryParameters;
import com.esri.arcgisruntime.geometry.AreaUnit;
import com.esri.arcgisruntime.geometry.AreaUnitId;
import com.esri.arcgisruntime.geometry.Envelope;
import com.esri.arcgisruntime.geometry.GeodeticCurveType;
import com.esri.arcgisruntime.geometry.Geometry;
import com.esri.arcgisruntime.geometry.GeometryEngine;
import com.esri.arcgisruntime.geometry.GeometryType;
import com.esri.arcgisruntime.geometry.LinearUnit;
import com.esri.arcgisruntime.geometry.LinearUnitId;
import com.esri.arcgisruntime.geometry.Point;
import com.esri.arcgisruntime.geometry.Polygon;
import com.esri.arcgisruntime.geometry.Polyline;
import com.esri.arcgisruntime.geometry.SpatialReference;
import com.esri.arcgisruntime.layers.ArcGISTiledLayer;
import com.esri.arcgisruntime.layers.FeatureLayer;
import com.esri.arcgisruntime.layers.GroupLayer;
import com.esri.arcgisruntime.layers.Layer;
import com.esri.arcgisruntime.layers.LegendInfo;
import com.esri.arcgisruntime.loadable.LoadStatus;
import com.esri.arcgisruntime.loadable.LoadStatusChangedEvent;
import com.esri.arcgisruntime.loadable.LoadStatusChangedListener;
import com.esri.arcgisruntime.mapping.ArcGISMap;
import com.esri.arcgisruntime.mapping.Basemap;
import com.esri.arcgisruntime.mapping.LayerList;
import com.esri.arcgisruntime.mapping.MobileMapPackage;
import com.esri.arcgisruntime.mapping.Viewpoint;
import com.esri.arcgisruntime.mapping.view.Callout;
import com.esri.arcgisruntime.mapping.view.DefaultMapViewOnTouchListener;
import com.esri.arcgisruntime.mapping.view.Graphic;
import com.esri.arcgisruntime.mapping.view.GraphicsOverlay;
import com.esri.arcgisruntime.mapping.view.IdentifyGraphicsOverlayResult;
import com.esri.arcgisruntime.mapping.view.LocationDisplay;
import com.esri.arcgisruntime.mapping.view.MapRotationChangedEvent;
import com.esri.arcgisruntime.mapping.view.MapRotationChangedListener;
import com.esri.arcgisruntime.mapping.view.MapView;
import com.esri.arcgisruntime.mapping.view.SketchCreationMode;
import com.esri.arcgisruntime.mapping.view.SketchEditor;
import com.esri.arcgisruntime.mapping.view.SketchGeometryChangedEvent;
import com.esri.arcgisruntime.mapping.view.SketchGeometryChangedListener;
import com.esri.arcgisruntime.symbology.SimpleFillSymbol;
import com.esri.arcgisruntime.symbology.SimpleLineSymbol;
import com.esri.arcgisruntime.symbology.SimpleMarkerSymbol;
import com.esri.arcgisruntime.symbology.Symbol;
import com.esri.arcgisruntime.symbology.TextSymbol;
import com.esri.arcgisruntime.toolkit.scalebar.Scalebar;
import com.esri.arcgisruntime.util.ListenableList;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.snackbar.Snackbar;
import com.vividsolutions.jts.math.Vector2D;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.concurrent.ExecutionException;

import uni.dcloud.io.uniplugin_module.R;

//import org.gdal.gdal.gdal;
//import org.gdal.ogr.DataSource;
//import org.gdal.ogr.Driver;
//import org.gdal.ogr.FeatureDefn;
//import org.gdal.ogr.FieldDefn;
//import org.gdal.ogr.Geometry;
//import org.gdal.ogr.Layer;
//import org.gdal.ogr.ogr;
//import org.gdal.osr.SpatialReference;

public class MainActivity extends AppCompatActivity implements OnLayerCheckedChangedListener {


    private MapView mapView;
    private BottomSheetBehavior mBottomSheetBehavior;
    private RecyclerView mLayersRecyclerView;
    private LayersAdapter mLayersAdapter;
    private TestModule testModule;


    private SimpleMarkerSymbol mPointSymbol;
    private SimpleLineSymbol mLineSymbol;
    private SimpleFillSymbol mFillSymbol;
    private SketchEditor mSketchEditor;
    private GraphicsOverlay mGraphicsOverlay;
    private GraphicsOverlay dzwlGraphicsOverlay;

    private ImageButton mPolylineButton;
    private ImageButton mPolygonButton;

    private LocationDisplay mLocationDisplay;

    //identify弹出的窗口
    private Callout mCallout = null;

    private Callout callout = null;

    //layerName的集合
    public static List LayerName = new ArrayList<>();

    //identify下拉框
    public Spinner spner;


    //选择属性查询的，保存的图层的序号,初始化为0，是第一个图层
    int layer_index = 0;

    //导航按钮
    private ImageButton navigationButton;

    //点选的Point的位置
    private Point yd_point;

    //选中的point的Feature
    Feature feature;

    //地图切换按钮
    private ImageButton layerChange;

    //底部按钮的调查未调查的状态
    private LinearLayout dbtlLinearLayout;

    //创建影像单选表单
    final String[] map_item = new String[]{"影像底图", "DEM底图"};
    int index = 0;

    //identify当前图层名
    String current_layer_name;
    int current_layer_index = 0;

    //单个跳转样点的信息
    private GraphicsOverlay pointGraphicsOverlay;
    private GraphicsOverlay textGraphicsOverlay;
    String code;
    String location;
    String sampleType;
    String samplingType;
    Double lon;
    Double lat;

    //事实查看全部样点的信息
    String pointList;
    JSONArray pointList_final;
    JSONObject options1;
    Double pointlist_lan;
    Double pointlist_lon;
    String pointlist_sampleType;
    String pointlist_samplingType;
    String pointlist_code;
    String pointlist_location;
    String pointlist_tdlylxmc;
    //zt用来区分已调查未调查
    String zt;
    //cygc用来区分已调查未调查
    String cygc;
    //全部的点里面的突出点
    Object current_point;

    public static int cnt = 0;

    private final LinearUnit mUnitOfMeasurement = new LinearUnit(LinearUnitId.METERS);
    private final AreaUnit mAreaOfMeasurement = new AreaUnit(AreaUnitId.SQUARE_METERS);


    //创建Gdal点集
    List<String> pointList_gdal = new ArrayList<String>();

    //标识码
    String bsm;

    //identify按钮，为了防止点击详情时与identify的冲突
    private ImageButton identifyButton;

    //控制identify的参数,0代表未开启，1代表已开启
    int identify_index = 0;

    //创建identify开关表单
    final String[] identify_item = new String[]{"关闭功能", "开启功能"};

    //图层组的全局变量
    GroupLayer projectAreaGroupLayer = new GroupLayer();

    //定位中心,110.823538,21.971785
    double location_longitude = 110.823538;
    double location_latitude = 21.971785;

    float lastBearing = 0.0F;

    //图例按钮
    private ImageButton LegendButton;

    //土壤图图层
    private FeatureLayer tr_featureLayer;

    //土地利用图层
    private FeatureLayer td_featureLayer;

    //tpk路径
    String imageTpkPath;
    String demTpkPath;

    //
    ArcGISMap mainArcGISMap;

    //离线状态
    String offModel = "3";
    //控制第一次点击显示电子围栏，第二点击跳转填报。
    int clickstate = 0;
    String clickcode = "";


    private static String geometrystr = "";

    private OnResultCallback onResultCallback;

    static final int REQUEST_IMAGE_CAPTURE = 11111;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 初始化全局异常处理器（备份初始化，防止某些入口没有经过LoginActivity）
        try {
            GlobalExceptionHandler.init(this);
            Log.i("MainActivity", "全局异常处理器已在MainActivity中初始化（备份）");
        } catch (Exception e) {
            Log.e("MainActivity", "MainActivity中初始化全局异常处理器失败", e);
        }
        
        setContentView(R.layout.activity_main);
        ArcGISRuntimeEnvironment.setLicense("runtimelite,1000,rud7416273699,none,4N5X0H4AH7AH6XCFK036");
//        ArcGISRuntimeEnvironment.setApiKey("MW_xnhBqYtt2rsHYzsJuFu3DMQfL6jf2zQPTRmhS8UHK16s6x83rEnmEQPUueyTiXArqLjsyNdqZWlhwJQb8QKhrcmLYVzPV_mp6a1KFKYz7eY1OaWsPNs01ysopWAy1U3D0jgCGmwue1_xYFiK3iw..");
        //传递参数

//        ViewPager viewPager = findViewById(R.id.viewPager);
//       MyAdapter adapter = new MyAdapter(getSupportFragmentManager());
//        adapter.addFragment(new MyFragment());
//        viewPager.setAdapter(adapter);
//        getSupportFragmentManager()    //
//                .beginTransaction()
//                .add(R.id.fragment_container,new MyFragment())   // 此处的R.id.fragment_container是要盛放fragment的父容器
//                .commit();


        Intent mIntent = getIntent();

        //toUniAppByParams();
        /*String config = MainActivity.this.getResources().getConfiguration().toString();
        boolean isInMagicWindow = config.contains("hw-magic-windows");
        DisplayMetrics displayMetrics=MainActivity.this.getResources().getDisplayMetrics();
        Toast.makeText(MainActivity.this,displayMetrics.widthPixels+"", Toast.LENGTH_LONG).show();
        ImageButton compassButton = (ImageButton) findViewById(R.id.compassButton);
        TableRow.LayoutParams buttonParams = (TableRow.LayoutParams)compassButton.getLayoutParams();
        //displayMetrics.widthPixels-100
        compassButton.setLeft(1500);*/
        //context为Activity的context

        bsm = mIntent.getStringExtra("lon");
        offModel = mIntent.getStringExtra("offModel");
        //Log.d("验证",offModel);
        System.out.println("bsm" + bsm);
        if (Objects.equals(bsm, "0")) {
            lon = Double.parseDouble(bsm);
            pointList = mIntent.getStringExtra("pointList");
            pointList_final = JSONArray.parseArray(pointList);
            pointList = "";
            //System.out.println(pointList_final);

//            String point = longitude + "," + latitude;
//            //使用点集传参，先用点导入，用下标读取，后split方法切割为longitude，latitude,经测试，可以画线可以使用。
//            pointList_gdal.add(point);

        } else if (Objects.equals(bsm, "1")) {
            lon = Double.parseDouble(bsm);
            pointList = mIntent.getStringExtra("pointList");
            pointList_final = JSONArray.parseArray(pointList);
            current_point = mIntent.getStringExtra("currentPoint");
            System.out.println("1111111111" + pointList_final);
//            String point = longitude + "," + latitude;
//            //使用点集传参，先用点导入，用下标读取，后split方法切割为longitude，latitude,经测试，可以画线可以使用。
//            pointList_gdal.add(point);

        } else {
            //获取Intent，通过key获取对应的
            String lat2 = mIntent.getStringExtra("lat");
            lon = Double.parseDouble(bsm);
            lat = Double.parseDouble(lat2);
            code = mIntent.getStringExtra("code");
            location = mIntent.getStringExtra("location");
//            sampleType = mIntent.getStringExtra("sampleType");
            sampleType = "sampleType";
            samplingType = mIntent.getStringExtra("samplingType");
        }
        try {
            // define symbols
            mPointSymbol = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.SQUARE, 0xFFFF0000, 20);
            mLineSymbol = new SimpleLineSymbol(SimpleLineSymbol.Style.SOLID, 0xFFFF8800, 4);
            mFillSymbol = new SimpleFillSymbol(SimpleFillSymbol.Style.CROSS, 0x40FFA9A9, mLineSymbol);

            mapView = (MapView) findViewById(R.id.mapView);
            //mapView=(MapView)getSupportFragmentManager().findFragmentById(R.id.fragment_container).getView().findViewById(R.id.mapView);
            // 添加 MapView 的 null 检查以提高健壮性
            if (mapView == null) {
                Log.e(TAG, "MapView is null, cannot proceed with SketchEditor setup.");
                // 可以考虑抛出异常或结束 Activity
                throw new RuntimeException("MapView not found in layout");
            }
            mLayersRecyclerView = findViewById(R.id.layersRecyclerView);

            View bottomSheet = findViewById(R.id.bottomSheet);

            if (bottomSheet == null) {
                Log.e("LayersButton", "bottomSheet view not found!"); // 检查视图是否存在
                return;
            }
            
            // 确保底部表单有正确的高度
            int screenHeight = getResources().getDisplayMetrics().heightPixels;
            int peekHeight = screenHeight / 3; // 设置为屏幕高度的1/3
            
            mBottomSheetBehavior = BottomSheetBehavior.from(bottomSheet);
            mBottomSheetBehavior.setPeekHeight(peekHeight);
            mBottomSheetBehavior.setHideable(true); // 允许完全隐藏
            
            Log.i("BottomSheet", "初始化底部表单，屏幕高度: " + screenHeight + "，表单高度: " + peekHeight);
            
            // 设置初始状态
            mBottomSheetBehavior.setState(BottomSheetBehavior.STATE_HIDDEN);
            
            // 添加初始状态日志
            int currentState = mBottomSheetBehavior.getState();
            Log.i("BottomSheet", "底部表单初始状态: " + currentState);
            
            // 确保底部表单在最上层
            bottomSheet.bringToFront();

            //实例化导航按钮
            navigationButton = (ImageButton) findViewById(R.id.navigationButton);


            mapView.addMapRotationChangedListener(new MapRotationChangedListener() {
                @Override
                public void mapRotationChanged(MapRotationChangedEvent mapRotationChangedEvent) {
                    float index = (float) mapView.getMapRotation();
                    index = (float) 360 - index;
                    ImageButton compassButton = (ImageButton) findViewById(R.id.compassButton);
                    RotateAnimation rotateAnimation = new RotateAnimation(lastBearing, index, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
                    rotateAnimation.setFillAfter(true);
                    compassButton.startAnimation(rotateAnimation);
                    lastBearing = index;
                }
            });
            Scalebar mScalebar = (Scalebar) findViewById(R.id.scalebar);
            mScalebar.setLineColor(Color.BLACK);
            mScalebar.bindTo(mapView);
            //添加地图
//            InitMap initMap=new InitMap(MainActivity.this);
//            imageTpkPath = getSDPath(this) + "/TRSPGISData/image.tpk";
//            mainArcGISMap=initMap.loadTPK(imageTpkPath,mainArcGISMap,mapView);


//            //加载tpk文件
            String[] reqPermission = new String[]{Manifest.permission.READ_EXTERNAL_STORAGE};
            int requestCode = 2;
            // 在API23版本以上中，权限需要在运行时进行请求
            if (ContextCompat.checkSelfPermission(MainActivity.this,
                    reqPermission[0]) == PackageManager.PERMISSION_GRANTED) {
//                //String fileTPK = getExternalFilesDir(null) + "/gaozhou.tpk";
                imageTpkPath = getSDPath(this) + "/TRSPGISData/image.tpk";
////                imageTpkPath = getExternalFilesDir(null) + "/image.tpk";
                demTpkPath = getSDPath(this) + "/TRSPGISData/dem.tpk";
//                demTpkPath = getExternalFilesDir(null) + "/dem.tpk";

                Log.d("imageTpkPath", imageTpkPath);
                Log.d("demTpkPath", demTpkPath);
                String fileTPK = imageTpkPath;
                //Toast.makeText(this,fileTPK, Toast.LENGTH_SHORT).show();
                ArcGISTiledLayer arcGISTiledLayer = new ArcGISTiledLayer(fileTPK);
                Basemap basemap = new Basemap(arcGISTiledLayer);
                //TODO:一开始没有设置Map，然后定位无效，现在添加了坐标系，然后定位就可以了
                if (mainArcGISMap == null) {
                    mainArcGISMap = new ArcGISMap(basemap);//确定为这个类但不添加
                }
                mainArcGISMap.setBasemap(basemap);
                /*mainArcGISMap.addDoneLoadingListener(new Runnable() {
                    @Override
                    public void run() {
                        if (mainArcGISMap.getLoadStatus() == LoadStatus.LOADED) {
                            // Remove the done loading listener when the map has finished loading
                            mainArcGISMap.removeDoneLoadingListener(this);

                            // TODO: Add your code here to do something when the map is loaded
                        }
                    }
                });*/

                mapView.setMap(mainArcGISMap);

            } else {
                // 请求权限
                ActivityCompat.requestPermissions(MainActivity.this, reqPermission, requestCode);
            }
            //requestWritePermission();

            // create an initial extent envelope
//            Envelope initialExtent = new Envelope(12198107.235, 2279034.9, 13054039.508, 2937630.595,
//                    SpatialReference.create(102100));
//
//
//            // create a viewpoint from envelope
//            Viewpoint viewpoint = new Viewpoint(initialExtent);
//
//            // set initial map extent
//            mapView.setViewpoint(viewpoint);


            //加载MMPK数据,这里的路径需要更换
            try {
                String mmpkPath = "";
                // String mmpkPath = getExternalFilesDir(null) + "/polayers2.mmpk";
                // 在API23版本以上中，权限需要在运行时进行请求
                if (ContextCompat.checkSelfPermission(MainActivity.this,
                        reqPermission[0]) == PackageManager.PERMISSION_GRANTED) {
                    mmpkPath = getSDPath(this) + "/TRSPGISData/layers.mmpk";
                    // mmpkPath = getExternalFilesDir(null) + "/layers.mmpk";
                    Log.d(mmpkPath, "mmpkPath");
                } else {
                    // 请求权限
                    ActivityCompat.requestPermissions(MainActivity.this, reqPermission, requestCode);
                }

                //Toast.makeText(this,mmpkPath, Toast.LENGTH_SHORT).show();
//                //测试数据
//          String mmpkPath = getExternalFilesDir(null) + "/yangdian.mmpk";
//                Toast.makeText(MainActivity.this, mmpkPath, Toast.LENGTH_LONG).show();
//                System.out.println(mmpkPath);
//                String fileVTPK =getExternalFilesDir(null)+"/layers.vtpk";
//                ArcGISVectorTiledLayer tiledLayer = new ArcGISVectorTiledLayer(fileVTPK); //vtpk
//                Basemap basemap = new Basemap(tiledLayer);
//                ArcGISMap map = new ArcGISMap(basemap);
//                mapView.setMap(map);
//                tiledLayer.addDoneLoadingListener(new Runnable() {
//                    @Override
//                    public void run() {
//                        LoadStatus mainLoadStasus = tiledLayer.getLoadStatus();
//                        if (mainLoadStasus == LoadStatus.LOADED) {
//                             ;
//                            for (int i = 0; i < operationalLayers.size(); i++) {
//                                Layer layer = operationalLayers.get(i);
//
//                                // 检查图层类型（如果需要）
//                                if (layer instanceof ArcGISTiledLayer) {
//                                    ArcGISTiledLayer arcGISTiledLayer = (ArcGISTiledLayer) layer;
//
//                                    // 访问属性和方法
//                                    String layerName = arcGISTiledLayer.getName();
//                                    System.out.println("图层名:"+layerName);
//                                }
//                            }
//                        }
//                    }
//                });
//             添加mmpk
//           final String mResult;
//            AddMMPK addMMPK=new AddMMPK();
//
//            String mmpkPath= getSDPath(this) + "/TRSPGISData/layers.mmpk";
//            addMMPK.setContext(this);
//            addMMPK.setMainArcGISMap(mainArcGISMap);
//            addMMPK.LoadMMPK(mmpkPath);
//
//            tr_featureLayer=addMMPK.getTr_featureLayer();
//            td_featureLayer=addMMPK.getTd_featureLayer();
//
//            dbtlLinearLayout=addMMPK.getDbtlLinearLayout();
//            projectAreaGroupLayer=addMMPK.getProjectAreaGroupLayer();
//            Log.d("测试测试",projectAreaGroupLayer.getLayers().size()+"");
////            MyBroadcastReceiver receiver = new MyBroadcastReceiver();
////            IntentFilter filter = new IntentFilter();
////            filter.addAction("com.example.MY_ACTION");
////            registerReceiver(receiver, filter);
//            Log.d("图层数量:",LayerName.size()+"");
//            addMMPK.LayerFilter(bsm);

                MobileMapPackage mobileMapPackage = new MobileMapPackage(mmpkPath);
                projectAreaGroupLayer.setName("业务图层");
                mobileMapPackage.loadAsync();
                mobileMapPackage.addLoadStatusChangedListener(new LoadStatusChangedListener() {
                    @Override
                    public void loadStatusChanged(LoadStatusChangedEvent loadStatusChangedEvent) {
                        LoadStatus status = loadStatusChangedEvent.getNewLoadStatus();
                        if (status == LoadStatus.LOADED) {
                            // 移动地图包已成功加载
                            // 在此处执行相应的操作
                            Toast.makeText(MainActivity.this, "加载成功！", Toast.LENGTH_LONG).show();
                        } else if (status == LoadStatus.FAILED_TO_LOAD || status == LoadStatus.NOT_LOADED) {
                            // 移动地图包加载失败或已卸载
                            // 在此处执行相应的操作
                            Toast.makeText(MainActivity.this, "加载失败！", Toast.LENGTH_LONG).show();
                        }
                    }
                });
                mobileMapPackage.addDoneLoadingListener(() -> {
                    LoadStatus mainLoadStasus = mobileMapPackage.getLoadStatus();
                    Log.d("mmpk测试", "加载监听");
                    if (mainLoadStasus == LoadStatus.LOADED) {
                        List<ArcGISMap> mainArcGISMapL = mobileMapPackage.getMaps();
                        ArcGISMap mainArcGISMapMMPK = mainArcGISMapL.get(0);
                        LayerList mainMMPKLL = mainArcGISMapMMPK.getOperationalLayers();
                        int h = mainMMPKLL.toArray().length;
                        for (int i = 0; i < h; i++) {
                            FeatureLayer mainFeatureLayer = (FeatureLayer) mainMMPKLL.get(0);
                            //报错Object is already owned.: Already owned.
                            //mapView.getMap().getOperationalLayers().add(mainFeatureLayer);
                            Log.d("mmpk当前加载的操作图层的名字", mainFeatureLayer.getName());
                            //获取土壤图的图层
                            if (Objects.equals(mainFeatureLayer.getName(), "土壤图")) {
                                tr_featureLayer = mainFeatureLayer;
                            }
                            if (Objects.equals(mainFeatureLayer.getName(), "土地利用现状")) {
                                td_featureLayer = mainFeatureLayer;
                            }
                            //将图层的名称存入数组中,这里的图层名称是别名
                            LayerName.add(mainFeatureLayer.getName());
                            /* //mainFeatureLayer.setOpacity(0.8f); */
                            mainArcGISMapMMPK.getOperationalLayers().remove(0);
                            //往grouplayer中加载featurelayer
                            projectAreaGroupLayer.getLayers().add(mainFeatureLayer);
//                            mainLayerList.add(mainFeatureLayer);
                            Log.d("第", i + "次循环");
                        }
                        // add the group layer and other layers to the scene as operational layers

                        mainArcGISMap.getOperationalLayers().addAll(Arrays.asList(projectAreaGroupLayer));

                        //获取底部调查未调查样点状态的图例
                        dbtlLinearLayout = (LinearLayout) findViewById(R.id.pointInfoL);

                        //信息采集的时候跳转过来的，需要显示当前样点蓝色的点
                        if (Objects.equals(bsm, "1")) {
                            //底部样点状态图例可见
                            dbtlLinearLayout.setVisibility(View.VISIBLE);
                            //尤且仅有一个图层就是总图层
                            GroupLayer layer_group = (GroupLayer) mainArcGISMap.getOperationalLayers().get(0);
                            int layer_count = layer_group.getLayers().size();
                            for (int i = 0; i < layer_count; i++) {
                                String layer_name = layer_group.getLayers().get(i).getName();
//                                    if(Objects.equals(layer_name, "土壤图") || Objects.equals(layer_name, "土地利用现状图")||Objects.equals(layer_name, "三普图"))
                                if ( Objects.equals(layer_name, "土地利用现状（2021）") || Objects.equals(layer_name, "母岩图") || Objects.equals(layer_name, "等高线") || Objects.equals(layer_name, "坡向图") || Objects.equals(layer_name, "坡型图")) {

                                    System.out.println(layer_name);
                                    //layer_group.getLayers().get(i).setVisible(false);
                                    FeatureLayer featureLayer = (FeatureLayer) layer_group.getLayers().get(i);
                                    featureLayer.setMinScale(12000);
                                    featureLayer.setMaxScale(500);
//                                        if (featureLayer.isVisible()) {
//                                            Polygon polygon = mapView.getVisibleArea();
//                                            QueryParameters query = new QueryParameters();
//                                            query.setGeometry(polygon);
//                                            ArrayList arrayList = new ArrayList<>();
//                                            ListenableFuture<FeatureQueryResult> featureQueryResult = featureLayer.getFeatureTable().queryFeaturesAsync(query);
//                                            featureQueryResult.addDoneListener(new Runnable() {
//                                                @Override
//                                                public void run() {
//                                                    FeatureQueryResult result = null;
//                                                    try {
//                                                        result = featureQueryResult.get();
//                                                        Iterator<Feature> iterator = result.iterator();
//                                                        Feature feature=null;
//                                                        while (iterator.hasNext()) {
//                                                            feature = iterator.next();
//                                                            arrayList.add(feature.getAttributes().get("objectid"));
//                                                        }
//                                                    } catch (ExecutionException ex) {
//                                                        ex.printStackTrace();
//                                                    } catch (InterruptedException ex) {
//                                                        ex.printStackTrace();
//                                                    }
//                                                }
//                                            });
//                                            Toast.makeText(MainActivity.this, arrayList.toString().replace("[", "(").replace("]", ")"), Toast.LENGTH_LONG).show();
//                                            featureLayer.setDefinitionExpression("object in " + arrayList.toString().replace("[", "(").replace("]", ")"));
//                                        }
                                }
                            }

                        }
//                            //地图底图跳转过来的
                        else if (Objects.equals(bsm, "0")) {
                            //底部样点状态图例不可见
                            dbtlLinearLayout.setVisibility(View.VISIBLE);
                            //尤且仅有一个图层就是总图层
                            GroupLayer layer_group = (GroupLayer) mainArcGISMap.getOperationalLayers().get(0);
                            int layer_count = layer_group.getLayers().size();
                            for (int i = 0; i < layer_count; i++) {
                                String layer_name = layer_group.getLayers().get(i).getName();
//                                    if(Objects.equals(layer_name, "土壤图") || Objects.equals(layer_name, "土地利用现状图")||Objects.equals(layer_name, "三普图"))
                                if ( Objects.equals(layer_name, "土地利用现状（2021）") || Objects.equals(layer_name, "母岩图")) {


                                    FeatureLayer featureLayer = (FeatureLayer) layer_group.getLayers().get(i);
                                    featureLayer.setMinScale(12000);
                                    featureLayer.setMaxScale(500);
                                } else if (Objects.equals(layer_name, "等高线") || Objects.equals(layer_name, "坡向图") || Objects.equals(layer_name, "坡型图")) {
                                    layer_group.getLayers().get(i).setVisible(false);

                                }
                            }

                        } else {
                            //采样任务线路导航跳转过来的
                            //底部样点状态图例可见
                            dbtlLinearLayout.setVisibility(View.VISIBLE);
                            //尤且仅有一个图层就是总图层
                            GroupLayer layer_group = (GroupLayer) mainArcGISMap.getOperationalLayers().get(0);
                            int layer_count = layer_group.getLayers().size();
                            for (int i = 0; i < layer_count; i++) {
                                String layer_name = layer_group.getLayers().get(i).getName();
                                if (!Objects.equals(layer_name, "道路")) {
                                    System.out.println(layer_name);
                                    layer_group.getLayers().get(i).setVisible(false);
                                }

                            }

                        }


                        Log.d("图层组的长度", String.valueOf(projectAreaGroupLayer.getLayers().toArray().length));
                        // zoom to the extent of the group layer when the child layers are loaded
                        ListenableList<Layer> layers = projectAreaGroupLayer.getLayers();
                        for (Layer childLayer : layers) {
                            childLayer.addDoneLoadingListener(() -> {
                                if (childLayer.getLoadStatus() == LoadStatus.LOADED) {
                                    if (lon == 0) {
//                                        mapView.setViewpoint(new Viewpoint(projectAreaGroupLayer.getFullExtent()));
                                    }

                                }
                            });
                        }
                        setupRecyclerView(mainArcGISMap.getOperationalLayers());
                    } else {
                        Exception e = mobileMapPackage.getLoadError();
                        e.printStackTrace();
                        Toast.makeText(MainActivity.this, "加载失败！", Toast.LENGTH_LONG).show();
                        mobileMapPackage.loadAsync();
                    }
                });
                //mobileMapPackage.removeDoneLoadingListener(mobileMapPackage.);
            } catch (Exception e) {
                Toast.makeText(MainActivity.this, "离线地图加载失败，请退出后重试", Toast.LENGTH_LONG).show();
            }

            //监控
//            mapView. addViewpointChangedListener(new ViewpointChangedListener() {
//                @Override
//                public void viewpointChanged(ViewpointChangedEvent event) {
//                    if (tr_featureLayer!=null&&(mapView.getMapScale()>500||mapView.getMapScale()<12000)) {
//                        FeatureLayer featureLayer=tr_featureLayer;
//                        Polygon polygon = mapView.getVisibleArea();
//
//                        SimpleLineSymbol lineSymbol = new SimpleLineSymbol(SimpleLineSymbol.Style.SOLID, Color.parseColor("#FC8145"), 3.0f);
//                        SimpleFillSymbol simpleFillSymbol = new SimpleFillSymbol(SimpleFillSymbol.Style.SOLID, Color.parseColor("#33e97676"), lineSymbol);
//
//                        Graphic graphic = new Graphic(polygon, simpleFillSymbol);
//                        QueryParameters query = new QueryParameters();
//                        query.setGeometry(polygon);
//                        query.setSpatialRelationship(QueryParameters.SpatialRelationship.INTERSECTS);
//                        ArrayList arrayList = new ArrayList<>();
//                        ListenableFuture<FeatureQueryResult> featureQueryResult = featureLayer.getFeatureTable().queryFeaturesAsync(query);
//
//                        featureQueryResult.addDoneListener(new Runnable() {
//                            @Override
//                            public void run() {
//                                FeatureQueryResult result = null;
//                                try {
//                                    result = featureQueryResult.get();
//
//                                    Iterator<Feature> iterator = result.iterator();
//                                    while (iterator.hasNext()) {
//                                        feature = iterator.next();
//                                        arrayList.add(feature.getAttributes().get("objectid"));
//                                    }
//                                } catch (ExecutionException ex) {
//                                    ex.printStackTrace();
//                                } catch (InterruptedException ex) {
//                                    ex.printStackTrace();
//                                }
//
//                                Log.d("要素数量1111:",arrayList.size()+"");
//                                if(arrayList.size()>0) {
//                                    DisplayFilter displayFilter=new DisplayFilter("guolv","objectid in " + arrayList.toString().replace("[", "(").replace("]", ")"));
//                                    Iterable<DisplayFilter> filters = Arrays.asList(
//                                            new DisplayFilter("guolv","objectid in " + arrayList.toString().replace("[", "(").replace("]", ")"))
//                                    );
//                                    DisplayFilterDefinition filterDef = new ManualDisplayFilterDefinition(filters.iterator().next(), filters);
//                                    featureLayer.setDisplayFilterDefinition(filterDef);
//                                }
//                            }
//
//                        });
//
//                    }
//                }
//            });

            open_attr();

            //实例化identify按钮以控制
            identifyButton = (ImageButton) findViewById(R.id.identifyButton);
            identifyButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    //弹出一个选择框，选择是否开启identify功能
                    AlertDialog alertDialog = new AlertDialog.Builder(MainActivity.this)
                            .setTitle("是否要对当前图层进行属性查询？")
                            .setSingleChoiceItems(identify_item, identify_index, new DialogInterface.OnClickListener() {//添加单选框
                                public void onClick(DialogInterface dialogInterface, int i) {
                                    //0为关闭，1为开启
                                    identify_index = i;
                                }
                            })
                            .setPositiveButton("确定", new DialogInterface.OnClickListener() {//添加"Yes"按钮
                                @Override
                                public void onClick(DialogInterface dialogInterface, int i) {
                                    // Toast.makeText(MainActivity.this, "切换到" + map_item[index], Toast.LENGTH_SHORT).show();
                                    //开关
                                    if (identify_index == 0)//关闭
                                    {
                                        Toast.makeText(MainActivity.this, "关闭属性查询功能", Toast.LENGTH_SHORT).show();
                                        System.out.println("当前的identify_index为" + identify_index);
                                        //销毁mapview的触摸事件
                                        open_attr();
                                    } else if (identify_index == 1)//开启
                                    {
                                        Toast.makeText(MainActivity.this, "开启属性查询功能", Toast.LENGTH_SHORT).show();
                                        System.out.println("当前的identify_index为" + identify_index);
                                        //identify();
                                        IdentifyFeature identifyFeature = new IdentifyFeature();
                                        identifyFeature.identifyResult(mapView, mBottomSheetBehavior);
                                    }
                                }
                            })
                            .setNegativeButton("取消", new DialogInterface.OnClickListener() {//添加取消
                                @Override
                                public void onClick(DialogInterface dialogInterface, int i) {
                                    //Toast.makeText(MainActivity.this, "这是取消按钮", Toast.LENGTH_SHORT).show();
                                }
                            })
                            .create();
                    //防止点击弹出框外的地方关闭弹出框
                    alertDialog.setCanceledOnTouchOutside(false);
                    alertDialog.show();
                }
            });


            //底图切换
            layerChange = (ImageButton) findViewById(R.id.layerChange);
            //按钮最高级
//            layerChange.bringToFront();
            layerChange.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    //设置弹窗
                    AlertDialog alertDialog = new AlertDialog.Builder(MainActivity.this)
                            .setTitle("选择加载的地图")
                            .setSingleChoiceItems(map_item, index, new DialogInterface.OnClickListener() {//添加单选框
                                public void onClick(DialogInterface dialogInterface, int i) {
                                    //影像图为0，dem为1
                                    index = i;
                                }
                            })
                            .setPositiveButton("确定", new DialogInterface.OnClickListener() {//添加"Yes"按钮
                                @Override
                                public void onClick(DialogInterface dialogInterface, int i) {
                                    // Toast.makeText(MainActivity.this, "切换到" + map_item[index], Toast.LENGTH_SHORT).show();
                                    //切换地图
                                    if (index == 0)//影像地图
                                    {
                                        //加载文件
                                        String fileTPK = imageTpkPath;
                                        ArcGISTiledLayer arcGISTiledLayer = new ArcGISTiledLayer(fileTPK);
                                        Basemap basemap = new Basemap(arcGISTiledLayer);
                                        //确定为这个类但不添加底图
                                        mainArcGISMap.setBasemap(basemap);

                                    } else if (index == 1)//DEM地图
                                    {
                                        String DemUrl = demTpkPath;
                                        Log.d("demTpkPath", demTpkPath);
                                        ArcGISTiledLayer arcGISTiledLayer = new ArcGISTiledLayer(DemUrl);
                                        Basemap basemap = new Basemap(arcGISTiledLayer);
                                        //确定为这个类但不添加底图
                                        mainArcGISMap.setBasemap(basemap);
                                    }
                                }
                            })
                            .setNegativeButton("取消", new DialogInterface.OnClickListener() {//添加取消
                                @Override
                                public void onClick(DialogInterface dialogInterface, int i) {
                                    //Toast.makeText(MainActivity.this, "这是取消按钮", Toast.LENGTH_SHORT).show();
                                }
                            })
                            .create();
                    //防止点击弹出框外的地方关闭弹出框
                    alertDialog.setCanceledOnTouchOutside(false);
                    alertDialog.show();
                }
            });
            mapView.setAttributionTextVisible(false);//去掉底部水印


//            // create a group layer for the buildings and set its visibility mode to exclusive
//            GroupLayer buildingsGroupLayer = new GroupLayer();
//            buildingsGroupLayer.setName("专题图组");
//            buildingsGroupLayer.getLayers().addAll(Arrays.asList(mainArcGISVectorTiledLayer, soilMap));
//
//            // create a group layer from scratch by adding the trees, pathways, and project area as children
//            GroupLayer projectAreaGroupLayer = new GroupLayer();
//            projectAreaGroupLayer.setName("道路组");
//            projectAreaGroupLayer.getLayers().addAll(Arrays.asList(upRoatLayer,downRoatLayer));


            //操作图层的打印结果是2，说明两个grouplayer确实是可以换位置的！
            Log.d("操作图层长度", String.valueOf(mainArcGISMap.getOperationalLayers().toArray().length));

            //按钮组的按钮事件
            //获取指北针
            ImageButton compassButton = (ImageButton) findViewById(R.id.compassButton);
            BtnTouth(compassButton);//设置指北针点击样式
            //给指北针添加点击事件
            compassButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    mapView.setViewpointRotationAsync(0);
                }
            });
            //获取放大按钮
            ImageButton zoomInButton = (ImageButton) findViewById(R.id.zoomInButton);
            BtnTouth(zoomInButton);
            //添加放大按钮的点击事件
            zoomInButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    //获取当前的地图缩放等级
                    double mScale = mapView.getMapScale();
                    mapView.setViewpointScaleAsync(mScale * 0.5);
//                    Log.d("mapscale", String.valueOf(mScale));
                }
            });
            //获取缩小按钮
            ImageButton zoomOutButton = (ImageButton) findViewById(R.id.zomOutButton);
            BtnTouth(zoomOutButton);
            //添加缩小按钮的点击事件
            zoomOutButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    //获取当前的地图缩放等级
                    double mScale = mapView.getMapScale();
                    mapView.setViewpointScaleAsync(mScale * 2);
                }
            });
            //获取全图按钮
            ImageButton fullPicButton = (ImageButton) findViewById(R.id.fullPicButton);
            BtnTouth(fullPicButton);
            fullPicButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    //这里也是偷懒直接用底图图层的范围确定范围
                    Viewpoint viewpoint = new Viewpoint(mapView.getMap().getInitialViewpoint().getTargetGeometry().getExtent());
                    mapView.setViewpointAsync(viewpoint);
                }
            });
            //获取图层管理按钮
            ImageButton layersButton = (ImageButton) findViewById(R.id.layersButton);
            BtnTouth(layersButton);
            //添加图层管理按钮的点击事件
            layersButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    Log.i("LayersButton", "点击图层按钮，当前状态: " + mBottomSheetBehavior.getState());
                    
                    if (mBottomSheetBehavior.getState() != BottomSheetBehavior.STATE_EXPANDED) {
                        // 显示底部表单
                        showLayersBottomSheet();
                    } else {
                        // 隐藏底部表单
                        hideLayersBottomSheet();
                    }
                }
            });
            
            // 添加BottomSheet状态变化监听器
            mBottomSheetBehavior.addBottomSheetCallback(new com.google.android.material.bottomsheet.BottomSheetBehavior.BottomSheetCallback() {
                @Override
                public void onStateChanged(@NonNull View bottomSheet, int newState) {
                    Log.i("BottomSheet", "底部表单状态变化: " + newState);
                }

                @Override
                public void onSlide(@NonNull View bottomSheet, float slideOffset) {
                    // 滑动事件，不需要处理
                }
            });
            //获取距离量测按钮
            ImageButton measurementButton = (ImageButton) findViewById(R.id.measurementButton);
            BtnTouth(measurementButton);//添加点击样式
            final boolean[] btnBoolean = {false};
            measurementButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (btnBoolean[0] == false) {
                        findViewById(R.id.group).setVisibility(View.VISIBLE);
                        measurementButton.setBackgroundColor(Color.BLUE);//设置图片透明度0-255，0完全透明
                        measurementButton.invalidate();
                        btnBoolean[0] = true;
                    } else {
                        findViewById(R.id.group).setVisibility(View.GONE);
                        //隐藏控件时，清空绘制图层的图案
//                        if (mGraphicsOverlay.getGraphics() != null) {
//                            mGraphicsOverlay.getGraphics().removeAll(mGraphicsOverlay.getGraphics());
//                        }

                        mSketchEditor.stop();
                        resetButtons();
                        measurementButton.setBackgroundColor(Color.WHITE);//设置图片透明度0-255，0完全透明
                        measurementButton.invalidate();
                        //关闭时文字恢复默认
                        TextView textView = (TextView) findViewById(R.id.result_text);
                        textView.setText("请开始绘制！");
                        btnBoolean[0] = false;
                    }

                }
            });

            //获取离线导航的按钮
            BtnTouth(navigationButton);
            navigationButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    //绘制该点，点位
                    SimpleMarkerSymbol simpleMarkerSymbol = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.RED, 50);
                    Graphic graphic = new Graphic(yd_point, simpleMarkerSymbol);
                    //绘制定位点
//                    Vector2D lonlat1 = new Vector2D(location_longitude,location_latitude);
//                    Vector2D mecator1 = lonLatToMercator(lonlat1);
//
//                    double location_x = mecator1.getX();
//                    double location_y = mecator1.getY();
//
//                    System.out.println("定位的经纬度为：" + location_x + "," + location_y);

                    Point location_point = new Point(location_longitude, location_latitude);
                    SimpleMarkerSymbol simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.YELLOW, 50);
                    Graphic graphic1 = new Graphic(location_point, simpleMarkerSymbol1);
                    //清除上一个点
                    mGraphicsOverlay.getGraphics().clear();
                    mGraphicsOverlay.getGraphics().add(graphic);
                    mGraphicsOverlay.getGraphics().add(graphic1);
                    //计算两点间距离，并延长500米左右
                    //转换点选的样点的坐标
                    double x = yd_point.getX();
                    double y = yd_point.getY();
                    Vector2D mecator = new Vector2D(x, y);
                    Vector2D lonlat = MercatorTolonLat(mecator);
                    double lon = lonlat.getX();
                    double lat = lonlat.getY();
                    //获取了点选的点的经纬度
                    System.out.println(lon);
                    System.out.println(lat);

                    //与location定位一齐计算距离，并判断是否在200m内
                    Double distance = getDistance(lon, lat, location_longitude, location_latitude);
                    Double final_distance = distance + 500;

                    //获取中心点经纬度
                    double centerPointLon = getCenterPointLon(lon, lat, location_longitude, location_latitude);
                    double centerPointLat = getCenterPointLat(lon, lat, location_longitude, location_latitude);

                    System.out.println(centerPointLon);
                    System.out.println(centerPointLat);
                    System.out.println(final_distance);

                    double scale = final_distance / 0.07;
                    //移动地图的中心视角
                    mapView.setViewpoint(new Viewpoint(centerPointLat, centerPointLon, scale));
                }
            });

            //获取显示当前位置按钮
            ImageButton currentLoBtn = (ImageButton) findViewById(R.id.currentLoButton);

            BtnTouth(currentLoBtn);
            final boolean[] loBtnBoolean = {false};
            //地图加载完开始定位设置
//            if(lon==0) {
//                loBtn();
//                currentLoBtn.setBackgroundColor(Color.BLUE);//设置图片透明度0-255，0完全透明
//                currentLoBtn.invalidate();
//                loBtnBoolean[0] = true;
//            }
            currentLoBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (loBtnBoolean[0] == false) {
                        loBtn();
                        currentLoBtn.setBackgroundColor(Color.BLUE);//设置图片透明度0-255，0完全透明
                        currentLoBtn.invalidate();
                        loBtnBoolean[0] = true;
                    } else {
                        mLocationDisplay.stop();
                        currentLoBtn.setBackgroundColor(Color.WHITE);//设置图片透明度0-255，0完全透明
                        currentLoBtn.invalidate();
                        loBtnBoolean[0] = false;
                    }
                }
            });

            //添加绘制图层
            mGraphicsOverlay = new GraphicsOverlay();
            mapView.getGraphicsOverlays().add(mGraphicsOverlay);


            // 在 MapView 准备好后立即初始化 SketchEditor 并添加其监听器
            mSketchEditor = new SketchEditor();
            mapView.setSketchEditor(mSketchEditor);

            // 立即添加 geometry changed listener
            mSketchEditor.addGeometryChangedListener(new SketchGeometryChangedListener() {
                @Override
                public void geometryChanged(SketchGeometryChangedEvent sketchGeometryChangedEvent) {
                    Geometry geometry = mSketchEditor.getGeometry();
                    if (geometry == null) {
                        return;
                    }
                    Log.d("草图模式", mSketchEditor.getSketchCreationMode().toString());
                    switch (mSketchEditor.getSketchCreationMode()) {
                        case POLYLINE:
                            if (mSketchEditor.isSketchValid()) {
                                double length = GeometryEngine.lengthGeodetic((Polyline) geometry, mUnitOfMeasurement, GeodeticCurveType.GEODESIC);
                                runOnUiThread(() -> {
                                    TextView result_text = (TextView) findViewById(R.id.result_text);
                                    // 添加 null 检查以确保安全
                                    if (result_text != null) {
                                        if (length >= 1000) {
                                            result_text.setText(String.format("%.2f", length / 1000) + "千米");
                                        } else {
                                            result_text.setText(String.format("%.2f", length) + "米");
                                        }
                                    }
                                });
                            }
                            break;
                        case POLYGON:
                            if (mSketchEditor.isSketchValid()) {
                                double area = GeometryEngine.areaGeodetic((Polygon) geometry, mAreaOfMeasurement, GeodeticCurveType.GEODESIC);
                                runOnUiThread(() -> {
                                    TextView result_text = (TextView) findViewById(R.id.result_text);
                                    // 添加 null 检查以确保安全
                                    if (result_text != null) {
                                        if (area >= 666.7) {
                                            result_text.setText(String.format("%.2f", area / 666.7) + "亩");
                                        } else {
                                            result_text.setText(String.format("%.2f", area) + "平方米");
                                        }
                                    }
                                });
                            }
                            break;
                    }
                }
            });

            // get buttons from layouts
            mPolylineButton = findViewById(R.id.polylineButton);
            mPolygonButton = findViewById(R.id.polygonButton);
            ImageButton undo = (ImageButton) findViewById(R.id.undo);
            ImageButton redo = (ImageButton) findViewById(R.id.redo);
            ImageButton stop = (ImageButton) findViewById(R.id.stop);
            ImageButton cancle_btn = (ImageButton) findViewById(R.id.cancle_btn);
            // add click listeners
            mPolylineButton.setOnClickListener(view -> createModePolyline());
            mPolygonButton.setOnClickListener(view -> createModePolygon());
            undo.setOnClickListener(view -> undo());
            redo.setOnClickListener(view -> redo());
            //stop.setOnClickListener(view -> stop());
            cancle_btn.setOnClickListener(view -> cancle_btnClick());

//            mapView.setOnTouchListener(new DefaultMapViewOnTouchListener(this, mapView) {
//                @Override
//                public boolean onSingleTapConfirmed(MotionEvent e) {
//                    TextView result_text = (TextView) findViewById(R.id.result_text);
//                    String span = "请开始绘制！";
//                    // 如果当前 SketchEditor 不为空，并且正在绘制线段，则计算线段长度并输出
//                    if (mSketchEditor != null && mSketchEditor.getSketchCreationMode() == SketchCreationMode.POLYLINE) {
//                        Geometry sketchGeometry = mSketchEditor.getGeometry();
//                        Polyline polyline = (Polyline) GeometryEngine.project(sketchGeometry, SpatialReference.create(3857));
//                        BigDecimal polylineB = BigDecimal.valueOf(GeometryEngine.length(polyline));
//                        span = "长度: " + polylineB.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue() + "m";
//                        result_text.setText(String.format("%.6f",span));
//                        Toast.makeText(MainActivity.this,span,Toast.LENGTH_LONG).show();
//                    }
//
//                    return true;
//                }
//            });

            //这里绘制point
            pointGraphicsOverlay = new GraphicsOverlay();
            textGraphicsOverlay = new GraphicsOverlay();
            mapView.getGraphicsOverlays().add(pointGraphicsOverlay);
            mapView.getGraphicsOverlays().add(textGraphicsOverlay);
            if (lon == 0) {
                //作业底图
                Log.d("测试", "作业底图进来了");
                addPoint_plus();
                if (!geometrystr.isEmpty()) {
                    Geometry geometry = Geometry.fromJson(geometrystr);
                    Viewpoint viewpoint = new Viewpoint((Envelope) geometry);
                    mapView.setViewpoint(viewpoint);
                }
            } else if (lon == 1) {
                //信息采集
                Log.d("测试", "信息采集进来了");
                addPoint_plus1();
            } else {
                System.out.println("执行addPoint()");
                // 线路导航
                addPoint();
            }

            //工具箱按钮
            ImageButton toolbox = (ImageButton) findViewById(R.id.toolbar_content);
            toolbox.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    Log.i("Toolbox", "点击工具箱按钮");
                    
                    // 获取所有需要控制的按钮
                    ImageButton identifyButton = (ImageButton) findViewById(R.id.identifyButton);
                    ImageButton layersButton = (ImageButton) findViewById(R.id.layersButton);
                    ImageButton fullPicButton = (ImageButton) findViewById(R.id.fullPicButton);
                    ImageButton measurementButton = (ImageButton) findViewById(R.id.measurementButton);
                    ImageButton layerChange = (ImageButton) findViewById(R.id.layerChange);
                    
                    // 检查并切换按钮的可见性状态
                    if (identifyButton.getVisibility() == View.VISIBLE) {
                        Log.i("Toolbox", "隐藏所有工具按钮");
                        identifyButton.setVisibility(View.INVISIBLE);
                        layersButton.setVisibility(View.INVISIBLE);
                        fullPicButton.setVisibility(View.INVISIBLE);
                        measurementButton.setVisibility(View.INVISIBLE);
                        layerChange.setVisibility(View.INVISIBLE);
                        
                        // 如果底部表单正在显示，则隐藏它
                        if (mBottomSheetBehavior.getState() == BottomSheetBehavior.STATE_EXPANDED) {
                            mBottomSheetBehavior.setState(BottomSheetBehavior.STATE_HIDDEN);
                        }
                    } else {
                        Log.i("Toolbox", "显示所有工具按钮");
                        identifyButton.setVisibility(View.VISIBLE);
                        layersButton.setVisibility(View.VISIBLE);
                        fullPicButton.setVisibility(View.VISIBLE);
                        measurementButton.setVisibility(View.VISIBLE);
                        layerChange.setVisibility(View.VISIBLE);
                    }
                }
            });

            //图例按钮
            LegendButton = (ImageButton) findViewById(R.id.LegendButton);
            LegendButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    Bitmap bitmap = null;

                    ListenableFuture<List<LegendInfo>> listenableFuture = tr_featureLayer.fetchLegendInfosAsync();
                    try {
                        List<LegendInfo> legendInfoList = listenableFuture.get();

                        for (int i = 0; i < legendInfoList.size(); i++) {
                            String tName = legendInfoList.get(i).getName();
                            Symbol tSymbol = legendInfoList.get(i).getSymbol();
                            ListenableFuture<Bitmap> tListFuture
                                    = tSymbol.createSwatchAsync(Color.WHITE, 3);
                            try {
                                bitmap = tListFuture.get();//把Symbol转换成bitmap
                            } catch (ExecutionException | InterruptedException e) {
                                e.printStackTrace();
                            }
                        }


//                        for (LegendInfo legendInfo: legendInfoList) {
//                            /**
//                             * 该方法拥有多个构造方法
//                             * 1. createSwatchAsync(int width, int height, float screenScale, int backgroundColor)
//                             * 2. createSwatchAsync(Context context, int backgroundColor)
//                             * 3. createSwatchAsync(int backgroundColor, float screenScale)
//                             * 4.  createSwatchAsync(int width, int height, float screenScale, int backgroundColor, Geometry geometry)
//                             * */
//                            ListenableFuture<Bitmap> bitmapListenableFuture = legendInfo.getSymbol().createSwatchAsync(Color.WHITE, 3);
//                            bitmap = bitmapListenableFuture.get();
//                        }
                        //将bitmap存放到ImageView中
                        ImageView legend = (ImageView) findViewById(R.id.legend);
                        legend.setImageBitmap(bitmap);

                    } catch (ExecutionException e) {
                        e.printStackTrace();
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            });

            //导航栏添加返回按钮
            //这里在Activity的onCreat()方法里写
            ActionBar actionBar = getSupportActionBar();
            actionBar.setHomeButtonEnabled(true);
            actionBar.setDisplayHomeAsUpEnabled(true);  //添加返回的图标

            //比例尺
            //TextView mapScale = findViewById(R.id.map_scale);
            //mapView.addMapScaleChangedListener(
                    //       mapScaleChangedEvent -> mapScale.setText("1:"+String.format("%.4f",String.valueOf(Math.round(mapView.getMapScale())))));

                    //mapScaleChangedEvent -> mapScale.setText("1:" + String.valueOf(Math.round(mapView.getMapScale()))));
        } catch (Exception e) {
            String eResult = e.getMessage();
            Log.e(TAG, "Error during onCreate initialization", e); // 记录完整异常信息
        }
        //监听编辑 - 这部分代码已移动到 MapView 初始化之后
        /*
        mSketchEditor.addGeometryChangedListener(new SketchGeometryChangedListener() {
            @Override
            public void geometryChanged(SketchGeometryChangedEvent sketchGeometryChangedEvent) {
                Geometry geometry = mSketchEditor.getGeometry();
                if (geometry == null) {
                    return;
                }
                Log.d("草图模式", mSketchEditor.getSketchCreationMode().toString());
                switch (mSketchEditor.getSketchCreationMode()) {
                    case POLYLINE:
                        if (mSketchEditor.isSketchValid()) {
                            double length = GeometryEngine.lengthGeodetic((Polyline) geometry, mUnitOfMeasurement, GeodeticCurveType.GEODESIC);
                            runOnUiThread(() -> {
                                TextView result_text = (TextView) findViewById(R.id.result_text);
                                if (length >= 1000) {
                                    result_text.setText(String.format("%.2f", length / 1000) + "千米");
                                } else {
                                    result_text.setText(String.format("%.2f", length) + "米");
                                }
                                String span = "请开始绘制！";
                            });
                        }
                        break;
                    case POLYGON:
                        if (mSketchEditor.isSketchValid()) {
                            double area = GeometryEngine.areaGeodetic((Polygon) geometry, mAreaOfMeasurement, GeodeticCurveType.GEODESIC);
                            runOnUiThread(() -> {
                                TextView result_text = (TextView) findViewById(R.id.result_text);
                                if (area >= 666.7) {
                                    result_text.setText(String.format("%.2f", area / 666.7) + "亩");
                                } else {
                                    result_text.setText(String.format("%.2f", area) + "平方米");
                                }
                                String span = "请开始绘制！";
                            });
                        }
                        break;
                }
            }
        });
        */
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                onBackPressed();
                finish(); // 关闭当前页面
                return true;
        }
        return super.onOptionsItemSelected(item);
    }


    //    根据获取到的按钮的id，为其添加鼠标点击样式
    private void BtnTouth(ImageButton btn) {
        btn.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent motionEvent) {
                if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                    btn.setBackgroundColor(Color.BLUE);//设置图片透明度0-255，0完全透明
                    btn.invalidate();
                } else {
                    btn.setBackgroundColor(Color.WHITE);//还原图片
                    btn.invalidate();
                }
                return false;
            }
        });
    }
    //图层管理相关函数开始

    /**
     * Setup {@link RecyclerView} to display layers
     *
     * @param layers
     */
    private void setupRecyclerView(List<Layer> layers) {
        mLayersAdapter = new LayersAdapter(this, this, MainActivity.this);
        mLayersRecyclerView.setAdapter(mLayersAdapter);
        
        // 添加日志以跟踪RecyclerView的设置
        Log.i("RecyclerView", "设置图层适配器, 图层数量: " + layers.size());
        
        // 确保RecyclerView可见
        if (mLayersRecyclerView != null) {
            mLayersRecyclerView.setVisibility(View.VISIBLE);
        } else {
            Log.e("RecyclerView", "mLayersRecyclerView为空");
        }

        testModule = new TestModule(this);

        for (Layer layer : layers) {
            // if layer can be shown in legend
            if (layer.canShowInLegend()) {
                layer.addDoneLoadingListener(() -> {
                    mLayersAdapter.addLayer(layer);
                    Log.i("RecyclerView", "添加图层: " + layer.getName());
                });
                layer.loadAsync();
            }
        }
    }

    /**
     * Called when a checkbox on a layer in the list is checked or unchecked
     *
     * @param layer   that has been checked or unchecked
     * @param checked whether the checkbox has been checked or unchecked
     */
    @Override
    public void layerCheckedChanged(Layer layer, boolean checked) {
        layer.setVisible(checked);
        if (layer instanceof GroupLayer) {
            for (Layer childLayer : ((GroupLayer) layer).getLayers()) {
                childLayer.setVisible(checked);
            }
        }
    }
    //图层管理相关函数结束

    //弹窗相关函数
    private void callExit() {
        if (callout != null) {
            callout.dismiss();
        }

    }


    //量测相关函数开始

    /**
     * When the polyline button is clicked, reset other buttons, show the polyline button as selected, and start
     * polyline drawing mode.
     */
    private void createModePolyline() {
        //选择了一个画线，清空绘制图层的图案
//        if (mGraphicsOverlay.getGraphics() != null) {
//            mGraphicsOverlay.getGraphics().removeAll(mGraphicsOverlay.getGraphics());
//        }
        resetButtons();
        mPolylineButton.setSelected(true);
        mSketchEditor.start(SketchCreationMode.POLYLINE);

    }

    /**
     * When the polygon button is clicked, reset other buttons, show the polygon button as selected, and start polygon
     * drawing mode.
     */
    private void createModePolygon() {
        //选择了一个画面，清空绘制图层的图案
//        if (mGraphicsOverlay.getGraphics() != null) {
//            mGraphicsOverlay.getGraphics().removeAll(mGraphicsOverlay.getGraphics());
//        }
        resetButtons();
        mPolygonButton.setSelected(true);
        mSketchEditor.start(SketchCreationMode.POLYGON);
    }

    /**
     * When the undo button is clicked, undo the last event on the SketchEditor.
     */
    private void undo() {
        if (mSketchEditor.canUndo()) {
            mSketchEditor.undo();
        }
    }

    /**
     * When the redo button is clicked, redo the last undone event on the SketchEditor.
     */
    private void redo() {
        if (mSketchEditor.canRedo()) {
            mSketchEditor.redo();
        }
    }

    /**
     * When the stop button is clicked, check that sketch is valid. If so, get the geometry from the sketch, set its
     * symbol and add it to the graphics overlay.
     */
    private void stop() {
        if (!mSketchEditor.isSketchValid()) {
            reportNotValid();
            mSketchEditor.stop();
            resetButtons();
            return;
        }
        //获取显示测量结果的文本框
        TextView result_text = (TextView) findViewById(R.id.result_text);
        String span = "请开始绘制！";
        // get the geometry from sketch editor
        Geometry sketchGeometry = mSketchEditor.getGeometry();
        //mSketchEditor.stop();
        resetButtons();

        if (sketchGeometry != null) {

            // create a graphic from the sketch editor geometry
            Graphic graphic = new Graphic(sketchGeometry);

            // assign a symbol based on geometry type
            if (graphic.getGeometry().getGeometryType() == GeometryType.POLYGON) {
                graphic.setSymbol(mFillSymbol);
                //如果绘制的是面，则计算面积
                Polygon polygon = (Polygon) GeometryEngine.project(sketchGeometry, SpatialReference.create(3857));
                BigDecimal areaB = BigDecimal.valueOf(Math.abs(GeometryEngine.area(polygon)));
                double area = areaB.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
                //TODO:保留两位小数
                span = "面积: " + 0.0015 * area + "亩";
                result_text.setText(String.format("%.6f", span));
            } else if (graphic.getGeometry().getGeometryType() == GeometryType.POLYLINE) {
                graphic.setSymbol(mLineSymbol);
                //如果绘制的是线，则计算长度
                Polyline polyline = (Polyline) GeometryEngine.project(sketchGeometry, SpatialReference.create(3857));
                BigDecimal polylineB = BigDecimal.valueOf(GeometryEngine.length(polyline));
                span = "长度: " + polylineB.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue() + "m";
                result_text.setText(String.format("%.6f", span));
            } else if (graphic.getGeometry().getGeometryType() == GeometryType.POINT ||
                    graphic.getGeometry().getGeometryType() == GeometryType.MULTIPOINT) {
                graphic.setSymbol(mPointSymbol);
            }
            // add the graphic to the graphics overlay
            mGraphicsOverlay.getGraphics().add(graphic);
        }
    }

    /**
     * Called if sketch is invalid. Reports to user why the sketch was invalid.
     */
    private void reportNotValid() {
        String validIf;
        if (mSketchEditor.getSketchCreationMode() == SketchCreationMode.POLYLINE) {
            validIf = "绘制线至少点击两个点！";
        } else if (mSketchEditor.getSketchCreationMode() == SketchCreationMode.POLYGON) {
            validIf = "多边形至少包含三个点！";
        } else {
            validIf = "请选择创建的类型！";
        }
        String report = "警告:\n" + validIf;
        Snackbar reportSnackbar = Snackbar.make(findViewById(R.id.toolbarInclude), report, Snackbar.LENGTH_INDEFINITE);
        reportSnackbar.setAction("确定", view -> reportSnackbar.dismiss());
        TextView snackbarTextView = reportSnackbar.getView().findViewById(com.google.android.material.R.id.snackbar_text);
        snackbarTextView.setSingleLine(false);
        reportSnackbar.show();
        Log.e(TAG, report);
    }

    /**
     * De-selects all buttons.
     */
    private void resetButtons() {
        mPolylineButton.setSelected(false);
        mPolygonButton.setSelected(false);
    }

    private void cancle_btnClick() {
        resetButtons();
        mSketchEditor.stop();
        //点击取消，清空绘制图层的图案
//        if (mGraphicsOverlay.getGraphics() != null) {
//            mGraphicsOverlay.getGraphics().removeAll(mGraphicsOverlay.getGraphics());
//        }
        //关闭时文字恢复默认
        TextView textView = (TextView) findViewById(R.id.result_text);
        textView.setText("请开始绘制！");
    }
    //量测相关函数结束

    //定位相关函数
    private void loBtn() {
        //请求权限
        List<String> permissionList = new ArrayList<>();
        if (ContextCompat.checkSelfPermission(MainActivity.this, Manifest.
                permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            permissionList.add(Manifest.permission.ACCESS_FINE_LOCATION);
        }
        if (ContextCompat.checkSelfPermission(MainActivity.this, Manifest.
                permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            permissionList.add(Manifest.permission.ACCESS_COARSE_LOCATION);
        }
        if (!permissionList.isEmpty()) {
            String[] permissions = permissionList.toArray(new String[permissionList.size()]);
            ActivityCompat.requestPermissions(MainActivity.this, permissions, 1);
        }

        mLocationDisplay = mapView.getLocationDisplay();

        mLocationDisplay.setAutoPanMode(LocationDisplay.AutoPanMode.RECENTER);
        //显示定位的符号
        mLocationDisplay.isShowLocation();
        mLocationDisplay.setShowAccuracy(false);
        if (!mLocationDisplay.isStarted())
            mLocationDisplay.startAsync();
//        mLocationDisplay.addLocationChangedListener(locationChangedEvent -> {
//            //TODO：位置变化触发的回调，后面可以在这里添加计算据样点的距离
//            double longitude = locationChangedEvent.getLocation().getPosition().getX();
//            double latitude = locationChangedEvent.getLocation().getPosition().getY();
//
//            //绘制一个蓝色的定位点
//            Vector2D lonlat1 = new Vector2D(longitude,latitude);
//            Vector2D mecator1 = lonLatToMercator(lonlat1);
//
//            double location_x = mecator1.getX();
//            double location_y = mecator1.getY();
        //Point location_point = new Point(longitude,latitude);
        //SimpleMarkerSymbol simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE,Color.BLUE,18);
        //Graphic graphic1 = new Graphic(location_point,simpleMarkerSymbol1);
        //mGraphicsOverlay.getGraphics().clear();
        //mGraphicsOverlay.getGraphics().add(graphic1);
//       });
    }

    //    地球半径
    private static final double EARTH_RADIUS = 6378.137;
    //第一种方法，返回多少米

    /**
     * 计算两点之间距离
     *
     * @return 返回米
     */
    public Double getDistance(double longitude1, double latitude1,
                              double longitude2, double latitude2) {
        double lat1 = (Math.PI / 180) * latitude1;
        double lat2 = (Math.PI / 180) * latitude2;

        double lon1 = (Math.PI / 180) * longitude1;
        double lon2 = (Math.PI / 180) * longitude2;

        //两点间距离 km，如果想要米的话，结果*1000
        double d = Math.acos(Math.sin(lat1) * Math.sin(lat2) + Math.cos(lat1) * Math.cos(lat2) * Math.cos(lon2 - lon1)) * EARTH_RADIUS;
        if (d < 1)
            return (Double) (d * 1000);
        else
            return Double.valueOf(String.format("%.2f", d * 1000));
    }

    //计算两点间中心点经度
    private double getCenterPointLon(double lon1, double lat1, double lon2, double lat2) {
        double α1 = Math.toRadians(lon1);  // 经度角α1
        double β1 = Math.toRadians(lat1);   //纬度角β1
        double α2 = Math.toRadians(lon2);  // 经度角α2
        double β2 = Math.toRadians(lat2);  // 纬度角β1
        double dα = α2 - α1;
        double x = Math.cos(β2) * Math.cos(dα);
        double y = Math.cos(β2) * Math.sin(dα);
        double α = α1 + Math.atan2(y, Math.cos(β1) + x);
        double β = Math.atan2(Math.sin(β1) + Math.sin(β2), Math.pow(Math.sqrt((Math.cos(β1) + x)), 2) + Math.pow(y, 2));
        return Math.toDegrees(α);

    }

    //计算两点间中心点纬度
    private double getCenterPointLat(double lon1, double lat1, double lon2, double lat2) {
        double α1 = Math.toRadians(lon1);  // 经度角α1
        double β1 = Math.toRadians(lat1);   //纬度角β1
        double α2 = Math.toRadians(lon2);  // 经度角α2
        double β2 = Math.toRadians(lat2);  // 纬度角β1
        double dα = α2 - α1;
        double x = Math.cos(β2) * Math.cos(dα);
        double y = Math.cos(β2) * Math.sin(dα);
        double α = α1 + Math.atan2(y, Math.cos(β1) + x);
        double β = Math.atan2(Math.sin(β1) + Math.sin(β2), Math.pow(Math.sqrt((Math.cos(β1) + x)), 2) + Math.pow(y, 2));
        return Math.toDegrees(β);
    }

    //投影坐标转地理坐标
    public Vector2D MercatorTolonLat(Vector2D mercator) {
        double x = mercator.getX() / 20037508.34 * 180;
        double y = mercator.getY() / 20037508.34 * 180;
        y = 180 / Math.PI * (2 * Math.atan(Math.exp(y * Math.PI / 180)) - Math.PI / 2);
        Vector2D lonLat = new Vector2D(x, y);
        return lonLat;
    }

    //地理坐标转投影坐标

    /**
     * 经纬度转墨卡托投影
     */
    public Vector2D lonLatToMercator(Vector2D lonLat) {
        double x = lonLat.getX() * 20037508.34 / 180;
        double y = Math.log(Math.tan((90 + lonLat.getY()) * Math.PI / 360)) / (Math.PI / 180);
        y = y * 20037508.34 / 180;

        Vector2D mercator = new Vector2D(x, y);

        return mercator;
    }

    //路线导航
    private void addPoint() {
        //绘制定位点
        // 正确的写法
//        Vector2D lonlat1 = new Vector2D(lon,lat);
//        //Vector2D lonlat1 = new Vector2D(111.02637422544808,21.826948349312186);
//        Vector2D mecator1 = lonLatToMercator(lonlat1);
//        double location_x = mecator1.getX();
//        double location_y = mecator1.getY();
        Point location_point = new Point(lon, lat);
        //旧的点样式
        SimpleMarkerSymbol simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.YELLOW, 18);

        Graphic graphic1 = new Graphic(location_point, simpleMarkerSymbol1);
        //清除上一个点
        pointGraphicsOverlay.getGraphics().clear();
        pointGraphicsOverlay.getGraphics().add(graphic1);
        DrawGeometryTool drawGeometryTool = new DrawGeometryTool(this, mapView);
        System.out.println("执行了addPoint...");
        System.out.println("线路导航获取当前点的类型" + sampleType);
        System.out.println(sampleType);
        if (sampleType.equals("表层样品")) {
            System.out.println("线路导航画表层样品点");
            drawGeometryTool.getCircle(mapView, location_point, 0.002);
        } else {
            System.out.println("线路导航画剖面样品点");
            drawGeometryTool.getCircle(mapView, location_point, 0.02);
        }
        //mapView移动到当前的点位
        mapView.setViewpoint(new Viewpoint(location_point, 27977.57437667137));

    }

    //  路线导航
    private void addPoint_plus() {
        //绘制定位点
        // 正确的写法
        Log.d("测试", "执行了");
        for (int i = 0; i < pointList_final.size(); i++) {
            //获取jsonObject
            JSONObject jsonObject = pointList_final.getJSONObject(i);
            String lon1 = jsonObject.getString("lon");
            String lat1 = jsonObject.getString("lat");
            //点击事件弹窗所需的属性
            String code_1 = jsonObject.getString("code");
            String sampleType_1 = jsonObject.getString("sampleType");
            String samplingType_1 = jsonObject.getString("samplingType");
            String location_1 = jsonObject.getString("location");

            Double lon_d = Double.parseDouble(lon1);
            Double lat_d = Double.parseDouble(lat1);
//            Vector2D lonlat1 = new Vector2D(lon_d, lat_d);
//            Vector2D mecator1 = lonLatToMercator(lonlat1);
//            double location_x = mecator1.getX();
//            double location_y = mecator1.getY();
//            Point location_point = new Point(location_x,location_y);
            Point location_point = new Point(lon_d, lat_d);

            //获取状态以控制颜色,3为未调查黄色，其余为已调查红色
            String zt = jsonObject.getString("zt");
            String cygc = jsonObject.getString("cygc");
            String sfsjptzg = jsonObject.getString("sfsjptzg");

            Log.i("xxx", cygc);
            Log.i("xxx", sfsjptzg);
            int ztValue = Integer.parseInt(zt);

            SimpleMarkerSymbol simpleMarkerSymbol1 = null;
            if (ztValue>3&&ztValue<7) {
                simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.GREEN, 18);
            }  else  if (ztValue == 7) {
                simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.BLUE, 18);
            }else  if (ztValue == 8) {
                simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.rgb(0 ,205 ,0	), 18);
            }
            else {
                simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.YELLOW, 18);

//                if (Objects.equals(cygc, "1")) {
//                    simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.GREEN, 18);
//                } else {
//                    System.out.println(code_1);
//                    System.out.println(current_point);
//                    if (Objects.equals(code_1, current_point)) {
//                        simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.BLUE, 18);
//                        DrawGeometryTool drawGeometryTool = new DrawGeometryTool(this, mapView);
//                        System.out.println("执行了addPoint_plus1...");
//                        System.out.println("信息采集获取当前点的类型" + sampleType_1);
//                        System.out.println(sampleType_1);
//                        if (sampleType_1.equals("表层样品")) {
//                            System.out.println("信息采集画表层样品点");
//                            drawGeometryTool.getCircle(mapView, location_point, 0.002);
//                        } else {
//                            System.out.println("信息采集画剖面样品点");
//                            drawGeometryTool.getCircle(mapView, location_point, 0.02);
//                        }
//
//                        Viewpoint viewpoint = new Viewpoint(location_point, 50000);
//                        mapView.setViewpointCenterAsync(location_point, 50000);
//
//                    } else {
//                        simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.YELLOW, 18);
//                    }
//
//                }
            }
            //将传递的pointlist的数据传递给attributes
            final Map<String, Object> attributes = new TreeMap<>();
            attributes.put("code", code_1);
            attributes.put("sampleType", sampleType_1);
            attributes.put("samplingType", samplingType_1);
            attributes.put("location", location_1);
            attributes.put("cygc", cygc);
            attributes.put("sfsjptzg", sfsjptzg);
            //创建Graphic
            Graphic graphic1 = new Graphic(location_point, attributes, simpleMarkerSymbol1);

            TextSymbol bassRockSymbol = new TextSymbol(10, code_1.substring(11,16), Color.RED, TextSymbol.HorizontalAlignment.LEFT, TextSymbol.VerticalAlignment.BOTTOM);
            bassRockSymbol.setHaloColor( Color.rgb(255,255,255));
            bassRockSymbol.setHaloWidth(2);
            Graphic graphic2 = new Graphic(location_point, attributes, bassRockSymbol);


            //print一下attr
            System.out.println(graphic1.getAttributes());
            //不能清除上一个点.......
            //pointGraphicsOverlay.getGraphics().clear();
            pointGraphicsOverlay.getGraphics().add(graphic1);
            textGraphicsOverlay.getGraphics().add(graphic2);
        }
        pointList_final.clear();
    }

    //信息采集
    private void addPoint_plus1() {
        // 正确的写法
        for (int i = 0; i < pointList_final.size(); i++) {
            //获取jsonObject
            JSONObject jsonObject = pointList_final.getJSONObject(i);
            String lon1 = jsonObject.getString("lon");
            String lat1 = jsonObject.getString("lat");
            //点击事件弹窗所需的属性
            String code_1 = jsonObject.getString("code");
            String sampleType_1 = jsonObject.getString("sampleType");
            String samplingType_1 = jsonObject.getString("samplingType");
            String location_1 = jsonObject.getString("location");

            Double lon_d = Double.parseDouble(lon1);
            Double lat_d = Double.parseDouble(lat1);
//            Vector2D lonlat1 = new Vector2D(lon_d, lat_d);
//            Vector2D mecator1 = lonLatToMercator(lonlat1);
//            double location_x = mecator1.getX();
//            double location_y = mecator1.getY();
            Point location_point = new Point(lon_d, lat_d);
            //获取状态以控制颜色,3为未调查黄色，其余为已调查红色
            String zt = jsonObject.getString("zt");
            String cygc = jsonObject.getString("cygc");
            String sfsjptzg = jsonObject.getString("sfsjptzg");
            Log.i("xxx", cygc);
            int ztValue = Integer.parseInt(zt);
            SimpleMarkerSymbol simpleMarkerSymbol1;
            if (ztValue>3&&ztValue<7) {
                simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.GREEN, 18);
            }  else  if (ztValue == 7) {
                simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.BLUE, 18);
            }else  if (ztValue == 8) {
                simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.LTGRAY, 18);
            }
            else {
                simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.YELLOW, 18);

//                if (Objects.equals(cygc, "1")) {
//                    simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.GREEN, 18);
//                } else {
//                    System.out.println(code_1);
//                    System.out.println(current_point);
//                    if (Objects.equals(code_1, current_point)) {
//                        simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.BLUE, 18);
//                        DrawGeometryTool drawGeometryTool = new DrawGeometryTool(this, mapView);
//                        System.out.println("执行了addPoint_plus1...");
//                        System.out.println("信息采集获取当前点的类型" + sampleType_1);
//                        System.out.println(sampleType_1);
//                        if (sampleType_1.equals("表层样品")) {
//                            System.out.println("信息采集画表层样品点");
//                            drawGeometryTool.getCircle(mapView, location_point, 0.002);
//                        } else {
//                            System.out.println("信息采集画剖面样品点");
//                            drawGeometryTool.getCircle(mapView, location_point, 0.02);
//                        }
//
//                        Viewpoint viewpoint = new Viewpoint(location_point, 50000);
//                        mapView.setViewpointCenterAsync(location_point, 50000);
//
//                    } else {
//                        simpleMarkerSymbol1 = new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.YELLOW, 18);
//                    }
//
//                }
            }
            //将传递的pointlist的数据传递给attributes
            final Map<String, Object> attributes = new TreeMap<>();
            attributes.put("code", code_1);
            attributes.put("sampleType", sampleType_1);
            attributes.put("samplingType", samplingType_1);
            attributes.put("location", location_1);
            attributes.put("cygc", cygc);
            attributes.put("sfsjptzg", sfsjptzg);
            //创建Graphic
            Graphic graphic1 = new Graphic(location_point, attributes, simpleMarkerSymbol1);


            //print一下attr
            System.out.println(graphic1.getAttributes());
            //不能清除上一个点.......
            //pointGraphicsOverlay.getGraphics().clear();
            pointGraphicsOverlay.getGraphics().add(graphic1);
        }
        pointList_final.clear();
    }

    /* @setNeutralButton 设置中间的按钮
     * 若只需一个按钮，仅设置 setPositiveButton 即可
     */


    private void showMultiBtnDialog(String code_1, String sampleType_1, String samplingType_1, String location_1) {
//        Intent intent = new Intent();
//        intent.putExtra("code", code_1);
//        setResult(TestModule.REQUEST_CODE, intent);
//        finish();
        //构建弹窗
//        AlertDialog.Builder normalDialog =
//                new AlertDialog.Builder(MainActivity.this);
//        final View pointInfoDia = View.inflate(this,R.layout.point_info_dialog,null);
//        //将获取到的数据填入到视图中
//        TextView pointCode = pointInfoDia.findViewById(R.id.pointCode);
//        pointCode.setText(code_1);
//        TextView pointSamplingType = pointInfoDia.findViewById(R.id.pointSamplingType);
//        pointSamplingType.setText(samplingType_1);
//        TextView pointSampleType = pointInfoDia.findViewById(R.id.pointSampleType);
//        pointSampleType.setText(sampleType_1);
//        TextView pointLocation = pointInfoDia.findViewById(R.id.pointLocation);
//        pointLocation.setText(location_1);
//        normalDialog.setTitle("样点详情").setView(pointInfoDia);
//        normalDialog.setPositiveButton("预填报信息",
//                new DialogInterface.OnClickListener() {
//                    @Override
//                    public void onClick(DialogInterface dialog, int which) {
//                        Intent intent = new Intent();
//                        intent.putExtra("respond", "0");
//                        setResult(TestModule.REQUEST_CODE, intent);
//                        finish();
//                    }
//                });
//        normalDialog.setNeutralButton("到这里去",
//                new DialogInterface.OnClickListener() {
//                    @Override
//                    public void onClick(DialogInterface dialog, int which) {
//                        // ...To-do
//                    }
//                });
//        normalDialog.setNegativeButton("上传补充照片", new DialogInterface.OnClickListener() {
//            @Override
//            public void onClick(DialogInterface dialog, int which) {
//                Intent intent = new Intent();
//                intent.putExtra("respond", "1");
//                setResult(TestModule.REQUEST_CODE, intent);
//                finish();
//            }
//        });
//        // 创建实例并显示
//        normalDialog.show();

    }

    //这句是在说明，可能会与点击事件有冲突
    @SuppressLint("ClickableViewAccessibility")
    private void identify() {
        //点击地图时，判断下拉框是否存在，存在则关闭，不存在则identify
        mapView.setOnTouchListener(new DefaultMapViewOnTouchListener(this, mapView) {
            @Override
            public boolean onSingleTapConfirmed(MotionEvent v) {
                if (mBottomSheetBehavior.getState() == BottomSheetBehavior.STATE_EXPANDED) {
                    mBottomSheetBehavior.setState(BottomSheetBehavior.STATE_HIDDEN);
                } else {
                    //设定一开始进入的时候选择最高层级的图层
                    current_layer_name = LayerName.get(0).toString();
                    //获取点选的点位
                    android.graphics.Point screenPoint = new android.graphics.Point(Math.round(v.getX()), Math.round(v.getY()));
                    final Point clickPoint = mapView.screenToLocation(screenPoint);//
                    //IdentifyFeature identifyFeature=new IdentifyFeature();
                    //identifyFeature.identifyResult(screenPoint,mapView);
                    //获取spinner所在的layout的样式
                    LayoutInflater inflater = LayoutInflater
                            .from(MainActivity.this);
                    View view2 = inflater.inflate(R.layout.calloutdisplay, null);
                    TextView calloutContent = (TextView) view2.findViewById(R.id.identifyContent);

                    //退出按钮
                    Button button = (Button) view2.findViewById(R.id.callexit);
                    button.setOnClickListener(view1 -> callExit());
                    //实例化spinner控件
                    // get buttons from layouts
                    spner = view2.findViewById(R.id.spinner3);
                    //将图层名称数组写入spinner中
                    spner.setPrompt("请选择属性查询的图层:");
                    ArrayAdapter eduAdapter = new ArrayAdapter(MainActivity.this, android.R.layout.simple_spinner_item, LayerName);
                    eduAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
                    spner.setAdapter(eduAdapter);
                    //获取选中的图层的序号
                    spner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                        @Override
                        public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                            //保留图层的序号
                            System.out.println("12345678");
                            layer_index = i;
                            //通过图层的序号，获取当前选择的图层名称
                            current_layer_name = LayerName.get(layer_index).toString();
                            System.out.println(current_layer_name);

                            //再进行一次identify
                            //FeatureLayer从GroupLayer中获取,仅获取了一个图层，这里可能需要做图层筛选。
                            //由于上下移动的原因，图层的layer_index会变化，而这里没有变，所以需要重新筛选与控制
                            for (int j = 0; j < LayerName.size(); j++) {
                                if (Objects.equals(LayerName.get(j), current_layer_name)) {
                                    current_layer_index = j;
                                }
                            }
                            //通过get后面的数字控制
                            Log.d("图层的长度", String.valueOf(LayerName.size()));
                            Log.d("识别的图层--名称", current_layer_name);
                            Log.d("识别的图层--序列", String.valueOf(current_layer_index));
                            FeatureLayer featureLayer = (FeatureLayer) projectAreaGroupLayer.getLayers().get(current_layer_index);
                            //判断图层的显隐性
                            if (!featureLayer.isVisible()) {
                                Toast.makeText(MainActivity.this, "图层尚未显示，无法进行属性查询，请在图层树勾选后再点击", Toast.LENGTH_SHORT).show();
                                if (mCallout != null) {
                                    mCallout.dismiss();
                                }
                            }
                            //如果图层显示的话，就进行属性查询
                            else {
                                //                        FeatureLayer featureLayer=(FeatureLayer) mMapView.getMap().getOperationalLayers().get(0);
                                FeatureTable mTable = featureLayer.getFeatureTable();//得到查询属性表
                                //获取点选的图层的图层类型，若为点类型，则展示导航按钮
                                String GeometryType = mTable.getGeometryType().toString();
                                //获取字段的别名
                                //                        System.out.println("字段的别名是："+mTable.getFields().get(1).getAlias().toString());
                                System.out.println(GeometryType);
                                //展现导航按钮

                                //获取经纬度，并在该点位添加一个点位图标
                                int tolerance = 10;
                                double mapTolerance = tolerance * mapView.getUnitsPerDensityIndependentPixel();
                                Envelope envelope = new Envelope(clickPoint.getX() - mapTolerance, clickPoint.getY() - mapTolerance,
                                        clickPoint.getX() + mapTolerance, clickPoint.getY() + mapTolerance, mapView.getSpatialReference());
                                QueryParameters query = new QueryParameters();
                                query.setGeometry(envelope);// 设置查询范围
                                final ListenableFuture<FeatureQueryResult> featureQueryResultFuture = featureLayer.selectFeaturesAsync(query, FeatureLayer.SelectionMode.NEW);
                                mCallout = mapView.getCallout();
                                callout = mMapView.getCallout();
                                featureQueryResultFuture.addDoneListener(new Runnable() {
                                    @Override
                                    public void run() {
                                        try {
                                            FeatureQueryResult featureQueryResult2 = featureQueryResultFuture.get();
                                            // create a textview to display field values
                                            calloutContent.setText("");
                                            calloutContent.setTextColor(Color.BLACK);
                                            calloutContent.setSingleLine(false);
                                            calloutContent.setVerticalScrollBarEnabled(true);
                                            calloutContent.setScrollBarStyle(View.SCROLLBARS_INSIDE_INSET);
                                            calloutContent.setMovementMethod(new ScrollingMovementMethod());
                                            calloutContent.setLines(5);
                                            Iterator<Feature> iterator = featureQueryResult2.iterator();
                                            //字段名是一致的
//                                    feature = iterator.next();
                                            //存放字段别名
                                            List<String> alias_field_name_List = new ArrayList<>();
                                            //存放字段名称
                                            List<String> field_name_List = new ArrayList<>();
                                            //identify中获取别名
                                            while (iterator.hasNext()) {
                                                feature = iterator.next();
                                                //这个方法获取的是正式名称，是英文。
                                                FeatureTable featureTable = feature.getFeatureTable();
                                                System.out.println("图层的原始名称名称为：" + feature.getFeatureTable().getTableName().toString());
                                                //统计字段的数量
                                                System.out.println(featureTable.getFields().size());
                                                for (int i = 0; i < featureTable.getFields().size(); i++) {
                                                    //别名
                                                    String alias_field_name = featureTable.getFields().get(i).getAlias().toString();
                                                    alias_field_name_List.add(alias_field_name);
                                                    //名称
                                                    String field_name = featureTable.getFields().get(i).getName().toString();
                                                    field_name_List.add(field_name);
                                                }

                                                // create a Map of all available attributes as name value pairs

                                                Map<String, Object> attr = feature.getAttributes();
//                                        Set<String> keys = attr.keySet();
                                                for (int j = 0; j < field_name_List.size(); j++) {
                                                    Object value = attr.get(field_name_List.get(j));
                                                    calloutContent.append(alias_field_name_List.get(j) + "：" + value.toString() + "\n");
                                                }
                                                // center the mapview on selected feature
//                                                Envelope envelope = feature.getGeometry().getExtent();
//                                                mapView.setViewpointGeometryAsync(envelope, 200);
                                                // show callout

                                                callout.show(view2, envelope.getCenter());
                                                //原生的mcallout
//                                        mCallout.setLocation(envelope.getCenter());
//                                        mCallout.setContent(calloutContent);
//                                        mCallout.show();
                                            }
                                        } catch (Exception e1) {
                                            String error = "Select feature failed: " + e1.getMessage();
                                            Toast.makeText(MainActivity.this, error, Toast.LENGTH_LONG).show();

                                        }
                                    }
                                });
                                System.out.println("可以进行离线导航1");
                                if (GeometryType.equals("POINT")) {
                                    System.out.println("可以进行离线导航2");
//                                    navigationButton.setVisibility(View.VISIBLE);
                                    yd_point = clickPoint;
                                    //转换点选的样点的坐标
                                    double x = yd_point.getX();
                                    double y = yd_point.getY();
                                    Vector2D mecator = new Vector2D(x, y);
                                    Vector2D lonlat = MercatorTolonLat(mecator);
                                    double lon = lonlat.getX();
                                    double lat = lonlat.getY();
                                    //获取了点选的点的经纬度
                                    System.out.println(lon);
                                    System.out.println(lat);

                                    //与location定位一齐计算距离，并判断是否在200m内
                                    Double distance = getDistance(lon, lat, location_longitude, location_latitude);

                                    //TODO:这里识别点图层的时候会计算距离，先注释掉
//                                    if (distance > 200)
//                                    {
//                                        //弹窗
//                                        AlertDialog alertDialog = new AlertDialog.Builder(MainActivity.this)
//                                                .setTitle("定位不在样点200米范围内")
//                                                .setPositiveButton("确定", new DialogInterface.OnClickListener() {//添加"Yes"按钮
//                                                    @Override
//                                                    public void onClick(DialogInterface dialogInterface, int i) {
//                                                        Toast.makeText(MainActivity.this, "定位不在样点200米范围内", Toast.LENGTH_SHORT).show();
//                                                    }
//                                                })
//                                                .create();
//                                        //防止点击弹出框外的地方关闭弹出框
//                                        alertDialog.setCanceledOnTouchOutside(false);
//                                        alertDialog.show();
//                                    }
//                                    else
//                                    {
//                                        Toast.makeText(MainActivity.this, "定位在样点200米范围内,可以进行样点信息的填报", Toast.LENGTH_SHORT).show();
//                                    }
                                }
                            }
//
                        }

                        @Override
                        public void onNothingSelected(AdapterView<?> adapterView) {
                            Toast.makeText(MainActivity.this, "请选择图层！", Toast.LENGTH_LONG);
                        }
                    });

                    //FeatureLayer从GroupLayer中获取,仅获取了一个图层，这里可能需要做图层筛选。
                    //由于上下移动的原因，图层的layer_index会变化，而这里没有变，所以需要重新筛选与控制
                    for (int i = 0; i < LayerName.size(); i++) {
                        if (Objects.equals(LayerName.get(i), current_layer_name)) {
                            current_layer_index = i;
                        }
                    }
                    //通过get后面的数字控制
                    Log.d("图层的长度", String.valueOf(LayerName.size()));
                    Log.d("识别的图层--名称", current_layer_name);
                    Log.d("识别的图层--序列", String.valueOf(current_layer_index));
                    FeatureLayer featureLayer = (FeatureLayer) projectAreaGroupLayer.getLayers().get(current_layer_index);
                    //判断图层的显隐性
                    if (!featureLayer.isVisible()) {
                        Toast.makeText(MainActivity.this, "图层尚未显示，无法进行属性查询，请在图层树勾选后再点击", Toast.LENGTH_SHORT).show();
                        if (mCallout != null) {
                            mCallout.dismiss();
                        }
                    }
                    //如果图层显示的话，就进行属性查询
                    else {
//                        FeatureLayer featureLayer=(FeatureLayer) mMapView.getMap().getOperationalLayers().get(0);
                        FeatureTable mTable = featureLayer.getFeatureTable();//得到查询属性表
                        //获取点选的图层的图层类型，若为点类型，则展示导航按钮
                        String GeometryType = mTable.getGeometryType().toString();
                        //获取字段的别名
//                        System.out.println("字段的别名是："+mTable.getFields().get(1).getAlias().toString());
                        System.out.println(GeometryType);
                        //展现导航按钮

                        //获取经纬度，并在该点位添加一个点位图标
                        double tolerance = 0.001;
                        double mapTolerance = tolerance * mapView.getUnitsPerDensityIndependentPixel();
                        Envelope envelope = new Envelope(clickPoint.getX() - mapTolerance, clickPoint.getY() - mapTolerance,
                                clickPoint.getX() + mapTolerance, clickPoint.getY() + mapTolerance, mapView.getSpatialReference());
                        QueryParameters query = new QueryParameters();
                        query.setGeometry(envelope);// 设置查询范围
                        final ListenableFuture<FeatureQueryResult> featureQueryResultFuture = featureLayer.selectFeaturesAsync(query, FeatureLayer.SelectionMode.NEW);
                        mCallout = mapView.getCallout();
                        callout = mMapView.getCallout();
                        featureQueryResultFuture.addDoneListener(new Runnable() {
                            @Override
                            public void run() {
                                try {
                                    FeatureQueryResult featureQueryResult2 = featureQueryResultFuture.get();
                                    // create a textview to display field values
//                                    TextView calloutContent = view2.findViewById(R.id.identifyContent);
                                    calloutContent.setText("");
                                    calloutContent.setTextColor(Color.BLACK);
                                    calloutContent.setSingleLine(false);
                                    calloutContent.setVerticalScrollBarEnabled(true);
                                    calloutContent.setScrollBarStyle(View.SCROLLBARS_INSIDE_INSET);
                                    calloutContent.setMovementMethod(new ScrollingMovementMethod());
                                    calloutContent.setLines(5);
                                    Iterator<Feature> iterator = featureQueryResult2.iterator();
                                    //字段名是一致的
//                                    feature = iterator.next();
                                    //存放字段别名
                                    List<String> alias_field_name_List = new ArrayList<>();
                                    //存放字段名称
                                    List<String> field_name_List = new ArrayList<>();
                                    //identify中获取别名
                                    while (iterator.hasNext()) {
                                        feature = iterator.next();
                                        //这个方法获取的是正式名称，是英文。
                                        FeatureTable featureTable = feature.getFeatureTable();
                                        System.out.println("图层的原始名称名称为：" + feature.getFeatureTable().getTableName().toString());
                                        //统计字段的数量
                                        System.out.println(featureTable.getFields().size());
                                        for (int i = 0; i < featureTable.getFields().size(); i++) {
                                            //别名
                                            String alias_field_name = featureTable.getFields().get(i).getAlias().toString();
                                            alias_field_name_List.add(alias_field_name);
                                            //名称
                                            String field_name = featureTable.getFields().get(i).getName().toString();
                                            field_name_List.add(field_name);
                                        }

                                        // create a Map of all available attributes as name value pairs

                                        Map<String, Object> attr = feature.getAttributes();
//                                        Set<String> keys = attr.keySet();
                                        System.out.println("故障：" + field_name_List.size());
                                        for (int j = 0; j < field_name_List.size(); j++) {
                                            Object value = attr.get(field_name_List.get(j));
                                            calloutContent.append(alias_field_name_List.get(j) + "：" + value.toString() + "\n");
                                        }
                                        // center the mapview on selected feature
//                                        Envelope envelope = feature.getGeometry().getExtent();
//                                        mapView.setViewpointGeometryAsync(envelope, 10);
                                        // show callout
                                        callout.show(view2, envelope.getCenter());
                                        //原生的mcallout
//                                        mCallout.setLocation(envelope.getCenter());
//                                        mCallout.setContent(calloutContent);
//                                        mCallout.show();
                                    }
                                } catch (Exception e1) {
                                    String error = "Select feature failed: " + e1.getMessage();
                                    Toast.makeText(MainActivity.this, error, Toast.LENGTH_LONG).show();

                                }
                            }
                        });
                        System.out.println("可以进行离线导航1");
                        if (GeometryType.equals("POINT")) {
                            System.out.println("可以进行离线导航2");

//                            navigationButton.setVisibility(View.VISIBLE);
                            yd_point = clickPoint;
                            //转换点选的样点的坐标
                            double x = yd_point.getX();
                            double y = yd_point.getY();
                            Vector2D mecator = new Vector2D(x, y);
                            Vector2D lonlat = MercatorTolonLat(mecator);
                            double lon = lonlat.getX();
                            double lat = lonlat.getY();
                            //获取了点选的点的经纬度
                            System.out.println(lon);
                            System.out.println(lat);

                            //与location定位一齐计算距离，并判断是否在200m内
                            Double distance = getDistance(lon, lat, location_longitude, location_latitude);
                            if (distance > 200) {
                                //弹窗
                                AlertDialog alertDialog = new AlertDialog.Builder(MainActivity.this)
                                        .setTitle("定位不在样点200米范围内")
                                        .setPositiveButton("确定", new DialogInterface.OnClickListener() {//添加"Yes"按钮
                                            @Override
                                            public void onClick(DialogInterface dialogInterface, int i) {
                                                Toast.makeText(MainActivity.this, "定位不在样点200米范围内", Toast.LENGTH_SHORT).show();
                                            }
                                        })
                                        .create();
                                //防止点击弹出框外的地方关闭弹出框
                                alertDialog.setCanceledOnTouchOutside(false);
                                alertDialog.show();
                            } else {
                                Toast.makeText(MainActivity.this, "定位在样点200米范围内,可以进行样点信息的填报", Toast.LENGTH_SHORT).show();
                            }
                        }
                    }
//
                }
                return true;
            }
        });
    }

    //关闭identify功能
    @SuppressLint("ClickableViewAccessibility")
    private void stop_identify() {
        //需要清楚identify的气泡框
        if (mCallout != null) {
            mCallout.dismiss();
        }
        mapView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent motionEvent) {
                System.out.println("identify功能关闭！");
                return false;
            }
        });
    }

    @SuppressLint("ClickableViewAccessibility")
    //样点弹窗展示的方法
    private void open_attr() {
        System.out.println("我进来了11111111");

        //需要清楚identify的气泡框
        if (mCallout != null) {
            mCallout.dismiss();
        }
        //dzwlGraphicsOverlay=new GraphicsOverlay();
        //mapView.getGraphicsOverlays().add(dzwlGraphicsOverlay);


        mapView.setOnTouchListener(new DefaultMapViewOnTouchListener(this, mapView) {
            @Override
            public boolean onSingleTapConfirmed(MotionEvent v) {

//                if (identify_index == 0)
//                {
//                    System.out.println("bsm1");
//                    if (mBottomSheetBehavior.getState() == BottomSheetBehavior.STATE_EXPANDED) {
//                        mBottomSheetBehavior.setState(BottomSheetBehavior.STATE_HIDDEN);
//                    } else {
//                        android.graphics.Point screenPoint = new android.graphics.Point(Math.round(v.getX()), Math.round(v.getY()));
//                        final Point clickPoint2 = mapView.screenToLocation(screenPoint);
//
//                        //转成经纬度
//                        double x = clickPoint2.getX();
//                        double y = clickPoint2.getY();
//                        Vector2D mecator = new Vector2D(x,y);
//                        Vector2D lonlat = MercatorTolonLat(mecator);
//                        double lon = lonlat.getX();
//                        double lat = lonlat.getY();
//                        //获取了点选的点的经纬度
//                        System.out.println("经度："+lon);
//                        System.out.println("纬度："+lat);
//                        //
//                        //FeatureLayer从GroupLayer中获取,仅获取了一个图层，这里可能需要做图层筛选。
//                        //由于上下移动的原因，图层的layer_index会变化，而这里没有变，所以需要重新筛选与控制
//                        for (int i = 0; i < LayerName.size(); i++) {
//                            if (Objects.equals(LayerName.get(i), "样点数据")) {
//                                current_layer_index = i;
//                            }
//                        }
//                        //通过get后面的数字控制
//                        Log.d("图层的长度", String.valueOf(LayerName.size()));
//                        Log.d("识别的图层--名称", "样点数据");
//                        Log.d("识别的图层--序列", String.valueOf(current_layer_index));
//
//                        //首先获取到样点数据的图层与外业调查人员点击的点的位置。
//                        FeatureLayer featureLayer = (FeatureLayer) projectAreaGroupLayer.getLayers().get(current_layer_index);
//
//                        //空间查询的参数
//                        int tolerance = 10;
//                        double mapTolerance = tolerance * mapView.getUnitsPerDensityIndependentPixel();
//                        Envelope envelope = new Envelope(clickPoint2.getX() - mapTolerance, clickPoint2.getY() - mapTolerance,
//                                clickPoint2.getX() + mapTolerance, clickPoint2.getY() + mapTolerance, mapView.getSpatialReference());
//                        QueryParameters query = new QueryParameters();
//                        query.setGeometry(envelope);// 设置查询范围
//
//                        final ListenableFuture<FeatureQueryResult> featureQueryResultFuture = featureLayer.selectFeaturesAsync(query, FeatureLayer.SelectionMode.NEW);
//
//                        featureQueryResultFuture.addDoneListener(new Runnable() {
//                            @Override
//                            public void run() {
//                                FeatureQueryResult featureQueryResult2 = null;
//                                try {
//                                    featureQueryResult2 = featureQueryResultFuture.get();
//                                } catch (ExecutionException e) {
//                                    e.printStackTrace();
//                                } catch (InterruptedException e) {
//                                    e.printStackTrace();
//                                }
//                                Iterator<Feature> iterator = featureQueryResult2.iterator();
//                                Feature point_feature = iterator.next();
//                                if(point_feature != null)
//                                {
//                                    Map<String, Object> attr = point_feature.getAttributes();
//                                    Set<String> keys = attr.keySet();
//                                    for (String key : keys) {
//                                        System.out.println(key);
//                                        if (Objects.equals(key, "OBJECTID"))
//                                        {
//                                            Object value = attr.get(key);
//                                            String point_feature_id = value.toString();
//                                            System.out.println(point_feature_id);
//                                            //这里写死，等待串通，id需要等下自己识别
//                                            if (point_feature_id.equals("1363"))
//                                            {
//                                                System.out.println("开始弹出弹窗");
//                                                //弹窗方法
//                                                showMultiBtnDialog();
//                                            }
//                                        }
//                                    }
//                                }
//                            }
//                        });
//                    }
//                }
                // 获取用户点击的屏幕点
                if (lon == 0) {
                    android.graphics.Point screenPoint = new android.graphics.Point((int) v.getX(), (int) v.getY());
                    // 识别GraphicsOverlay中的Graphic
                    final ListenableFuture<IdentifyGraphicsOverlayResult> identifyGraphic = mMapView.identifyGraphicsOverlayAsync(pointGraphicsOverlay, screenPoint, 10.0, false, 2);
                    identifyGraphic.addDoneListener(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                //Toast.makeText(MainActivity.this, "该样点为已调查样点，无法重复填报！", Toast.LENGTH_LONG).show();
                                IdentifyGraphicsOverlayResult grOverlayResult = identifyGraphic.get();
                                // 获取识别的GraphicOverlay返回的图形列表
                                List<Graphic> graphic = grOverlayResult.getGraphics();
                                if (!graphic.isEmpty()) {
                                    Graphic chosen_graphic = grOverlayResult.getGraphics().get(0);
                                    System.out.println(chosen_graphic.getAttributes());
                                    //分别获取数值，并打开弹窗
                                    Map<String, Object> attributes = chosen_graphic.getAttributes();
                                    String sampleType1 = attributes.get("sampleType").toString();
                                    String code_1 = attributes.get("code").toString();
//                                    String sampleType_1 = attributes.get("sampleType").toString();
//                                    String samplingType_1 = attributes.get("samplingType").toString();
//                                    String location = attributes.get("location").toString();
//                                    showMultiBtnDialog(code_1, sampleType_1, samplingType_1, location);
                                    String cygc_1 = attributes.get("cygc").toString();
                                    String sfsjptzg_1 = attributes.get("sfsjptzg").toString();
                                    //String cygc = attributes.get("cygc").toString();
                                    if (Objects.equals(cygc_1, "1") && !checkNet(MainActivity.this)) {
                                        Toast.makeText(MainActivity.this, "离线模式下不支持查看已上报样点信息", Toast.LENGTH_LONG).show();
                                        return;
                                    }
                                    Point location_point = (Point) chosen_graphic.getGeometry();
                                    DrawGeometryTool drawGeometryTool = new DrawGeometryTool(MainActivity.this, mapView);

                                    System.out.println("执行了open_attr...");
                                    System.out.println("用户点击屏幕监听");
                                    // 不一定点击到样点，此时类型为null，需做处理
                                    System.out.println("点击获取当前点的类型" + sampleType1);
                                    System.out.println(sampleType1);

                                    if (sampleType1.equals("表层样品")) {
                                        System.out.println("点击画表层样品点");
                                        drawGeometryTool.getCircle(mapView, location_point, 0.002);
                                    } else {
                                        System.out.println("点击画剖面样品点");
                                        drawGeometryTool.getCircle(mapView, location_point, 0.02);
                                    }
                                    Viewpoint viewpoint = new Viewpoint(location_point, 50000);
                                    mapView.setViewpointCenterAsync(location_point, 50000);


                                    //clickstate++;
                                    //if(clickstate%2==1){
                                    //    clickcode=code_1;
                                    //}
//                                    if (Objects.equals(cygc_1,"1") && Objects.equals(sfsjptzg_1,"0"))
//                                    {
//                                        Toast.makeText(MainActivity.this, "该样点为已调查样点，无法重复填报！", Toast.LENGTH_LONG).show();
//                                    }
//                                    else{
//                                        String url = "file:///android_asset/apps/HBuilderUniApp/www/index.html"; // Uniapp 入口页面的路径
//                                        WebView webView = new WebView(context);
//                                        WebSettings settings = webView.getSettings();
//                                        settings.setCacheMode(WebSettings.LOAD_NO_CACHE); // 禁用缓存
//                                        webView.loadUrl(url);
                                    Log.d("条件判断", clickcode + "");
                                    Log.d("条件判断", (clickstate == 2 && clickcode == code_1) + "");
                                    if (clickcode == code_1) {
//                                        Log.e("跳转编码确认", code_1);
//                                        Intent intent = new Intent();
//                                        Log.d("code_1是否有值", code_1);
//                                        intent.putExtra("code", code_1);
//                                        //Toast.makeText(MainActivity.this, "作业底图跳转前"+code_1, Toast.LENGTH_LONG).show();
//                                        //TestModule.hasResult=false;
//                                        Log.d("offModel", offModel);
//                                        Log.d("offModel判断结果", Objects.equals(offModel, "0") + "");
//                                        if (Objects.equals(offModel, "0")) {
////                                            setResult(TestModule.REQUEST_CODE_LON_ZERO, intent);
//                                        }
//                                        if (Objects.equals(offModel, "1")) {
////                                            setResult(TestModule.REQUEST_CODE_LON_ZERO_OFFLINE, intent);
//                                        }
////                                        finish();
                                        final String[] items = {"目标点", "已调查"};
                                        AlertDialog.Builder builder = new AlertDialog.Builder(MainActivity.this);
                                        builder.setTitle("状态设置("+code_1+")");
                                        builder.setItems(items,
                                                new DialogInterface.OnClickListener() {
                                                    @Override
                                                    public void onClick(DialogInterface dialog, int which) {
                                                        // 列表项响应事件
                                                        switch(which){
                                                            case 0: //目标点
                                                                grOverlayResult.getGraphics().get(0).setSymbol(new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.BLUE, 18));
//                                                                TextSymbol bassRockSymbol = new TextSymbol(10, code_1.substring(11,16), Color.RED, TextSymbol.HorizontalAlignment.LEFT, TextSymbol.VerticalAlignment.BOTTOM);
//                                                                bassRockSymbol.setHaloColor( Color.rgb(255,255,255));
//                                                                bassRockSymbol.setHaloWidth(2);
//                                                                grOverlayResult.getGraphics().get(0).setSymbol(bassRockSymbol);
                                                                updateydstatus(code_1,"7");
                                                                break;
                                                            case 1: //已调查
//                                                                TextSymbol bassRockSymbol1 = new TextSymbol(10, code_1.substring(11,16), Color.RED, TextSymbol.HorizontalAlignment.LEFT, TextSymbol.VerticalAlignment.BOTTOM);
//                                                                bassRockSymbol1.setHaloColor( Color.rgb(255,255,255));
//                                                                bassRockSymbol1.setHaloWidth(2);
                                                                grOverlayResult.getGraphics().get(0).setSymbol(new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.rgb(0,205,0), 18));
                                                                updateydstatus(code_1,"8");
                                                                break;
                                                        }
                                                    }
                                                });
                                        builder.show();
                                        clickstate = 0;
                                    } else {
                                        clickcode = code_1;
                                    }
                                    //  }
                                }
//                                // 获取结果列表的大小
//                                int identifyResultSize = graphic.size();
//
//                                    // 当识别的图形成功返回时，返回一个Toast信息提示
//                                    Toast.makeText(getApplicationContext(), "Tapped on " + identifyResultSize + " Graphic", Toast.LENGTH_SHORT).show();
//                                }
                            } catch (InterruptedException | ExecutionException ie) {
                                ie.printStackTrace();
                            }
                        }
                    });
                }
                //突出点
                else if (lon == 1) {
                    android.graphics.Point screenPoint = new android.graphics.Point((int) v.getX(), (int) v.getY());
                    // 识别GraphicsOverlay中的Graphic
                    final ListenableFuture<IdentifyGraphicsOverlayResult> identifyGraphic = mMapView.identifyGraphicsOverlayAsync(pointGraphicsOverlay, screenPoint, 10.0, false, 2);
                    identifyGraphic.addDoneListener(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                IdentifyGraphicsOverlayResult grOverlayResult = identifyGraphic.get();
                                // 获取识别的GraphicOverlay返回的图形列表
                                List<Graphic> graphic = grOverlayResult.getGraphics();
                                if (!graphic.isEmpty()) {
                                    Graphic chosen_graphic = grOverlayResult.getGraphics().get(0);
                                    System.out.println(chosen_graphic.getAttributes());
                                    //分别获取数值，并打开弹窗
                                    Map<String, Object> attributes = chosen_graphic.getAttributes();
                                    String code_1 = attributes.get("code").toString();
//                                    String sampleType_1 = attributes.get("sampleType").toString();
//                                    String samplingType_1 = attributes.get("samplingType").toString();
//                                    String location = attributes.get("location").toString();
//                                    showMultiBtnDialog(code_1, sampleType_1, samplingType_1, location);
                                    String cygc_1 = attributes.get("cygc").toString();
                                    String sfsjptzg = attributes.get("sfsjptzg").toString();
                                    System.out.println("xxxx" + cygc_1);
                                    System.out.println("yyyy" + sfsjptzg);

//                                    if (Objects.equals(cygc_1,"1") && Objects.equals(sfsjptzg,"0"))
//                                    {
//                                        Toast.makeText(MainActivity.this, "该样点为已调查样点，无法重复填报！", Toast.LENGTH_LONG).show();
//                                    }
//                                    else{
                                    Intent intent = new Intent();
                                    intent.putExtra("code", code_1);
                                    //Toast.makeText(MainActivity.this, "信息采集跳转前"+TestModule.REQUEST_CODE_LON_ONE+","+code_1, Toast.LENGTH_LONG).show();
                                    //TestModule.hasResult=false;
                                    //setResult(TestModule.REQUEST_CODE_LON_ONE, intent);
                                    //finish();
                                    //
                                }
//                                // 获取结果列表的大小
//                                int identifyResultSize = graphic.size();
//
//                                    // 当识别的图形成功返回时，返回一个Toast信息提示
//                                    Toast.makeText(getApplicationContext(), "Tapped on " + identifyResultSize + " Graphic", Toast.LENGTH_SHORT).show();
//                                }
                            } catch (InterruptedException | ExecutionException ie) {
                                ie.printStackTrace();
                            }
                        }
                    });
                } else {
                    android.graphics.Point screenPoint = new android.graphics.Point((int) v.getX(), (int) v.getY());
                    // 识别GraphicsOverlay中的Graphic
                    final ListenableFuture<IdentifyGraphicsOverlayResult> identifyGraphic = mMapView.identifyGraphicsOverlayAsync(pointGraphicsOverlay, screenPoint, 10.0, false, 2);
                    identifyGraphic.addDoneListener(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                IdentifyGraphicsOverlayResult grOverlayResult = identifyGraphic.get();
                                // 获取识别的GraphicOverlay返回的图形列表
                                List<Graphic> graphic = grOverlayResult.getGraphics();
                                if (!graphic.isEmpty()) {
                                    Graphic chosen_graphic = grOverlayResult.getGraphics().get(0);
                                    System.out.println(chosen_graphic.getAttributes());
                                    //分别获取数值，并打开弹窗
                                    Map<String, Object> attributes = chosen_graphic.getAttributes();
//                                    String code_1 = attributes.get("code").toString();
//                                    String sampleType_1 = attributes.get("sampleType").toString();
//                                    String samplingType_1 = attributes.get("samplingType").toString();
//                                    String location = attributes.get("location").toString();
//                                    showMultiBtnDialog(code_1, sampleType_1, samplingType_1, location);

                                    Intent intent = new Intent();

                                    //final  Bundle bundle =new Bundle();
                                    //bundle.putString("code", code);
                                    //bundle.putString("tzstate", "1");
                                    intent.putExtra("code", code);
//                                    setResult(TestModule.REQUEST_CODE_LON_ELSE, intent);
                                    //TestModule.hasResult=false;
//                                    finish();
                                    // 定义需要传递的参数
                                    //Bundle extras = new Bundle();
                                    //extras.putString("from_native", "true");
                                    final String[] items = {"目标点", "已调查"};
                                    AlertDialog.Builder builder = new AlertDialog.Builder(MainActivity.this);
                                    builder.setTitle("状态设置("+code+")");
                                    builder.setItems(items,
                                            new DialogInterface.OnClickListener() {
                                                @Override
                                                public void onClick(DialogInterface dialog, int which) {
                                                    // 列表项响应事件
                                                    switch(which){
                                                        case 0: //目标点
                                                            grOverlayResult.getGraphics().get(0).setSymbol(new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.BLUE, 18));
//                                                                TextSymbol bassRockSymbol = new TextSymbol(10, code_1.substring(11,16), Color.RED, TextSymbol.HorizontalAlignment.LEFT, TextSymbol.VerticalAlignment.BOTTOM);
//                                                                bassRockSymbol.setHaloColor( Color.rgb(255,255,255));
//                                                                bassRockSymbol.setHaloWidth(2);
//                                                                grOverlayResult.getGraphics().get(0).setSymbol(bassRockSymbol);
                                                            updateydstatus(code,"7");
                                                            break;
                                                        case 1: //已调查
//                                                                TextSymbol bassRockSymbol1 = new TextSymbol(10, code_1.substring(11,16), Color.RED, TextSymbol.HorizontalAlignment.LEFT, TextSymbol.VerticalAlignment.BOTTOM);
//                                                                bassRockSymbol1.setHaloColor( Color.rgb(255,255,255));
//                                                                bassRockSymbol1.setHaloWidth(2);
                                                            grOverlayResult.getGraphics().get(0).setSymbol(new SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.rgb(0,205,0), 18));
                                                            updateydstatus(code,"8");
                                                            break;
                                                    }
                                                }
                                            });
                                    builder.show();
                                    clickstate = 0;
                                    Log.d("测试lon", "其它情况我也执行了");
                                    //NewPandoraEntry.startApp(this, "/pages/index/index", extras);


                                }
//                                // 获取结果列表的大小
//                                int identifyResultSize = graphic.size();
//
//                                    // 当识别的图形成功返回时，返回一个Toast信息提示
//                                    Toast.makeText(getApplicationContext(), "Tapped on " + identifyResultSize + " Graphic", Toast.LENGTH_SHORT).show();
//                                }
                            } catch (InterruptedException | ExecutionException ie) {
                                ie.printStackTrace();
                            }
                        }
                    });
////                    showMultiBtnDialog(code, sampleType, samplingType, location);
//                    Intent intent = new Intent();
//                    intent.putExtra("code", code);
//                    setResult(TestModule.REQUEST_CODE, intent);
//                    finish();
                }
                return true;
            }
        });
    }

    @Override
    protected void onPause() {
        super.onPause();
        //mapView.pause();
    }

    @Override
    protected void onResume() {
        super.onResume();
        mapView.resume();
    }

    @Override
    protected void onDestroy() {

        mapView.pause();
        //该activity销毁的时候清空Layer Name存储内容
        LayerName.clear();
        pointGraphicsOverlay.getGraphics().clear();//清空图层
        if (lon == 0) {
            Viewpoint viewpoint = mapView.getCurrentViewpoint(Viewpoint.Type.BOUNDING_GEOMETRY);
            Geometry geometry = viewpoint.getTargetGeometry();
            geometrystr = geometry.toJson();
        }
        super.onDestroy();
        mapView.dispose();
        System.gc();
    }

    //关闭图层管理的方法
    public void closeLayerAdapter() {
        mBottomSheetBehavior.setState(BottomSheetBehavior.STATE_HIDDEN);
    }

    //获取图层路径
    public String getSDPath(Context context) {
        File sdDir = null;
        boolean sdCardExist = Environment.getExternalStorageState().equals(
                Environment.MEDIA_MOUNTED);// 判断sd卡是否存在
        if (sdCardExist) {
            if (Build.VERSION.SDK_INT >= 29) {
                //Android10之后
                File[] externalDirs = context.getExternalFilesDirs(null);
                if (externalDirs.length > 0) {
                    String rootPath = externalDirs[0].getAbsolutePath();
                    rootPath = rootPath.substring(0, rootPath.indexOf("/Android/"));
                    //Toast.makeText(context, rootPath, Toast.LENGTH_SHORT).show();
                    sdDir = new File(rootPath);
                }
            } else {
                sdDir = Environment.getExternalStorageDirectory();// 获取SD卡根目录
            }
            return sdDir.toString();
        } else {
            sdDir = Environment.getRootDirectory();// 获取跟目录
        }
        return sdDir.toString();
    }

    //请求设备读写权限并加载数据
    private void requestWritePermission() {
        // 定义请求权限
        String[] reqPermission = new String[]{Manifest.permission.READ_EXTERNAL_STORAGE};
        int requestCode = 2;
        // 在API23版本以上中，权限需要在运行时进行请求
        if (ContextCompat.checkSelfPermission(MainActivity.this,
                reqPermission[0]) == PackageManager.PERMISSION_GRANTED) {
            //加载数据
            String fileTPK = imageTpkPath;
            //Toast.makeText(this,fileTPK, Toast.LENGTH_SHORT).show();
            ArcGISTiledLayer arcGISTiledLayer = new ArcGISTiledLayer(fileTPK);
            Basemap basemap = new Basemap(arcGISTiledLayer);
            //TODO:一开始没有设置Map，然后定位无效，现在添加了坐标系，然后定位就可以了
            ArcGISMap mainArcGISMap = new ArcGISMap(basemap);//确定为这个类但不添加底图
            mainArcGISMap.setBasemap(basemap);
            mapView.setMap(mainArcGISMap);
        } else {
            // 请求权限
            ActivityCompat.requestPermissions(MainActivity.this, reqPermission, requestCode);
        }
    }

    //处理权限请求响应,用户选择完权限后响应
    public void onRequestPermissionsResult(int requestCode,
                                           @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            //加载数据
            String fileTPK = imageTpkPath;
            //Toast.makeText(this,fileTPK, Toast.LENGTH_SHORT).show();
            ArcGISTiledLayer arcGISTiledLayer = new ArcGISTiledLayer(fileTPK);
            Basemap basemap = new Basemap(arcGISTiledLayer);
            //TODO:一开始没有设置Map，然后定位无效，现在添加了坐标系，然后定位就可以了
            ArcGISMap mainArcGISMap = new ArcGISMap(basemap);//确定为这个类但不添加底图
            mainArcGISMap.setBasemap(basemap);
            mapView.setMap(mainArcGISMap);
        } else {
            Toast.makeText(MainActivity.this, "读写权限被拒绝", Toast.LENGTH_SHORT).show();
        }
    }


    //获取SD卡路径
    public String getSDCardPath() {
        return Environment.getExternalStorageDirectory().getAbsolutePath()
                + File.separator;
    }

    public int updateydstatus(String code,String status ){
        SQLiteDatabase db= openOrCreateDatabase(getSDPath(this) + "/TRSPGISData/TRSPSqliteData/mydata.db", Context.MODE_PRIVATE, null);;
        ContentValues values;
        //db = mySQLiteHelp.getWritableDatabase();//获取可读写SQLiteDatabse对象
        values = new ContentValues();       // 创建ContentValues对象
        values.put("zt", status);
        db.update("ydb",values," code=?",new String[]{code});
        Toast.makeText(this, "信息已修改", Toast.LENGTH_SHORT).show();
        db.close();
        return 0;
    }

    //    /*使用gdal生成shp*/
//    public void writeShp() throws UnsupportedEncodingException {
//
//        //构造一个shpfile名称，由初始开始用户填写的工作空间名称为主,这里的路径最好设置为自动路径,便于手机自动生成
//        ///storage/emulated/0/Android/data/com.example.myapplication/files
//        String autoShpPath = MainActivity.this.getExternalFilesDir(null).getAbsolutePath();
//        String shpPath = autoShpPath + spaceName + ".shp";
//        ogr.RegisterAll();
//        gdal.SetConfigOption("GDAL_FILENAME_IS_UTF8", "NO");
//        gdal.SetConfigOption("SHAPE_ENCODING", "UTF-8");
//
//        String strDriverName = "ESRI Shapefile";//文件格式
//        Driver oDriver = ogr.GetDriverByName(strDriverName);
//        if (oDriver == null) {
//            System.out.println(strDriverName + " 驱动不可用！\n");
//            return;
//        }
//        DataSource oDS = oDriver.CreateDataSource(shpPath, null);
//        if (oDS == null) {
//            System.out.println("创建矢量文件【" + shpPath + "】失败！\n");
//            return;
//        }
//        // ogr.wkbPolygon 面   wkbLineString 线 wkbPoint 点
//        //定义坐标可以从shp的prj文件里获取所需要的参数
//        org.gdal.osr.SpatialReference sap = new org.gdal.osr.SpatialReference();
//        sap.SetWellKnownGeogCS("WGS84");
//        org.gdal.ogr.Layer oLayer = oDS.CreateLayer("WorkingLine", sap, ogr.wkbLineString, null);
//        if (oLayer == null) {
//            System.out.println("图层创建失败！\n");
//            return;
//        }
//        // 下面创建属性表
//        // 先创建一个叫FieldID的整型属性
//        FieldDefn oFieldID = new FieldDefn("FieldID", ogr.OFTInteger);
//        oLayer.CreateField(oFieldID);
//
//        // 再创建一个叫FeatureName的字符型属性，字符长度为50
//        FieldDefn oFieldName = new FieldDefn("FieldName", ogr.OFTString);
//        oFieldName.SetWidth(100);
//        oLayer.CreateField(oFieldName);
//
//        //创建一个StartTime属性
//        FieldDefn oFieldStartTime = new FieldDefn("StartTime",ogr.OFTString);
//        oFieldStartTime.SetWidth(100);
//        oLayer.CreateField(oFieldStartTime);
//
//        //创建一个EndTime属性
//        FieldDefn oFieldEndTime = new FieldDefn("EndTime",ogr.OFTString);
//        oFieldEndTime.SetWidth(100);
//        oLayer.CreateField(oFieldEndTime);
//
//        try {
//            oLayer.SyncToDisk();
//            oDS.SyncToDisk();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//
//
//        FeatureDefn oDefn = oLayer.GetLayerDefn();
//        // 创建线要素
//        org.gdal.ogr.Feature oFeatureLine= new org.gdal.ogr.Feature(oDefn);
//        oFeatureLine.SetField(0, 1);
//        oFeatureLine.SetField(1, "line");
//        oFeatureLine.SetField(1, new String("line".getBytes(), StandardCharsets.UTF_8));
//        oFeatureLine.SetField(2, StartTime);
//        oFeatureLine.SetField(3, EndTime);
//        String LINESTRING = "LINESTRING(";
//        for(int i = 0; i< pointList.size();i++){
//            String final_point = pointList.get(i);
//            String[] split = final_point.split(",");
//            String x = split[0];
//            String y = split[1];
//            LINESTRING = LINESTRING + x + " " + y + ",";
//        }
//        LINESTRING = LINESTRING.substring(0,LINESTRING.length()-1)+")";
//        org.gdal.ogr.Geometry geomLine = org.gdal.ogr.Geometry.CreateFromWkt(LINESTRING);
//        oFeatureLine.SetGeometry(geomLine);
//        oLayer.CreateFeature(oFeatureLine);
//        System.out.println("\n数据集创建完成！\n");
//        Toast.makeText(MainActivity.this, "工作路线轨迹记录完毕", Toast.LENGTH_LONG).show();
//    }
    @Override
    public void onBackPressed() {

        Intent intent = new Intent();
        if (lon == 0) {
            intent.putExtra("backUrl", "/pages/suryeysampling/suryeysampling"); // 需要返回的uniapp页面的路由信息
        } else if (lon == 1) {
            intent.putExtra("backUrl", "/pages/suryeysampling/sampleTask/sampleUpload");
            String code_1 = "";
            for (int i = 0; i < pointList_final.size(); i++) {
                //获取jsonObject
                JSONObject jsonObject = pointList_final.getJSONObject(i);
                code_1 = jsonObject.getString("code");
            }
            intent.putExtra("code", code_1);
        } else {
            intent.putExtra("backUrl", "/pages/suryeysampling/sampleTask/sampleTask");
            Log.e("跳转测试", TestModule.REQUEST_BACK_PAGE + "");
        }

        setResult(TestModule.REQUEST_BACK_PAGE, intent);

        finish(); // 关闭当前页面


    }

    public static boolean checkNet(Context context) {

        try {
            ConnectivityManager connectivity = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            if (connectivity != null) {

                NetworkInfo info = connectivity.getActiveNetworkInfo();
                if (info != null && info.isConnected()) {

                    if (info.getState() == NetworkInfo.State.CONNECTED) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    /**
     * 显示底部图层表单
     */
    private void showLayersBottomSheet() {
        View bottomSheet = findViewById(R.id.bottomSheet);
        if (bottomSheet == null) {
            Log.e("BottomSheet", "找不到bottomSheet视图!");
            return;
        }
        
        // 确保底部表单可见
        bottomSheet.setVisibility(View.VISIBLE);
        
        // 确保RecyclerView可见
        if (mLayersRecyclerView != null) {
            mLayersRecyclerView.setVisibility(View.VISIBLE);
        }
        
        // 设置状态为展开
        mBottomSheetBehavior.setState(BottomSheetBehavior.STATE_EXPANDED);
        
        Log.i("BottomSheet", "显示底部图层表单");
    }
    
    /**
     * 隐藏底部图层表单
     */
    private void hideLayersBottomSheet() {
        // 设置状态为隐藏
        mBottomSheetBehavior.setState(BottomSheetBehavior.STATE_HIDDEN);
        
        Log.i("BottomSheet", "隐藏底部图层表单");
    }

}
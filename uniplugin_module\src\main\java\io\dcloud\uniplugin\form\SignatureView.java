package io.dcloud.uniplugin.form;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewParent;

public class SignatureView extends View {
    private Path path;
    private Paint paint;
    private Bitmap bitmap;
    private Canvas canvas;
    private float lastX;
    private float lastY;

    public SignatureView(Context context) {
        super(context);
        init();
    }

    public SignatureView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        path = new Path();
        paint = new Paint();
        paint.setAntiAlias(true);
        paint.setColor(Color.BLACK);
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeJoin(Paint.Join.ROUND);
        paint.setStrokeCap(Paint.Cap.ROUND);
        paint.setStrokeWidth(5f);
        
        // 设置背景为白色，添加边框
        setBackgroundColor(Color.WHITE);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        bitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888);
        canvas = new Canvas(bitmap);
        canvas.drawColor(Color.WHITE);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        canvas.drawBitmap(bitmap, 0, 0, paint);
        canvas.drawPath(path, paint);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        float x = event.getX();
        float y = event.getY();

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                // 请求父视图不要拦截触摸事件
                ViewParent parent = getParent();
                if (parent != null) {
                    parent.requestDisallowInterceptTouchEvent(true);
                }
                path.moveTo(x, y);
                lastX = x;
                lastY = y;
                return true;

            case MotionEvent.ACTION_MOVE:
                // 计算移动距离
                float dx = Math.abs(x - lastX);
                float dy = Math.abs(y - lastY);
                
                // 如果移动距离太小，认为是误触
                if (dx >= 3 || dy >= 3) {
                    path.quadTo(lastX, lastY, (x + lastX) / 2, (y + lastY) / 2);
                    lastX = x;
                    lastY = y;
                }
                break;

            case MotionEvent.ACTION_UP:
                // 允许父视图重新拦截触摸事件
                ViewParent parent2 = getParent();
                if (parent2 != null) {
                    parent2.requestDisallowInterceptTouchEvent(false);
                }
                path.lineTo(lastX, lastY);
                canvas.drawPath(path, paint);
                path.reset();
                break;

            case MotionEvent.ACTION_CANCEL:
                // 允许父视图重新拦截触摸事件
                ViewParent parent3 = getParent();
                if (parent3 != null) {
                    parent3.requestDisallowInterceptTouchEvent(false);
                }
                path.reset();
                break;
        }

        invalidate();
        return true;
    }

    public Bitmap getSignatureBitmap() {
        return bitmap;
    }

    /**
     * 检查是否已签名
     * @return 如果已签名返回true，否则返回false
     */
    public boolean isSigned() {
        // 创建一个纯白色的位图作为比较基准
        Bitmap emptyBitmap = Bitmap.createBitmap(bitmap.getWidth(), bitmap.getHeight(), bitmap.getConfig());
        Canvas emptyCanvas = new Canvas(emptyBitmap);
        emptyCanvas.drawColor(Color.WHITE);
        
        // 比较当前位图与空白位图
        return !bitmap.sameAs(emptyBitmap);
    }

    public void clear() {
        path.reset();
        canvas.drawColor(Color.WHITE);
        invalidate();
    }
} 
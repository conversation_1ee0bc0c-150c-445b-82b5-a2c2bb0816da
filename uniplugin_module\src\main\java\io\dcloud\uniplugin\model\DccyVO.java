package io.dcloud.uniplugin.model;

import java.io.Serializable;
import java.util.List;

public class DccyVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    private Long pjdyId;
    private String pjdybh;
    private Long dcdwId;//调查单位
    private String dcdwName;
    private Long dcrId;//调查人
    private String dcrName;
    private Double dcjd;
    private Double dcwd;
    private Long dcTime; //调查时间
    private Long uploadTime;
    private Long dcTimeEnd;
    private String trMy;
    private String trMz;
    private Long xfjlId;
    private String xmmc;



    private Integer sfShiCj;//是否市级抽鉴
    private Integer sfShengCj;//是否省级抽鉴
    private Integer zt;
    private String bz;
    private Integer hasLocalData;
    private String formData;
    private String dynamicFormData; // 动态表单数据JSON字符串
    private Long userId;
    private List<DccyMediaVO> photoList;
    private List<DccyMediaVO> videoList;

    public class DccyMediaVO implements Serializable {
        private String fieldName;
        private Long id;
        private Long dccyId;
        private String pjdybh;
        private Long fileId;
        private String fileName;
        private String path;
        private String fileType;
        private Double jd;
        private Double wd;
        private Double fwj;//方位角
        private String fileTime;//采集时间
        private boolean needDownload; // 是否需要下载

        public String getFieldName() {
            return fieldName;
        }

        public void setFieldName(String fieldName) {
            this.fieldName = fieldName;
        }

        public boolean isNeedDownload() {
            return needDownload;
        }

        public void setNeedDownload(boolean needDownload) {
            this.needDownload = needDownload;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public String getFileType() {
            return fileType;
        }

        public void setFileType(String fileType) {
            this.fileType = fileType;
        }

        public Double getJd() {
            return jd;
        }

        public void setJd(Double jd) {
            this.jd = jd;
        }

        public Double getWd() {
            return wd;
        }

        public void setWd(Double wd) {
            this.wd = wd;
        }

        public Double getFwj() {
            return fwj;
        }

        public void setFwj(Double fwj) {
            this.fwj = fwj;
        }

        public String getFileTime() {
            return fileTime;
        }

        public void setFileTime(String fileTime) {
            this.fileTime = fileTime;
        }

        public Long getDccyId() {
            return dccyId;
        }

        public void setDccyId(Long dccyId) {
            this.dccyId = dccyId;
        }

        public String getpjdybh() {
            return pjdybh;
        }

        public void setpjdybh(String pjdybh) {
            this.pjdybh = pjdybh;
        }

        public Long getFileId() {
            return fileId;
        }

        public void setFileId(Long fileId) {
            this.fileId = fileId;
        }
    }

    public String getXmmc() {
        return xmmc;
    }

    public void setXmmc(String xmmc) {
        this.xmmc = xmmc;
    }

    public Long getXfjlId() {
        return xfjlId;
    }

    public void setXfjlId(Long xfjlId) {
        this.xfjlId = xfjlId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }


    public Double getDcjd() {
        return dcjd;
    }

    public void setDcjd(Double dcjd) {
        this.dcjd = dcjd;
    }

    public Double getDcwd() {
        return dcwd;
    }

    public void setDcwd(Double dcwd) {
        this.dcwd = dcwd;
    }

    public Integer getZt() {
        return zt;
    }

    public void setZt(Integer zt) {
        this.zt = zt;
    }

    public String getBz() {
        return bz;
    }

    public void setBz(String bz) {
        this.bz = bz;
    }

    public List<DccyMediaVO> getPhotoList() {
        return photoList;
    }

    public void setPhotoList(List<DccyMediaVO> photoList) {
        this.photoList = photoList;
    }

    public List<DccyMediaVO> getVideoList() {
        return videoList;
    }

    public void setVideoList(List<DccyMediaVO> videoList) {
        this.videoList = videoList;
    }

    public String getFormData() {
        return formData;
    }

    public void setFormData(String formData) {
        this.formData = formData;
    }

    public Integer getSfShiCj() {
        return sfShiCj;
    }

    public void setSfShiCj(Integer sfShiCj) {
        this.sfShiCj = sfShiCj;
    }

    public Integer getSfShengCj() {
        return sfShengCj;
    }

    public void setSfShengCj(Integer sfShengCj) {
        this.sfShengCj = sfShengCj;
    }

    public Integer getHasLocalData() {
        return hasLocalData;
    }

    public void setHasLocalData(Integer hasLocalData) {
        this.hasLocalData = hasLocalData;
    }

    public Long getPjdyId() {
        return pjdyId;
    }

    public void setPjdyId(Long pjdyId) {
        this.pjdyId = pjdyId;
    }

    public String getpjdybh() {
        return pjdybh;
    }

    public void setpjdybh(String pjdybh) {
        this.pjdybh = pjdybh;
    }

    public Long getDcdwId() {
        return dcdwId;
    }

    public void setDcdwId(Long dcdwId) {
        this.dcdwId = dcdwId;
    }

    public String getDcdwName() {
        return dcdwName;
    }

    public void setDcdwName(String dcdwName) {
        this.dcdwName = dcdwName;
    }

    public Long getDcrId() {
        return dcrId;
    }

    public void setDcrId(Long dcrId) {
        this.dcrId = dcrId;
    }

    public String getDcrName() {
        return dcrName;
    }

    public void setDcrName(String dcrName) {
        this.dcrName = dcrName;
    }

    public Long getDcTime() {
        return dcTime;
    }

    public void setDcTime(Long dcTime) {
        this.dcTime = dcTime;
    }

    public Long getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(Long uploadTime) {
        this.uploadTime = uploadTime;
    }

    public Long getDcTimeEnd() {
        return dcTimeEnd;
    }

    public void setDcTimeEnd(Long dcTimeEnd) {
        this.dcTimeEnd = dcTimeEnd;
    }

    public String getTrMy() {
        return trMy;
    }

    public void setTrMy(String trMy) {
        this.trMy = trMy;
    }

    public String getTrMz() {
        return trMz;
    }

    public void setTrMz(String trMz) {
        this.trMz = trMz;
    }

    public Integer getsfShiCj() {
        return sfShiCj;
    }

    public void setsfShiCj(Integer sfShiCj) {
        this.sfShiCj = sfShiCj;
    }

    public Integer getsfShengCj() {
        return sfShengCj;
    }

    public void setsfShengCj(Integer sfShengCj) {
        this.sfShengCj = sfShengCj;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getDynamicFormData() {
        return dynamicFormData;
    }

    public void setDynamicFormData(String dynamicFormData) {
        this.dynamicFormData = dynamicFormData;
    }
}

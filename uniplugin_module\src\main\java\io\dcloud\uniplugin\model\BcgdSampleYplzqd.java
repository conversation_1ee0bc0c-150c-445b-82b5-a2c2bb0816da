package io.dcloud.uniplugin.model;

/**
 * 补充耕地-样品信息
 */
public class BcgdSampleYplzqd {
    /**
     * ID
     */
    private Long id;

    /**
     * 标识码
     */
    private String bsm;

    /**
     * 样品编号
     */
    private String sampleCode;

    /**
     * 样品名称
     */
    private String sampleName;

    /**
     * 样品类型
     */
    private String sampleType;

    /**
     * 样品重量
     */
    private Double sampleWeight;

    /**
     * 称重单位
     */
    private String weightUnit;

    /**
     * 外观状态
     */
    private String sampleExteriorState;

    /**
     * 质地
     */
    private String sampleTexture;

    /**
     * 颜色
     */
    private String sampleColour;

    /**
     * 保存条件
     */
    private String samplePreservationCondition;

    /**
     * 包装材质
     */
    private String samplePackageMaterial;

    /**
     * 包装是否破损（否0  是1）
     */
    private Integer samplePackageBroken;

    /**
     * 包装是否密封（否0  是1）
     */
    private Integer samplePackageSeal;

    /**
     * 标签是否完整（否0  是1）
     */
    private Integer sampleTagComplete;

    /**
     * 标签是否清晰（否0  是1）
     */
    private Integer sampleTagClear;

    /**
     * 关联项目ID
     */
    private Long supplementProjectId;

    /**
     * 关联批次ID
     */
    private Long sampleBatchId;

    /**
     * 不合格原因
     */
    private String unqualifiedReason;

    /**
     * 不合格照片
     */
    private String unqualifiedPhoto;

    /**
     * 备注
     */
    private String remark;


    /**
     * 关联项目名称
     */
    private Integer supplementProjectName;

    /**
     * 已寄送/已接收/已拒收 (0/1/2)
     */
    private Integer status;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBsm() {
        return bsm;
    }

    public void setBsm(String bsm) {
        this.bsm = bsm;
    }

    public String getSampleCode() {
        return sampleCode;
    }

    public void setSampleCode(String sampleCode) {
        this.sampleCode = sampleCode;
    }

    public String getSampleName() {
        return sampleName;
    }

    public void setSampleName(String sampleName) {
        this.sampleName = sampleName;
    }

    public String getSampleType() {
        return sampleType;
    }

    public void setSampleType(String sampleType) {
        this.sampleType = sampleType;
    }

    public Double getSampleWeight() {
        return sampleWeight;
    }

    public void setSampleWeight(Double sampleWeight) {
        this.sampleWeight = sampleWeight;
    }

    public String getWeightUnit() {
        return weightUnit;
    }

    public void setWeightUnit(String weightUnit) {
        this.weightUnit = weightUnit;
    }

    public String getSampleExteriorState() {
        return sampleExteriorState;
    }

    public void setSampleExteriorState(String sampleExteriorState) {
        this.sampleExteriorState = sampleExteriorState;
    }

    public String getSampleTexture() {
        return sampleTexture;
    }

    public void setSampleTexture(String sampleTexture) {
        this.sampleTexture = sampleTexture;
    }

    public String getSampleColour() {
        return sampleColour;
    }

    public void setSampleColour(String sampleColour) {
        this.sampleColour = sampleColour;
    }

    public String getSamplePreservationCondition() {
        return samplePreservationCondition;
    }

    public void setSamplePreservationCondition(String samplePreservationCondition) {
        this.samplePreservationCondition = samplePreservationCondition;
    }

    public String getSamplePackageMaterial() {
        return samplePackageMaterial;
    }

    public void setSamplePackageMaterial(String samplePackageMaterial) {
        this.samplePackageMaterial = samplePackageMaterial;
    }

    public Integer getSamplePackageBroken() {
        return samplePackageBroken;
    }

    public void setSamplePackageBroken(Integer samplePackageBroken) {
        this.samplePackageBroken = samplePackageBroken;
    }

    public Integer getSamplePackageSeal() {
        return samplePackageSeal;
    }

    public void setSamplePackageSeal(Integer samplePackageSeal) {
        this.samplePackageSeal = samplePackageSeal;
    }

    public Integer getSampleTagComplete() {
        return sampleTagComplete;
    }

    public void setSampleTagComplete(Integer sampleTagComplete) {
        this.sampleTagComplete = sampleTagComplete;
    }

    public Integer getSampleTagClear() {
        return sampleTagClear;
    }

    public void setSampleTagClear(Integer sampleTagClear) {
        this.sampleTagClear = sampleTagClear;
    }

    public Long getSupplementProjectId() {
        return supplementProjectId;
    }

    public void setSupplementProjectId(Long supplementProjectId) {
        this.supplementProjectId = supplementProjectId;
    }

    public Long getSampleBatchId() {
        return sampleBatchId;
    }

    public void setSampleBatchId(Long sampleBatchId) {
        this.sampleBatchId = sampleBatchId;
    }

    public String getUnqualifiedReason() {
        return unqualifiedReason;
    }

    public void setUnqualifiedReason(String unqualifiedReason) {
        this.unqualifiedReason = unqualifiedReason;
    }

    public String getUnqualifiedPhoto() {
        return unqualifiedPhoto;
    }

    public void setUnqualifiedPhoto(String unqualifiedPhoto) {
        this.unqualifiedPhoto = unqualifiedPhoto;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }



    public Integer getSupplementProjectName() {
        return supplementProjectName;
    }

    public void setSupplementProjectName(Integer supplementProjectName) {
        this.supplementProjectName = supplementProjectName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}

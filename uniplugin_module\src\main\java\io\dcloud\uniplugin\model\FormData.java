package io.dcloud.uniplugin.model;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 表单提交数据模型
 */
public class FormData implements Serializable {
    
    @SerializedName("id")
    private long id;
    
    @SerializedName("formId")
    private String formId;
    
    @SerializedName("samplingPointId")
    private String samplingPointId;
    
    @SerializedName("formVersion")
    private String formVersion;
    
    @SerializedName("submissionDate")
    private long submissionDate;
    
    @SerializedName("formData")
    private Map<String, Object> formData = new HashMap<>();
    
    @SerializedName("files")
    private Map<String, List<String>> files = new HashMap<>();
    
    @SerializedName("location")
    private Map<String, Double> location;
    
    @SerializedName("isComplete")
    private boolean isComplete = true;
    
    // Getter and Setters
    public long getId() {
        return id;
    }
    
    public void setId(long id) {
        this.id = id;
    }
    
    public String getFormId() {
        return formId;
    }
    
    public void setFormId(String formId) {
        this.formId = formId;
    }
    
    public String getSamplingPointId() {
        return samplingPointId;
    }
    
    public void setSamplingPointId(String samplingPointId) {
        this.samplingPointId = samplingPointId;
    }
    
    public String getFormVersion() {
        return formVersion;
    }
    
    public void setFormVersion(String formVersion) {
        this.formVersion = formVersion;
    }
    
    public long getSubmissionDate() {
        return submissionDate;
    }
    
    public void setSubmissionDate(long submissionDate) {
        this.submissionDate = submissionDate;
    }
    
    public Map<String, Object> getFormData() {
        return formData;
    }
    
    public void setFormData(Map<String, Object> formData) {
        this.formData = formData;
    }
    
    public void addFormField(String fieldId, Object value) {
        this.formData.put(fieldId, value);
    }
    
    public Map<String, List<String>> getFiles() {
        return files;
    }
    
    public void setFiles(Map<String, List<String>> files) {
        this.files = files;
    }
    
    public void addFiles(String fieldId, List<String> filePaths) {
        this.files.put(fieldId, filePaths);
    }
    
    public void addFile(String fieldId, String filePath) {
        if (!this.files.containsKey(fieldId)) {
            this.files.put(fieldId, new ArrayList<>());
        }
        this.files.get(fieldId).add(filePath);
    }
    
    public Map<String, Double> getLocation() {
        return location;
    }
    
    public void setLocation(Map<String, Double> location) {
        this.location = location;
    }
    
    public void setLocation(double latitude, double longitude) {
        Map<String, Double> loc = new HashMap<>();
        loc.put("latitude", latitude);
        loc.put("longitude", longitude);
        this.location = loc;
    }
    
    public boolean isComplete() {
        return isComplete;
    }
    
    public void setComplete(boolean complete) {
        isComplete = complete;
    }
    
    /**
     * 获取所有表单字段值
     */
    public Map<String, String> getFormFields() {
        Map<String, String> result = new HashMap<>();
        for (Map.Entry<String, Object> entry : formData.entrySet()) {
            result.put(entry.getKey(), entry.getValue() != null ? entry.getValue().toString() : "");
        }
        return result;
    }
} 
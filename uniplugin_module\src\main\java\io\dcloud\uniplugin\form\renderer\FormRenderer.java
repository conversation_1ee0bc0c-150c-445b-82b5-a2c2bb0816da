package io.dcloud.uniplugin.form.renderer;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.location.Location;
import android.location.LocationManager;
import android.text.InputType;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import io.dcloud.uniplugin.fileUpload.ImageAdapter;
import io.dcloud.uniplugin.form.SignatureView;
import io.dcloud.uniplugin.form.processor.DropdownFieldProcessor;
import io.dcloud.uniplugin.form.utils.FormLocationManager;
import io.dcloud.uniplugin.form.utils.FormUtils;
import io.dcloud.uniplugin.form.utils.LocationUtils;
import io.dcloud.uniplugin.model.FormConfig;
import io.dcloud.uniplugin.model.FormFieldConfig;
import io.dcloud.uniplugin.model.FormFieldGroup;
import uni.dcloud.io.uniplugin_module.R;

/**
 * 表单渲染器，负责渲染表单UI
 */
public class FormRenderer {
    private static final String TAG = "FormRenderer";

    private Context context;
    private Activity activity;
    private LinearLayout formContainer;
    private Map<String, View> formViews;
    private Map<String, ImageAdapter> imageAdapters;
    private Map<String, List<String>> fieldFiles;
    private TextView locationText;
    private Location lastKnownLocation;
    private double latitude;
    private double longitude;
    private ImageAdapter.OnItemClickListener itemClickListener;
    private ImageAdapter.OnItemDeleteListener itemDeleteListener;
    
    // 添加FormLocationManager引用
    private FormLocationManager locationManager;
    // 添加pjdyId变量，用于存储从Intent传入的项目点样ID
    private Long pjdyId;

    // 表单字段集合，用于查找字段配置
    private List<FormFieldConfig> formFields = new ArrayList<>();

    /**
     * 构造函数
     * @param activity 活动上下文
     * @param container 表单容器
     */
    public FormRenderer(Activity activity, LinearLayout container) {
        this.activity = activity;
        this.context = activity;
        this.formContainer = container;
        this.formViews = new HashMap<>();
        this.imageAdapters = new HashMap<>();
        this.fieldFiles = new HashMap<>();
    }

    /**
     * 设置项目点样ID
     * @param pjdyId 项目点样ID
     */
    public void setPjdyId(Long pjdyId) {
        this.pjdyId = pjdyId;
        Log.d(TAG, "设置项目点样ID: " + pjdyId);
    }

    /**
     * 设置位置管理器
     * @param locationManager 位置管理器
     */
    public void setLocationManager(FormLocationManager locationManager) {
        this.locationManager = locationManager;
        Log.d(TAG, "设置位置管理器: " + (locationManager != null ? "成功" : "失败"));
    }

    /**
     * 设置当前位置坐标
     * @param latitude 纬度
     * @param longitude 经度
     */
    public void setCurrentLocation(double latitude, double longitude) {
        this.latitude = latitude;
        this.longitude = longitude;
        
        // 记录当前位置信息
        Log.d(TAG, "设置当前定位位置: 纬度=" + latitude + ", 经度=" + longitude);
        
        // 如果dcjd和dcwd字段已存在，则更新它们的值
        updateCoordinateFields();
    }
    
    /**
     * 更新已存在的经纬度字段值
     */
    private void updateCoordinateFields() {
        try {
            // 检查并更新经度字段
            if (formViews.containsKey("dcjd")) {
                View view = formViews.get("dcjd");
                if (view instanceof EditText) {
                    EditText editText = (EditText) view;
                    String formattedValue = String.format(Locale.getDefault(), "%.6f", longitude);
                    editText.setText(formattedValue);
                    Log.d(TAG, "已更新经度字段值为: " + formattedValue);
                }
            }
            
            // 检查并更新纬度字段
            if (formViews.containsKey("dcwd")) {
                View view = formViews.get("dcwd");
                if (view instanceof EditText) {
                    EditText editText = (EditText) view;
                    String formattedValue = String.format(Locale.getDefault(), "%.6f", latitude);
                    editText.setText(formattedValue);
                    Log.d(TAG, "已更新纬度字段值为: " + formattedValue);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "更新经纬度字段值失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取当前实际定位的坐标
     * @param isLongitude 是否获取经度(true表示经度,false表示纬度)
     * @return 坐标值
     */
    private double getCurrentLocationCoordinate(boolean isLongitude) {
        // 1. 尝试从locationManager获取当前位置
        if (locationManager != null) {
            try {
                double lat = locationManager.getLatitude();
                double lng = locationManager.getLongitude();
                
                if (lat != 0.0 || lng != 0.0) {
                    Log.d(TAG, "从位置管理器获取到当前位置: 纬度=" + lat + ", 经度=" + lng);
                    return isLongitude ? lng : lat;
                }
            } catch (Exception e) {
                Log.e(TAG, "从位置管理器获取位置失败: " + e.getMessage());
            }
        }
        
        // 2. 尝试获取系统最后已知位置
        try {
            LocationManager systemLocationManager = (LocationManager) activity.getSystemService(Context.LOCATION_SERVICE);
            if (systemLocationManager != null) {
                Location location = LocationUtils.getLastKnownLocation(context, systemLocationManager);
                if (location != null) {
                    Log.d(TAG, "从系统获取到当前位置: 纬度=" + location.getLatitude() + ", 经度=" + location.getLongitude());
                    return isLongitude ? location.getLongitude() : location.getLatitude();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "获取系统位置失败: " + e.getMessage());
        }
        
        // 3. 使用当前保存的坐标（可能是位置管理器更新的，也可能是之前设置的）
        if (latitude != 0.0 || longitude != 0.0) {
            Log.d(TAG, "使用已保存的位置作为回退: 纬度=" + latitude + ", 经度=" + longitude);
            return isLongitude ? longitude : latitude;
        }
        
        // 4. 最后回退到0
        Log.w(TAG, "无法获取位置信息，使用默认值0");
        return 0.0;
    }

    /**
     * 渲染表单
     * @param formConfig 表单配置
     * @param pjdybh
     * @param xmmc
     */
    public void renderForm(FormConfig formConfig, String pjdybh, String xmmc) {
        try {
            // 检查配置是否为null
            if (formConfig == null) {
                Log.e(TAG, "表单配置为null，无法渲染表单");
                Toast.makeText(context, "无法加载表单配置", Toast.LENGTH_SHORT).show();
                return;
            }

            // 清空表单容器和数据
            formContainer.removeAllViews();
            formViews.clear();
            imageAdapters.clear();
            fieldFiles.clear();
            formFields.clear(); // 清空字段列表

            // 检查是否有字段组
            if (formConfig.getFieldGroups() != null && !formConfig.getFieldGroups().isEmpty()) {
                renderFormGroups(formConfig.getFieldGroups());
                
                // 保存所有字段配置，用于后续获取值
                for (FormFieldGroup group : formConfig.getFieldGroups()) {
                    if (group != null && group.getFields() != null) {
                        formFields.addAll(group.getFields());
                    }
                }
            } else if (formConfig.getFields() != null && !formConfig.getFields().isEmpty()) {
                renderFormFields(formConfig.getFields());
                
                // 保存所有字段配置
                formFields.addAll(formConfig.getFields());
            } else {
                // 没有字段，显示空表单提示
                TextView emptyText = new TextView(context);
                emptyText.setText("表单配置中没有字段");
                emptyText.setGravity(Gravity.CENTER);
                emptyText.setPadding(20, 50, 20, 50);
                formContainer.addView(emptyText);
                Log.w(TAG, "表单配置中没有字段");
            }
            
            Log.d(TAG, "表单渲染完成，保存了 " + formFields.size() + " 个字段配置");
        } catch (Exception e) {
            Log.e(TAG, "渲染表单出错: " + e.getMessage(), e);
            // 显示错误信息
            Toast.makeText(context, "渲染表单出错: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 渲染表单字段组
     * @param fieldGroups 字段组列表
     */
    private void renderFormGroups(List<FormFieldGroup> fieldGroups) {
        // 使用字段组渲染表单
        for (FormFieldGroup group : fieldGroups) {
            // 跳过null组
            if (group == null) {
                Log.w(TAG, "跳过空的字段组");
                continue;
            }

            // 创建字段组视图
            View groupView = LayoutInflater.from(context).inflate(R.layout.form_field_group, null);
            TextView groupTitle = groupView.findViewById(R.id.groupTitle);
            LinearLayout groupContainer = groupView.findViewById(R.id.groupContainer);

            // 设置组标题，确保不为null
            String title = group.getTitle();
            if (title == null || title.isEmpty()) {
                title = "未命名分组";
                Log.w(TAG, "字段组没有标题，使用默认标题: " + title);
            }
            groupTitle.setText(title);

            // 渲染组内的字段
            if (group.getFields() != null) {
                for (FormFieldConfig fieldConfig : group.getFields()) {
                    // 跳过null字段配置
                    if (fieldConfig == null) {
                        Log.w(TAG, "跳过空的字段配置");
                        continue;
                    }

                    View fieldView = renderField(fieldConfig, groupContainer);
                    if (fieldView != null) {
                        // 添加到字段组容器
                        groupContainer.addView(fieldView);
                    }
                }
            } else {
                Log.w(TAG, "字段组 " + title + " 没有字段");
                TextView emptyText = new TextView(context);
                emptyText.setText("此分组没有字段");
                emptyText.setPadding(20, 10, 20, 10);
                groupContainer.addView(emptyText);
            }

            // 添加字段组到表单容器
            formContainer.addView(groupView);
        }
    }

    /**
     * 渲染表单字段列表
     * @param fields 字段列表
     */
    private void renderFormFields(List<FormFieldConfig> fields) {
        // 使用旧格式渲染表单
        // 将字段按组分类
        Map<String, List<FormFieldConfig>> groups = new HashMap<>();
        for (FormFieldConfig field : fields) {
            // 跳过null字段配置
            if (field == null) {
                Log.w(TAG, "跳过空的字段配置");
                continue;
            }

            String groupId = field.getDisplayCondition(); // 使用displayCondition作为组ID
            if (groupId == null) {
                groupId = "default"; // 没有组ID的归为默认组
            }

            if (!groups.containsKey(groupId)) {
                groups.put(groupId, new ArrayList<>());
            }
            groups.get(groupId).add(field);
        }

        // 渲染每个分组
        for (Map.Entry<String, List<FormFieldConfig>> entry : groups.entrySet()) {
            String groupId = entry.getKey();
            List<FormFieldConfig> groupFields = entry.getValue();

            // 创建字段组视图
            View groupView = LayoutInflater.from(context).inflate(R.layout.form_field_group, null);
            TextView groupTitle = groupView.findViewById(R.id.groupTitle);
            LinearLayout groupContainer = groupView.findViewById(R.id.groupContainer);

            // 设置组标题
            groupTitle.setText(groupId.equals("default") ? "基本信息" : groupId);

            // 渲染组内的字段
            for (FormFieldConfig fieldConfig : groupFields) {
                // 跳过null字段配置
                if (fieldConfig == null) {
                    Log.w(TAG, "跳过空的字段配置");
                    continue;
                }

                View fieldView = renderField(fieldConfig, groupContainer);
                if (fieldView != null) {
                    // 添加到字段组容器
                    groupContainer.addView(fieldView);
                }
            }

            // 添加字段组到表单容器
            formContainer.addView(groupView);
        }
    }

    /**
     * 渲染单个表单字段
     * @param fieldConfig 字段配置
     * @param parent 父容器
     * @return 渲染后的字段视图
     */
    public View renderField(FormFieldConfig fieldConfig, ViewGroup parent) {
        try {
            // 检查字段配置是否为null
            if (fieldConfig == null) {
                Log.e(TAG, "字段配置为null，无法渲染");
                return null;
            }
            
            // 检查字段ID，为空则生成临时ID
            String fieldId = fieldConfig.getFieldId();
            if (fieldId == null || fieldId.isEmpty()) {
                fieldId = FormUtils.generateUniqueId("field");
                Log.w(TAG, "字段ID为null，生成临时ID: " + fieldId);
                fieldConfig.setFieldId(fieldId);
            }
            
            // 检查字段名称，为空则使用默认名称
            String fieldName = fieldConfig.getFieldName();
            if (fieldName == null || fieldName.isEmpty()) {
                fieldName = "未命名字段";
                Log.w(TAG, "字段名称为null，使用默认名称: " + fieldName);
                fieldConfig.setFieldName(fieldName);
            }
            
            // 检查字段标签，为空则使用字段名称
            String label = fieldConfig.getLabel();
            if (label == null || label.isEmpty()) {
                label = fieldName;
                Log.w(TAG, "字段标签为null，使用字段名称: " + label);
                fieldConfig.setLabel(label);
            }
            
            // 检查字段类型，为空则使用默认文本类型
            String fieldType = fieldConfig.getFieldType();
            if (fieldType == null || fieldType.isEmpty()) {
                fieldType = FormFieldConfig.TYPE_TEXT;
                Log.w(TAG, "字段类型为null，使用默认文本类型");
                fieldConfig.setFieldType(fieldType);
            }
            
            // 创建字段布局视图
            View fieldView = LayoutInflater.from(context).inflate(R.layout.form_field_item, null);
            TextView labelView = fieldView.findViewById(R.id.fieldLabel);
            FrameLayout containerView = fieldView.findViewById(R.id.fieldContainer);

            // 设置字段标签
            String labelText = label;
            if (fieldConfig.isRequired() != null && fieldConfig.isRequired()) {
                labelText += " *"; // 必填字段标记星号
            }
            labelView.setText(labelText);
            
            // 记录字段信息
            Log.d(TAG, "渲染字段: id=" + fieldId + ", name=" + fieldName + ", type=" + fieldType + ", required=" + fieldConfig.isRequired());

            // 根据字段类型渲染具体的输入控件
            View inputView = null;

            // 检查是否是经度或纬度字段(dcjd: 定位经度，dcwd: 定位纬度)
            boolean isLongitudeField = "dcjd".equalsIgnoreCase(fieldId);
            boolean isLatitudeField = "dcwd".equalsIgnoreCase(fieldId);
            boolean isPjdyIdField = "pjdyId".equalsIgnoreCase(fieldId);

            if (isLongitudeField || isLatitudeField) {
                // 对于经纬度字段，强制使用文本类型，并锁定值
                Log.d(TAG, "检测到" + (isLongitudeField ? "经度" : "纬度") + "字段: " + fieldId);
                inputView = createCoordinateInput(fieldConfig, isLongitudeField);
            } else if (isPjdyIdField) {
                // 对于pjdy_id字段，强制使用文本类型，并锁定为Intent传入的值
                Log.d(TAG, "检测到项目点样ID字段: " + fieldId);
                inputView = createPjdyIdInput(fieldConfig);
            } else {
                // 正常根据字段类型渲染控件
                switch (fieldType) {
                    case FormFieldConfig.TYPE_TEXT:
                        inputView = createTextInput(fieldConfig);
                        break;
                    case FormFieldConfig.TYPE_NUMBER:
                        inputView = createNumberInput(fieldConfig);
                        break;
                    case FormFieldConfig.TYPE_DROPDOWN:
                        inputView = createSelectInput(fieldConfig);
                        break;
                    case FormFieldConfig.TYPE_DATE:
                        inputView = createDateInput(fieldConfig);
                        break;
                    case FormFieldConfig.TYPE_TEXTAREA:
                        inputView = createTextareaInput(fieldConfig);
                        break;
                    case FormFieldConfig.TYPE_FILE:
                    case FormFieldConfig.TYPE_PHOTO:
                    case FormFieldConfig.TYPE_VIDEO:
                        inputView = createPhotoInput(fieldConfig);
                        break;
                    case FormFieldConfig.TYPE_LOCATION:
                        inputView = createLocationInput(fieldConfig);
                        break;
                    case FormFieldConfig.TYPE_SIGNATURE:
                        inputView = createSignatureInput(fieldConfig);
                        break;
                    default:
                        Log.w(TAG, "未知字段类型: " + fieldType + "，使用默认文本输入");
                        inputView = createTextInput(fieldConfig);
                        break;
                }
            }

            containerView.addView(inputView);
            formViews.put(fieldId, inputView);
            return fieldView;
        } catch (Exception e) {
            Log.e(TAG, "渲染字段失败: " + (fieldConfig != null ? fieldConfig.getFieldId() : "未知") + ", " + e.getMessage(), e);
        }

        return null;
    }

    /**
     * 创建文本输入框
     */
    private EditText createTextInput(FormFieldConfig fieldConfig) {
        EditText editText = new EditText(context);
        editText.setLayoutParams(new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.WRAP_CONTENT));

        // 设置提示文本
        String placeholder = fieldConfig.getPlaceholder();
        if (placeholder != null && !placeholder.isEmpty()) {
            editText.setHint(placeholder);
        } else {
            editText.setHint("请输入" + fieldConfig.getFieldName());
        }

        // 设置最大长度
        Integer maxLength = fieldConfig.getMaxLength();
        if (maxLength != null && maxLength > 0) {
            editText.setMaxLines(maxLength);
        }

        // 设置默认值
        String defaultValue = fieldConfig.getDefaultValue();
        if (defaultValue != null && !defaultValue.isEmpty()) {
            editText.setText(defaultValue);
           //Log.d(TAG, "文本输入框设置默认值: " + defaultValue);
        } else {
            // 设置空字符串而不是null，防止显示"null"
            editText.setText("");
        }

        return editText;
    }

    /**
     * 创建数字输入框
     */
    private EditText createNumberInput(FormFieldConfig fieldConfig) {
        EditText editText = new EditText(context);
        editText.setLayoutParams(new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.WRAP_CONTENT));

        // 设置数字输入类型
        editText.setInputType(InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_FLAG_DECIMAL);

        // 设置提示文本
        String placeholder = fieldConfig.getPlaceholder();
        if (placeholder != null && !placeholder.isEmpty()) {
            editText.setHint(placeholder);
        } else {
            editText.setHint("请输入数字");
        }

        // 设置默认值
        String defaultValue = fieldConfig.getDefaultValue();
        if (defaultValue != null && !defaultValue.isEmpty()) {
            try {
                // 尝试解析为数字，确保是有效的数字
                Double.parseDouble(defaultValue);
                editText.setText(defaultValue);
               //Log.d(TAG, "数字输入框设置默认值: " + defaultValue);
            } catch (NumberFormatException e) {
                Log.w(TAG, "数字输入框默认值不是有效数字: " + defaultValue);
                editText.setText("");
            }
        } else {
            // 设置空字符串而不是null，防止显示"null"
            editText.setText("");
        }

        return editText;
    }

    /**
     * 创建下拉选择框
     */
    private View createSelectInput(FormFieldConfig fieldConfig) {
        // 使用DropdownFieldProcessor创建下拉框
        return DropdownFieldProcessor.createDropdownField(context, fieldConfig);
    }

    /**
     * 创建日期输入框
     */
    private EditText createDateInput(FormFieldConfig fieldConfig) {
        EditText editText = new EditText(context);
        editText.setLayoutParams(new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.WRAP_CONTENT));
        editText.setFocusable(false);
        editText.setClickable(true);

        // 设置提示文本
        String placeholder = fieldConfig.getPlaceholder();
        if (placeholder != null && !placeholder.isEmpty()) {
            editText.setHint(placeholder);
        } else {
            editText.setHint("点击选择日期");
        }

        // 设置默认值，优先使用配置的默认值，否则使用当前时间
        String defaultValue = fieldConfig.getDefaultValue();
        if (defaultValue != null && !defaultValue.isEmpty()) {
            editText.setText(defaultValue);
           //Log.d(TAG, "日期输入框设置默认值: " + defaultValue);
        } else {
            // 使用当前时间
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
            String currentTime = dateFormat.format(new Date());
            editText.setText(currentTime);
           //Log.d(TAG, "日期输入框设置当前时间: " + currentTime);
        }

        // 点击时显示日期时间选择器
//        editText.setOnClickListener(v -> {
//            // 日期时间选择器逻辑需要在Activity中实现，暂时留空
//            // 这部分可以通过回调方式让Activity实现
//            Toast.makeText(context, "请点击日期时间选择", Toast.LENGTH_SHORT).show();
//        });

        return editText;
    }

    /**
     * 创建多行文本输入框
     */
    private EditText createTextareaInput(FormFieldConfig fieldConfig) {
        EditText editText = new EditText(context);
        editText.setLayoutParams(new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.WRAP_CONTENT));

        // 设置多行输入
        editText.setMinLines(3);
        editText.setMaxLines(5);
        editText.setGravity(Gravity.TOP | Gravity.START);
        editText.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_FLAG_MULTI_LINE);

        // 设置提示文本
        String placeholder = fieldConfig.getPlaceholder();
        if (placeholder != null && !placeholder.isEmpty()) {
            editText.setHint(placeholder);
        } else {
            editText.setHint("请输入" + fieldConfig.getFieldName());
        }

        // 设置最大长度
        Integer maxLength = fieldConfig.getMaxLength();
        if (maxLength != null && maxLength > 0) {
            editText.setMaxLines(maxLength);
        }

        // 设置默认值
        String defaultValue = fieldConfig.getDefaultValue();
        if (defaultValue != null && !defaultValue.isEmpty()) {
            editText.setText(defaultValue);
           //Log.d(TAG, "多行文本框设置默认值: " + defaultValue);
        } else {
            // 设置空字符串而不是null，防止显示"null"
            editText.setText("");
        }

        return editText;
    }

    /**
     * 创建照片输入组件
     */
    private View createPhotoInput(FormFieldConfig fieldConfig) {
        // 创建照片上传布局
        View view = LayoutInflater.from(context).inflate(R.layout.form_media_upload, null);

        // 获取视图
        RecyclerView recyclerView = view.findViewById(R.id.recyclerView);
        Button btnCamera = view.findViewById(R.id.btnCamera);
        // 选择照片按钮已从布局中移除

        // 设置RecyclerView
        recyclerView.setLayoutParams(new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT));
        recyclerView.setLayoutManager(new GridLayoutManager(context, 3));

        // 创建适配器
        String fieldId = fieldConfig.getFieldId();
        ImageAdapter adapter = new ImageAdapter(context);
        adapter.setShowDeleteButton(true);
        
        // 设置监听器
        if (itemClickListener != null) {
            adapter.setOnItemClickListener(itemClickListener);
        }
        if (itemDeleteListener != null) {
            adapter.setOnItemDeleteListener(itemDeleteListener);
        }
        
        recyclerView.setAdapter(adapter);
        imageAdapters.put(fieldId, adapter);

        // 初始化文件列表
        fieldFiles.put(fieldId, new ArrayList<>());

        // 设置按钮文本
        boolean isVideo = FormFieldConfig.TYPE_VIDEO.equals(fieldConfig.getFieldType());
        if (isVideo) {
            btnCamera.setText("录制视频");
        } else {
            btnCamera.setText("拍摄照片");
        }

        // 设置拍照/录像按钮点击事件逻辑需要在Activity中实现，这里留空
        // 可以通过回调方式让Activity实现
        return view;
    }

    /**
     * 创建位置输入组件
     */
    private View createLocationInput(FormFieldConfig fieldConfig) {
        // 创建位置选择布局
        View view = LayoutInflater.from(context).inflate(R.layout.form_location_input, null);

        // 获取视图组件
        TextView locationTextView = view.findViewById(R.id.locationText);
        Button btnSelectLocation = view.findViewById(R.id.btnSelectLocation);
        Button btnRefreshLocation = view.findViewById(R.id.btnRefreshLocation);

        // 保存位置文本视图
        this.locationText = locationTextView;

        // 位置刷新和选择的逻辑需要在Activity中实现，这里留空
        // 可以通过回调方式让Activity实现

        return view;
    }

    /**
     * 创建签名输入组件
     */
    private View createSignatureInput(FormFieldConfig fieldConfig) {
        try {
            // 创建签名输入布局
            View view = LayoutInflater.from(context).inflate(R.layout.form_signature_input, null);

            // 获取视图组件
            SignatureView signatureView = view.findViewById(R.id.signatureView);
            ImageView signatureImage = view.findViewById(R.id.signatureImage);
            Button btnClear = view.findViewById(R.id.btnClear);
            Button btnConfirm = view.findViewById(R.id.btnConfirm);

            // 获取字段ID，确保不为null
            final String fieldId = fieldConfig.getFieldId() != null ? 
                    fieldConfig.getFieldId() : FormUtils.generateUniqueId("field");

            // 签名操作的逻辑需要在Activity中实现，这里留空
            // 可以通过回调方式让Activity实现

            return view;
        } catch (Exception e) {
            Log.e(TAG, "创建签名输入控件失败: " + e.getMessage(), e);
            TextView errorText = new TextView(context);
            errorText.setText("无法创建签名控件: " + e.getMessage());
            errorText.setTextColor(Color.RED);
            return errorText;
        }
    }

    /**
     * 创建经纬度输入框（固定值，不可编辑）
     */
    private EditText createCoordinateInput(FormFieldConfig fieldConfig, boolean isLongitude) {
        EditText editText = new EditText(context);
        editText.setLayoutParams(new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.WRAP_CONTENT));
        
        // 设置不可编辑
        editText.setEnabled(false);
        editText.setFocusable(false);
        editText.setBackgroundColor(Color.parseColor("#F5F5F5")); // 设置浅灰色背景表示不可编辑
        
        // 获取实际的当前位置坐标
        double value = getCurrentLocationCoordinate(isLongitude);
        
        // 格式化为6位小数
        String formattedValue = String.format(Locale.getDefault(), "%.6f", value);
        editText.setText(formattedValue);
        
        Log.d(TAG, "创建" + (isLongitude ? "经度" : "纬度") + "输入框，值为: " + formattedValue);
        
        return editText;
    }

    /**
     * 创建pjdy_id输入框（固定值，不可编辑）
     */
    private EditText createPjdyIdInput(FormFieldConfig fieldConfig) {
        EditText editText = new EditText(context);
        editText.setLayoutParams(new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.WRAP_CONTENT));
        
        // 设置不可编辑
        editText.setEnabled(false);
        editText.setFocusable(false);
        editText.setBackgroundColor(Color.parseColor("#F5F5F5")); // 设置浅灰色背景表示不可编辑
        
        // 优先使用从Intent传入的pjdyId值
        String value = "";
        if (this.pjdyId != null) {
            value = String.valueOf(this.pjdyId);
        } else {
            // 如果没有传入值，尝试使用字段配置中的默认值
            String defaultValue = fieldConfig.getDefaultValue();
            if (defaultValue != null && !defaultValue.isEmpty()) {
                value = defaultValue;
            }
        }
        
        editText.setText(value);
        Log.d(TAG, "创建pjdy_id输入框，值为: " + value);
        
        return editText;
    }

    /**
     * 获取表单视图映射
     * @return 表单视图映射
     */
    public Map<String, View> getFormViews() {
        return formViews;
    }

    /**
     * 获取图片适配器映射
     * @return 图片适配器映射
     */
    public Map<String, ImageAdapter> getImageAdapters() {
        return imageAdapters;
    }

    /**
     * 获取字段文件映射
     * @return 字段文件映射
     */
    public Map<String, List<String>> getFieldFiles() {
        return fieldFiles;
    }

    /**
     * 获取表单中所有字段的值
     * @return 字段ID和值的映射
     */
    public Map<String, Object> getFormValues() {
        Map<String, Object> values = new HashMap<>();
        
        // 遍历表单视图，获取每个字段的值
        for (Map.Entry<String, View> entry : formViews.entrySet()) {
            String fieldId = entry.getKey();
            View view = entry.getValue();
            
            try {
                // 跳过空ID
                if (fieldId == null || fieldId.isEmpty()) {
                    continue;
                }
                
                // 根据不同类型的视图获取值
                if (view instanceof EditText) {
                    // 文本框、数字框、日期框等
                    String text = ((EditText) view).getText().toString();
                    values.put(fieldId, text);
                } else if (view instanceof Spinner || (view instanceof LinearLayout && ((LinearLayout) view).getTag() instanceof Spinner)) {
                    // 下拉框 - 使用DropdownFieldProcessor获取值
                    // 查找对应的FormFieldConfig
                    FormFieldConfig fieldConfig = null;
                    for (FormFieldConfig field : formFields) {
                        if (fieldId.equals(field.getFieldId())) {
                            fieldConfig = field;
                            break;
                        }
                    }
                    
                    if (fieldConfig != null) {
                        // 使用处理器获取实际值
                        String value = DropdownFieldProcessor.getSelectedValue(view, fieldConfig);
                        values.put(fieldId, value);
                        Log.d(TAG, "下拉框字段 " + fieldId + " 选中值: " + value);
                    } else {
                        // 降级处理
                        Spinner spinner = null;
                        if (view instanceof Spinner) {
                            spinner = (Spinner) view;
                        } else if (view instanceof LinearLayout) {
                            Object tag = ((LinearLayout) view).getTag();
                            if (tag instanceof Spinner) {
                                spinner = (Spinner) tag;
                            }
                        }
                        
                        if (spinner != null) {
                            Object selectedItem = spinner.getSelectedItem();
                            if (selectedItem != null) {
                                values.put(fieldId, selectedItem.toString());
                            } else {
                                values.put(fieldId, "");
                            }
                        } else {
                            values.put(fieldId, "");
                        }
                    }
                }
                // 可以根据需要添加其他控件类型的处理
                
            } catch (Exception e) {
                Log.e(TAG, "获取字段值失败: " + fieldId + ", " + e.getMessage(), e);
                values.put(fieldId, "");
            }
        }
        
        // 处理文件字段（照片、视频、签名等）
        for (Map.Entry<String, List<String>> entry : fieldFiles.entrySet()) {
            String fieldId = entry.getKey();
            List<String> files = entry.getValue();
            
            if (files != null && !files.isEmpty()) {
                values.put(fieldId, files);
            }
        }
        
        return values;
    }
} 
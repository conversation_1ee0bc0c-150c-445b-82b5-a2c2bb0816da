<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.esri.arcgisruntime.mapping.view.MapView xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/mapView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
    <com.esri.arcgisruntime.toolkit.scalebar.Scalebar
        android:orientation="horizontal"
        android:id="@+id/scalebar"
        android:layout_width="60dp"
        android:layout_height="30dp"
        android:layout_margin="65dp"
        app:fillColor="@android:color/holo_orange_light"
        app:lineColor="@android:color/holo_blue_bright"
        app:style="line"
        app:textColor="@android:color/black"
        app:textShadowColor="@android:color/white"
        app:unitSystem="metric"
        android:layout_gravity="bottom"
        tools:ignore="MissingClass" />

    <ImageButton
        android:id="@+id/compassButton"
        style="@style/map_btn"
        android:layout_gravity="end"
        android:layout_marginTop="50dp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/bg_go"
        app:srcCompat="@drawable/north" />
    <!--    放大按钮-->
    <ImageButton
        android:id="@+id/zoomInButton"
        style="@style/map_btn"
        android:layout_gravity="bottom"
        android:layout_marginStart="20dp"
        android:layout_marginBottom="140dp"
        app:srcCompat="@drawable/addbold" />
    <!--    缩小按钮-->
    <ImageButton
        android:id="@+id/zoomOutButton"
        style="@style/map_btn"
        android:layout_marginStart="20dp"
        android:layout_gravity="bottom"
        android:layout_marginBottom="80dp"
        app:srcCompat="@drawable/minus"
        />
    <!--    全图按钮-->
    <ImageButton
        android:id="@+id/fullPicButton"
        style="@style/map_btn"
        android:layout_gravity="end"
        android:layout_marginTop="100dp"
        android:layout_marginEnd="20dp"
        app:srcCompat="@drawable/fullpic"
        />
    <!--    距离量测-->
    <ImageButton
        android:id="@+id/measurementButton"
        style="@style/map_btn"
        android:layout_gravity="end"
        android:layout_marginTop="150dp"
        android:layout_marginEnd="20dp"
        app:srcCompat="@drawable/ruler"
        />
    <!--    图层管理-->
    <ImageButton
        android:id="@+id/layersButton"
        style="@style/map_btn"
        android:layout_gravity="end"
        android:layout_marginTop="200dp"
        android:layout_marginEnd="20dp"
        app:srcCompat="@drawable/layers"
        />
    <!--    显示当前位置-->
    <ImageButton
        android:id="@+id/currentLoButton"
        style="@style/map_btn"
        android:layout_gravity="end"
        android:layout_marginTop="440dp"
        android:layout_marginEnd="20dp"
        app:srcCompat="@drawable/current_lo"
        />
    <!--图例-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:visibility="visible"
        android:id="@+id/pointInfoL"
        android:gravity="center"
        android:layout_gravity="bottom"
        android:layout_marginBottom="20dp">
        <!--横向布局-->
        <TextView
            android:layout_width="50dp"
            android:layout_height="40dp"
            android:id="@+id/legend_label1"
            android:background="@color/white"
            android:text="已调查"
            android:gravity="center" />
        <!--已调查图例-->
        <ImageButton
            android:id="@+id/red"
            style="@style/color_btn"
            app:srcCompat="@drawable/green" />
        <TextView
            android:layout_width="50dp"
            android:layout_height="40dp"
            android:id="@+id/legend_label2"
            android:background="@color/white"
            android:text="未调查"
            android:gravity="center" />
        <!--未调查图例-->
        <ImageButton
            android:id="@+id/yellow"
            style="@style/color_btn"
            app:srcCompat="@drawable/yellow" />
        <TextView
            android:layout_width="50dp"
            android:layout_height="40dp"
            android:id="@+id/legend_label3"
            android:background="@color/white"
            android:text="待整改"
            android:gravity="center" />
        <!--待整改图例-->
        <ImageButton
            android:id="@+id/green"
            style="@style/color_btn"
            app:srcCompat="@drawable/red"
            />
    </LinearLayout>
    <include
        layout="@layout/measurement_toolbar"
        android:layout_width="wrap_content"
        android:layout_height="60dp"
        android:layout_marginTop="10dp"
        android:layout_gravity="center|top"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:id="@+id/toolbarInclude" />



</androidx.coordinatorlayout.widget.CoordinatorLayout>
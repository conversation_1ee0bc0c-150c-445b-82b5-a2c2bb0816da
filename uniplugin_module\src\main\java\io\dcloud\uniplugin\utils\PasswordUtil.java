package io.dcloud.uniplugin.utils;

import android.util.Log;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Random;

/**
 * 密码工具类，提供MD5加盐加密功能
 */
public class PasswordUtil {
    private static final String TAG = "PasswordUtil";
    
    // 盐的长度
    private static final int SALT_LENGTH = 8;
    
    /**
     * 生成随机盐
     * @return 随机生成的盐字符串
     */
    public static String generateSalt() {
        Random random = new Random();
        StringBuilder salt = new StringBuilder();
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        for (int i = 0; i < SALT_LENGTH; i++) {
            salt.append(chars.charAt(random.nextInt(chars.length())));
        }
        return salt.toString();
    }
    
    /**
     * 使用MD5对密码进行加盐加密
     * @param password 原始密码
     * @param salt 盐值
     * @return 加密后的密码
     */
    public static String encryptPassword(String password, String salt) {
        try {
            // 将密码和盐拼接
            String passwordWithSalt = password + salt;
            
            // 创建MD5摘要实例
            MessageDigest md = MessageDigest.getInstance("MD5");
            
            // 计算MD5摘要
            byte[] bytes = md.digest(passwordWithSalt.getBytes(StandardCharsets.UTF_8));
            
            // 将字节数组转换为十六进制字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(String.format("%02x", b));
            }
            
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            Log.e(TAG, "MD5加密失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 验证密码是否匹配
     * @param inputPassword 输入的密码
     * @param salt 盐值
     * @param encryptedPassword 已加密的密码
     * @return 如果密码匹配返回true，否则返回false
     */
    public static boolean verifyPassword(String inputPassword, String salt, String encryptedPassword) {
        String inputEncrypted = encryptPassword(inputPassword, salt);
        return inputEncrypted != null && inputEncrypted.equals(encryptedPassword);
    }
} 
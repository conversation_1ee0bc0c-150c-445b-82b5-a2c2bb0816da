package io.dcloud.uniplugin.model;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 表单字段配置模型
 */
public class FormFieldConfig implements Serializable {

    public static final String TYPE_TEXT = "text";
    public static final String TYPE_NUMBER = "number";
    public static final String TYPE_DROPDOWN = "dropdown";
    public static final String TYPE_PHOTO = "photo";
    public static final String TYPE_VIDEO = "video";
    public static final String TYPE_DATE = "date";
    public static final String TYPE_TIME = "time";
    public static final String TYPE_DATETIME = "datetime";
    public static final String TYPE_CHECKBOX = "checkbox";
    public static final String TYPE_RADIO = "radio";
    public static final String TYPE_TEXTAREA = "textarea";
    public static final String TYPE_FILE = "file";
    public static final String TYPE_LOCATION = "location";
    public static final String TYPE_SIGNATURE = "signature";
    public static final String TYPE_SCANNER = "scanner";
    public static final String TYPE_AUDIO = "audio";

    @SerializedName("fieldId")
    private String fieldId;

    @SerializedName("fieldName")
    private String fieldName;

    @SerializedName("label")
    private String label;

    @SerializedName("fieldType")
    private String fieldType;

    @SerializedName("placeholder")
    private String placeholder;

    @SerializedName("required")
    private Boolean required;

    @SerializedName("defaultValue")
    private String defaultValue;

    @SerializedName("options")
    private List<OptionItem> options;

    @SerializedName("maxLength")
    private Integer maxLength;

    @SerializedName("minValue")
    private Double minValue;

    @SerializedName("maxValue")
    private Double maxValue;

    @SerializedName("maxFiles")
    private Integer maxFiles;

    @SerializedName("minFiles")
    private Integer minFiles;

    @SerializedName("validationRules")
    private Map<String, Object> validationRules;

    @SerializedName("displayOrder")
    private Integer displayOrder;

    @SerializedName("displayCondition")
    private String displayCondition;

    // 存储原始的选项字符串，例如JSON格式字符串
    @SerializedName("optionsString")
    private String optionsString;

    // 校验规则相关属性
    private int minLength = 0;
    private String pattern;
    private String patternError;
    private Double min;
    private Double max;
    private boolean integer = false;
    private String minDate;
    private String maxDate;

    /**
     * 下拉框、复选框和单选框的选项项
     */
    public static class OptionItem implements Serializable {
        @SerializedName("value")
        private String value;

        @SerializedName("label")
        private String label;

        @SerializedName("selected")
        private boolean selected;

        public OptionItem() {
        }

        public OptionItem(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public boolean isSelected() {
            return selected;
        }

        public void setSelected(boolean selected) {
            this.selected = selected;
        }
    }

    // Getters and Setters
    public String getFieldId() {
        return fieldId;
    }

    public void setFieldId(String fieldId) {
        this.fieldId = fieldId;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getLabel() {
        return label != null ? label : fieldName;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    /**
     * setType作为setFieldType的别名，提高兼容性
     *
     * @param type 字段类型
     */
    public void setType(String type) {
        this.fieldType = type;
    }

    public String getPlaceholder() {
        return placeholder;
    }

    public void setPlaceholder(String placeholder) {
        this.placeholder = placeholder;
    }

    public Boolean isRequired() {
        return required;
    }

    public void setRequired(Boolean required) {
        this.required = required;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public List<OptionItem> getOptions() {
        return options;
    }

    public void setOptions(List<OptionItem> options) {
        this.options = options;
    }

    /**
     * 适配String列表的选项设置方法
     *
     * @param stringOptions 字符串选项列表
     */
    public void setStringOptions(List<String> stringOptions) {
        if (stringOptions != null) {
            List<OptionItem> optionItems = new ArrayList<>();
            for (String option : stringOptions) {
                OptionItem item = new OptionItem(option, option);
                optionItems.add(item);
            }
            this.options = optionItems;
        }
    }

    public Integer getMaxLength() {
        return maxLength;
    }

    public void setMaxLength(Integer maxLength) {
        this.maxLength = maxLength;
    }

    public Double getMinValue() {
        return minValue;
    }

    public void setMinValue(Double minValue) {
        this.minValue = minValue;
    }

    public Double getMaxValue() {
        return maxValue;
    }

    public void setMaxValue(Double maxValue) {
        this.maxValue = maxValue;
    }

    public Integer getMaxFiles() {
        return maxFiles;
    }

    public void setMaxFiles(Integer maxFiles) {
        this.maxFiles = maxFiles;
    }

    public Boolean getRequired() {
        return required;
    }

    public Integer getMinFiles() {
        return minFiles;
    }


    public void setMinFiles(Integer minFiles) {
        this.minFiles = minFiles;
    }

    public Map<String, Object> getValidationRules() {
        return validationRules;
    }

    public void setValidationRules(Map<String, Object> validationRules) {
        this.validationRules = validationRules;
    }

    public Integer getDisplayOrder() {
        return displayOrder;
    }

    public void setDisplayOrder(Integer displayOrder) {
        this.displayOrder = displayOrder;
    }

    public String getDisplayCondition() {
        return displayCondition;
    }

    public void setDisplayCondition(String displayCondition) {
        this.displayCondition = displayCondition;
    }

    // Getters and Setters for validation properties
    public int getMinLength() {
        return minLength;
    }

    public void setMinLength(int minLength) {
        this.minLength = minLength;
    }

    public String getPattern() {
        return pattern;
    }

    public void setPattern(String pattern) {
        this.pattern = pattern;
    }

    public String getPatternError() {
        return patternError;
    }

    public void setPatternError(String patternError) {
        this.patternError = patternError;
    }

    public Double getMin() {
        return min;
    }

    public void setMin(Double min) {
        this.min = min;
    }

    public Double getMax() {
        return max;
    }

    public void setMax(Double max) {
        this.max = max;
    }

    public boolean isInteger() {
        return integer;
    }

    public void setInteger(boolean integer) {
        this.integer = integer;
    }

    public String getMinDate() {
        return minDate;
    }

    public void setMinDate(String minDate) {
        this.minDate = minDate;
    }

    public String getMaxDate() {
        return maxDate;
    }

    public void setMaxDate(String maxDate) {
        this.maxDate = maxDate;
    }

    public String getOptionsString() {
        return optionsString;
    }

    public void setOptionsString(String optionsString) {
        this.optionsString = optionsString;
    }
}

package io.dcloud.uniplugin.form.media;

import android.app.Activity;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;

import com.deltaphone.cameramodule.CameraActivity;

import org.json.JSONObject;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import io.dcloud.uniplugin.model.PhotoInfo;

/**
 * 媒体处理器，负责处理表单中的照片和视频
 */
public class MediaHandler implements SensorEventListener, LocationListener {
    private static final String TAG = "MediaHandler";
    private static final int REQUEST_CAMERA = 1001;
    private static final int REQUEST_GALLERY = 1002;
    private static final int REQUEST_VIDEO = 1003;
    private static final int REQUEST_VIDEO_GALLERY = 1004;
    // 使用自定义Camera模块的请求码
    private static final int REQUEST_CUSTOM_CAMERA = 1000;

    private Context context;
    private MediaPlayer mediaPlayer;
    private String currentPhotoPath;
    private String currentFieldId;
    private MediaCallback callback;
    
    // 传感器相关
    private SensorManager sensorManager;
    private Sensor accelerometer;
    private Sensor magneticField;
    private float[] accelerometerReading = new float[3];
    private float[] magnetometerReading = new float[3];
    private float[] rotationMatrix = new float[9];
    private float[] orientationAngles = new float[3];
    private float currentDirection = 0f; // 方位角（方向）
    
    // 位置相关
    private LocationManager locationManager;
    private Location currentLocation;
    private static final long MIN_TIME_BW_UPDATES = 1000; // 1秒
    private static final float MIN_DISTANCE_CHANGE_FOR_UPDATES = 1; // 1米

    /**
     * 媒体处理回调接口
     */
    public interface MediaCallback {
        /**
         * 媒体文件选择完成回调
         * @param fieldId 字段ID
         * @param filePath 文件路径
         */
        void onMediaSelected(String fieldId, String filePath);

        /**
         * 媒体处理错误回调
         * @param fieldId 字段ID
         * @param errorMessage 错误信息
         */
        void onMediaError(String fieldId, String errorMessage);
        
        /**
         * 照片选择完成回调，包含照片详细信息
         * @param fieldId 字段ID
         * @param photoInfo 照片信息对象，包含位置和方位角
         */
        default void onPhotoSelected(String fieldId, io.dcloud.uniplugin.model.PhotoInfo photoInfo) {
            // 默认实现，转调用普通的媒体选择回调
            onMediaSelected(fieldId, photoInfo.getFilePath());
        }
    }

    /**
     * 构造函数
     * @param context 上下文
     */
    public MediaHandler(Context context) {
        this.context = context;
        
        // 初始化传感器管理器
        sensorManager = (SensorManager) context.getSystemService(Context.SENSOR_SERVICE);
        if (sensorManager != null) {
            accelerometer = sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER);
            magneticField = sensorManager.getDefaultSensor(Sensor.TYPE_MAGNETIC_FIELD);
        }
        
        // 初始化位置管理器
        try {
            locationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
        } catch (Exception e) {
            Log.e(TAG, "初始化位置管理器失败: " + e.getMessage());
        }
    }

    /**
     * 设置媒体处理回调
     * @param callback 回调接口
     */
    public void setMediaCallback(MediaCallback callback) {
        this.callback = callback;
    }
    
    /**
     * 打开相机拍照
     * @param activity 活动
     * @param fieldId 字段ID
     * @return 是否成功启动相机
     */
    public boolean takePicture(Activity activity, String fieldId,String pjdybh,String xmmc) {
        this.currentFieldId = fieldId;
        Log.d(TAG, "尝试使用自定义相机拍照，字段ID: " + fieldId);
        
        try {
            // 创建并传递参数给自定义相机
            JSONObject options = new JSONObject();
            options.put("model", "0"); // 0表示拍照模式

            // 创建Intent启动自定义相机Activity
            Intent cameraIntent = new Intent(activity, CameraActivity.class);
            cameraIntent.putExtra("model", "0"); // 传递参数，0表示拍照模式
            cameraIntent.putExtra("pjdybh", pjdybh);
            cameraIntent.putExtra("xmmc", xmmc);

            // 不再在这里获取位置和方位角，而是让相机模块在拍照瞬间获取
            // 传递一个参数，让相机知道需要获取位置和方位角
            cameraIntent.putExtra("captureLocationOnShot", true);

            // 启动自定义相机Activity
            activity.startActivityForResult(cameraIntent, REQUEST_CUSTOM_CAMERA);

            return true;
        } catch (Exception e) {
            Log.e(TAG, "启动自定义相机失败", e);
            if (callback != null) {
                callback.onMediaError(fieldId, "启动相机失败: " + e.getMessage());
            }
            
            return false;
        }
    }

    /**
     * 打开图库选择照片
     * @param activity 活动
     * @param fieldId 字段ID
     * @return 是否成功启动图库
     */
    public boolean pickPicture(Activity activity, String fieldId) {
        this.currentFieldId = fieldId;
        Log.d(TAG, "尝试打开图库选择照片，字段ID: " + fieldId);

        // 创建图库Intent
        Intent pickPhotoIntent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
        pickPhotoIntent.setType("image/*");

        // 确保有图库应用可以处理
        if (pickPhotoIntent.resolveActivity(activity.getPackageManager()) != null) {
            activity.startActivityForResult(pickPhotoIntent, REQUEST_GALLERY);
            return true;
        } else {
            Log.e(TAG, "没有可用的图库应用");
            if (callback != null) {
                callback.onMediaError(fieldId, "没有可用的图库应用");
            }
        }
        return false;
    }

    /**
     * 打开相机录制视频
     * @param activity 活动
     * @param fieldId 字段ID
     * @return 是否成功启动视频录制
     */
    public boolean recordVideo(Activity activity, String fieldId) {
        this.currentFieldId = fieldId;
        Log.d(TAG, "尝试使用自定义相机录制视频，字段ID: " + fieldId);

        try {
            // 创建Intent启动自定义相机Activity
            Intent cameraIntent = new Intent(activity, com.deltaphone.cameramodule.CameraActivity.class);
            cameraIntent.putExtra("model", "1"); // 传递参数，1表示录像模式
            // 视频也可以添加位置信息
            cameraIntent.putExtra("captureLocationOnShot", true);

            // 启动自定义相机Activity
            activity.startActivityForResult(cameraIntent, REQUEST_CUSTOM_CAMERA);

            return true;
        } catch (Exception e) {
            Log.e(TAG, "启动自定义相机录像失败", e);
            if (callback != null) {
                callback.onMediaError(fieldId, "启动录像失败: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * 打开图库选择视频
     * @param activity 活动
     * @param fieldId 字段ID
     * @return 是否成功启动视频图库
     */
    public boolean pickVideo(Activity activity, String fieldId) {
        this.currentFieldId = fieldId;

        // 创建视频图库Intent
        Intent pickVideoIntent = new Intent(Intent.ACTION_PICK, MediaStore.Video.Media.EXTERNAL_CONTENT_URI);
        pickVideoIntent.setType("video/*");

        // 确保有图库应用可以处理
        if (pickVideoIntent.resolveActivity(activity.getPackageManager()) != null) {
            activity.startActivityForResult(pickVideoIntent, REQUEST_VIDEO_GALLERY);
            return true;
        } else {
            Log.e(TAG, "没有可用的视频图库应用");
            if (callback != null) {
                callback.onMediaError(fieldId, "没有可用的视频图库应用");
            }
        }
        return false;
    }

    /**
     * 处理活动结果
     * @param requestCode 请求码
     * @param resultCode 结果码
     * @param data 数据
     */
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        Log.d(TAG, "====== 开始处理活动结果 ======");
        Log.d(TAG, "请求码: " + requestCode + ", 结果码: " + resultCode + ", 当前字段ID: " + currentFieldId);
        Log.d(TAG, "传入的Intent数据: " + (data != null ? "不为null" : "为null"));
        
        // CameraActivity返回的成功状态码是1000，而不是标准的RESULT_OK
        if (resultCode != Activity.RESULT_OK && resultCode != 1000) {
            // 用户取消了操作
            Log.w(TAG, "用户取消了操作，结果码: " + resultCode);
            return;
        }

        // 处理1000状态码的特殊情况（相机模块返回）
        if (resultCode == 1000) {
            Log.d(TAG, "检测到相机模块的成功返回状态码: 1000");
        }

        String filePath = null;
        PhotoInfo photoInfo = null;
        
        try {
            switch (requestCode) {
                case REQUEST_CUSTOM_CAMERA:
                    Log.d(TAG, "----- 处理自定义相机结果 -----");
                    // 自定义相机拍照结果处理
                    if (data != null) {
                        if (data.getStringExtra("imagePath") != null) {
                            filePath = data.getStringExtra("imagePath");
                            if (filePath.startsWith("file://")) {
                                filePath = filePath.substring(7); // 去掉"file://"前缀
                            }
                            Log.d(TAG, "自定义相机拍照/录像成功，文件路径: " + filePath);
                            
                            // 检查文件是否存在
                            File f = new File(filePath);
                            if (!f.exists()) {
                                Log.e(TAG, "文件不存在: " + filePath);
                            } else {
                                Log.d(TAG, "文件存在且大小为: " + f.length() + " 字节");
                            }
                            
                            // 如果是照片，创建照片信息对象
                            if (filePath.toLowerCase().endsWith(".jpg") || filePath.toLowerCase().endsWith(".jpeg")) {
                                Log.d(TAG, "检测到照片文件，将创建照片信息对象");
                                
                                // 从Intent中获取相机模块返回的位置和方位角信息
                                double latitude = data.getDoubleExtra("latitude", 0.0);
                                double longitude = data.getDoubleExtra("longitude", 0.0);
                                float direction = data.getFloatExtra("direction", 0.0f);
                                
                                Log.d(TAG, "从Intent中获取的位置和方位角信息:");
                                Log.d(TAG, "- 纬度: " + latitude);
                                Log.d(TAG, "- 经度: " + longitude);
                                Log.d(TAG, "- 方位角: " + direction);
                                
                                // 检查是否有有效的位置信息
                                boolean hasValidLocation = latitude != 0.0 || longitude != 0.0;

                                // 创建PhotoInfo对象，但不写入EXIF
                                File file = new File(filePath);
                                String fileName = file.getName();
                                try {
                                    //使用当前时间生成格式为YYYY-MM-DD HH:mm:ss格式的文件时间
                                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                    String fileTime = sdf.format(new Date());
                                    photoInfo = new PhotoInfo(fileName, longitude, latitude, direction, filePath, fileTime);
                                    
                                    if (hasValidLocation) {
                                        Log.d(TAG, "已成功创建带位置信息的照片信息对象: " + 
                                                "文件名=" + fileName +
                                                ", 纬度=" + latitude + 
                                                ", 经度=" + longitude + 
                                                ", 方位角=" + direction +
                                                ", 拍摄时间=" + fileTime);
                                    } else {
                                        Log.w(TAG, "相机返回的位置信息无效，创建的照片信息对象不包含位置数据");
                                    }
                                    
                                    Log.d(TAG, "成功创建PhotoInfo对象: " + photoInfo.toString());
                                } catch (Exception e) {
                                    Log.e(TAG, "创建PhotoInfo对象失败: " + e.getMessage(), e);
                                }
                            } else {
                                Log.d(TAG, "文件不是照片格式，不创建PhotoInfo对象");
                            }
                        } else {
                            Log.e(TAG, "自定义相机返回的Intent中不包含imagePath");
                            // 打印intent中的所有extras
                            if (data.getExtras() != null) {
                                Bundle extras = data.getExtras();
                                Log.d(TAG, "Intent extras包含以下键:");
                                for (String key : extras.keySet()) {
                                    Log.d(TAG, "- " + key + ": " + extras.get(key));
                                }
                            }
                        }
                    } else {
                        Log.e(TAG, "自定义相机返回数据为空");
                    }
                    break;
                case REQUEST_CAMERA:
                    // 系统相机拍照结果处理
                    if (!TextUtils.isEmpty(currentPhotoPath)) {
                        // 添加照片到媒体库
                        addPhotoToGallery(currentPhotoPath);
                        filePath = currentPhotoPath;
                        Log.d(TAG, "相机拍照成功，文件路径: " + filePath);
                        
                        // 对于系统相机，我们需要从Intent中获取位置信息
                        if (data != null) {
                            double latitude = data.getDoubleExtra("latitude", 0.0);
                            double longitude = data.getDoubleExtra("longitude", 0.0);
                            float direction = data.getFloatExtra("direction", 0.0f);
                            
                            boolean hasValidLocation = latitude != 0.0 || longitude != 0.0;
                            
                            // 创建PhotoInfo对象
                            if (hasValidLocation) {
                                File file = new File(filePath);
                                String fileName = file.getName();
                                //使用当前时间生成格式为YYYY-MM-DD HH:mm:ss格式的文件时间
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                String fileTime = sdf.format(new Date());
                                photoInfo = new PhotoInfo(fileName, longitude, latitude, direction, filePath, fileTime);
                                
                                Log.d(TAG, "已成功创建系统相机照片信息对象: " + 
                                        "文件名=" + fileName +
                                        ", 纬度=" + latitude + 
                                        ", 经度=" + longitude + 
                                        ", 方位角=" + direction +
                                        ", 拍摄时间=" + fileTime);
                            } else {
                                Log.w(TAG, "相机返回的位置信息无效，无法创建带位置信息的照片对象");
                            }
                        } else {
                            Log.w(TAG, "系统相机未返回Intent数据，无法获取位置信息");
                        }
                    } else {
                        Log.e(TAG, "相机拍照成功，但照片路径为空");
                    }
                    break;
                case REQUEST_GALLERY:
                    // 图库选择照片结果处理
                    if (data != null && data.getData() != null) {
                        Uri selectedImage = data.getData();
                        Log.d(TAG, "图库选择照片成功，URI: " + selectedImage);
                        filePath = getPathFromUri(context, selectedImage);

                        // 如果无法获取真实路径，则复制文件到应用目录
                        if (TextUtils.isEmpty(filePath)) {
                            Log.d(TAG, "无法从URI获取真实路径，复制文件到应用目录");
                            filePath = copyFileToInternal(selectedImage, "image");
                        }

                        Log.d(TAG, "图库选择照片成功，文件路径: " + filePath);
                    } else {
                        Log.e(TAG, "图库选择照片成功，但数据为空");
                    }
                    break;
                case REQUEST_VIDEO:
                    // 相机录制视频结果处理
                    if (data != null && data.getData() != null) {
                        Uri videoUri = data.getData();
                        filePath = getPathFromUri(context, videoUri);

                        // 如果无法获取真实路径，则复制文件到应用目录
                        if (TextUtils.isEmpty(filePath)) {
                            filePath = copyFileToInternal(videoUri, "video");
                        }
                    }
                    break;
                case REQUEST_VIDEO_GALLERY:
                    // 图库选择视频结果处理
                    if (data != null && data.getData() != null) {
                        Uri selectedVideo = data.getData();
                        filePath = getPathFromUri(context, selectedVideo);

                        // 如果无法获取真实路径，则复制文件到应用目录
                        if (TextUtils.isEmpty(filePath)) {
                            filePath = copyFileToInternal(selectedVideo, "video");
                        }
                    }
                    break;
            }

            // 回调处理结果
            if (callback != null) {
                if (photoInfo != null) {
                    // 如果有照片信息对象，使用带照片信息的回调
                    Log.d(TAG, "有照片信息对象，调用onPhotoSelected回调");
                    Log.d(TAG, "照片信息: 字段ID=" + currentFieldId + 
                          ", 文件名=" + photoInfo.getFileName() + 
                          ", 文件路径=" + photoInfo.getFilePath() + 
                          ", 纬度=" + photoInfo.getLatitude() + 
                          ", 经度=" + photoInfo.getLongitude() + 
                          ", 方位角=" + photoInfo.getDirection());
                    
                    try {
                        callback.onPhotoSelected(currentFieldId, photoInfo);
                        Log.d(TAG, "onPhotoSelected回调执行成功");
                    } catch (Exception e) {
                        Log.e(TAG, "onPhotoSelected回调执行失败: " + e.getMessage(), e);
                    }
                } else if (!TextUtils.isEmpty(filePath)) {
                    // 否则使用普通回调
                    Log.d(TAG, "没有照片信息对象，调用onMediaSelected回调");
                    Log.d(TAG, "媒体信息: 字段ID=" + currentFieldId + ", 文件路径=" + filePath);
                    
                    try {
                        callback.onMediaSelected(currentFieldId, filePath);
                        Log.d(TAG, "onMediaSelected回调执行成功");
                    } catch (Exception e) {
                        Log.e(TAG, "onMediaSelected回调执行失败: " + e.getMessage(), e);
                    }
                } else {
                    Log.e(TAG, "无法获取媒体文件路径");
                    callback.onMediaError(currentFieldId, "无法获取媒体文件路径");
                }
            } else {
                Log.e(TAG, "回调对象为null，无法处理媒体结果");
            }
        } catch (Exception e) {
            Log.e(TAG, "处理媒体结果出错: " + e.getMessage(), e);
            e.printStackTrace();
            if (callback != null) {
                callback.onMediaError(currentFieldId, "处理媒体结果出错: " + e.getMessage());
            }
        }
        
        Log.d(TAG, "====== 结束处理活动结果 ======");
    }

    /**
     * 创建图片文件
     * @return 创建的图片文件
     * @throws IOException 创建文件时可能抛出的异常
     */
    private File createImageFile() throws IOException {
        // 创建图片文件名
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
        String imageFileName = "JPEG_" + timeStamp + "_";
        File storageDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES);

        if (!storageDir.exists()) {
            if (!storageDir.mkdirs()) {
                throw new IOException("无法创建图片存储目录");
            }
        }

        File image = File.createTempFile(
                imageFileName,  /* 前缀 */
                ".jpg",         /* 后缀 */
                storageDir      /* 目录 */
        );

        return image;
    }

    /**
     * 将照片添加到媒体库
     * @param filePath 文件路径
     */
    private void addPhotoToGallery(String filePath) {
        Intent mediaScanIntent = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
        File f = new File(filePath);
        Uri contentUri = Uri.fromFile(f);
        mediaScanIntent.setData(contentUri);
        context.sendBroadcast(mediaScanIntent);
    }

    /**
     * 从URI获取文件路径
     * @param context 上下文
     * @param uri URI
     * @return 文件路径
     */
    public static String getPathFromUri(Context context, Uri uri) {
        if (uri == null) {
            return null;
        }

        // 文件方案直接返回路径
        if ("file".equalsIgnoreCase(uri.getScheme())) {
            return uri.getPath();
        }

        // 内容方案需要查询媒体库
        if ("content".equalsIgnoreCase(uri.getScheme())) {
            String[] projection = { MediaStore.Images.Media.DATA };
            ContentResolver resolver = context.getContentResolver();
            Cursor cursor = resolver.query(uri, projection, null, null, null);

            if (cursor != null) {
                try {
                    if (cursor.moveToFirst()) {
                        int columnIndex = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);
                        return cursor.getString(columnIndex);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "从URI获取路径出错", e);
                } finally {
                    cursor.close();
                }
            }
        }

        return null;
    }

    /**
     * 复制文件到内部存储
     * @param uri 源文件URI
     * @param type 文件类型（图片/视频）
     * @return 复制后的文件路径
     */
    private String copyFileToInternal(Uri uri, String type) {
        try {
            InputStream inputStream = context.getContentResolver().openInputStream(uri);
            if (inputStream == null) {
                Log.e(TAG, "无法打开输入流");
                return null;
            }

            String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
            String fileName = type + "_" + timeStamp;
            String extension = type.equals("image") ? ".jpg" : ".mp4";

            File directory;
            if (type.equals("image")) {
                directory = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES);
            } else {
                directory = context.getExternalFilesDir(Environment.DIRECTORY_MOVIES);
            }

            if (!directory.exists()) {
                if (!directory.mkdirs()) {
                    Log.e(TAG, "无法创建目录");
                    return null;
                }
            }

            File outputFile = new File(directory, fileName + extension);
            FileOutputStream outputStream = new FileOutputStream(outputFile);

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            inputStream.close();
            outputStream.close();

//            // 如果是图片，进行压缩处理
//            if (type.equals("image")) {
//                compressImage(outputFile.getAbsolutePath());
//            }

            return outputFile.getAbsolutePath();
        } catch (Exception e) {
            Log.e(TAG, "复制文件失败", e);
            return null;
        }
    }

    /**
     * 压缩图片
     * @param imagePath 图片路径
     * @return 压缩后的图片路径
     */
    private String compressImage(String imagePath) {
        try {
            // 加载图片
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = true;
            BitmapFactory.decodeFile(imagePath, options);

            // 计算压缩比例
            int maxWidth = 1024;
            int maxHeight = 1024;
            int width = options.outWidth;
            int height = options.outHeight;
            int inSampleSize = 1;

            if (height > maxHeight || width > maxWidth) {
                final int halfHeight = height / 2;
                final int halfWidth = width / 2;

                while ((halfHeight / inSampleSize) >= maxHeight && (halfWidth / inSampleSize) >= maxWidth) {
                    inSampleSize *= 2;
                }
            }

            // 加载压缩后的图片
            options.inJustDecodeBounds = false;
            options.inSampleSize = inSampleSize;
            Bitmap bitmap = BitmapFactory.decodeFile(imagePath, options);

            // 保存压缩后的图片
            File file = new File(imagePath);
            FileOutputStream outputStream = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.JPEG, 85, outputStream);
            outputStream.flush();
            outputStream.close();

            return imagePath;
        } catch (Exception e) {
            Log.e(TAG, "压缩图片失败", e);
            return imagePath;
        }
    }

    /**
     * 获取当前处理的字段ID
     * @return 字段ID
     */
    public String getCurrentFieldId() {
        return currentFieldId;
    }

    /**
     * 处理权限请求结果
     * @param requestCode 请求码
     * @param permissions 权限
     * @param grantResults 授权结果
     */
    public void handlePermissionResult(int requestCode, String[] permissions, int[] grantResults) {
        Log.d(TAG, "处理权限请求结果: requestCode=" + requestCode);

        if (grantResults.length == 0) {
            Log.w(TAG, "权限请求被取消");
            if (callback != null) {
                callback.onMediaError(currentFieldId, "权限请求被取消");
            }
            return;
        }

        // 相机权限请求结果处理
        if (requestCode == 2002) { // REQUEST_PERMISSION_CAMERA
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != android.content.pm.PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }

            if (allGranted) {
                Log.d(TAG, "相机权限已授予");
                // 权限已获取，可以继续操作，但不自动触发拍照
                if (callback != null) {
                    callback.onMediaError(currentFieldId, "相机权限已获取，请重新点击拍照按钮");
                }
            } else {
                Log.w(TAG, "相机权限被拒绝");
                if (callback != null) {
                    callback.onMediaError(currentFieldId, "无法使用相机：权限被拒绝");
                }
            }
        }
    }

    /**
     * 手动触发媒体回调通知，用于非标准媒体选择流程（如签名）
     * @param fieldId 字段ID
     * @param filePath 文件路径
     */
    public void notifyMediaCallbacks(String fieldId, String filePath) {
        if (callback != null && fieldId != null && filePath != null) {
            Log.d(TAG, "手动触发媒体选择回调: " + fieldId + " -> " + filePath);
            callback.onMediaSelected(fieldId, filePath);
        }
    }

    /**
     * 释放资源
     */
    public void release() {
        try {
            if (mediaPlayer != null) {
                mediaPlayer.release();
                mediaPlayer = null;
            }
            
            context = null;
        } catch (Exception e) {
            Log.e(TAG, "释放资源时出错", e);
        }
    }

    @Override
    public void onSensorChanged(SensorEvent event) {
        if (event.sensor.getType() == Sensor.TYPE_ACCELEROMETER) {
            System.arraycopy(event.values, 0, accelerometerReading, 0, accelerometerReading.length);
        } else if (event.sensor.getType() == Sensor.TYPE_MAGNETIC_FIELD) {
            System.arraycopy(event.values, 0, magnetometerReading, 0, magnetometerReading.length);
        }
        
        // 更新方位角
        updateOrientationAngles();
    }

    /**
     * 更新方位角
     */
    private void updateOrientationAngles() {
        // 更新旋转矩阵
        boolean rotationOk = SensorManager.getRotationMatrix(rotationMatrix, null, accelerometerReading, magnetometerReading);
        
        if (rotationOk) {
            // 获取方位角、俯仰角和滚动角
            SensorManager.getOrientation(rotationMatrix, orientationAngles);
            
            // orientationAngles[0]是方位角（方向），单位为弧度，需要转换为角度
            float azimuthRadians = orientationAngles[0];
            float azimuthDegrees = (float) Math.toDegrees(azimuthRadians);
            
            // 确保角度在0-360之间
            azimuthDegrees = (azimuthDegrees + 360) % 360;
            
            // 更新当前方位角
            currentDirection = azimuthDegrees;
            
            // 只在变化较大时输出日志
            if (Math.abs(currentDirection - azimuthDegrees) > 5) {
                Log.d(TAG, "当前方位角: " + azimuthDegrees + "°");
            }
        }
    }

    @Override
    public void onAccuracyChanged(Sensor sensor, int accuracy) {
        // 不需要处理传感器精度变化
    }

    @Override
    public void onLocationChanged(Location location) {
        if (location != null) {
            this.currentLocation = location;
            Log.d(TAG, "位置已更新: " + location.getLatitude() + ", " + location.getLongitude());
        }
    }

    @Override
    public void onStatusChanged(String provider, int status, Bundle extras) {
        // Android新版本已废弃此方法
    }

    @Override
    public void onProviderEnabled(String provider) {
        Log.d(TAG, "位置提供程序已启用: " + provider);
    }

    @Override
    public void onProviderDisabled(String provider) {
        Log.d(TAG, "位置提供程序已禁用: " + provider);
    }
}
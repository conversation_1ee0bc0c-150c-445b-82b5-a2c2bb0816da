package io.dcloud.uniplugin.model;

import java.util.List;

/**
 * 样品批次创建请求模型
 */
public class YplzCreateRequest {
    /**
     * 批次编号（项目编号）
     */
    private String batchCode;
    
    /**
     * 批次名称
     */
    private String batchName;
    
    /**
     * 批次状态
     */
    private Long batchState;
    
    /**
     * 批次类型（县0/市1/省2）
     */
    private Integer batchType;
    
    /**
     * 运送信息
     */
    private String deliveryMessage;
    
    /**
     * 运送方式
     */
    private String deliveryType;
    
    /**
     * 接样单位
     */
    private String receiveOrganization;
    
    /**
     * 接样单位ID（检测机构ID）
     */
    private Long receiveOrganizationId;
    
    /**
     * 接样人姓名
     */
    private String receiverName;
    
    /**
     * 接样人联系方式
     */
    private String receiverPhone;
    
    /**
     * 接样人签名
     */
    private String receiverSignature;
    
    /**
     * 样品数量
     */
    private Long sampleNumber;
    
    /**
     * 送样人ID
     */
    private Long senderId;
    
    /**
     * 送样人姓名
     */
    private String senderName;
    
    /**
     * 送样人联系方式
     */
    private String senderPhone;
    
    /**
     * 送样人签名
     */
    private String senderSignature;
    
    /**
     * 送样单位
     */
    private String sendOrganization;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 样品列表
     */
    private List<YPLZ> samples;

    public String getBatchCode() {
        return batchCode;
    }
    
    public void setBatchCode(String batchCode) {
        this.batchCode = batchCode;
    }

    public String getBatchName() {
        return batchName;
    }

    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }

    public Long getBatchState() {
        return batchState;
    }

    public void setBatchState(Long batchState) {
        this.batchState = batchState;
    }
    
    public Integer getBatchType() {
        return batchType;
    }
    
    public void setBatchType(Integer batchType) {
        this.batchType = batchType;
    }

    public String getDeliveryMessage() {
        return deliveryMessage;
    }

    public void setDeliveryMessage(String deliveryMessage) {
        this.deliveryMessage = deliveryMessage;
    }

    public String getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(String deliveryType) {
        this.deliveryType = deliveryType;
    }
    
    public String getReceiveOrganization() {
        return receiveOrganization;
    }
    
    public void setReceiveOrganization(String receiveOrganization) {
        this.receiveOrganization = receiveOrganization;
    }
    
    public Long getReceiveOrganizationId() {
        return receiveOrganizationId;
    }
    
    public void setReceiveOrganizationId(Long receiveOrganizationId) {
        this.receiveOrganizationId = receiveOrganizationId;
    }
    
    public String getReceiverName() {
        return receiverName;
    }
    
    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }
    
    public String getReceiverPhone() {
        return receiverPhone;
    }
    
    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
    }
    
    public String getReceiverSignature() {
        return receiverSignature;
    }
    
    public void setReceiverSignature(String receiverSignature) {
        this.receiverSignature = receiverSignature;
    }
    
    public Long getSampleNumber() {
        return sampleNumber;
    }
    
    public void setSampleNumber(Long sampleNumber) {
        this.sampleNumber = sampleNumber;
    }
    
    public Long getSenderId() {
        return senderId;
    }
    
    public void setSenderId(Long senderId) {
        this.senderId = senderId;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public String getSenderPhone() {
        return senderPhone;
    }

    public void setSenderPhone(String senderPhone) {
        this.senderPhone = senderPhone;
    }

    public String getSenderSignature() {
        return senderSignature;
    }

    public void setSenderSignature(String senderSignature) {
        this.senderSignature = senderSignature;
    }

    public String getSendOrganization() {
        return sendOrganization;
    }

    public void setSendOrganization(String sendOrganization) {
        this.sendOrganization = sendOrganization;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<YPLZ> getSamples() {
        return samples;
    }

    public void setSamples(List<YPLZ> samples) {
        this.samples = samples;
    }
} 
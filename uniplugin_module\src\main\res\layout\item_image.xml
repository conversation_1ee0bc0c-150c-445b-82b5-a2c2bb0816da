<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/imagePreview"
                android:layout_width="match_parent"
                android:layout_height="150dp"
                android:scaleType="centerCrop"
                android:background="#EEEEEE" />

            <ImageButton
                android:id="@+id/buttonDelete"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_gravity="top|end"
                android:layout_margin="8dp"
                android:background="@android:drawable/ic_menu_close_clear_cancel"
                android:contentDescription="删除" />
        </FrameLayout>

        <TextView
            android:id="@+id/textFileName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:padding="8dp"
            android:text="文件名"
            android:textSize="14sp" />

    </LinearLayout>
</androidx.cardview.widget.CardView> 
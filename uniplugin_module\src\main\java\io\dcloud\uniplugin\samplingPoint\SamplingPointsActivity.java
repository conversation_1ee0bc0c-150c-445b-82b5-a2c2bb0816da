package io.dcloud.uniplugin.samplingPoint;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.IntentFilter;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;

import java.util.ArrayList;
import java.util.List;

import io.dcloud.uniplugin.db.DatabaseConstants;
import io.dcloud.uniplugin.db.DatabaseHelper;
import io.dcloud.uniplugin.enums.SamplingPointStatus;
import io.dcloud.uniplugin.model.DccyVO;
import io.dcloud.uniplugin.utils.SamplingPointSyncManager;
import uni.dcloud.io.uniplugin_module.R;

public class SamplingPointsActivity extends AppCompatActivity {

    private static final String TAG = "SamplingPointsActivity";

    private TabLayout tabLayout;
    private ViewPager2 viewPager;
    private ProgressBar progressBar;
    private SwipeRefreshLayout swipeRefreshLayout;
    private androidx.appcompat.widget.SearchView searchView;
    private Button btnSyncData;
    private CountDownTimer syncCooldownTimer;
    private static final int SYNC_COOLDOWN_TIME = 10000;

    private SamplingPointsPagerAdapter pagerAdapter;
    private DatabaseHelper dbHelper;

    // 添加样点状态计数变量
    private int toBeInvestigatedCount = 0;
    private int toBeSubmittedCount = 0;
    private int submittedCount = 0;
    private int toBeRectifiedCount = 0;

    // 广播接收器，用于接收表单保存成功的广播
    private BroadcastReceiver refreshReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, android.content.Intent intent) {
            if ("io.dcloud.uniplugin.REFRESH_SAMPLING_POINTS".equals(intent.getAction())) {
                Log.d(TAG, "接收到刷新样点列表的广播");
                
                // 检查是否需要重新加载数据（同步远程数据）
                boolean needReload = intent.getBooleanExtra("RELOAD_SAMPLING_POINTS", false);
                
                if (needReload) {
                    Log.d(TAG, "广播要求重新加载远程数据，开始同步");
                    // 使用同步方式下载最新数据
                    syncSamplingPointsData();
                } else {
                    // 仅从本地数据库重新加载数据，但强制刷新UI
                    Log.d(TAG, "广播要求刷新本地数据，不同步远程");
                    loadSamplingPoints(true); // 使用强制刷新模式
                }
                
                // 检查是否需要切换到已提交标签页
                boolean switchToSubmitted = intent.getBooleanExtra("SWITCH_TO_SUBMITTED_TAB", false);
                if (switchToSubmitted) {
                    Log.d(TAG, "广播要求切换到已提交标签页");
                    // 延迟切换，确保数据加载完成
                    new Handler().postDelayed(() -> {
                        // 切换到已提交标签页（索引为2）
                        viewPager.setCurrentItem(2);
                        Log.d(TAG, "已切换到已提交标签页(索引2)");
                    }, 500);
                } else {
                    // 否则切换到待提交标签页（索引为1）
                    new Handler().postDelayed(() -> {
                        viewPager.setCurrentItem(1);
                        Log.d(TAG, "已切换到待提交标签页(索引1)");
                    }, 500);
                }
            }
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sampling_points);

        // 设置ActionBar的返回按钮
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setTitle("评价·单元管理");
        }

        // 初始化视图
        tabLayout = findViewById(R.id.tabLayout);
        viewPager = findViewById(R.id.viewPager);
        progressBar = findViewById(R.id.progressBar);
        swipeRefreshLayout = findViewById(R.id.swipeRefreshLayout);
        searchView = findViewById(R.id.searchView);
        btnSyncData = findViewById(R.id.btnSyncData);
        
        // 注册广播接收器
        IntentFilter intentFilter = new IntentFilter("io.dcloud.uniplugin.REFRESH_SAMPLING_POINTS");
        registerReceiver(refreshReceiver, intentFilter);

        // 设置同步按钮点击事件
        btnSyncData.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 调用同步方法
                syncSamplingPointsData();
//                Toast.makeText(SamplingPointsActivity.this, "正在同步数据...", Toast.LENGTH_SHORT).show();
            }
        });

        // 设置搜索框监听器
        searchView.setOnQueryTextListener(new androidx.appcompat.widget.SearchView.OnQueryTextListener() {
            @Override
            public boolean onQueryTextSubmit(String query) {
                // 提交搜索时触发
                loadSamplingPoints(query);
                return true;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                // 文本变化时触发
                if (newText.isEmpty()) {
                    // 如果搜索框为空，加载所有数据
                    loadSamplingPoints();
                } else {
                    // 否则按关键字搜索
                    loadSamplingPoints(newText);
                }
                return true;
            }
        });

        // 设置搜索框关闭按钮监听
        searchView.setOnCloseListener(new androidx.appcompat.widget.SearchView.OnCloseListener() {
            @Override
            public boolean onClose() {
                // 关闭搜索时，重新加载所有数据
                loadSamplingPoints();
                return false;
            }
        });

        // 初始化数据库
        dbHelper = DatabaseHelper.getInstance(this);

        // 设置下拉刷新
        swipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                // 只从数据库重新加载数据，不同步远程数据
                loadSamplingPoints();
                swipeRefreshLayout.setRefreshing(false);
            }
        });

        // 初始化ViewPager和TabLayout
        setupViewPager();

        // 加载数据
        loadSamplingPoints();

        // 移除对不存在的initViews()的调用
        updateStatusCounts();
    }

    @Override
    protected void onDestroy() {
        // 注销广播接收器
        try {
            unregisterReceiver(refreshReceiver);
        } catch (Exception e) {
            Log.e(TAG, "注销广播接收器失败: " + e.getMessage());
        }
        
        // 取消倒计时计时器
        if (syncCooldownTimer != null) {
            syncCooldownTimer.cancel();
            syncCooldownTimer = null;
        }
        
        super.onDestroy();
        // 关闭数据库连接
        if (dbHelper != null) {
            dbHelper.close();
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * 处理活动结果
     * 用于捕获表单编辑活动返回的结果，以便在表单保存后刷新数据
     */
    @Override
    protected void onActivityResult(int requestCode, int resultCode, android.content.Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        // 如果返回结果是RESULT_OK，表示表单已保存，需要刷新数据
        if (resultCode == RESULT_OK) {
            boolean needReload = data != null && data.getBooleanExtra("RELOAD_SAMPLING_POINTS", false);
            boolean switchToSubmitted = data != null && data.getBooleanExtra("SWITCH_TO_SUBMITTED_TAB", false);
            
            Log.d(TAG, "从表单编辑活动返回，结果: RESULT_OK" 
                    + (needReload ? "，需要重新加载数据" : "") 
                    + (switchToSubmitted ? "，需要切换到已提交标签页" : "")
                    + "，开始刷新数据");
            
            if (needReload) {
                // 如果明确要求重新加载，先同步远程数据
                syncSamplingPointsData();
            } else {
                // 否则只加载本地数据，但强制刷新UI
                loadSamplingPoints(true); // 使用强制刷新模式
            }
            
            // 根据标记决定切换到哪个标签页，使用延迟确保数据加载完成
            new Handler().postDelayed(() -> {
                if (switchToSubmitted) {
                    viewPager.setCurrentItem(2); // 切换到已提交标签页
                    Log.d(TAG, "已切换到已提交标签页(索引2)");
                } else if (needReload) {
                    viewPager.setCurrentItem(1); // 切换到待提交标签页
                    Log.d(TAG, "已切换到待提交标签页(索引1)");
                }
            }, 500); // 500毫秒延迟确保数据加载完成
        } else {
            Log.d(TAG, "从表单编辑活动返回，结果: " + resultCode + "，不刷新数据");
        }
    }

    /**
     * 设置ViewPager和TabLayout
     */
    private void setupViewPager() {
        pagerAdapter = new SamplingPointsPagerAdapter(this);
        viewPager.setAdapter(pagerAdapter);

        // 设置ViewPager2实例到适配器
        pagerAdapter.setViewPager(viewPager);

        // 添加页面切换监听器
        viewPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                
                // 转换position为1开始的索引
                int tabPosition = position + 1;
                
                // 获取当前Fragment并更新其tabPosition
                Fragment currentFragment = getSupportFragmentManager()
                        .findFragmentByTag("f" + pagerAdapter.getItemId(position));
                
                if (currentFragment instanceof SamplingPointListFragment) {
                    ((SamplingPointListFragment) currentFragment).setTabPosition(tabPosition);
                    Log.d(TAG, "页面切换到: " + position + ", 已更新Fragment的tabPosition为: " + tabPosition);
                    
                    // 切换页面时，强制刷新当前Fragment的数据，确保显示最新数据
                    List<DccyVO> latestData = null;
                    switch (position) {
                        case 0: // 待调查
                            latestData = getSamplingPointsByStatus(SamplingPointStatus.PENDING_SURVEY, "");
                            pagerAdapter.pendingSurveyPoints = latestData != null ? latestData : new ArrayList<>();
                            break;
                        case 1: // 待提交
                            latestData = getSamplingPointsByStatus(SamplingPointStatus.PENDING_SUBMIT, "");
                            pagerAdapter.pendingSubmitPoints = latestData != null ? latestData : new ArrayList<>();
                            break;
                        case 2: // 已提交
                            latestData = getSamplingPointsByStatus(SamplingPointStatus.SUBMITTED, "");
                            pagerAdapter.submittedPoints = latestData != null ? latestData : new ArrayList<>();
                            break;
                        case 3: // 待整改
                            latestData = getSamplingPointsByStatus(SamplingPointStatus.PENDING_RECTIFY, "");
                            pagerAdapter.pendingRectifyPoints = latestData != null ? latestData : new ArrayList<>();
                            break;
                    }
                    
                    // 如果获取到了最新数据，更新当前Fragment
                    if (latestData != null) {
                        ((SamplingPointListFragment) currentFragment).updateData(latestData);
                        Log.d(TAG, "页面切换时刷新数据: position=" + position + ", 数据量=" + latestData.size());
                    }
                } else {
                    // 如果Fragment还没有创建，说明这是第一次切换到这个Tab
                    // 我们需要确保适配器中有最新的数据，这样当Fragment创建时就能使用最新数据
                    Log.d(TAG, "页面切换到未创建的Fragment: " + position + ", 准备最新数据");
                    
                    switch (position) {
                        case 0: // 待调查
                            pagerAdapter.pendingSurveyPoints = getSamplingPointsByStatus(SamplingPointStatus.PENDING_SURVEY, "");
                            break;
                        case 1: // 待提交
                            pagerAdapter.pendingSubmitPoints = getSamplingPointsByStatus(SamplingPointStatus.PENDING_SUBMIT, "");
                            break;
                        case 2: // 已提交
                            pagerAdapter.submittedPoints = getSamplingPointsByStatus(SamplingPointStatus.SUBMITTED, "");
                            break;
                        case 3: // 待整改
                            pagerAdapter.pendingRectifyPoints = getSamplingPointsByStatus(SamplingPointStatus.PENDING_RECTIFY, "");
                            Log.d(TAG, "页面切换时更新待整改数据: " + (pagerAdapter.pendingRectifyPoints != null ? pagerAdapter.pendingRectifyPoints.size() : 0));
                            break;
                    }
                    
                    // 强制适配器重新创建Fragment
                    pagerAdapter.notifyItemChanged(position);
                }
            }
        });

        // 连接TabLayout和ViewPager2
        new TabLayoutMediator(tabLayout, viewPager, new TabLayoutMediator.TabConfigurationStrategy() {
            @Override
            public void onConfigureTab(@NonNull TabLayout.Tab tab, int position) {
                switch (position) {
                    case 0:
                        tab.setText("待调查 (" + toBeInvestigatedCount + ")");
                        break;
                    case 1:
                        tab.setText("待提交 (" + toBeSubmittedCount + ")");
                        break;
                    case 2:
                        tab.setText("已提交 (" + submittedCount + ")");
                        break;
                    case 3:
                        tab.setText("待整改 (" + toBeRectifiedCount + ")");
                        break;
                }
            }
        }).attach();
    }

    /**
     * 同步样点数据
     * 使用SamplingPointSyncManager下载最新数据，然后刷新本地列表
     */
    private void syncSamplingPointsData() {
        // 禁用同步按钮
        btnSyncData.setEnabled(false);
        btnSyncData.setText("同步中...");
        
        // 在后台线程中执行同步
        new Thread(() -> {
            try {
                // 使用带回调的同步方式下载数据
                SamplingPointSyncManager.getInstance().downloadDataSynchronously(this, new SamplingPointSyncManager.SyncCallback() {
                    @Override
                    public void onSuccess() {
                        // 确保在主线程中执行UI更新
                        runOnUiThread(() -> {
                            Log.d(TAG, "数据同步完成，开始刷新UI");
                            
                            // 强制刷新数据
                            loadSamplingPoints(true);
                            
                            // 延迟500毫秒后再次强制刷新当前可见的Fragment，确保数据完全加载
                            new Handler().postDelayed(() -> {
                                try {
                                    // 重新获取所有状态的最新数据，而不仅仅是当前可见的
                                    List<DccyVO> pendingSurveyPoints = getSamplingPointsByStatus(SamplingPointStatus.PENDING_SURVEY, "");
                                    List<DccyVO> pendingSubmitPoints = getSamplingPointsByStatus(SamplingPointStatus.PENDING_SUBMIT, "");
                                    List<DccyVO> submittedPoints = getSamplingPointsByStatus(SamplingPointStatus.SUBMITTED, "");
                                    List<DccyVO> pendingRectifyPoints = getSamplingPointsByStatus(SamplingPointStatus.PENDING_RECTIFY, "");
                                    
                                    Log.d(TAG, "延迟刷新获取的数据: 待调查=" + pendingSurveyPoints.size() + 
                                              ", 待提交=" + pendingSubmitPoints.size() + 
                                              ", 已提交=" + submittedPoints.size() + 
                                              ", 待整改=" + pendingRectifyPoints.size());
                                    
                                    // 更新适配器中的所有数据
                                    pagerAdapter.pendingSurveyPoints = pendingSurveyPoints != null ? pendingSurveyPoints : new ArrayList<>();
                                    pagerAdapter.pendingSubmitPoints = pendingSubmitPoints != null ? pendingSubmitPoints : new ArrayList<>();
                                    pagerAdapter.submittedPoints = submittedPoints != null ? submittedPoints : new ArrayList<>();
                                    pagerAdapter.pendingRectifyPoints = pendingRectifyPoints != null ? pendingRectifyPoints : new ArrayList<>();
                                    
                                    // 使用适配器的updateData方法更新所有Fragment
                                    pagerAdapter.updateData(pendingSurveyPoints, pendingSubmitPoints, submittedPoints, pendingRectifyPoints);
                                    
                                    // 获取当前页面位置进行额外的强制刷新
                                    int currentPosition = viewPager.getCurrentItem();
                                    List<DccyVO> currentData = null;
                                    switch (currentPosition) {
                                        case 0:
                                            currentData = pendingSurveyPoints;
                                            break;
                                        case 1:
                                            currentData = pendingSubmitPoints;
                                            break;
                                        case 2:
                                            currentData = submittedPoints;
                                            break;
                                        case 3:
                                            currentData = pendingRectifyPoints;
                                            break;
                                    }
                                    
                                    // 对当前可见的Fragment进行额外的强制更新
                                    if (currentData != null) {
                                        pagerAdapter.forceUpdateFragment(currentPosition, currentData);
                                        Log.d(TAG, "延迟刷新成功: position=" + currentPosition + ", 数据量=" + currentData.size());
                                    }
                                    
                                    // 针对ViewPager2的特殊处理：确保所有可能存在的Fragment都被更新
                                    // 由于ViewPager2可能缓存了相邻的Fragment，我们需要强制更新它们
                                    for (int pos = 0; pos < 4; pos++) {
                                        List<DccyVO> dataForPos = null;
                                        switch (pos) {
                                            case 0:
                                                dataForPos = pendingSurveyPoints;
                                                break;
                                            case 1:
                                                dataForPos = pendingSubmitPoints;
                                                break;
                                            case 2:
                                                dataForPos = submittedPoints;
                                                break;
                                            case 3:
                                                dataForPos = pendingRectifyPoints;
                                                break;
                                        }
                                        
                                        if (dataForPos != null) {
                                            // 尝试更新可能存在的Fragment
                                            Fragment fragment = getSupportFragmentManager()
                                                    .findFragmentByTag("f" + pagerAdapter.getItemId(pos));
                                            if (fragment instanceof SamplingPointListFragment) {
                                                ((SamplingPointListFragment) fragment).updateData(dataForPos);
                                                Log.d(TAG, "强制更新Fragment[" + pos + "]数据: " + dataForPos.size() + "条");
                                            } else {
                                                Log.d(TAG, "Fragment[" + pos + "]尚未创建，数据将在切换时更新");
                                            }
                                        }
                                    }
                                    
                                    // 重新计算并更新状态统计数字和Tab标签文本
                                    updateStatusCounts();
                                    Log.d(TAG, "已更新Tab标签统计数字");
                                    
                                } catch (Exception e) {
                                    Log.e(TAG, "延迟刷新Fragment失败: " + e.getMessage());
                                }
                            }, 500);
                            
                            // 显示同步成功提示
                            Toast.makeText(SamplingPointsActivity.this, "数据同步成功", Toast.LENGTH_SHORT).show();
                            
                            // 开始倒计时
                            startSyncCooldown();
                        });
                    }
                    
                    @Override
                    public void onFailure(String errorMessage) {
                        // 同步失败处理
                        Log.e(TAG, "同步数据失败: " + errorMessage);
                        runOnUiThread(() -> {
                            // 尝试只刷新本地数据
                            loadSamplingPoints();
                            
                            // 显示同步失败提示
                            Toast.makeText(SamplingPointsActivity.this, "数据同步失败: " + errorMessage, Toast.LENGTH_SHORT).show();
                            
                            // 恢复按钮状态
                            btnSyncData.setEnabled(true);
                            btnSyncData.setText("同步在线数据");
                        });
                    }
                });
                
            } catch (Exception e) {
                // 同步失败处理
                Log.e(TAG, "同步数据异常: " + e.getMessage());
                runOnUiThread(() -> {
                    // 尝试只刷新本地数据
                    loadSamplingPoints();
                    
                    // 显示同步失败提示
                    Toast.makeText(SamplingPointsActivity.this, "数据同步异常: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    
                    // 恢复按钮状态
                    btnSyncData.setEnabled(true);
                    btnSyncData.setText("同步在线数据");
                });
            }
        }).start();
    }
    
    /**
     * 开始同步按钮的冷却倒计时
     */
    private void startSyncCooldown() {
        // 取消可能存在的旧计时器
        if (syncCooldownTimer != null) {
            syncCooldownTimer.cancel();
        }
        
        // 创建新的倒计时计时器
        syncCooldownTimer = new CountDownTimer(SYNC_COOLDOWN_TIME, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                // 更新按钮文本显示剩余时间
                int secondsLeft = (int) (millisUntilFinished / 1000);
                btnSyncData.setText("同步冷却中(" + secondsLeft + "s)");
            }
            
            @Override
            public void onFinish() {
                // 倒计时结束，恢复按钮状态
                btnSyncData.setEnabled(true);
                btnSyncData.setText("同步在线数据");
            }
        }.start();
    }
    
    /**
     * 加载样点数据
     */
    private void loadSamplingPoints() {
        loadSamplingPoints("", false);
    }
    
    /**
     * 强制刷新样点数据（用于同步数据后调用）
     * @param forceRefresh 是否强制刷新
     */
    private void loadSamplingPoints(boolean forceRefresh) {
        loadSamplingPoints("", forceRefresh);
    }

    /**
     * 根据关键字加载样点数据
     *
     * @param keyword 搜索关键字
     */
    private void loadSamplingPoints(String keyword) {
        loadSamplingPoints(keyword, false);
    }
    
    /**
     * 根据关键字加载样点数据，可选强制刷新
     *
     * @param keyword 搜索关键字
     * @param forceRefresh 是否强制刷新UI
     */
    private void loadSamplingPoints(String keyword, boolean forceRefresh) {
        progressBar.setVisibility(View.VISIBLE);

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    // 获取各状态的样点数据
                    List<DccyVO> pendingSurveyPoints = getSamplingPointsByStatus(SamplingPointStatus.PENDING_SURVEY, keyword);
                    List<DccyVO> pendingSubmitPoints = getSamplingPointsByStatus(SamplingPointStatus.PENDING_SUBMIT, keyword);
                    List<DccyVO> submittedPoints = getSamplingPointsByStatus(SamplingPointStatus.SUBMITTED, keyword);
                    List<DccyVO> pendingRectifyPoints = getSamplingPointsByStatus(SamplingPointStatus.PENDING_RECTIFY, keyword);
                    
                    // 收集所有样点ID，用于预加载表单配置
                    List<String> allPointIds = new ArrayList<>();
                    collectSamplingPointIds(pendingSurveyPoints, allPointIds);
                    collectSamplingPointIds(pendingSubmitPoints, allPointIds);
                    collectSamplingPointIds(submittedPoints, allPointIds);
                    collectSamplingPointIds(pendingRectifyPoints, allPointIds);

                    // 日志输出当前数据状态
                    Log.d(TAG, "加载到的数据: 待调查=" + pendingSurveyPoints.size() + 
                          ", 待提交=" + pendingSubmitPoints.size() + 
                          ", 已提交=" + submittedPoints.size() + 
                          ", 待整改=" + pendingRectifyPoints.size());
                          
                    final boolean needsUIUpdate = forceRefresh || 
                                                 pendingSurveyPoints.size() > 0 || 
                                                 pendingSubmitPoints.size() > 0 || 
                                                 submittedPoints.size() > 0 || 
                                                 pendingRectifyPoints.size() > 0;

                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            // 更新适配器数据
                            pagerAdapter.updateData(
                                    pendingSurveyPoints,
                                    pendingSubmitPoints,
                                    submittedPoints,
                                    pendingRectifyPoints
                            );
                            
                            // 强制重新刷新，确保Fragment内的RecyclerView更新
                            if (forceRefresh) {
                                // 获取当前显示的Fragment
                                int currentPosition = viewPager.getCurrentItem();
                                Fragment currentFragment = getSupportFragmentManager()
                                        .findFragmentByTag("f" + pagerAdapter.getItemId(currentPosition));
                                
                                if (currentFragment instanceof SamplingPointListFragment) {
                                    // 强制刷新Fragment中的数据
                                    switch (currentPosition) {
                                        case 0: // 待调查
                                            ((SamplingPointListFragment) currentFragment).updateData(pendingSurveyPoints);
                                            break;
                                        case 1: // 待提交
                                            ((SamplingPointListFragment) currentFragment).updateData(pendingSubmitPoints);
                                            break;
                                        case 2: // 已提交
                                            ((SamplingPointListFragment) currentFragment).updateData(submittedPoints);
                                            break;
                                        case 3: // 待整改
                                            ((SamplingPointListFragment) currentFragment).updateData(pendingRectifyPoints);
                                            break;
                                    }
                                    
                                    Log.d(TAG, "强制刷新Fragment: position=" + currentPosition);
                                }
                            }

                            // 隐藏加载中
                            progressBar.setVisibility(View.GONE);
                            swipeRefreshLayout.setRefreshing(false);

                            // 更新样点状态计数
                            updateStatusCounts();
                            
                            if (needsUIUpdate) {
                                Log.d(TAG, "数据已加载，强制更新UI");
                                // 强制刷新界面
                                pagerAdapter.notifyDataSetChanged();
                                
                                // 尝试刷新当前标签页
                                int currentItem = viewPager.getCurrentItem();
                                viewPager.setAdapter(pagerAdapter);
                                viewPager.setCurrentItem(currentItem);
                            }
                        }
                    });

                } catch (Exception e) {
                    Log.e(TAG, "加载样点数据失败: " + e.getMessage());
                    e.printStackTrace();

                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            Toast.makeText(SamplingPointsActivity.this, "加载样点数据失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                            progressBar.setVisibility(View.GONE);
                            swipeRefreshLayout.setRefreshing(false);
                        }
                    });
                }
            }
        }).start();
    }

    /**
     * 收集样点ID
     *
     * @param samplingPoints 样点列表
     * @param targetList     目标列表，用于存储收集到的样点ID
     */
    private void collectSamplingPointIds(List<DccyVO> samplingPoints, List<String> targetList) {
        if (samplingPoints != null) {
            for (DccyVO point : samplingPoints) {
                if (point.getpjdybh() != null && !point.getpjdybh().isEmpty() && !targetList.contains(point.getpjdybh())) {
                    targetList.add(point.getpjdybh());
                }
            }
        }
    }

    /**
     * 根据状态和关键字获取样点数据
     *
     * @param status  状态枚举
     * @param keyword 搜索关键字
     * @return 样点列表
     */
    private List<DccyVO> getSamplingPointsByStatus(SamplingPointStatus status, String keyword) {
        List<DccyVO> points = new ArrayList<>();
        SQLiteDatabase db = null;
        Cursor cursor = null;

        try {
            db = dbHelper.getReadableDatabase();
            
            // 验证表是否存在
            Cursor tableCheck = db.rawQuery(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name=?", 
                    new String[]{DatabaseConstants.TABLE_DDC_POINTS});
            boolean tableExists = tableCheck.moveToFirst();
            tableCheck.close();
            
            if (!tableExists) {
                Log.e(TAG, "表不存在: " + DatabaseConstants.TABLE_DDC_POINTS);
                return points;
            }

            // 根据状态选择不同的表和查询条件
            if (status == SamplingPointStatus.PENDING_SURVEY) { // 待调查 - 从sampling_ddc_points表查询has_local_data=0的记录
                String query = "SELECT * FROM " + DatabaseConstants.TABLE_DDC_POINTS;
                String whereClause = DatabaseConstants.COLUMN_HAS_LOCAL_DATA + " = ?";
                String[] whereArgs = null;

                if (keyword != null && !keyword.isEmpty()) {
                    whereClause += " AND " + DatabaseConstants.COLUMN_DDC_PJDY_BSM + " LIKE ?";
                    whereArgs = new String[]{"0", "%" + keyword + "%"};
                } else {
                    whereArgs = new String[]{"0"};
                }

                query += " WHERE " + whereClause;
                Log.d(TAG, "待调查样点查询SQL: " + query + ", 参数: " + whereArgs[0]);
                
                cursor = db.rawQuery(query, whereArgs);
                Log.d(TAG, "待调查样点查询结果数量: " + (cursor != null ? cursor.getCount() : 0));

                if (cursor != null && cursor.moveToFirst()) {
                    do {
                        DccyVO point = new DccyVO();

                        // 设置样点基本信息（从sampling_ddc_points表）
                        point.setId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_DDC_ID)));
                        point.setPjdyId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_DDC_PJDY_ID)));
                        point.setpjdybh(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_DDC_PJDY_BSM)));
                        point.setDcdwId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_DDC_DCDW_ID)));
                        point.setDcdwName(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_DDC_DCDW_NAME)));
                        point.setDcrId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_DDC_DCR_ID)));
                        point.setDcrName(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_DDC_DCR_NAME)));
                        point.setXfjlId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_ID)));
                        point.setXmmc(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_XMMC)));
                        // 获取经纬度信息
                        int dcjdIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_DDC_DWJD);
                        int dcwdIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_DDC_DWWD);
                        if (dcjdIndex >= 0 && !cursor.isNull(dcjdIndex)) {
                            try {
                                String jdStr = cursor.getString(dcjdIndex);
                                if (jdStr != null && !jdStr.isEmpty()) {
                                    point.setDcjd(Double.parseDouble(jdStr));
                                }
                            } catch (NumberFormatException e) {
                                Log.e(TAG, "经度值转换失败: " + e.getMessage());
                            }
                        }
                        if (dcwdIndex >= 0 && !cursor.isNull(dcwdIndex)) {
                            try {
                                String wdStr = cursor.getString(dcwdIndex);
                                if (wdStr != null && !wdStr.isEmpty()) {
                                    point.setDcwd(Double.parseDouble(wdStr));
                                }
                            } catch (NumberFormatException e) {
                                Log.e(TAG, "纬度值转换失败: " + e.getMessage());
                            }
                        }
                        
                        // 设置默认状态为待调查
                        point.setZt(SamplingPointStatus.PENDING_SURVEY.getCode());
                        
                        Log.d(TAG, "找到待调查样点: ID=" + point.getId() + ", BSM=" + point.getpjdybh() + 
                                ", 单位=" + point.getDcdwName() + ", 调查人=" + point.getDcrName() +
                                ", 经度=" + point.getDcjd() + ", 纬度=" + point.getDcwd());
                        
                        points.add(point);
                    } while (cursor.moveToNext());
                } else {
                    // 检查表中的数据
                    Cursor dataCursor = db.rawQuery("SELECT COUNT(*) FROM " + DatabaseConstants.TABLE_DDC_POINTS, null);
                    int totalCount = 0;
                    if (dataCursor.moveToFirst()) {
                        totalCount = dataCursor.getInt(0);
                    }
                    dataCursor.close();
                    
                    Log.d(TAG, DatabaseConstants.TABLE_DDC_POINTS + " 表中的总记录数: " + totalCount);
                    
                    // 检查has_local_data字段的分布
                    Cursor distributionCursor = db.rawQuery(
                            "SELECT " + DatabaseConstants.COLUMN_HAS_LOCAL_DATA + ", COUNT(*) FROM " + 
                            DatabaseConstants.TABLE_DDC_POINTS + " GROUP BY " + DatabaseConstants.COLUMN_HAS_LOCAL_DATA, null);
                    
                    if (distributionCursor.moveToFirst()) {
                        do {
                            int hasLocalData = distributionCursor.getInt(0);
                            int count = distributionCursor.getInt(1);
                            Log.d(TAG, "has_local_data=" + hasLocalData + " 的记录数: " + count);
                        } while (distributionCursor.moveToNext());
                    }
                    distributionCursor.close();
                }
            } else if (status == SamplingPointStatus.PENDING_SUBMIT) { // 待提交 - 从sampling_ddc_points表查询has_local_data=1的记录
                String query = "SELECT * FROM " + DatabaseConstants.TABLE_DDC_POINTS;
                String whereClause = DatabaseConstants.COLUMN_HAS_LOCAL_DATA + " = ?";
                String[] whereArgs = null;

                if (keyword != null && !keyword.isEmpty()) {
                    whereClause += " AND " + DatabaseConstants.COLUMN_DDC_PJDY_BSM + " LIKE ?";
                    whereArgs = new String[]{"1", "%" + keyword + "%"};
                } else {
                    whereArgs = new String[]{"1"};
                }

                query += " WHERE " + whereClause;
                cursor = db.rawQuery(query, whereArgs);

                if (cursor != null && cursor.moveToFirst()) {
                    do {
                        DccyVO point = new DccyVO();

                        // 设置样点基本信息（从sampling_ddc_points表）
                        point.setId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_DDC_ID)));
                        point.setPjdyId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_DDC_PJDY_ID)));
                        point.setpjdybh(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_DDC_PJDY_BSM)));
                        point.setDcdwId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_DDC_DCDW_ID)));
                        point.setDcdwName(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_DDC_DCDW_NAME)));
                        point.setDcrId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_DDC_DCR_ID)));
                        point.setDcrName(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_DDC_DCR_NAME)));
                        point.setXfjlId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_ID)));
                        point.setXmmc(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_XMMC)));
                        // 获取经纬度信息
                        int dcjdIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_DDC_DWJD);
                        int dcwdIndex = cursor.getColumnIndex(DatabaseConstants.COLUMN_DDC_DWWD);
                        if (dcjdIndex >= 0 && !cursor.isNull(dcjdIndex)) {
                            try {
                                String jdStr = cursor.getString(dcjdIndex);
                                if (jdStr != null && !jdStr.isEmpty()) {
                                    point.setDcjd(Double.parseDouble(jdStr));
                                }
                            } catch (NumberFormatException e) {
                                Log.e(TAG, "经度值转换失败: " + e.getMessage());
                            }
                        }
                        if (dcwdIndex >= 0 && !cursor.isNull(dcwdIndex)) {
                            try {
                                String wdStr = cursor.getString(dcwdIndex);
                                if (wdStr != null && !wdStr.isEmpty()) {
                                    point.setDcwd(Double.parseDouble(wdStr));
                                }
                            } catch (NumberFormatException e) {
                                Log.e(TAG, "纬度值转换失败: " + e.getMessage());
                            }
                        }
                        
                        // 设置默认状态为待提交，并标记有本地数据
                        point.setZt(SamplingPointStatus.PENDING_SUBMIT.getCode());
                        point.setHasLocalData(1);
                        
                        // 为待提交的样点添加[本地]标记
                        String originalName = point.getDcdwName();
                        if (originalName != null && !originalName.contains("[本地]")) {
                            point.setDcdwName(originalName + " [本地]");
                        }
                        
                        Log.d(TAG, "找到待提交样点: ID=" + point.getId() + ", BSM=" + point.getpjdybh() + 
                                ", 单位=" + point.getDcdwName() + ", 调查人=" + point.getDcrName() +
                                ", 经度=" + point.getDcjd() + ", 纬度=" + point.getDcwd());
                        
                        points.add(point);
                    } while (cursor.moveToNext());
                }
            } else if (status == SamplingPointStatus.SUBMITTED) { // 已提交标签页显示所有已调查的记录
                String query = "SELECT * FROM " + DatabaseConstants.TABLE_SAMPLING_POINTS;
                String whereClause = DatabaseConstants.COLUMN_ZT + " NOT IN (-1, 0)";
                String[] whereArgs = null;

                if (keyword != null && !keyword.isEmpty()) {
                    whereClause += " AND " + DatabaseConstants.COLUMN_PJDY_BSM + " LIKE ?";
                    whereArgs = new String[]{"%" + keyword + "%"};
                }

                query += " WHERE " + whereClause;
                Log.d(TAG, "已提交样点查询SQL: " + query);
                cursor = db.rawQuery(query, whereArgs);

                if (cursor != null && cursor.moveToFirst()) {
                    do {
                        DccyVO point = new DccyVO();

                        // 设置样点基本信息（从sampling_points表）
                        point.setId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_SAMPLING_ID)));
                        point.setPjdyId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_PJDY_ID)));
                        point.setpjdybh(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_PJDY_BSM)));
                        point.setDcdwId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_DCDW_ID)));
                        point.setDcdwName(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_DCDW_NAME)));
                        point.setDcrId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_DCR_ID)));
                        point.setDcrName(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_DCR_NAME)));
                        point.setDcjd(cursor.getDouble(cursor.getColumnIndex(DatabaseConstants.COLUMN_DCJD)));
                        point.setDcwd(cursor.getDouble(cursor.getColumnIndex(DatabaseConstants.COLUMN_DCWD)));
                        point.setsfShiCj(cursor.getInt(cursor.getColumnIndex(DatabaseConstants.COLUMN_sfShiCj)));
                        point.setsfShengCj(cursor.getInt(cursor.getColumnIndex(DatabaseConstants.COLUMN_sfShengCj)));
                        point.setZt(cursor.getInt(cursor.getColumnIndex(DatabaseConstants.COLUMN_ZT)));
                        point.setBz(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_BZ)));
                        point.setTrMy(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_TRMY)));
                        point.setTrMz(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_TRMZ)));
                        point.setXfjlId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_XFJL_ID)));

                        // 获取关联的媒体文件
                        point.setPhotoList(getMediaFiles(db, point.getpjdybh(), "photo"));
                        point.setVideoList(getMediaFiles(db, point.getpjdybh(), "video"));

                        points.add(point);
                    } while (cursor.moveToNext());
                }
            } else if (status == SamplingPointStatus.PENDING_RECTIFY) { // 待整改 - 从sampling_points表查询zt=4的记录
                String query = "SELECT * FROM " + DatabaseConstants.TABLE_SAMPLING_POINTS;
                String whereClause = DatabaseConstants.COLUMN_ZT + " = ?";
                String[] whereArgs = null;

                if (keyword != null && !keyword.isEmpty()) {
                    whereClause += " AND " + DatabaseConstants.COLUMN_PJDY_BSM + " LIKE ?";
                    whereArgs = new String[]{"-1", "%" + keyword + "%"};
                } else {
                    whereArgs = new String[]{"-1"};
                }

                query += " WHERE " + whereClause;
                Log.d(TAG, "待整改样点查询SQL: " + query);
                cursor = db.rawQuery(query, whereArgs);

                if (cursor != null && cursor.moveToFirst()) {
                    do {
                        DccyVO point = new DccyVO();

                        // 设置样点基本信息（从sampling_points表）
                        point.setId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_SAMPLING_ID)));
                        point.setPjdyId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_PJDY_ID)));
                        point.setpjdybh(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_PJDY_BSM)));
                        point.setDcdwId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_DCDW_ID)));
                        point.setDcdwName(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_DCDW_NAME)));
                        point.setDcrId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_DCR_ID)));
                        point.setDcrName(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_DCR_NAME)));
                        point.setDcjd(cursor.getDouble(cursor.getColumnIndex(DatabaseConstants.COLUMN_DCJD)));
                        point.setDcwd(cursor.getDouble(cursor.getColumnIndex(DatabaseConstants.COLUMN_DCWD)));
                        point.setsfShiCj(cursor.getInt(cursor.getColumnIndex(DatabaseConstants.COLUMN_sfShiCj)));
                        point.setsfShengCj(cursor.getInt(cursor.getColumnIndex(DatabaseConstants.COLUMN_sfShengCj)));
                        point.setZt(cursor.getInt(cursor.getColumnIndex(DatabaseConstants.COLUMN_ZT)));
                        point.setBz(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_BZ)));
                        point.setTrMy(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_TRMY)));
                        point.setTrMz(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_TRMZ)));
                        point.setXfjlId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_XFJL_ID)));
                        point.setXmmc(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_XMMC)));

                        // 获取关联的媒体文件
                        point.setPhotoList(getMediaFiles(db, point.getpjdybh(), "photo"));
                        point.setVideoList(getMediaFiles(db, point.getpjdybh(), "video"));

                        points.add(point);
                    } while (cursor.moveToNext());
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "查询样点数据失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        return points;
    }

    /**
     * 获取样点关联的媒体文件
     *
     * @param db        数据库连接
     * @param pjdybh   样点标识码
     * @param mediaType 媒体类型（photo/video）
     * @return 媒体文件列表
     */
    private List<DccyVO.DccyMediaVO> getMediaFiles(SQLiteDatabase db, String pjdybh, String mediaType) {
        List<DccyVO.DccyMediaVO> mediaList = new ArrayList<>();
        Cursor cursor = null;

        try {
            // 构建查询
            String query = "SELECT * FROM " + DatabaseConstants.TABLE_SAMPLING_MEDIA +
                    " WHERE " + DatabaseConstants.COLUMN_MEDIA_PJDY_BSM + " = ? AND " +
                    DatabaseConstants.COLUMN_MEDIA_TYPE + " = ?";

            cursor = db.rawQuery(query, new String[]{pjdybh, mediaType});

            if (cursor != null && cursor.moveToFirst()) {
                do {
                    DccyVO point = new DccyVO();
                    DccyVO.DccyMediaVO media = point.new DccyMediaVO();
                    // 设置媒体文件信息
                    media.setId(cursor.getLong(cursor.getColumnIndex(DatabaseConstants.COLUMN_MEDIA_ID)));
                    media.setFileName(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_MEDIA_FILE_NAME)));
                    media.setPath(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_MEDIA_FILE_PATH)));
                    media.setFileType(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_MEDIA_FILE_TYPE)));
                    media.setJd(cursor.getDouble(cursor.getColumnIndex(DatabaseConstants.COLUMN_MEDIA_JD)));
                    media.setWd(cursor.getDouble(cursor.getColumnIndex(DatabaseConstants.COLUMN_MEDIA_WD)));
                    media.setFwj(cursor.getDouble(cursor.getColumnIndex(DatabaseConstants.COLUMN_MEDIA_FWJ)));
                    media.setFileTime(cursor.getString(cursor.getColumnIndex(DatabaseConstants.COLUMN_MEDIA_FILE_TIME)));
                    mediaList.add(media);
                } while (cursor.moveToNext());
            }
        } catch (Exception e) {
            Log.e(TAG, "查询媒体文件失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        return mediaList;
    }

    // 修改updateStatusCounts方法，添加更新Tab文本的功能
    private void updateStatusCounts() {
        // 重置计数
        toBeInvestigatedCount = 0;
        toBeSubmittedCount = 0;
        submittedCount = 0;
        toBeRectifiedCount = 0;

        // 获取各状态样点数量
        try {
            SQLiteDatabase db = dbHelper.getReadableDatabase();

            // 验证表是否存在
            Cursor tableCheck = db.rawQuery(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name=?", 
                    new String[]{DatabaseConstants.TABLE_DDC_POINTS});
            boolean tableExists = tableCheck.moveToFirst();
            tableCheck.close();
            
            if (!tableExists) {
                Log.e(TAG, "表不存在: " + DatabaseConstants.TABLE_DDC_POINTS);
                return;
            }

            // 查询待调查样点数量 (来自sampling_ddc_points表has_local_data=0)
            Cursor cursor = db.rawQuery(
                    "SELECT COUNT(*) FROM " + DatabaseConstants.TABLE_DDC_POINTS +
                            " WHERE " + DatabaseConstants.COLUMN_HAS_LOCAL_DATA + " = 0", null);
            if (cursor.moveToFirst()) {
                toBeInvestigatedCount = cursor.getInt(0);
                Log.d(TAG, "待调查样点数量: " + toBeInvestigatedCount);
            }
            cursor.close();

            // 查询待提交样点数量 (来自sampling_ddc_points表has_local_data=1)
            cursor = db.rawQuery(
                    "SELECT COUNT(*) FROM " + DatabaseConstants.TABLE_DDC_POINTS +
                            " WHERE " + DatabaseConstants.COLUMN_HAS_LOCAL_DATA + " = 1", null);
            if (cursor.moveToFirst()) {
                toBeSubmittedCount = cursor.getInt(0);
                Log.d(TAG, "待提交样点数量: " + toBeSubmittedCount);
            }
            cursor.close();

            // 查询表中的总记录
            cursor = db.rawQuery("SELECT COUNT(*) FROM " + DatabaseConstants.TABLE_DDC_POINTS, null);
            if (cursor.moveToFirst()) {
                int totalCount = cursor.getInt(0);
                Log.d(TAG, DatabaseConstants.TABLE_DDC_POINTS + " 表中的总记录数: " + totalCount);
            }
            cursor.close();

            // 查询已提交样点数量 (来自sampling_points表的记录)
            cursor = db.rawQuery(
                    "SELECT COUNT(*) FROM " + DatabaseConstants.TABLE_SAMPLING_POINTS +
                            " WHERE " + DatabaseConstants.COLUMN_ZT + "  NOT IN (-1,0) ", null);
            if (cursor.moveToFirst()) {
                submittedCount = cursor.getInt(0);
                Log.d(TAG, "已提交样点数量: " + submittedCount);
            }
            cursor.close();

            // 查询待整改样点数量 (来自sampling_points表zt=4)
            cursor = db.rawQuery(
                    "SELECT COUNT(*) FROM " + DatabaseConstants.TABLE_SAMPLING_POINTS +
                            " WHERE " + DatabaseConstants.COLUMN_ZT + " = -1", null);
            if (cursor.moveToFirst()) {
                toBeRectifiedCount = cursor.getInt(0);
                Log.d(TAG, "待整改样点数量: " + toBeRectifiedCount);
            }
            cursor.close();

        } catch (Exception e) {
            Log.e(TAG, "获取样点数量失败: " + e.getMessage());
            e.printStackTrace();
        }

        // 更新UI显示计数
        updateStatusCountsUI();
        
        // 更新Tab标签文本
        updateTabLabels();
    }

    // 添加一个方法来更新UI上的状态计数
    private void updateStatusCountsUI() {
        // 获取显示计数的TextView
        TextView tvStatusCounts = findViewById(R.id.tv_status_counts);
        if (tvStatusCounts != null) {
            String statusCountsText = String.format("待调查: %d | 待提交: %d | 已提交: %d | 待整改: %d",
                    toBeInvestigatedCount, toBeSubmittedCount, submittedCount, toBeRectifiedCount);
            tvStatusCounts.setText(statusCountsText);
        }
    }

    /**
     * 更新TabLayout标签文本
     */
    private void updateTabLabels() {
        if (tabLayout != null) {
            // TabLayout有4个标签，按顺序更新文本
            TabLayout.Tab tab;
            
            // 待调查
            if ((tab = tabLayout.getTabAt(0)) != null) {
                tab.setText("待调查 (" + toBeInvestigatedCount + ")");
            }
            
            // 待提交
            if ((tab = tabLayout.getTabAt(1)) != null) {
                tab.setText("待提交 (" + toBeSubmittedCount + ")");
            }
            
            // 已提交
            if ((tab = tabLayout.getTabAt(2)) != null) {
                tab.setText("已提交 (" + submittedCount + ")");
            }
            
            // 待整改
            if ((tab = tabLayout.getTabAt(3)) != null) {
                tab.setText("待整改 (" + toBeRectifiedCount + ")");
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        
        // 每次恢复Activity时重新加载样点数据
        Log.d(TAG, "onResume: 重新加载样点数据");
        loadSamplingPoints();
    }
}
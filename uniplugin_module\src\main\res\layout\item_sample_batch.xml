<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="4dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <TextView
            android:id="@+id/textViewBatchName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="批次名称"
            android:textColor="#333333"
            android:textSize="16sp"
            android:textStyle="bold"
            android:lines="1"
            android:ellipsize="end" />

        <TextView
            android:id="@+id/textViewBatchCode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="批次编号：XXXX"
            android:textColor="#666666"
            android:textSize="14sp"
            android:layout_marginTop="4dp"
            android:lines="1"
            android:ellipsize="end" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="6dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/textViewSendOrg"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="送样单位：XXX"
                    android:textColor="#666666"
                    android:textSize="13sp"
                    android:lines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/textViewSender"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="送样人：XXX"
                    android:textColor="#666666"
                    android:textSize="13sp"
                    android:layout_marginTop="2dp"
                    android:lines="1"
                    android:ellipsize="end" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/textViewReceiveOrg"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="接样单位：XXX"
                    android:textColor="#666666"
                    android:textSize="13sp"
                    android:lines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/textViewReceiver"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="接样人：XXX"
                    android:textColor="#666666"
                    android:textSize="13sp"
                    android:layout_marginTop="2dp"
                    android:lines="1"
                    android:ellipsize="end" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="6dp">

            <TextView
                android:id="@+id/textViewSampleCount"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="样品数量：10"
                android:textColor="#007BFF"
                android:textSize="14sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/textViewCreateTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2023-01-01 12:00:00"
                android:textColor="#666666"
                android:textSize="12sp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView> 
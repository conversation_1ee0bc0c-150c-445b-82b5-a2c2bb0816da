package io.dcloud.uniplugin.utils;

import android.util.Base64;
import android.util.Log;

import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;

import javax.crypto.Cipher;

/**
 * RSA加密工具类
 */
public class RsaUtils {
    private static final String TAG = "RsaUtils";
    
    /**
     * RSA加密
     * @param plainText 明文
     * @param publicKey Base64编码的公钥
     * @return Base64编码的密文
     */
    public static String encrypt(String plainText, String publicKey) {
        try {
            // 将Base64编码的公钥转换为PublicKey对象
            byte[] keyBytes = Base64.decode(publicKey, Base64.DEFAULT);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey pubKey = keyFactory.generatePublic(keySpec);
            
            // 使用RSA加密
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(Cipher.ENCRYPT_MODE, pubKey);
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            
            // 返回Base64编码的密文
            return Base64.encodeToString(encryptedBytes, Base64.NO_WRAP);
        } catch (Exception e) {
            Log.e(TAG, "RSA加密失败: " + e.getMessage());
            return null;
        }
    }
} 
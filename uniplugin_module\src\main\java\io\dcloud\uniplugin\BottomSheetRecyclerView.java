package io.dcloud.uniplugin;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

public class BottomSheetRecyclerView extends RecyclerView {

    public BottomSheetRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public BottomSheetRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    /**
     * Intercept touch events and determine if {@link RecyclerView} should grab touch event to allow scrolling of RecyclerView
     * within Bottom Sheet
     * @param e event intercepted
     * @return return true to consume the event, false otherwise
     */
    @Override public boolean onInterceptTouchEvent(MotionEvent e) {
        if (e.getAction() == MotionEvent.ACTION_SCROLL && canScrollVertically(1)) {
            return true;
        }
        return super.onInterceptTouchEvent(e);
    }
}
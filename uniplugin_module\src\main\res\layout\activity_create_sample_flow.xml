<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F5F5F5">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 批次信息 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="批次信息"
                        android:textColor="#333333"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />
                        
                    <!-- 选择批次按钮 -->
                    <Button
                        android:id="@+id/buttonSelectBatch"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="选择现有批次"
                        android:textColor="#333333"
                        android:background="@drawable/button_secondary"
                        android:drawableStart="@android:drawable/ic_menu_search"
                        android:drawablePadding="8dp"
                        android:paddingStart="12dp"
                        android:textSize="14sp" />

                    <!-- 批次名称 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:hint="批次名称"
                        app:boxStrokeColor="@color/colorPrimary"
                        app:hintTextColor="@color/colorPrimary">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/editTextBatchName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 备注 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="备注（可选）"
                        app:boxStrokeColor="@color/colorPrimary"
                        app:hintTextColor="@color/colorPrimary">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/editTextRemark"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textMultiLine"
                            android:maxLines="3"
                            android:minLines="2" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 样品列表 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <!-- 样品列表标题和添加按钮 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="样品列表"
                            android:textColor="#333333"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/buttonAddSampleQuick"
                            android:layout_width="wrap_content"
                            android:layout_height="36dp"
                            android:text="扫码添加"
                            android:textSize="12sp"
                            android:background="@drawable/button_primary_small"
                            android:textColor="@android:color/white"
                            android:paddingLeft="12dp"
                            android:paddingRight="12dp"
                            android:drawableLeft="@drawable/ic_qr_code_scanner"
                            android:drawablePadding="4dp" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E0E0E0" />

                    <!-- 样品列表内容 -->
                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1">

                        <!-- 空状态 -->
                        <LinearLayout
                            android:id="@+id/layoutEmptySamples"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="32dp"
                            android:visibility="visible">

                            <ImageView
                                android:layout_width="64dp"
                                android:layout_height="64dp"
                                android:src="@drawable/ic_empty_box"
                                android:alpha="0.3"
                                android:layout_marginBottom="16dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="暂无样品"
                                android:textColor="#999999"
                                android:textSize="14sp"
                                android:layout_marginBottom="8dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="点击扫码添加按钮添加样品"
                                android:textColor="#CCCCCC"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <!-- 样品列表 -->
                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recyclerViewSamples"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:visibility="gone"
                            android:padding="8dp"
                            android:clipToPadding="false" />

                    </FrameLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 添加样品按钮 -->
            <Button
                android:id="@+id/buttonAddSample"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="扫码添加样品"
                android:textColor="#FFFFFF"
                android:backgroundTint="#1971AC" />
                
            <!-- 添加测试样品按钮 -->
            <Button
                android:id="@+id/buttonAddTestSample"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="添加测试样品"
                android:textColor="#FFFFFF"
                android:backgroundTint="#009688" />

        </LinearLayout>

    </ScrollView>

    <!-- 底部按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@android:color/white"
        android:elevation="4dp">

        <Button
            android:id="@+id/buttonCancel"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:layout_marginRight="8dp"
            android:text="取消"
            android:textColor="#666666"
            android:background="@drawable/button_secondary"
            android:textSize="16sp" />

        <Button
            android:id="@+id/buttonSubmit"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:layout_marginLeft="8dp"
            android:text="提交"
            android:textColor="@android:color/white"
            android:background="@drawable/button_primary"
            android:textSize="16sp" />

    </LinearLayout>

</LinearLayout> 
package io.dcloud.uniplugin.form.utils;

import android.os.Environment;
import android.util.Log;

import com.google.android.gms.common.util.CollectionUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import io.dcloud.uniplugin.form.DropdownProcessor;
import io.dcloud.uniplugin.model.DccyVO;
import io.dcloud.uniplugin.model.FormConfig;
import io.dcloud.uniplugin.model.FormConfigResponse;
import io.dcloud.uniplugin.model.FormFieldConfig;
import io.dcloud.uniplugin.model.FormFieldGroup;

/**
 * 表单 JSON 解析工具类
 * 处理表单配置相关的 JSON 解析逻辑
 */
public class FormJsonUtils {
    private static final String TAG = "FormJsonUtils";

    /**
     * 规范化表单配置 JSON 数据
     * 处理 API 响应格式、字段名差异等
     */
    public static String normalizeFormConfigJson(FormConfigResponse configJson, FormConfig formConfig) {
        String configJsonStr = null;
        try {
            // 转换为标准格式
            JSONObject standardJson = new JSONObject();
            // 处理基本属性
            List<FormConfigResponse.FieldGroup> fields = configJson.getFields();

            standardJson.put("formId", formConfig.getFormId());
            standardJson.put("formName", formConfig.getFormName());
            standardJson.put("description", formConfig.getDescription());
            standardJson.put("version", formConfig.getVersion());
            standardJson.put("submitUrl",formConfig.getSubmitUrl());
            standardJson.put("isOfflineSupported", true);
            
            // 不要用JSON.toJSON(fields)，直接构建JSONArray
            JSONArray fieldsArray = new JSONArray();
            if (fields != null) {
                changeGroupToJson(fields, fieldsArray);
            }
            
            // 直接将JSONArray添加到standardJson中
            standardJson.put("fields", fieldsArray);
            
            configJsonStr = standardJson.toString();
            Log.d(TAG, "生成表单配置JSON成功");
        } catch (Exception e) {
            Log.e(TAG, "处理 JSON 时出错: " + e.getMessage(), e);
        }

        return configJsonStr;
    }

    private static void changeGroupToJson(List<FormConfigResponse.FieldGroup> fields, JSONArray fieldsArray) throws JSONException {
        for (FormConfigResponse.FieldGroup group : fields) {
            JSONObject groupJson = new JSONObject();
            groupJson.put("name", group.getName());
            groupJson.put("type", group.getType());

            // 转换字段组内的字段为JSONArray
            JSONArray groupFieldsArray = new JSONArray();
            if (group.getFields() != null) {
                for (FormConfigResponse.Field field : group.getFields()) {
                    JSONObject fieldJson = new JSONObject();
                    fieldJson.put("fieldName", field.getFieldName());
                    fieldJson.put("fieldRemark", field.getFieldRemark());
                    fieldJson.put("fieldType", field.getFieldType());
                    fieldJson.put("fieldGroup", field.getFieldGroup());
                    if (field.getFieldLength() != null) {
                        fieldJson.put("fieldLength", field.getFieldLength());
                    }
                    fieldJson.put("fieldRequired", field.getFieldRequired());
                    fieldJson.put("fieldElement", field.getFieldElement());
                    fieldJson.put("fieldMax", field.getFieldMax());
                    fieldJson.put("fieldMin", field.getFieldMin());

                    // 处理fieldOptions
                    if (field.getFieldOptions() != null) {
                        fieldJson.put("fieldOptions", field.getFieldOptions());
                    } else {
                        fieldJson.put("fieldOptions", JSONObject.NULL);
                    }

                    // 处理value
                    if (field.getValue() != null) {
                        fieldJson.put("value", field.getValue());
                    } else {
                        fieldJson.put("value", JSONObject.NULL);
                    }

                    groupFieldsArray.put(fieldJson);
                }
            }

            groupJson.put("fields", groupFieldsArray);
            fieldsArray.put(groupJson);
        }
    }

    /**
     * 解析表单配置 JSON
     */
    public static FormConfig parseFormConfigJson(String configJson, String formId) {
        try {
            return manuallyParseFormConfig(configJson);
        } catch (Exception e) {
            Log.e(TAG, "JSON 解析为 FormConfig 失败: " + e.getMessage(), e);
            return createDefaultFormConfig(formId); // 如果解析失败，返回默认配置
        }
    }

    /**
     * 手动解析表单配置
     */
    private static FormConfig manuallyParseFormConfig(String jsonStr) {
        try {
            FormConfig formConfig = new FormConfig();
            JSONObject jsonObject = new JSONObject(jsonStr);

            // 解析基本属性
            if (jsonObject.has("formId")) formConfig.setFormId(jsonObject.getString("formId"));
            if (jsonObject.has("formName")) formConfig.setFormName(jsonObject.getString("formName"));
            if (jsonObject.has("description")) formConfig.setDescription(jsonObject.getString("description"));
            if (jsonObject.has("version")) formConfig.setVersion(jsonObject.getString("version"));
            if (jsonObject.has("submitUrl")) formConfig.setSubmitUrl(jsonObject.getString("submitUrl"));
            if (jsonObject.has("isOfflineSupported")) formConfig.setOfflineSupported(jsonObject.getBoolean("isOfflineSupported"));

            // 处理根级别字段数组
            if (jsonObject.has("fields")) {
                JSONArray rootFieldsArray = jsonObject.getJSONArray("fields");
                List<FormFieldConfig> allFields = new ArrayList<>();
                List<FormFieldGroup> fieldGroups = new ArrayList<>();

                // 遍历顶级字段/字段组
                for (int i = 0; i < rootFieldsArray.length(); i++) {
                    JSONObject fieldGroupJson = rootFieldsArray.getJSONObject(i);

                    // 检查是否是字段组
                    if (fieldGroupJson.has("fields") && fieldGroupJson.has("type") &&
                            "group".equals(fieldGroupJson.getString("type"))) {

                        // 这是一个字段组，创建 FormFieldGroup 对象
                        FormFieldGroup fieldGroup = new FormFieldGroup();
                        String groupName = fieldGroupJson.optString("name", "未命名组");
                        String groupId = groupName.replaceAll("\\s+", "_").toLowerCase();

                        fieldGroup.setGroupId(groupId);
                        fieldGroup.setTitle(groupName);
                        if (fieldGroupJson.has("description")) {
                            fieldGroup.setDescription(fieldGroupJson.getString("description"));
                        }

                        // 解析组内字段
                        JSONArray groupFieldsArray = fieldGroupJson.getJSONArray("fields");
                        List<FormFieldConfig> groupFields = new ArrayList<>();

                        for (int j = 0; j < groupFieldsArray.length(); j++) {
                            JSONObject fieldJson = groupFieldsArray.getJSONObject(j);
                            FormFieldConfig field = parseFieldJson(fieldJson, groupName);
                            if (field != null) {
                                groupFields.add(field);
                                allFields.add(field); // 同时添加到全局字段列表
                            }
                        }

                        // 设置组内字段
                        fieldGroup.setFields(groupFields);
                        fieldGroups.add(fieldGroup);
                    }
                }

                // 设置字段和字段组
                formConfig.setFields(allFields);
                formConfig.setFieldGroups(fieldGroups);
                
                Log.d(TAG, "手动解析成功，总字段数量: " + allFields.size() + ", 字段组数量: " + fieldGroups.size());
            }

            return formConfig;
        } catch (Exception e) {
            Log.e(TAG, "手动解析失败: " + e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 解析字段JSON
     */
    private static FormFieldConfig parseFieldJson(JSONObject fieldJson, String groupName) {
        try {
            FormFieldConfig fieldConfig = new FormFieldConfig();
            
            // 处理基本字段属性
            String fieldName = fieldJson.optString("fieldName", null);
            String fieldRemark = fieldJson.optString("fieldRemark", null);
            String fieldElement = fieldJson.optString("fieldElement", null);
            Integer fieldRequired = fieldJson.has("fieldRequired") ? fieldJson.getInt("fieldRequired") : 0;
            Integer fieldLength =fieldJson.has("fieldLength")  ?  fieldJson.getInt("fieldLength") : 999;
            // 设置字段ID (使用fieldName作为ID)
            fieldConfig.setMaxLength(fieldLength);
            fieldConfig.setFieldId(fieldName);
            
            // 设置字段名称 (使用fieldRemark作为显示名称)
            fieldConfig.setFieldName(fieldRemark);
            
            // 设置标签
            fieldConfig.setLabel(fieldRemark);
            
            // 设置字段类型 (根据fieldElement映射)
            String fieldType = mapFieldType(fieldElement);
            fieldConfig.setFieldType(fieldType);
            
            // 设置是否必填
            fieldConfig.setRequired(fieldRequired == 1);

            //设置附件限制
            if (fieldJson.has("fieldMax") && !fieldJson.isNull("fieldMax")) {
                fieldConfig.setMaxFiles(fieldJson.getInt("fieldMax"));
            }
            
            // 处理选项
            if (fieldJson.has("fieldOptions") && !fieldJson.isNull("fieldOptions")) {
                Object fieldOptions = fieldJson.get("fieldOptions");
                
                // 保存原始选项字符串，用于下拉框处理
                if (fieldOptions instanceof String) {
                    String optionsStr = (String) fieldOptions;
                    fieldConfig.setOptionsString(optionsStr);
                    
                    // 如果是下拉框类型，使用DropdownProcessor解析选项
                    if (FormFieldConfig.TYPE_DROPDOWN.equals(fieldType)) {
                        // 使用DropdownProcessor解析选项
                        try {
                            List<FormFieldConfig.OptionItem> options = DropdownProcessor.parseOptions(optionsStr);
                            fieldConfig.setOptions(options);
                            Log.d(TAG, "使用DropdownProcessor解析下拉选项，共" + options.size() + "个选项");
                        } catch (Exception e) {
                            Log.e(TAG, "解析下拉选项失败: " + e.getMessage(), e);
                        }
                    } else {
                        // 使用默认的解析方式处理选项
                        parseDefaultOptions(optionsStr, fieldConfig);
                    }
                } else if (fieldOptions instanceof JSONArray) {
                    // 如果是JSONArray，直接处理
                    JSONArray optionsArray = (JSONArray) fieldOptions;
                    fieldConfig.setOptionsString(optionsArray.toString());
                    
                    List<FormFieldConfig.OptionItem> options = new ArrayList<>();
                    for (int i = 0; i < optionsArray.length(); i++) {
                        JSONObject optionJson = optionsArray.getJSONObject(i);
                        FormFieldConfig.OptionItem option = new FormFieldConfig.OptionItem();
                        option.setLabel(optionJson.optString("label"));
                        option.setValue(optionJson.optString("value"));
                        options.add(option);
                    }
                    
                    fieldConfig.setOptions(options);
                }
            }
            
            // 设置特定类型的属性
            if ("photo".equals(fieldElement) || "video".equals(fieldElement)) {
                if (fieldJson.has("fieldMin") && !fieldJson.isNull("fieldMin")) {
                    fieldConfig.setMinFiles(fieldJson.getInt("fieldMin"));
                }

                if (fieldJson.has("fieldMax") && !fieldJson.isNull("fieldMax")) {
                    fieldConfig.setMaxFiles(fieldJson.getInt("fieldMax"));
                }
            }
            
            return fieldConfig;
        } catch (Exception e) {
            Log.e(TAG, "解析字段出错: " + e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 解析默认选项格式（逗号分隔的字符串）
     */
    private static void parseDefaultOptions(String optionsStr, FormFieldConfig fieldConfig) {
        // 如果是逗号分隔的字符串，解析为选项
        String[] optionParts = optionsStr.split(",");
        List<FormFieldConfig.OptionItem> options = new ArrayList<>();
        
        for (String part : optionParts) {
            String trimmed = part.trim();
            if (!trimmed.isEmpty()) {
                FormFieldConfig.OptionItem option = new FormFieldConfig.OptionItem();
                
                // 处理可能存在的label:value格式
                if (trimmed.contains(":")) {
                    String[] labelValue = trimmed.split(":", 2);
                    option.setLabel(labelValue[0].trim());
                    option.setValue(labelValue[1].trim());
                } else {
                    option.setLabel(trimmed);
                    option.setValue(trimmed);
                }
                
                options.add(option);
            }
        }
        
        fieldConfig.setOptions(options);
    }
    
    /**
     * 映射字段类型，将API返回的fieldElement映射为FormFieldConfig中定义的常量
     */
    private static String mapFieldType(String fieldElement) {
        if (fieldElement == null) {
            return FormFieldConfig.TYPE_TEXT; // 默认为文本类型
        }
        
        switch (fieldElement) {
            case "text":
                return FormFieldConfig.TYPE_TEXT;
            case "textarea":
                return FormFieldConfig.TYPE_TEXTAREA;
            case "number":
                return FormFieldConfig.TYPE_NUMBER;
            case "select":
            case "dropdown":
                return FormFieldConfig.TYPE_DROPDOWN;
            case "date":
                return FormFieldConfig.TYPE_DATE;
            case "photo":
                return FormFieldConfig.TYPE_PHOTO;
            case "video":
                return FormFieldConfig.TYPE_VIDEO;
            case "location":
                return FormFieldConfig.TYPE_LOCATION;
            case "signature":
                return FormFieldConfig.TYPE_SIGNATURE;
            default:
                return FormFieldConfig.TYPE_TEXT; // 不认识的类型默认为文本
        }
    }

    /**
     * 创建默认的表单配置
     */
    public static FormConfig createDefaultFormConfig(String formId) {
        FormConfig formConfig = new FormConfig();
        formConfig.setFormId(formId);
        formConfig.setFormName("默认表单");
        formConfig.setDescription("系统自动创建的默认表单");
        formConfig.setVersion("1.0");
        formConfig.setOfflineSupported(true);

        // 创建一个示例字段
        List<FormFieldConfig> fields = new ArrayList<>();
        FormFieldConfig field = new FormFieldConfig();
        field.setFieldId("field_example");
        field.setFieldName("示例字段");
        field.setFieldType(FormFieldConfig.TYPE_TEXT);
        field.setLabel("示例字段");
        field.setRequired(false);
        fields.add(field);

        formConfig.setFields(fields);

        Log.w(TAG, "创建了默认的表单配置: " + formId);
        return formConfig;
    }

    /**
     * 从JSON响应中提取DccyVO对象列表
     * @param responseObj JSON响应对象
     * @return DccyVO列表
     */
    public static List<DccyVO> extractDccyVOFromJson(List<FormConfigResponse> responseObj) {
        List<DccyVO> dccyList = new ArrayList<>();
        final String TAG = "FormJsonUtils";

        try {
            // 获取data数组
            if (CollectionUtils.isEmpty(responseObj)) {
                return dccyList;
            }

            // 遍历数组中的每个对象
            for (int i = 0; i < responseObj.size(); i++) {
                try {
                    List<FormConfigResponse.FieldGroup> fieldsGroup = responseObj.get(i).getFields();
                    if (!CollectionUtils.isEmpty(fieldsGroup)) {
                        DccyVO dccyVO = new DccyVO();
                        JSONArray fieldsArray = new JSONArray();
                        FormJsonUtils.changeGroupToJson(fieldsGroup, fieldsArray);
                        dccyVO.setFormData(fieldsArray.toString());
                        for (int j = 0; j < fieldsGroup.size(); j++) {
                            List<FormConfigResponse.Field> fields = fieldsGroup.get(j).getFields();

                                for (int k = 0; k < fields.size(); k++) {
                                    FormConfigResponse.Field field = fields.get(k);

                                    if (field.getFieldName() == null || field.getFieldType() == null) {
                                        continue;
                                    }

                                    String fieldName = field.getFieldName();
//                                    String fieldType = field.getFieldType();
                                    String fieldElement = field.getFieldElement();

                                    // 处理不同类型的字段
                                    if (field.getValue() != null) {
                                        // 处理视频和照片字段
                                        if ( "video".equals(fieldElement) || "photo".equals(fieldElement) || "signature".equals(fieldElement)) {
                                            processMediaField(dccyVO, field,fieldName);
                                        }
                                        
                                        // 基本类型字段
                                        switch (fieldName) {
                                            case "id":
                                                if (field.getValue() instanceof Number) {
                                                    dccyVO.setId(getLongValue(field.getValue()));
                                                }
                                                break;
                                            case "pjdyId":
                                                if (field.getValue() instanceof Number) {
                                                    dccyVO.setPjdyId(getLongValue(field.getValue()));
                                                }
                                                break;
                                            case "pjdybh":
                                                if (field.getValue() instanceof String) {
                                                    dccyVO.setpjdybh((String) field.getValue());
                                                }
                                                break;
                                            case "dcdwId":
                                                if (field.getValue() instanceof Number) {
                                                    dccyVO.setDcdwId(getLongValue(field.getValue()));
                                                }
                                                break;
                                            case "dcdwName":
                                                if (field.getValue() instanceof String) {
                                                    dccyVO.setDcdwName((String) field.getValue());
                                                }
                                                break;
                                            case "dcrId":
                                                if (field.getValue() instanceof Number) {
                                                    dccyVO.setDcrId(getLongValue(field.getValue()));
                                                }
                                                break;
                                            case "dcrName":
                                                if (field.getValue() instanceof String) {
                                                    dccyVO.setDcrName((String) field.getValue());
                                                }
                                                break;
                                            case "dcjd":
                                                if (field.getValue() instanceof Number) {
                                                    dccyVO.setDcjd(getDoubleValue(field.getValue()));
                                                }
                                                break;
                                            case "dcwd":
                                                if (field.getValue() instanceof Number) {
                                                    dccyVO.setDcwd(getDoubleValue(field.getValue()));
                                                }
                                                break;
                                            case "dcTime":
                                                if (field.getValue() instanceof Number) {
                                                    dccyVO.setDcTime(getLongValue(field.getValue()));
                                                }
                                                break;
                                            case "uploadTime":
                                                if (field.getValue() instanceof Number) {
                                                    dccyVO.setUploadTime(getLongValue(field.getValue()));
                                                }
                                                break;
                                            case "sfShiCj":
                                                if (field.getValue() instanceof Number) {
                                                    dccyVO.setsfShiCj(getIntValue(field.getValue()));
                                                }
                                                break;
                                            case "sfShengCj":
                                                if (field.getValue() instanceof Number) {
                                                    dccyVO.setsfShengCj(getIntValue(field.getValue()));
                                                }
                                                break;
                                            case "zt":
                                                if (field.getValue() instanceof Number) {
                                                    dccyVO.setZt(getIntValue(field.getValue()));
                                                }
                                                break;
                                            case "bz":
                                                if (field.getValue() instanceof String) {
                                                    dccyVO.setBz((String) field.getValue());
                                                }
                                                break;
                                            case "trMy":
                                                if (field.getValue() instanceof String) {
                                                    dccyVO.setTrMy((String) field.getValue());
                                                }
                                                break;
                                            case "trMz":
                                                if (field.getValue() instanceof String) {
                                                    dccyVO.setTrMz((String) field.getValue());
                                                }
                                                break;
                                            case "xfjlId":
                                                if (field.getValue() instanceof Number) {
                                                    dccyVO.setXfjlId(getLongValue(field.getValue()));
                                                }
                                                break;
                                            case "xmmc":
                                                if (field.getValue() instanceof String) {
                                                    dccyVO.setXmmc((String)(field.getValue()));
                                                }
                                                break;
                                            default:
                                                break;
                                        }
                                    }
                                }
                            }

                        // 为此DccyVO生成动态表单数据
                        String dynamicFormData = generateDynamicFormDataJson(dccyVO, responseObj.get(i).getFields());
                        if (dynamicFormData != null) {
                            dccyVO.setFormData(dynamicFormData);
                        }
                            
                        // 添加到结果列表
                        dccyList.add(dccyVO);
                        }

                    }catch (Exception e) {
                    Log.e(TAG, "处理单个表单数据时出错: " + e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "解析数据时出错: " + e.getMessage(), e);
        }

        return dccyList;
    }
    
    /**
     * 从Object获取Long值
     */
    private static Long getLongValue(Object value) {
        if (value instanceof Long) {
            return (Long) value;
        } else if (value instanceof Integer) {
            return ((Integer) value).longValue();
        } else if (value instanceof Double) {
            return ((Double) value).longValue();
        } else if (value instanceof Float) {
            return ((Float) value).longValue();
        } else if (value instanceof String) {
            try {
                // 对于可能含有小数点的字符串值，先转成Double再取长整型部分
                if (((String) value).contains(".")) {
                    return (long) Double.parseDouble((String) value);
                }
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return 0L;
            }
        }
        return 0L;
    }
    
    /**
     * 从Object获取Double值
     */
    private static Double getDoubleValue(Object value) {
        if (value instanceof Double) {
            return (Double) value;
        } else if (value instanceof Float) {
            return ((Float) value).doubleValue();
        } else if (value instanceof Integer) {
            return ((Integer) value).doubleValue();
        } else if (value instanceof Long) {
            return ((Long) value).doubleValue();
        } else if (value instanceof String) {
            try {
                return Double.parseDouble((String) value);
            } catch (NumberFormatException e) {
                return 0.0;
            }
        }
        return 0.0;
    }
    
    /**
     * 从Object获取Int值
     */
    private static Integer getIntValue(Object value) {
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof Long) {
            return ((Long) value).intValue();
        } else if (value instanceof Double) {
            return ((Double) value).intValue();
        } else if (value instanceof Float) {
            return ((Float) value).intValue();
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return 0;
            }
        }
        return 0;
    }

    /**
     * 处理媒体字段（视频和照片）
     * @param dccyVO DccyVO对象
     * @param field 字段对象
     * @param fieldName
     */
    private static void processMediaField(DccyVO dccyVO, FormConfigResponse.Field field, String fieldName) {
        final String TAG = "FormJsonUtils";
        try {
            Object value = field.getValue();
            if (value == null) {
                return;
            }
            
            // 如果值是单个媒体对象
            if (value instanceof Map) {
                processSingleMediaFile(dccyVO, (Map<String, Object>) value,fieldName);
            } 
            // 如果值是媒体对象数组
            else if (value instanceof List) {
                List<?> mediaList = (List<?>) value;
                for (Object mediaItem : mediaList) {
                    if (mediaItem instanceof Map) {
                        processSingleMediaFile(dccyVO, (Map<String, Object>) mediaItem,fieldName);
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "处理媒体字段时出错: " + e.getMessage(), e);
        }
    }
    
    /**
     * 处理单个媒体文件
     * @param dccyVO DccyVO对象
     * @param mediaData 媒体数据映射
     * @param fieldName
     */
    private static void processSingleMediaFile(DccyVO dccyVO, Map<String, Object> mediaData, String fieldName) {
        final String TAG = "FormJsonUtils";
        try {
            // 创建媒体对象
            DccyVO.DccyMediaVO mediaVO = dccyVO.new DccyMediaVO();
            
            // 提取媒体信息
            String fileName = "";
            String filePath = "";
            String fileType = null;
            
            // 提取文件名
            if (mediaData.containsKey("fileName")) {
                Object fileNameObj = mediaData.get("fileName");
                if (fileNameObj instanceof String) {
                    fileName = (String) fileNameObj;
                    mediaVO.setFileName(fileName);
                }
            }
            
            // 提取文件路径
            if (mediaData.containsKey("path")) {
                Object pathObj = mediaData.get("path");
                if (pathObj instanceof String) {
                    filePath = (String) pathObj;
                }
            }
            
            // 提取文件类型
            if (mediaData.containsKey("fileType")) {
                Object fileTypeObj = mediaData.get("fileType");
                if (fileTypeObj instanceof String) {
                    fileType = (String) fileTypeObj;
                    mediaVO.setFileType(fileType);
                }
            }
            
            // 提取经纬度
            if (mediaData.containsKey("jd") && mediaData.containsKey("wd")) {
                mediaVO.setJd(getDoubleValue(mediaData.get("jd")));
                mediaVO.setWd(getDoubleValue(mediaData.get("wd")));
            }
            
            // 提取方位角
            if (mediaData.containsKey("fwj")) {
                mediaVO.setFwj(getDoubleValue(mediaData.get("fwj")));
            }

            // 提取字段名
            if (fieldName!=null) {
                mediaVO.setFieldName(fieldName);
            }
            
            // 提取时间
            if (mediaData.containsKey("fileTime")) {
                Object timeObj = mediaData.get("fileTime");
                if (timeObj instanceof Number) {
                    mediaVO.setFileTime(String.valueOf(timeObj));
                } else if (timeObj instanceof String) {
                    mediaVO.setFileTime((String) timeObj);
                }
            }
            
            // 检查本地是否已存在该文件
            File bcgdCameraDir = new File(Environment.getExternalStorageDirectory() + "/BCGDGISData/BCGDCameraData");
            File localFile = new File(bcgdCameraDir, fileName);
            
            if (localFile.exists()) {
                // 文件已存在，使用本地路径
                Log.d(TAG, "媒体文件已存在: " + localFile.getAbsolutePath());
                mediaVO.setPath(localFile.getAbsolutePath());
            } else {
                // 文件不存在，需要下载
                Log.d(TAG, "需要下载媒体文件: " + filePath + " -> " + localFile.getAbsolutePath());
                // 设置远程路径，待后续下载
                mediaVO.setPath(filePath);
                mediaVO.setNeedDownload(true);
                
                // 不在这里直接下载，避免在主线程中进行网络操作
                // 而是由外部调用downloadMediaFilesIfNeeded来统一处理下载
            }
            
            // 添加到相应的媒体列表
            if (fileType != null && fileType.toLowerCase().contains("视频")) {
                // 视频文件
                List<DccyVO.DccyMediaVO> videoList = dccyVO.getVideoList();
                if (videoList == null) {
                    videoList = new ArrayList<>();
                    dccyVO.setVideoList(videoList);
                }
                videoList.add(mediaVO);
            } else {
                // 默认为照片文件
                List<DccyVO.DccyMediaVO> photoList = dccyVO.getPhotoList();
                if (photoList == null) {
                    photoList = new ArrayList<>();
                    dccyVO.setPhotoList(photoList);
                }
                photoList.add(mediaVO);
            }
        } catch (Exception e) {
            Log.e(TAG, "处理单个媒体文件时出错: " + e.getMessage(), e);
        }
    }

    /**
     * 生成动态表单数据JSON
     * 将DccyVO对象转换为动态表单可识别的JSON格式
     * @param dccy 样点数据对象
     * @return JSON字符串
     */
//    public static String generateFormDataJson(DccyVO dccy) {
//        if (dccy == null) {
//            return null;
//        }
//
//        try {
//            // 创建主JSON对象
//            JSONObject formDataJson = new JSONObject();
//
//            // 添加基本字段
//            if (dccy.getDcjd() != null) formDataJson.put("dcjd", dccy.getDcjd());
//            if (dccy.getDcwd() != null) formDataJson.put("dcwd", dccy.getDcwd());
//
//            // 处理时间格式 - 假设dcTime是时间戳
//            if (dccy.getDcTime() != null) {
//                // 转换时间戳为格式化的日期时间字符串
//                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault());
//                String formattedDate = sdf.format(new java.util.Date(dccy.getDcTime()));
//                formDataJson.put("dcTime", formattedDate);
//            }
//
//            if (dccy.getBz() != null) formDataJson.put("bz", dccy.getBz());
//            if (dccy.getTrMy() != null) formDataJson.put("trMy", dccy.getTrMy());
//            if (dccy.getTrMz() != null) formDataJson.put("trMz", dccy.getTrMz());
//            if (dccy.getPjdyId() != null) formDataJson.put("pjdyId", dccy.getPjdyId());
//            if (dccy.getDcrName() != null) formDataJson.put("dcrName", dccy.getDcrName());
//
//            // 添加表单名称
//            formDataJson.put("formName", "表单 " + dccy.getpjdybh());
//
//            // 创建文件对象
//            JSONObject filesJson = new JSONObject();
//
//            // 处理视频列表
//            List<DccyVO.DccyMediaVO> videoList = dccy.getVideoList();
//            if (videoList != null && !videoList.isEmpty()) {
//                JSONArray videoArray = new JSONArray();
//                for (DccyVO.DccyMediaVO video : videoList) {
//                    JSONObject videoJson = new JSONObject();
//
//                    // 处理视频路径 - 确保是标准格式
//                    String videoPath = standardizePath("/storage/emulated/0/BCGDGISData/BCGDCameraData/", video.getFileName(), video.getPath());
//                    videoJson.put("path", videoPath);
//
//                    videoArray.put(videoJson);
//                }
//                filesJson.put("xcsp", videoArray);
//            }
//
//            // 处理照片列表，同时寻找可能的签名文件
//            List<DccyVO.DccyMediaVO> photoList = dccy.getPhotoList();
//            JSONArray signatureArray = new JSONArray();
//            boolean foundSignature = false;
//
//            if (photoList != null && !photoList.isEmpty()) {
//                JSONArray photoArray = new JSONArray();
//                for (DccyVO.DccyMediaVO photo : photoList) {
//                    // 检查文件是否是签名类型
//                    boolean isSignature = false;
//                    if (photo.getFileName() != null &&
//                        (photo.getFileName().toLowerCase().contains("signature") ||
//                         photo.getFileType() != null && photo.getFileType().contains("签名"))) {
//                        isSignature = true;
//                        foundSignature = true;
//
//                        // 处理签名照片
//                        JSONObject signatureJson = new JSONObject();
//                        String signaturePath = standardizePath("/storage/emulated/0/BCGDGISData/BCGDCameraData/",
//                                                     photo.getFileName(), photo.getPath());
//                        signatureJson.put("path", signaturePath);
//
//                        // 添加位置信息
//                        if (photo.getWd() != null && photo.getWd() != 0) {
//                            signatureJson.put("latitude", photo.getWd());
//                        }
//                        if (photo.getJd() != null && photo.getJd() != 0) {
//                            signatureJson.put("longitude", photo.getJd());
//                        }
//                        if (photo.getFwj() != null && photo.getFwj() != 0) {
//                            signatureJson.put("direction", photo.getFwj());
//                        }
//
//                        signatureArray.put(signatureJson);
//                        Log.d(TAG, "使用现有签名文件: " + signaturePath);
//                    } else {
//                        // 处理普通照片
//                        JSONObject photoJson = new JSONObject();
//
//                        // 处理照片路径 - 确保是标准格式
//                        String photoPath = standardizePath("/storage/emulated/0/BCGDGISData/BCGDCameraData/",
//                                                        photo.getFileName(), photo.getPath());
//                        photoJson.put("path", photoPath);
//
//                        // 添加位置信息
//                        if (photo.getWd() != null && photo.getWd() != 0) {
//                            photoJson.put("latitude", photo.getWd());
//                        }
//                        if (photo.getJd() != null && photo.getJd() != 0) {
//                            photoJson.put("longitude", photo.getJd());
//                        }
//                        if (photo.getFwj() != null && photo.getFwj() != 0) {
//                            photoJson.put("direction", photo.getFwj());
//                        }
//
//                        photoArray.put(photoJson);
//                    }
//                }
//
//                // 将照片数组添加到文件对象
//                if (photoArray.length() > 0) {
//                    filesJson.put("xczp", photoArray);
//                }
//            }
//
//            // 将签名数组添加到文件对象
//            // 如果没有找到实际的签名文件，添加一个默认的占位符
//            if (signatureArray.length() > 0) {
//                filesJson.put("dcrQm", signatureArray);
//                Log.d(TAG, "添加了" + signatureArray.length() + "个实际签名文件");
//            } else {
//                // 创建一个默认签名占位符
//                JSONArray defaultSignatureArray = new JSONArray();
//                JSONObject defaultSignatureJson = new JSONObject();
//
//                // 使用固定的默认签名文件路径
//                String defaultSignaturePath = "/storage/emulated/0/BCGDGISData/BCGDCameraData/signature_default.png";
//                defaultSignatureJson.put("path", defaultSignaturePath);
//
//                // 使用样点位置作为签名位置
//                if (dccy.getDcwd() != null) {
//                    defaultSignatureJson.put("latitude", dccy.getDcwd());
//                }
//                if (dccy.getDcjd() != null) {
//                    defaultSignatureJson.put("longitude", dccy.getDcjd());
//                }
//
//                defaultSignatureArray.put(defaultSignatureJson);
//                filesJson.put("dcrQm", defaultSignatureArray);
//                Log.d(TAG, "未找到实际签名文件，添加默认签名占位符: " + defaultSignaturePath);
//            }
//
//            // 将文件对象添加到主JSON对象
//            formDataJson.put("__files", filesJson);
//
//            return formDataJson.toString();
//        } catch (Exception e) {
//            Log.e(TAG, "生成动态表单数据JSON失败: " + e.getMessage(), e);
//            return null;
//        }
//    }
    
    /**
     * 根据表单配置动态生成表单数据JSON
     * 将DccyVO对象和原始字段配置转换为动态表单可识别的JSON格式
     * @param dccy 样点数据对象
     * @param fieldsGroups 原始字段组配置
     * @return JSON字符串
     */
    public static String generateDynamicFormDataJson(DccyVO dccy, List<FormConfigResponse.FieldGroup> fieldsGroups) {
        if (dccy == null) {
            return null;
        }
        
        try {
            // 创建主JSON对象
            JSONObject formDataJson = new JSONObject();
            
            // 添加表单名称
            formDataJson.put("formName", "表单 " + (dccy.getpjdybh() != null ? dccy.getpjdybh() : "未命名"));
            
            // 创建文件对象
            JSONObject filesJson = new JSONObject();
            
            // 记录所有签名类型字段名
            Set<String> signatureFieldNames = new HashSet<>();
            
            // 动态遍历原始字段组配置，提取字段值和识别字段类型
            if (fieldsGroups != null && !fieldsGroups.isEmpty()) {
                for (FormConfigResponse.FieldGroup group : fieldsGroups) {
                    List<FormConfigResponse.Field> fields = group.getFields();
                    if (fields != null && !fields.isEmpty()) {
                        for (FormConfigResponse.Field field : fields) {
                            String fieldName = field.getFieldName();
                            String fieldType = field.getFieldType();
                            String fieldElement = field.getFieldElement();
                            
                            // 识别签名类型字段
                            if (fieldName != null && 
                                (fieldType != null && fieldType.equalsIgnoreCase("signature") ||
                                 fieldElement != null && fieldElement.equalsIgnoreCase("signature"))) {
                                signatureFieldNames.add(fieldName);
                                Log.d(TAG, "发现签名字段: " + fieldName);
                            }
                            
                            // 处理普通字段值
                            if (fieldName != null && field.getValue() != null) {
                                // 排除媒体类型字段的值，因为它们将单独处理
                                boolean isMediaField = (fieldType != null && (fieldType.equalsIgnoreCase("photo") || 
                                                        fieldType.equalsIgnoreCase("video") || 
                                                        fieldType.equalsIgnoreCase("signature"))) ||
                                                       (fieldElement != null && (fieldElement.equalsIgnoreCase("photo") || 
                                                        fieldElement.equalsIgnoreCase("video") || 
                                                        fieldElement.equalsIgnoreCase("signature")));
                                                        
                                // 新增：检查是否为日期类型
                                boolean isDateField = (fieldType != null && fieldType.equalsIgnoreCase("Date")) ||
                                                      (fieldElement != null && fieldElement.equalsIgnoreCase("date"));

                                if (isDateField && field.getValue() instanceof Number) {
                                    // 处理日期类型：将时间戳格式化
                                    try {
                                        long timestamp = getLongValue(field.getValue());
                                        // 只有当时间戳有效时才格式化
                                        if (timestamp > 0) { 
                                            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault());
                                            String formattedDate = sdf.format(new java.util.Date(timestamp));
                                            formDataJson.put(fieldName, formattedDate);
                                        } else {
                                            // 对于无效或0时间戳，可以放入null或空字符串，根据需要决定
                                            formDataJson.put(fieldName, JSONObject.NULL); 
                                        }
                                    } catch (Exception e) {
                                        Log.e(TAG, "格式化日期字段 '" + fieldName + "' 出错: " + e.getMessage());
                                        // 出错时也放入null
                                        formDataJson.put(fieldName, JSONObject.NULL); 
                                    }
                                } else if (!isMediaField && !isDateField) {
                                    // 处理其他非媒体、非日期类型的字段
                                    if (field.getValue() instanceof String) {
                                        formDataJson.put(fieldName, (String) field.getValue());
                                    } else if (field.getValue() instanceof Number) {
                                        if (field.getValue() instanceof Integer) {
                                            formDataJson.put(fieldName, (Integer) field.getValue());
                                        } else if (field.getValue() instanceof Long) {
                                            formDataJson.put(fieldName, (Long) field.getValue());
                                        } else if (field.getValue() instanceof Double) {
                                            formDataJson.put(fieldName, (Double) field.getValue());
                                        } else {
                                            formDataJson.put(fieldName, field.getValue().toString());
                                        }
                                    } else if (field.getValue() instanceof Boolean) {
                                        formDataJson.put(fieldName, (Boolean) field.getValue());
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            // 创建媒体文件查找映射，用于快速查找签名和其他媒体文件
            Map<String, DccyVO.DccyMediaVO> mediaMap = new HashMap<>();
            
            // 收集所有媒体文件，用于后续签名匹配
            if (dccy.getPhotoList() != null) {
                for (DccyVO.DccyMediaVO media : dccy.getPhotoList()) {
                    if (media.getFieldName() != null) {
                        mediaMap.put(media.getFieldName(), media);
                    }
                }
            }
            
            if (dccy.getVideoList() != null) {
                for (DccyVO.DccyMediaVO media : dccy.getVideoList()) {
                    if (media.getFieldName() != null) {
                        mediaMap.put(media.getFieldName(), media);
                    }
                }
            }
            
            // 动态处理媒体文件
            // 处理视频列表
            List<DccyVO.DccyMediaVO> videoList = dccy.getVideoList();
            if (videoList != null && !videoList.isEmpty()) {
                Map<String, JSONArray> videoFieldArrays = new HashMap<>();
                
                // 按字段名分组处理视频
                for (DccyVO.DccyMediaVO video : videoList) {
                    String fieldName = video.getFieldName();
                    if (fieldName == null || fieldName.isEmpty()) {
                        fieldName = "video"; // 默认字段名
                    }
                    
                    // 如果是签名字段，跳过（签名将单独处理）
                    if (signatureFieldNames.contains(fieldName)) {
                        continue;
                    }
                    
                    JSONArray videoArray = videoFieldArrays.get(fieldName);
                    if (videoArray == null) {
                        videoArray = new JSONArray();
                        videoFieldArrays.put(fieldName, videoArray);
                    }
                    
                    JSONObject videoJson = new JSONObject();
                    // 处理视频路径
                    String videoPath = standardizePath("/storage/emulated/0/BCGDGISData/BCGDCameraData/", 
                                                      video.getFileName(), video.getPath());
                    videoJson.put("path", videoPath);
                    
                    // 添加位置信息 - 确保所有媒体都有这些信息
                    if (video.getWd() != null && video.getWd() != 0) {
                        videoJson.put("latitude", video.getWd());
                    }
                    if (video.getJd() != null && video.getJd() != 0) {
                        videoJson.put("longitude", video.getJd());
                    }
                    if (video.getFwj() != null && video.getFwj() != 0) {
                        videoJson.put("direction", video.getFwj());
                    }
                    
                    // 添加时间信息
                    if (video.getFileTime() != null && !video.getFileTime().isEmpty()) {
                        videoJson.put("fileTime", video.getFileTime());
                    }
                    
                    videoArray.put(videoJson);
                }
                
                // 将所有视频字段添加到文件对象中
                for (Map.Entry<String, JSONArray> entry : videoFieldArrays.entrySet()) {
                    filesJson.put(entry.getKey(), entry.getValue());
                }
            }
            
            // 处理照片列表
            List<DccyVO.DccyMediaVO> photoList = dccy.getPhotoList();
            if (photoList != null && !photoList.isEmpty()) {
                Map<String, JSONArray> photoFieldArrays = new HashMap<>();
                
                // 按字段名分组处理照片
                for (DccyVO.DccyMediaVO photo : photoList) {
                    String fieldName = photo.getFieldName();
                    if (fieldName == null || fieldName.isEmpty()) {
                        fieldName = "photo"; // 默认字段名
                    }
                    
                    // 如果是签名字段，跳过（签名将单独处理）
                    if (signatureFieldNames.contains(fieldName)) {
                        continue;
                    }
                    
                    JSONArray photoArray = photoFieldArrays.get(fieldName);
                    if (photoArray == null) {
                        photoArray = new JSONArray();
                        photoFieldArrays.put(fieldName, photoArray);
                    }
                    
                    JSONObject photoJson = new JSONObject();
                    // 处理照片路径
                    String photoPath = standardizePath("/storage/emulated/0/BCGDGISData/BCGDCameraData/", 
                                                     photo.getFileName(), photo.getPath());
                    photoJson.put("path", photoPath);
                    
                    // 添加位置信息 - 确保所有照片都有这些信息
                    if (photo.getWd() != null && photo.getWd() != 0) {
                        photoJson.put("latitude", photo.getWd());
                    }
                    if (photo.getJd() != null && photo.getJd() != 0) {
                        photoJson.put("longitude", photo.getJd());
                    }
                    if (photo.getFwj() != null && photo.getFwj() != 0) {
                        photoJson.put("direction", photo.getFwj());
                    }
                    
                    // 添加时间信息
                    if (photo.getFileTime() != null && !photo.getFileTime().isEmpty()) {
                        photoJson.put("fileTime", photo.getFileTime());
                    }
                    
                    photoArray.put(photoJson);
                }
                
                // 将所有照片字段添加到文件对象中
                for (Map.Entry<String, JSONArray> entry : photoFieldArrays.entrySet()) {
                    filesJson.put(entry.getKey(), entry.getValue());
                }
            }
            
            // 处理签名文件 - 完全基于表单配置中的签名字段
            if (!signatureFieldNames.isEmpty()) {
                // 遍历所有找到的签名字段
                for (String signatureFieldName : signatureFieldNames) {
                    JSONArray signatureArray = new JSONArray();
                    
                    // 检查是否已有对应字段的媒体对象
                    DccyVO.DccyMediaVO signatureMedia = mediaMap.get(signatureFieldName);
                    
                    if (signatureMedia != null && signatureMedia.getFileName() != null) {
                        // 使用已有媒体对象的文件名和路径
                        JSONObject signatureJson = new JSONObject();
                        
                        // 处理签名文件路径
                        String signaturePath = "/storage/emulated/0/BCGDGISData/BCGDCameraData/"+
                                                            signatureMedia.getFileName();
                        signatureJson.put("path", signaturePath);
                        
                        signatureArray.put(signatureJson);
                    }
                    
                    // 使用字段名作为键，添加到文件对象中
                    filesJson.put(signatureFieldName, signatureArray);
                }
            }
            
            // 将文件对象添加到主JSON对象
            formDataJson.put("__files", filesJson);
            
            // 输出生成的JSON用于调试
            String resultJson = formDataJson.toString();
            Log.d(TAG, "生成的动态表单JSON: " + resultJson);
            
            return resultJson;
        } catch (Exception e) {
            Log.e(TAG, "生成动态表单数据JSON失败: " + e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 根据时间戳格式化为文件名日期部分
     * @param timestamp 时间戳
     * @return 格式化的日期时间字符串，格式为yyyyMMdd_HHmmss
     */
    private static String formatTimestamp(Long timestamp) {
        if (timestamp == null) {
            // 使用当前时间作为默认值
            timestamp = System.currentTimeMillis();
        }
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMdd_HHmmss", java.util.Locale.getDefault());
        return sdf.format(new java.util.Date(timestamp));
    }
    
    /**
     * 标准化文件路径
     * @param basePath 基础路径
     * @param fileName 文件名
     * @param originalPath 原始路径
     * @return 标准化后的路径
     */
    private static String standardizePath(String basePath, String fileName, String originalPath) {
        // 如果文件名为空，尝试从原始路径提取
        if (fileName == null || fileName.isEmpty()) {
            if (originalPath != null && !originalPath.isEmpty()) {
                File file = new File(originalPath);
                fileName = file.getName();
            } else {
                return "";
            }
        }
        
        // 确保基础路径以斜杠结尾
        if (!basePath.endsWith("/")) {
            basePath += "/";
        }
        
        // 构建完整路径
        return basePath + fileName;
    }

} 